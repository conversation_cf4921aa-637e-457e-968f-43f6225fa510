"""
測試 parquet_time_range_checker.py
"""

import pytest
import sys
import os
from datetime import datetime, timedelta, timezone
from unittest.mock import Mock, patch, MagicMock
import pandas as pd

# 加入專案根目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from scripts.parquet_time_range_checker import (
    ParquetTimeRangeChecker,
    ParquetFileInfo,
    TimeGap
)

class TestParquetFileInfo:
    """測試 ParquetFileInfo 資料類別"""


    def test_parquet_file_info_creation(self):
        """測試 ParquetFileInfo 建立"""
        file_info = ParquetFileInfo(
            path="gs://test-bucket/data.parquet",
            size_bytes=1024,
            modified_time=datetime.now()
        )


        assert file_info.path == "gs://test-bucket/data.parquet"
        assert file_info.size_bytes == 1024
        assert file_info.timestamp_columns == []

class TestTimeGap:
    """測試 TimeGap 資料類別"""


    def test_time_gap_properties(self):
        """測試 TimeGap 屬性計算"""
        start_time = datetime(2024, 1, 1, 12, 0, 0)
        end_time = datetime(2024, 1, 2, 12, 0, 0)  # 24 小時後

        gap = TimeGap(start_time, end_time, end_time - start_time)


        gap = TimeGap(start_time, end_time, end_time - start_time)

        assert gap.duration_hours == 24.0
        assert gap.duration_days == 1.0

class TestParquetTimeRangeChecker:
    """測試 ParquetTimeRangeChecker 類別"""


    @patch('scripts.parquet_time_range_checker.storage.Client')
    @patch('scripts.parquet_time_range_checker.gcsfs.GCSFileSystem')
    def test_initialization(self, mock_gcsfs, mock_storage_client):
        """測試檢查器初始化"""
        checker = ParquetTimeRangeChecker(
            bucket_name="test-bucket",
            prefix="data/",
            timestamp_columns=["event_time"]
        )

        assert checker.bucket_name == "test-bucket"
        assert checker.prefix == "data/"
        assert checker.timestamp_columns == ["event_time"]


        assert checker.bucket_name == "test-bucket"
        assert checker.prefix == "data/"
        assert checker.timestamp_columns == ["event_time"]

    @patch('scripts.parquet_time_range_checker.storage.Client')
    @patch('scripts.parquet_time_range_checker.gcsfs.GCSFileSystem')
    def test_list_parquet_files(self, mock_gcsfs, mock_storage_client):
        """測試列出 parquet 檔案"""
        # 模擬 bucket 和 blobs
        mock_bucket = Mock()
        mock_storage_client.return_value.bucket.return_value = mock_bucket


        # 模擬 blob 列表
        mock_blob1 = Mock()
        mock_blob1.name = "data/file1.parquet"
        mock_blob2 = Mock()
        mock_blob2.name = "data/file2.parquet"
        mock_blob3 = Mock()
        mock_blob3.name = "data/file3.txt"  # 非 parquet 檔案

        mock_bucket.list_blobs.return_value = [mock_blob1, mock_blob2, mock_blob3]

        checker = ParquetTimeRangeChecker("test-bucket", "data/")
        files = checker.list_parquet_files()

        assert len(files) == 2
        assert "gs://test-bucket/data/file1.parquet" in files
        assert "gs://test-bucket/data/file2.parquet" in files


        mock_bucket.list_blobs.return_value = [mock_blob1, mock_blob2, mock_blob3]

        checker = ParquetTimeRangeChecker("test-bucket", "data/")
        files = checker.list_parquet_files()

        assert len(files) == 2
        assert "gs://test-bucket/data/file1.parquet" in files
        assert "gs://test-bucket/data/file2.parquet" in files

    @patch('scripts.parquet_time_range_checker.storage.Client')
    @patch('scripts.parquet_time_range_checker.gcsfs.GCSFileSystem')
    def test_find_time_gaps(self, mock_gcsfs, mock_storage_client):
        """測試時間空隙識別"""
        checker = ParquetTimeRangeChecker("test-bucket")


        # 建立測試檔案資訊
        files = [
            ParquetFileInfo(
                path="file1.parquet",
                size_bytes=1024,
                modified_time=datetime.now(),
                min_timestamp=datetime(2024, 1, 1, 10, 0),
                max_timestamp=datetime(2024, 1, 1, 12, 0)
            ),
            ParquetFileInfo(
                path="file2.parquet",
                size_bytes=1024,
                modified_time=datetime.now(),
                min_timestamp=datetime(2024, 1, 1, 14, 0),  # 2小時空隙
                max_timestamp=datetime(2024, 1, 1, 16, 0)
            )
        ]

        gaps = checker.find_time_gaps(files, min_gap_hours=1.0)

        assert len(gaps) == 1
        assert gaps[0].duration_hours == 2.0


        gaps = checker.find_time_gaps(files, min_gap_hours=1.0)

        assert len(gaps) == 1
        assert gaps[0].duration_hours == 2.0

    @patch('scripts.parquet_time_range_checker.storage.Client')
    @patch('scripts.parquet_time_range_checker.gcsfs.GCSFileSystem')
    def test_analyze_time_coverage_no_timestamps(self, mock_gcsfs, mock_storage_client):
        """測試沒有時間戳資訊的情況"""
        checker = ParquetTimeRangeChecker("test-bucket")


        files = [
            ParquetFileInfo(
                path="file1.parquet",
                size_bytes=1024,
                modified_time=datetime.now()
                # 沒有時間戳資訊
            )
        ]

        result = checker.analyze_time_coverage(files)

        assert result['status'] == 'error'
        assert '沒有找到包含時間戳資訊的檔案' in result['message']


        result = checker.analyze_time_coverage(files)

        assert result['status'] == 'error'
        assert '沒有找到包含時間戳資訊的檔案' in result['message']

    @patch('scripts.parquet_time_range_checker.storage.Client')
    @patch('scripts.parquet_time_range_checker.gcsfs.GCSFileSystem')
    def test_analyze_time_coverage_success(self, mock_gcsfs, mock_storage_client):
        """測試成功的時間覆蓋分析"""
        checker = ParquetTimeRangeChecker("test-bucket")


        files = [
            ParquetFileInfo(
                path="file1.parquet",
                size_bytes=1024,
                modified_time=datetime.now(),
                min_timestamp=datetime(2024, 1, 1, 10, 0),
                max_timestamp=datetime(2024, 1, 1, 12, 0),
                row_count=1000
            ),
            ParquetFileInfo(
                path="file2.parquet",
                size_bytes=2048,
                modified_time=datetime.now(),
                min_timestamp=datetime(2024, 1, 2, 10, 0),
                max_timestamp=datetime(2024, 1, 2, 12, 0),
                row_count=2000
            )
        ]

        result = checker.analyze_time_coverage(files)


        result = checker.analyze_time_coverage(files)

        assert result['status'] == 'success'
        assert result['file_count'] == 2
        assert result['total_rows'] == 3000
        assert 'overall_time_range' in result
        assert 'time_gaps' in result


    @patch('scripts.parquet_time_range_checker.storage.Client')
    @patch('scripts.parquet_time_range_checker.gcsfs.GCSFileSystem')
    def test_group_files_by_date(self, mock_gcsfs, mock_storage_client):
        """測試按日期分組檔案"""
        checker = ParquetTimeRangeChecker("test-bucket")


        files = [
            ParquetFileInfo(
                path="file1.parquet",
                size_bytes=1024,
                modified_time=datetime.now(),
                min_timestamp=datetime(2024, 1, 1, 10, 0),
                max_timestamp=datetime(2024, 1, 1, 12, 0)
            ),
            ParquetFileInfo(
                path="file2.parquet",
                size_bytes=1024,
                modified_time=datetime.now(),
                min_timestamp=datetime(2024, 1, 1, 14, 0),
                max_timestamp=datetime(2024, 1, 1, 16, 0)
            ),
            ParquetFileInfo(
                path="file3.parquet",
                size_bytes=1024,
                modified_time=datetime.now(),
                min_timestamp=datetime(2024, 1, 2, 10, 0),
                max_timestamp=datetime(2024, 1, 2, 12, 0)
            )
        ]

        grouped = checker.group_files_by_date(files)

        # 檢查是否正確按日期分組
        assert len(grouped) == 2  # 兩個不同日期


        grouped = checker.group_files_by_date(files)

        # 檢查是否正確按日期分組
        assert len(grouped) == 2  # 兩個不同日期

        # 檢查 2024年1月1日 有2個檔案
        jan_1_key = None
        jan_2_key = None
        for key in grouped.keys():
            if '20240101' in key:
                jan_1_key = key
            elif '20240102' in key:
                jan_2_key = key


        assert jan_1_key is not None
        assert jan_2_key is not None
        assert len(grouped[jan_1_key]) == 2
        assert len(grouped[jan_2_key]) == 1

@pytest.mark.integration
class TestParquetTimeRangeCheckerIntegration:
    """整合測試 (需要實際的 GCS 存取權限)"""


    @pytest.mark.skip(reason="需要實際的 GCS 認證")
    def test_real_gcs_integration(self):
        """測試真實 GCS 整合 (需要認證)"""
        # 這個測試需要真實的 GCS 存取權限
        # 在實際環境中測試時取消 skip
        checker = ParquetTimeRangeChecker(
            bucket_name="your-test-bucket",
            prefix="test-data/"
        )


        report = checker.check_time_range(max_files=5)
        assert isinstance(report, str)
        assert len(report) > 0

if __name__ == "__main__":
    pytest.main([__file__, "-v"])
