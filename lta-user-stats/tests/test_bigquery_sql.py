import unittest
from unittest.mock import patch, MagicMock
import sys
import os
import re
from datetime import datetime

# 添加專案根目錄到 Python 路徑
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 將日誌設置為警告級別，減少測試輸出
import logging
logging.getLogger('lta_user_stats').setLevel(logging.WARNING)

class TestBigQuerySQL(unittest.TestCase):
    """測試 BigQuery SQL 查詢的結構和語法"""

    def setUp(self):
        # 為每個測試導入模組，以便在需要時重新導入
        from scripts.init_user_stats_for_ec_id import calculate_user_stats_via_bigquery
        self.calculate_func = calculate_user_stats_via_bigquery

    def extract_sql_query(self, ec_id, time_range=None):
        """
        從函數中提取 SQL 查詢，通過模擬 execute_single_query 捕獲傳入的 SQL
        """
        captured_query = []

        def mock_execute_query(query, *args, **kwargs):
            captured_query.append(query)
            # 模擬返回 None，這樣可以避免處理統計計算邏輯
            return None

        with patch('scripts.init_user_stats_for_ec_id.execute_single_query',
                   side_effect=mock_execute_query):
            # 執行函數
            self.calculate_func(ec_id, time_range)

        return captured_query[0] if captured_query else None

    def test_sql_contains_all_required_ctes(self):
        """測試 SQL 查詢包含所有必要的 CTE (Common Table Expressions)"""
        sql = self.extract_sql_query(107)

        # 檢查是否包含所有必要的 CTE
        required_ctes = [
            'UserSessions',
            'UserInteractions',
            'UserPurchases',
            'UserRegistrations'
        ]

        for cte in required_ctes:
            self.assertIn(f'{cte} AS (', sql, f"SQL 查詢應包含 {cte} CTE")

    def test_sql_aggregation_functions(self):
        """測試 SQL 查詢中的彙總函數使用正確"""
        sql = self.extract_sql_query(107)

        # 檢查會話計數計算
        self.assertRegex(sql, r'COUNT\s*\(\s*DISTINCT\s+session\.id\s*\)\s+AS\s+total_sessions',
                        "會話計數應使用 COUNT(DISTINCT session.id)")

        # 檢查購買次數計算
        self.assertRegex(sql, r'COUNT\s*\(\s*\*\s*\)\s+AS\s+purchase_count',
                         "購買次數應使用 COUNT(*)")

        # 檢查購買金額計算
        self.assertRegex(sql, r'SUM\s*\(\s*event\.value\s*\)\s+AS\s+total_purchase_amount',
                         "購買金額應使用 SUM(event.value)")

        # 檢查時間計算 - 第一互動時間、註冊時間
        basic_time_funcs = [
            r'MIN\s*\(\s*event_time\s*\)\s+AS\s+first_interaction_time',
            r'MIN\s*\(\s*event_time\s*\)\s+AS\s+registration_time'
        ]

        for pattern in basic_time_funcs:
            self.assertRegex(sql, pattern, f"SQL 應包含正確的時間計算: {pattern}")

        # 檢查最後互動時間 (使用了 CASE WHEN 結構)
        self.assertRegex(
            sql,
            r'CASE\s+WHEN\s+COUNT\(\*\)\s*=\s*1\s+THEN\s+MIN\s*\(\s*event_time\s*\)\s+ELSE\s+MAX\s*\(\s*event_time\s*\)\s+END\s+AS\s+last_interaction_time',
            "SQL 最後互動時間應使用 CASE WHEN 條件"
        )

        # 檢查購買時間計算
        self.assertRegex(sql, r'MIN\s*\(\s*event_time\s*\)\s+AS\s+first_purchase_time',
                         "SQL 應包含正確的首次購買時間計算")

        # 檢查最後購買時間 (使用了 CASE WHEN 結構)
        self.assertRegex(
            sql,
            r'CASE\s+WHEN\s+COUNT\(\*\)\s*=\s*1\s+THEN\s+MIN\s*\(\s*event_time\s*\)\s+ELSE\s+MAX\s*\(\s*event_time\s*\)\s+END\s+AS\s+last_purchase_time',
            "SQL 最後購買時間應使用 CASE WHEN 條件"
        )

    def test_sql_time_range_filter(self):
        """測試時間範圍過濾條件正確添加"""
        # 測試有時間範圍的情況
        time_range = [datetime(2023, 1, 1), datetime(2023, 3, 31)]
        sql_with_time = self.extract_sql_query(107, time_range)

        # 檢查時間過濾條件
        time_pattern = r'event_time\s+BETWEEN\s+TIMESTAMP\s*\(\s*\'[^\']+\'\s*\)\s+AND\s+TIMESTAMP\s*\(\s*\'[^\']+\'\s*\)'
        self.assertRegex(sql_with_time, time_pattern, "應包含正確的時間範圍過濾條件")

        # 檢查是否包含正確的時間值
        start_time_str = time_range[0].strftime('%Y-%m-%d %H:%M:%S')
        end_time_str = time_range[1].strftime('%Y-%m-%d %H:%M:%S')
        self.assertIn(start_time_str, sql_with_time, "SQL 應包含正確的開始時間")
        self.assertIn(end_time_str, sql_with_time, "SQL 應包含正確的結束時間")

    def test_sql_purchase_criteria(self):
        """測試購買條件過濾正確"""
        sql = self.extract_sql_query(107)

        # 檢查購買事件過濾條件
        purchase_filters = [
            r'event\.name\s*=\s*\'purchase\'',
            r'event\.value\s*>\s*0'
        ]

        for pattern in purchase_filters:
            self.assertRegex(sql, pattern, f"購買過濾條件應包含: {pattern}")

    def test_sql_join_structure(self):
        """測試 SQL JOIN 結構正確"""
        sql = self.extract_sql_query(107)

        # 檢查是否使用 LEFT JOIN
        joins = [
            r'FROM\s+UserInteractions\s+AS\s+i',
            r'LEFT\s+JOIN\s+UserSessions\s+AS\s+s\s+ON\s+i\.permanent\s*=\s*s\.permanent',
            r'LEFT\s+JOIN\s+UserPurchases\s+AS\s+p\s+ON\s+i\.permanent\s*=\s*p\.permanent',
            r'LEFT\s+JOIN\s+UserRegistrations\s+AS\s+r\s+ON\s+i\.permanent\s*=\s*r\.permanent'
        ]

        for pattern in joins:
            self.assertRegex(sql, pattern, f"SQL JOIN 結構應包含: {pattern}")

    def test_sql_coalesce_usage(self):
        """測試 COALESCE 函數使用正確"""
        sql = self.extract_sql_query(107)

        # 檢查 COALESCE 使用
        coalesce_patterns = [
            r'COALESCE\s*\(\s*s\.total_sessions\s*,\s*0\s*\)\s+AS\s+total_sessions',
            r'COALESCE\s*\(\s*p\.purchase_count\s*,\s*0\s*\)\s+AS\s+purchase_count',
            r'COALESCE\s*\(\s*p\.total_purchase_amount\s*,\s*0\s*\)\s+AS\s+total_purchase_amount'
        ]

        for pattern in coalesce_patterns:
            self.assertRegex(sql, pattern, f"SQL 應正確使用 COALESCE: {pattern}")

if __name__ == '__main__':
    unittest.main()