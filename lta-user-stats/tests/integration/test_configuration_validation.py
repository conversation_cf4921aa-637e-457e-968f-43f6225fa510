#!/usr/bin/env python3
"""
配置驗證集成測試

整合 test_terraform_config.py 和 test_final_config.py 的功能，
驗證 Terraform 配置和參數解析的正確性。
"""

import os
import sys
import json
import pytest
import logging
from unittest.mock import patch, MagicMock

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from src.main import parse_bool


class TestConfigurationValidation:
    """配置驗證測試類別"""

    @pytest.fixture
    def terraform_config(self):
        """Terraform 配置 fixture"""
        return {
            'use_bigquery_compute': True,
            'auto_repair_missing_days': True,
            'max_repair_days': 30
        }

    @pytest.fixture
    def old_config(self):
        """舊的 Terraform 配置（缺少部分參數）"""
        return {
            'use_bigquery_compute': True
            # 缺少 auto_repair_missing_days 和 max_repair_days
        }

    @pytest.fixture
    def final_config(self):
        """最終配置 fixture"""
        return {
            'ec_ids': [107],
            'use_bigquery_compute': True,
            'auto_repair_missing_days': True,
            'max_repair_days': 30
        }

    def test_terraform_config_parsing(self, terraform_config):
        """測試 Terraform 配置參數解析"""

        # 解析配置參數
        auto_repair = parse_bool(terraform_config.get('auto_repair_missing_days', True))
        use_bigquery = parse_bool(terraform_config.get('use_bigquery_compute', False))
        max_days = int(terraform_config.get('max_repair_days', 30))

        # 驗證解析結果
        assert auto_repair is True, "auto_repair_missing_days 應該為 True"
        assert use_bigquery is True, "use_bigquery_compute 應該為 True"
        assert max_days == 30, "max_repair_days 應該為 30"

        # 驗證會觸發增量修復
        will_trigger_repair = auto_repair and use_bigquery
        assert will_trigger_repair, "配置應該觸發增量修復功能"

        logging.info(f"✓ Terraform 配置解析正確: {terraform_config}")

    def test_old_vs_new_config_behavior(self, old_config, terraform_config):
        """測試舊配置 vs 新配置的行為差異"""

        # 解析舊配置（依賴預設值）
        old_auto_repair = parse_bool(old_config.get('auto_repair_missing_days', True))  # 預設值
        old_use_bigquery = parse_bool(old_config.get('use_bigquery_compute', False))
        old_max_days = int(old_config.get('max_repair_days', 30))  # 預設值

        # 解析新配置（明確設定）
        new_auto_repair = parse_bool(terraform_config.get('auto_repair_missing_days', True))
        new_use_bigquery = parse_bool(terraform_config.get('use_bigquery_compute', False))
        new_max_days = int(terraform_config.get('max_repair_days', 30))

        # 驗證兩種配置的行為
        old_will_trigger = old_auto_repair and old_use_bigquery
        new_will_trigger = new_auto_repair and new_use_bigquery

        assert old_will_trigger, "舊配置應該也會觸發增量修復（依賴預設值）"
        assert new_will_trigger, "新配置應該觸發增量修復（明確設定）"
        assert old_will_trigger == new_will_trigger, "兩種配置的行為應該一致"

        # 但新配置更明確，避免依賴預設值的風險
        old_explicit = all(key in old_config for key in ['auto_repair_missing_days', 'max_repair_days'])
        new_explicit = all(key in terraform_config for key in ['auto_repair_missing_days', 'max_repair_days'])

        assert not old_explicit, "舊配置不夠明確"
        assert new_explicit, "新配置應該明確設定所有參數"

        logging.info("✓ 舊配置 vs 新配置行為對比正確")

    def test_final_config_validation(self, final_config):
        """測試最終配置驗證"""

        # 解析最終配置
        use_bigquery = parse_bool(final_config["use_bigquery_compute"])
        auto_repair = parse_bool(final_config["auto_repair_missing_days"])
        max_days = final_config["max_repair_days"]
        ec_ids = final_config["ec_ids"]

        # 驗證配置項
        assert use_bigquery is True, "use_bigquery_compute 應該為 True"
        assert auto_repair is True, "auto_repair_missing_days 應該為 True"
        assert max_days == 30, "max_repair_days 應該為 30"
        assert isinstance(ec_ids, list) and len(ec_ids) > 0, "ec_ids 應該是非空列表"

        # 驗證會正確觸發功能
        if use_bigquery and auto_repair:
            expected_behavior = "會正確觸發 BigQuery 計算模式和增量修復功能"
            logging.info(f"✓ {expected_behavior}")
            assert True
        else:
            pytest.fail("配置有問題！")

    def test_parse_bool_function(self):
        """測試 parse_bool 函數的各種輸入"""

        test_cases = [
            # (輸入值, 期望結果)
            (True, True),
            (False, False),
            ('true', True),
            ('false', False),
            ('True', True),
            ('False', False),
            ('1', True),
            ('0', False),
            (1, True),
            (0, False),
            ('yes', True),
            ('no', False),
            ('YES', True),
            ('NO', False),
        ]

        for input_val, expected in test_cases:
            result = parse_bool(input_val)
            assert result == expected, f"parse_bool({input_val}) 返回 {result}，期望 {expected}"

        logging.info("✓ parse_bool 函數測試通過")

    def test_environment_variable_parsing(self):
        """測試環境變數解析"""

        # 模擬環境變數
        test_env_vars = {
            'AUTO_REPAIR_MISSING_DAYS': 'true',
            'USE_BIGQUERY_COMPUTE': 'True',
            'MAX_REPAIR_DAYS': '30'
        }

        with patch.dict(os.environ, test_env_vars):
            # 解析環境變數
            auto_repair = parse_bool(os.environ.get('AUTO_REPAIR_MISSING_DAYS', 'false'))
            use_bigquery = parse_bool(os.environ.get('USE_BIGQUERY_COMPUTE', 'false'))
            max_days = int(os.environ.get('MAX_REPAIR_DAYS', '0'))

            assert auto_repair is True
            assert use_bigquery is True
            assert max_days == 30

        logging.info("✓ 環境變數解析測試通過")

    @patch('src.main.calculate_user_stats_via_bigquery_wrapper')
    def test_config_integration_with_function_call(self, mock_wrapper, terraform_config):
        """測試配置與函數調用的集成"""

        # 設定 mock 返回值
        mock_wrapper.return_value = {
            'calculate_stats': True,
            'auto_repair_enabled': True,
            'days_repaired': 3,
            'repaired_dates': ['2024-01-01', '2024-01-02', '2024-01-03'],
            'message': '成功完成增量修復計算'
        }

        # 模擬使用配置調用函數
        from datetime import datetime, timezone

        result = mock_wrapper(
            ec_ids=[107],
            date_range=[
                datetime(2024, 1, 1, tzinfo=timezone.utc),
                datetime(2024, 1, 3, tzinfo=timezone.utc)
            ],
            should_calculate_stats=True,
            should_save_snapshot=False,
            should_write_lta=False,
            add_to_dxp=False,
            auto_repair_missing_days=parse_bool(terraform_config['auto_repair_missing_days']),
            max_repair_days=terraform_config['max_repair_days']
        )

        # 驗證調用和結果
        assert mock_wrapper.called
        assert result['auto_repair_enabled'] is True
        assert result['days_repaired'] > 0

        # 驗證調用參數
        call_args = mock_wrapper.call_args[1]  # 關鍵字參數
        assert call_args['auto_repair_missing_days'] is True
        assert call_args['max_repair_days'] == 30

        logging.info("✓ 配置與函數調用集成測試通過")

    def test_json_serialization(self, final_config):
        """測試配置的 JSON 序列化和反序列化"""

        # 序列化配置
        json_str = json.dumps(final_config, indent=2)
        assert isinstance(json_str, str)

        # 反序列化配置
        restored_config = json.loads(json_str)
        assert restored_config == final_config

        # 驗證反序列化後的配置仍然有效
        auto_repair = parse_bool(restored_config.get('auto_repair_missing_days', True))
        use_bigquery = parse_bool(restored_config.get('use_bigquery_compute', False))

        assert auto_repair and use_bigquery

        logging.info("✓ JSON 序列化/反序列化測試通過")

    def test_edge_cases(self):
        """測試邊緣情況"""

        # 測試空配置
        empty_config = {}
        auto_repair = parse_bool(empty_config.get('auto_repair_missing_days', True))
        use_bigquery = parse_bool(empty_config.get('use_bigquery_compute', False))

        assert auto_repair is True  # 預設值
        assert use_bigquery is False  # 預設值

        # 測試無效值
        invalid_config = {
            'auto_repair_missing_days': 'invalid',
            'use_bigquery_compute': None,
            'max_repair_days': 'not_a_number'
        }

        # parse_bool 應該對無效值有合理的處理
        try:
            auto_repair = parse_bool(invalid_config.get('auto_repair_missing_days'))
            # 如果沒有拋出異常，應該有合理的返回值
            assert isinstance(auto_repair, bool)
        except (ValueError, TypeError):
            # 如果拋出異常也是可接受的
            pass

        logging.info("✓ 邊緣情況測試通過")


if __name__ == '__main__':
    # 設定測試日誌
    logging.basicConfig(level=logging.INFO)

    # 執行測試
    pytest.main([__file__, '-v', '--tb=short'])