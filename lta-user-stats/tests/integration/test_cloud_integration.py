import pandas as pd
from unittest.mock import patch, MagicMock
from datetime import datetime, timezone, timedelta
from src.core.cloud_integration import BigQueryClient, write_to_special_lta, StorageManager, save_user_stats_snapshot
import src.core.cloud_integration as cloud_integration
import os
import pytest
import uuid

def test_write_to_special_lta():
    """測試寫入特殊 LTA 表格的功能"""
    # 明確定義測試用日期
    current_date = datetime(2024, 6, 20, tzinfo=timezone.utc)

    # 添加所有必要的欄位
    mock_data = pd.DataFrame({
        'permanent': ['u1', 'u2'],
        'ec_id': [107, 107],
        'last_interaction_time': [
            current_date - timedelta(days=10),
            current_date - timedelta(days=30)
        ],
        'first_interaction_time': [
            current_date - timedelta(days=100),
            current_date - timedelta(days=120)
        ],
        'last_interaction_days': [10, 30],
        'purchase_count': [1, 0],
        'total_purchase_amount': [100.0, 0.0]
    })

    with patch('src.core.cloud_integration.bigquery.Client') as mock_bq, \
         patch('src.rules.dynamic_audience_rules.calculate_audience_segments_dynamic') as mock_segments, \
         patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules') as mock_fetch:
        # 模擬表格存在
        mock_client = MagicMock()
        mock_bq.return_value = mock_client
        mock_client.get_table = MagicMock(return_value=True)

        # Mock fetch_audience_mapping_with_rules 避免實際 GCS 調用
        mock_fetch.return_value = {
        'tm:c_9999_107_c_001': {
            'description': '測試規則 - 有購買記錄的用戶',
            'rule': {
                'and': [
                    {'>=': [{'var': 'purchase_count'}, 0]}
                ]
            },
            'data': {},
            'metadata': {
                'created_at': '2025-01-01T00:00:00Z',
                'updated_at': '2025-01-01T00:00:00Z'
            }
        }
    }

        # 模擬分群結果
        mock_segments.return_value = {
            "tm:c_107_c_001": ['u1', 'u2']
        }

        # 執行寫入
        result = write_to_special_lta(mock_data, 107, current_date)
        result = write_to_special_lta(mock_data, 107, current_date)

        # 驗證函數成功執行
        assert result['success'] is True
        # 驗證函數成功執行
        assert result['success'] is True

        # 驗證資料被寫入
        # 驗證 BigQuery 客戶端被調用 (允許多次調用)
    assert mock_client.load_table_from_dataframe.call_count >= 1


def test_check_snapshot_exists():
    """測試檢查快照是否存在的功能"""
    # 模擬 StorageManager
    with patch('src.core.cloud_integration.StorageManager') as mock_storage_manager:
        mock_instance = mock_storage_manager.return_value

        # 測試1: 快照存在
        # Mock load_snapshot 返回非空 DataFrame
        mock_instance.load_snapshot.return_value = pd.DataFrame({'test': [1, 2, 3]})
        # Mock load_snapshot 返回非空 DataFrame
        mock_instance.load_snapshot.return_value = pd.DataFrame({'test': [1, 2, 3]})

        # 初始化測試對象
        client = BigQueryClient()

        # 設定測試時間
        current_date = datetime(2024, 2, 26, tzinfo=timezone.utc)
        next_date = datetime(2024, 2, 27, tzinfo=timezone.utc)

        # 檢查快照存在
        result = client.check_snapshot_exists(107, (current_date, next_date))

        # 驗證結果
        assert result is True
        # 驗證快照載入被調用 (允許多次調用)
        assert mock_instance.load_snapshot.call_count >= 1

        # 測試2: 快照不存在
        # Mock load_snapshot 返回空 DataFrame
        mock_instance.load_snapshot.return_value = pd.DataFrame()
        mock_instance.load_snapshot.reset_mock()

        result = client.check_snapshot_exists(107, (current_date, next_date))

        # 驗證結果
        assert result is False
        assert mock_instance.load_snapshot.call_count >= 1

@pytest.mark.skipif(os.environ.get('CI') == 'true', reason="真實的 GCS 連線測試不應在 CI 環境中執行")
def test_gcs_snapshot_path_handling():
    """測試 GCS 快照路徑處理的完整流程 (真實連線到 GCS)"""
    # 生成唯一的測試 ID 以避免衝突
    test_id = str(uuid.uuid4())[:8]

    # 確保測試環境變數設置
    with patch.dict(os.environ, {"PYTEST_CURRENT_TEST": "yes"}):
        # 創建測試數據
        test_df = pd.DataFrame({
            'ec_id': [107],
            'permanent': [f'test_user_{test_id}'],
            'first_interaction_time': [datetime.now(timezone.utc)],
            'last_interaction_time': [datetime.now(timezone.utc)],
            'total_sessions': [1],
            'total_purchase_amount': [100.0]
        })

        # 1. 儲存快照到 GCS
        storage_manager = StorageManager()
        save_result = storage_manager.save_snapshot(
            data_df=test_df,
            date=datetime.now(timezone.utc),
            ec_id=107
        )

        # 檢查是否成功
        assert save_result['success'] is True
        snapshot_path = save_result['path']
        assert snapshot_path.startswith('gs://')
        assert 'LTA_TEST' in snapshot_path  # 確認使用測試前綴
        assert 'ec_107' in snapshot_path

        # 2. 嘗試從返回的路徑讀取 (模擬 calculate_user_stats 的行為)
        from src.core.data_processing import calculate_user_stats

        # 模擬 calculate_user_stats 中的讀取邏輯
        try:
            if snapshot_path.startswith('gs://'):
                # 如果是 GCS 路徑，需要先下載
                taiwan_date_str = datetime.now(timezone.utc).strftime('%Y%m%d')
                local_temp = f"/tmp/test_user_stats_{test_id}_{taiwan_date_str}.parquet"

                # 從 gs:// 路徑轉換為 blob 路徑
                blob_path = snapshot_path.replace('gs://tagtoo-ml-workflow/', '')
                storage_manager.bucket.blob(blob_path).download_to_filename(local_temp)

                # 讀取下載的檔案
                loaded_df = pd.read_parquet(local_temp)

                # 清理臨時檔案
                if os.path.exists(local_temp):
                    os.remove(local_temp)
            else:
                # 本地路徑直接讀取 (這個分支不應該被執行)
                loaded_df = pd.read_parquet(snapshot_path)
                assert False, "路徑應該以 gs:// 開頭"

            # 驗證讀取的數據
            assert len(loaded_df) == 1
            assert loaded_df['permanent'].iloc[0] == f'test_user_{test_id}'

        except Exception as e:
            assert False, f"讀取 GCS 快照失敗: {str(e)}"

        # 3. 清理測試數據 (如果需要)
        try:
            blob_path = snapshot_path.replace('gs://tagtoo-ml-workflow/', '')
            storage_manager.bucket.blob(blob_path).delete()
        except Exception as e:
            print(f"清理測試數據失敗 (不影響測試結果): {str(e)}")

@pytest.mark.skipif(os.environ.get('CI') == 'true', reason="真實的 GCS 連線測試不應在 CI 環境中執行")
def test_ec_id_int64_type_handling():
    """測試 ec_id 欄位使用 Int64 類型時的處理邏輯"""
    # 生成唯一的測試 ID 以避免衝突
    test_id = str(uuid.uuid4())[:8]

    print("\n\n========== 開始測試 ec_id 類型處理 ==========")

    # 確保測試環境變數設置
    with patch.dict(os.environ, {"PYTEST_CURRENT_TEST": "yes"}):
        # 創建測試數據，特意使用 Int64 類型的 ec_id
        test_df = pd.DataFrame({
            'ec_id': pd.Series([107], dtype='Int64'),  # 使用 pandas 的 Int64 類型（大寫I開頭）
            'permanent': [f'test_user_{test_id}'],
            'first_interaction_time': [datetime.now(timezone.utc)],
            'last_interaction_time': [datetime.now(timezone.utc)],
            'total_sessions': [1],
            'purchase_count': [1],
            'total_purchase_amount': [100.0]
        })

        # 確認 ec_id 確實是 Int64 類型
        print(f"原始 ec_id 類型: {test_df['ec_id'].dtype}")
        print(f"原始 DataFrame 資訊:")
        print(test_df.dtypes)
        assert str(test_df['ec_id'].dtype) == 'Int64'

        # 儲存快照到 GCS
        storage_manager = StorageManager()
        save_result = storage_manager.save_snapshot(
            data_df=test_df,
            date=datetime.now(timezone.utc),
            ec_id=107
        )

        # 檢查是否成功
        assert save_result['success'] is True
        snapshot_path = save_result['path']
        print(f"快照儲存路徑: {snapshot_path}")
        assert snapshot_path.startswith('gs://')
        assert 'LTA_TEST' in snapshot_path

        # 從 GCS 路徑轉換為 blob 路徑
        blob_path = snapshot_path.replace('gs://tagtoo-ml-workflow/', '')
        # 下載檔案到本機
        local_temp = f"/tmp/test_user_stats_int64_{test_id}.parquet"
        storage_manager.bucket.blob(blob_path).download_to_filename(local_temp)
        print(f"檔案下載至: {local_temp}")

        # 讀取下載的檔案
        loaded_df = pd.read_parquet(local_temp)

        # 檢查讀取後的資料類型
        print(f"\n讀取後 ec_id 類型: {loaded_df['ec_id'].dtype}")
        print(f"讀取後 DataFrame 資訊:")
        print(loaded_df.dtypes)

        # 測試驗證原始 dataframe
        from src.core.data_processing import validate_snapshot
        print("\n驗證原始讀取的 DataFrame...")
        validation_result = validate_snapshot(loaded_df)
        print(f"驗證結果 (原始): {validation_result}")

        # 嘗試手動轉換 ec_id 為 int64 類型再驗證
        print("\n嘗試手動轉換 ec_id 為 int64 類型...")
        loaded_df_fixed = loaded_df.copy()
        loaded_df_fixed['ec_id'] = loaded_df_fixed['ec_id'].astype('int64')
        print(f"轉換後 ec_id 類型: {loaded_df_fixed['ec_id'].dtype}")
        print(f"轉換後 DataFrame 資訊:")
        print(loaded_df_fixed.dtypes)

        validation_result_fixed = validate_snapshot(loaded_df_fixed)
        print(f"驗證結果 (轉換後): {validation_result_fixed}")

        print("\n========== 測試 ec_id 類型處理完成 ==========")

        # 清理臨時檔案和 GCS 測試檔案
        if os.path.exists(local_temp):
            os.remove(local_temp)
            print(f"已刪除臨時檔案: {local_temp}")

        try:
            storage_manager.bucket.blob(blob_path).delete()
            print(f"已刪除 GCS 測試檔案: {blob_path}")
        except Exception as e:
            print(f"清理測試數據失敗 (不影響測試結果): {str(e)}")

        # 確認最終結果
        assert validation_result or validation_result_fixed, "經過類型轉換後的驗證應該通過"