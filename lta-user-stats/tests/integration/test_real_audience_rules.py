#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試真實的 audience_rules.json 中的規則
專門測試 EC ID 2980 的實際規則，特別是規則 003 和 006 因為資料時間範圍不足的情況
"""

import pytest
import pandas as pd
import numpy as np
import sys
import os
import json
from datetime import datetime, timedelta

# 添加項目根目錄到 Python 路徑
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.utils import vectorized_evaluate, analyze_data_time_range, check_rule_data_requirement

class TestRealAudienceRules:
    """測試真實的 audience_rules.json 規則"""

    @pytest.fixture
    def real_audience_rules(self):
        """從實際的 audience_rules.json 檔案載入 EC ID 2980 的規則"""
        # 嘗試多個可能的路徑，包含 CI 環境
        possible_paths = [
            os.path.join(project_root, 'scripts', 'audience_rules.json'),
            os.path.join(project_root, 'audience_rules.json'),
            os.path.join(os.path.dirname(__file__), '..', '..', 'scripts', 'audience_rules.json'),
            # CI 環境中可能的路徑
            os.path.join(os.getcwd(), 'lta-user-stats', 'scripts', 'audience_rules.json'),
            'lta-user-stats/scripts/audience_rules.json',
            'scripts/audience_rules.json',
            './audience_rules.json',
            '../audience_rules.json'
        ]

        rules_file_path = None
        for path in possible_paths:
            if os.path.exists(path):
                rules_file_path = path
                print(f"找到規則檔案: {rules_file_path}")
                break

        if not rules_file_path:
            # 記錄當前環境資訊以便除錯
            print(f"當前工作目錄: {os.getcwd()}")
            print(f"project_root: {project_root}")
            print(f"__file__: {__file__}")
            print(f"已檢查的路徑: {possible_paths}")
            raise FileNotFoundError(f"無法找到 audience_rules.json 檔案")

        with open(rules_file_path, 'r', encoding='utf-8') as f:
            all_rules = json.load(f)

        # 取得 EC ID 2980 的規則，並移除 9999 前綴
        ec_2980_rules = all_rules.get('2980', {}).get('rules', {})

        # 將規則 ID 轉換為去除 9999 前綴的格式
        processed_rules = {}
        for rule_id, rule_data in ec_2980_rules.items():
            # 將 tm:c_9999_2980_c_xxx 轉換為 tm:c_2980_c_xxx
            new_rule_id = rule_id.replace('9999_', '')
            processed_rules[new_rule_id] = rule_data

        return processed_rules

    @pytest.fixture
    def limited_time_range_data(self):
        """建立只有 0-13 天時間範圍的測試資料（模擬 EC ID 2980 的實際情況）"""
        np.random.seed(42)  # 固定隨機種子以確保結果一致

        data = []

        # 建立 1000 個用戶的資料，last_interaction_days 範圍為 0-13 天
        for i in range(1000):
            # 基本的互動天數 (0-13 天)
            last_interaction_days = np.random.randint(0, 14)
            last_view_item_days = np.random.randint(0, 14)
            last_add_to_cart_days = np.random.randint(0, 14) if np.random.random() > 0.7 else -1
            last_purchase_days = np.random.randint(0, 14) if np.random.random() > 0.8 else -1

            user_data = {
                'permanent': f'user_{i:04d}',
                'ec_id': 2980,

                # 時間相關欄位 - 實際的天數
                'last_interaction_days': last_interaction_days,
                'last_view_item_days': last_view_item_days,
                'last_add_to_cart_days': last_add_to_cart_days,
                'last_purchase_days': last_purchase_days,

                # 時間戳欄位 - 設定為非 null 以符合規則檢查
                'last_interaction_time': datetime.now() - timedelta(days=last_interaction_days),
                'last_view_item_time': datetime.now() - timedelta(days=last_view_item_days) if last_view_item_days != -1 else None,
                'last_add_to_cart_time': datetime.now() - timedelta(days=last_add_to_cart_days) if last_add_to_cart_days != -1 else None,
                'last_purchase_time': datetime.now() - timedelta(days=last_purchase_days) if last_purchase_days != -1 else None,

                # 其他統計欄位
                'purchase_count': np.random.randint(0, 3) if last_purchase_days != -1 else 0,
                'total_purchase_amount': np.random.uniform(100, 5000) if last_purchase_days != -1 else 0,
                'view_item_count': np.random.randint(1, 20),
                'add_to_cart_count': np.random.randint(0, 5) if last_add_to_cart_days != -1 else 0
            }
            data.append(user_data)

        return pd.DataFrame(data)

    @pytest.fixture
    def sufficient_time_range_data(self):
        """建立有足夠時間範圍的測試資料（0-400 天），用來測試規則是否正常工作"""
        np.random.seed(123)  # 不同的隨機種子

        data = []

        # 建立 1000 個用戶的資料，時間範圍擴展到 0-400 天
        for i in range(1000):
            # 擴展的互動天數 (0-400 天)
            last_interaction_days = np.random.randint(0, 401)
            last_view_item_days = np.random.randint(0, 401)
            last_add_to_cart_days = np.random.randint(0, 401) if np.random.random() > 0.7 else -1
            last_purchase_days = np.random.randint(0, 401) if np.random.random() > 0.8 else -1

            user_data = {
                'permanent': f'user_ext_{i:04d}',
                'ec_id': 2980,

                # 時間相關欄位 - 實際的天數
                'last_interaction_days': last_interaction_days,
                'last_view_item_days': last_view_item_days,
                'last_add_to_cart_days': last_add_to_cart_days,
                'last_purchase_days': last_purchase_days,

                # 時間戳欄位 - 設定為非 null 以符合規則檢查
                'last_interaction_time': datetime.now() - timedelta(days=last_interaction_days),
                'last_view_item_time': datetime.now() - timedelta(days=last_view_item_days) if last_view_item_days != -1 else None,
                'last_add_to_cart_time': datetime.now() - timedelta(days=last_add_to_cart_days) if last_add_to_cart_days != -1 else None,
                'last_purchase_time': datetime.now() - timedelta(days=last_purchase_days) if last_purchase_days != -1 else None,

                # 其他統計欄位
                'purchase_count': np.random.randint(0, 5) if last_purchase_days != -1 else 0,
                'total_purchase_amount': np.random.uniform(100, 10000) if last_purchase_days != -1 else 0,
                'view_item_count': np.random.randint(1, 50),
                'add_to_cart_count': np.random.randint(0, 10) if last_add_to_cart_days != -1 else 0
            }
            data.append(user_data)

        return pd.DataFrame(data)

    def test_load_real_rules(self, real_audience_rules):
        """測試是否正確載入了真實的規則"""
        print(f"載入的規則數量: {len(real_audience_rules)}")

        expected_rules = [
            'tm:c_2980_c_001',  # 認知受眾 (PV)
            'tm:c_2980_c_002',  # 興趣/意象受眾 (Viewcontent_新訪客)
            'tm:c_2980_c_003',  # 興趣/意象受眾 (Viewcontent_舊訪客)
            'tm:c_2980_c_004',  # 潛在購買受眾 (ATC)
            'tm:c_2980_c_005',  # 新客群 (PC_新客)
            'tm:c_2980_c_006'   # 舊客群 (PC_舊客)
        ]

        print("期望的規則:")
        for rule_id in expected_rules:
            if rule_id in real_audience_rules:
                rule_data = real_audience_rules[rule_id]
                print(f"✓ {rule_id}: {rule_data.get('description', 'No description')}")
                print(f"    資料參數: {rule_data.get('data', {})}")
            else:
                print(f"✗ {rule_id}: 未找到")

        # 驗證所有期望的規則都存在
        for rule_id in expected_rules:
            assert rule_id in real_audience_rules, f"規則 {rule_id} 未找到"

        # 特別檢查規則 003 和 006 的時間要求
        rule_003 = real_audience_rules['tm:c_2980_c_003']
        rule_006 = real_audience_rules['tm:c_2980_c_006']

        assert rule_003['data']['min_days'] == 60, "規則 003 應該要求 min_days = 60"
        assert rule_003['data']['max_days'] == 365, "規則 003 應該要求 max_days = 365"

        assert rule_006['data']['min_days'] == 60, "規則 006 應該要求 min_days = 60"
        assert rule_006['data']['max_days'] == 365, "規則 006 應該要求 max_days = 365"

    def test_analyze_limited_data_time_range(self, limited_time_range_data):
        """測試有限資料的時間範圍分析"""
        analysis = analyze_data_time_range(limited_time_range_data)

        print("\n=== 有限資料時間範圍分析 ===")
        for col, info in analysis.items():
            if isinstance(info, dict):
                print(f"{col}:")
                print(f"  最小天數: {info['min_days']}")
                print(f"  最大天數: {info['max_days']}")
                print(f"  有效資料數: {info['valid_count']}/{info['total_count']}")
            else:
                print(f"{col}: {info}")

        # 驗證關鍵欄位的時間範圍
        assert analysis['last_interaction_days']['max_days'] <= 13, "last_interaction_days 最大值應該 <= 13"
        assert analysis['last_view_item_days']['max_days'] <= 13, "last_view_item_days 最大值應該 <= 13"

    def test_check_real_rule_requirements(self, real_audience_rules, limited_time_range_data):
        """測試真實規則的資料需求檢查"""
        data_time_range = analyze_data_time_range(limited_time_range_data)

        print("\n=== 真實規則資料需求檢查 ===")

        # 檢查每個規則的資料需求
        for rule_id, rule_data in real_audience_rules.items():
            requirement_check = check_rule_data_requirement(rule_data, data_time_range)
            print(f"\n規則 {rule_id} ({rule_data.get('description', 'No description')}):")
            print(f"  資料參數: {rule_data.get('data', {})}")
            print(f"  有足夠資料: {requirement_check['has_sufficient_data']}")

            if not requirement_check['has_sufficient_data']:
                print(f"  原因: {requirement_check['reason']}")
                print(f"  缺失需求: {requirement_check['missing_requirements']}")

        # 驗證規則 003 和 006 應該被標記為資料不足
        rule_003_check = check_rule_data_requirement(real_audience_rules["tm:c_2980_c_003"], data_time_range)
        rule_006_check = check_rule_data_requirement(real_audience_rules["tm:c_2980_c_006"], data_time_range)

        assert not rule_003_check['has_sufficient_data'], "規則 003 應該被標記為資料不足"
        assert not rule_006_check['has_sufficient_data'], "規則 006 應該被標記為資料不足"

        # 驗證其他規則應該有足夠資料
        rule_001_check = check_rule_data_requirement(real_audience_rules["tm:c_2980_c_001"], data_time_range)
        rule_002_check = check_rule_data_requirement(real_audience_rules["tm:c_2980_c_002"], data_time_range)
        rule_005_check = check_rule_data_requirement(real_audience_rules["tm:c_2980_c_005"], data_time_range)

        assert rule_001_check['has_sufficient_data'], "規則 001 應該有足夠資料"
        assert rule_002_check['has_sufficient_data'], "規則 002 應該有足夠資料"
        assert rule_005_check['has_sufficient_data'], "規則 005 應該有足夠資料"

    def test_vectorized_evaluate_with_limited_data(self, real_audience_rules, limited_time_range_data):
        """測試向量化評估在有限資料情況下的處理（使用真實規則）"""
        print(f"\n=== 有限資料的向量化評估測試 ===")
        print(f"測試資料筆數: {len(limited_time_range_data)}")
        print(f"規則數量: {len(real_audience_rules)}")

        # 執行評估
        results = vectorized_evaluate(limited_time_range_data, real_audience_rules)

        print(f"\n評估結果:")
        for rule_id, users in results.items():
            rule_desc = real_audience_rules[rule_id].get('description', 'No description')
            print(f"  {rule_id}: {len(users)} 個用戶 - {rule_desc}")

        # 驗證所有規則都有結果記錄
        assert len(results) == len(real_audience_rules), f"應該有 {len(real_audience_rules)} 個規則結果，實際有 {len(results)} 個"

        # 驗證所有規則 ID 都存在於結果中
        for rule_id in real_audience_rules.keys():
            assert rule_id in results, f"規則 {rule_id} 應該在結果中"

        # 驗證規則 003 和 006 應該有 0 個用戶（因為資料時間範圍不足）
        assert len(results["tm:c_2980_c_003"]) == 0, "規則 003 應該因為資料不足而有 0 個用戶"
        assert len(results["tm:c_2980_c_006"]) == 0, "規則 006 應該因為資料不足而有 0 個用戶"

        # 驗證其他規則可能有符合的用戶
        other_rules = [rule_id for rule_id in real_audience_rules.keys()
                      if rule_id not in ["tm:c_2980_c_003", "tm:c_2980_c_006"]]

        total_users_other_rules = sum(len(results[rule_id]) for rule_id in other_rules)
        print(f"除了規則 003 和 006 外，其他規則總共有 {total_users_other_rules} 個用戶符合")

    def test_vectorized_evaluate_with_sufficient_data(self, real_audience_rules, sufficient_time_range_data):
        """測試向量化評估在充足資料情況下的處理（用來驗證規則邏輯是否正確）"""
        print(f"\n=== 充足資料的向量化評估測試 ===")
        print(f"測試資料筆數: {len(sufficient_time_range_data)}")
        print(f"規則數量: {len(real_audience_rules)}")

        # 執行評估
        results = vectorized_evaluate(sufficient_time_range_data, real_audience_rules)

        print(f"\n評估結果:")
        for rule_id, users in results.items():
            rule_desc = real_audience_rules[rule_id].get('description', 'No description')
            print(f"  {rule_id}: {len(users)} 個用戶 - {rule_desc}")

        # 驗證所有規則都有結果記錄
        assert len(results) == len(real_audience_rules), f"應該有 {len(real_audience_rules)} 個規則結果，實際有 {len(results)} 個"

        # 在充足資料的情況下，規則 003 和 006 應該可能有符合的用戶
        print(f"\n規則 003 符合用戶數: {len(results['tm:c_2980_c_003'])}")
        print(f"規則 006 符合用戶數: {len(results['tm:c_2980_c_006'])}")

        # 檢查是否有重複的用戶數量問題
        user_counts = {rule_id: len(users) for rule_id, users in results.items()}
        duplicate_counts = {}
        for count in user_counts.values():
            rules_with_this_count = [rule_id for rule_id, c in user_counts.items() if c == count]
            if len(rules_with_this_count) > 1:
                duplicate_counts[count] = rules_with_this_count

        print(f"重複的用戶數量: {duplicate_counts}")

        # 如果存在非零的重複，這可能表示規則邏輯有問題
        non_zero_duplicates = {k: v for k, v in duplicate_counts.items() if k > 0}
        if non_zero_duplicates:
            print(f"⚠️  發現非零重複用戶數量: {non_zero_duplicates}")
        else:
            print("✅ 沒有發現非零重複用戶數量問題")

    def test_rule_json_logic_analysis(self, real_audience_rules):
        """分析真實規則的 JSON Logic，確認邏輯是否合理"""
        print(f"\n=== 真實規則 JSON Logic 分析 ===")

        # 特別分析規則 003 和 006
        target_rules = ['tm:c_2980_c_003', 'tm:c_2980_c_006']

        for rule_id in target_rules:
            rule_data = real_audience_rules[rule_id]
            print(f"\n規則 {rule_id}:")
            print(f"  描述: {rule_data.get('description')}")
            print(f"  資料參數: {rule_data.get('data')}")
            print(f"  JSON Logic: {json.dumps(rule_data.get('rule'), indent=2, ensure_ascii=False)}")

            # 分析 JSON Logic 的邏輯
            rule_logic = rule_data.get('rule', {})
            data_params = rule_data.get('data', {})

            if 'and' in rule_logic:
                conditions = rule_logic['and']
                print(f"  邏輯條件分析:")
                for i, condition in enumerate(conditions):
                    print(f"    條件 {i+1}: {condition}")

                    # 特別檢查時間範圍條件
                    if isinstance(condition, dict):
                        if '>' in condition:
                            left, right = condition['>']
                            if isinstance(left, dict) and left.get('var') and 'days' in str(left.get('var')):
                                if isinstance(right, dict) and right.get('var') in data_params:
                                    min_days = data_params[right.get('var')]
                                    print(f"      -> 要求 {left.get('var')} > {min_days} 天")
                        elif '<=' in condition:
                            left, right = condition['<=']
                            if isinstance(left, dict) and left.get('var') and 'days' in str(left.get('var')):
                                if isinstance(right, dict) and right.get('var') in data_params:
                                    max_days = data_params[right.get('var')]
                                    print(f"      -> 要求 {left.get('var')} <= {max_days} 天")

if __name__ == "__main__":
    # 直接運行測試
    test = TestRealAudienceRules()

    # 載入真實規則
    real_rules = test.real_audience_rules()
    limited_data = test.limited_time_range_data()
    sufficient_data = test.sufficient_time_range_data()

    print("=== 載入真實規則測試 ===")
    test.test_load_real_rules(real_rules)

    print("\n=== 分析有限資料時間範圍 ===")
    test.test_analyze_limited_data_time_range(limited_data)

    print("\n=== 檢查真實規則需求 ===")
    test.test_check_real_rule_requirements(real_rules, limited_data)

    print("\n=== 有限資料向量化評估 ===")
    test.test_vectorized_evaluate_with_limited_data(real_rules, limited_data)

    print("\n=== 充足資料向量化評估 ===")
    test.test_vectorized_evaluate_with_sufficient_data(real_rules, sufficient_data)

    print("\n=== 規則 JSON Logic 分析 ===")
    test.test_rule_json_logic_analysis(real_rules)

    print("\n✅ 所有測試完成！")
