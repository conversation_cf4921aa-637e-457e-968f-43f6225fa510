#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試資料時間範圍不足的處理邏輯
專門測試 EC ID 2980 的規則 003 和 006 因為資料不足而無結果的情況
"""

import pytest
import pandas as pd
import numpy as np
import sys
import os
import json
from datetime import datetime, timedelta

# 添加項目根目錄到 Python 路徑
project_root = os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..'))
sys.path.insert(0, project_root)

from src.utils import vectorized_evaluate, analyze_data_time_range, check_rule_data_requirement

class TestDataInsufficientHandling:
    """測試資料時間範圍不足的處理"""

    @pytest.fixture
    def ec_2980_rules(self):
        """EC ID 2980 的規則定義"""
        return {
            "tm:c_2980_c_001": {
                "rule": {
                    "and": [
                        {"<=": [{"var": "purchase_count"}, 0]},
                        {"<=": [{"var": "last_interaction_days"}, 30]}
                    ]
                },
                "data": {},
                "description": "最近30天互動且未購買的用戶"
            },
            "tm:c_2980_c_002": {
                "rule": {
                    "and": [
                        {">=": [{"var": "purchase_count"}, 1]},
                        {"<=": [{"var": "last_interaction_days"}, 30]}
                    ]
                },
                "data": {},
                "description": "最近30天互動且有購買的用戶"
            },
            "tm:c_2980_c_003": {
                "rule": {
                    "and": [
                        {"<=": [{"var": "purchase_count"}, 0]},
                        {">": [{"var": "last_interaction_days"}, 60]}
                    ]
                },
                "data": {},
                "description": "超過60天前互動且未購買的用戶"
            },
            "tm:c_2980_c_004": {
                "rule": {
                    "and": [
                        {">=": [{"var": "purchase_count"}, 1]},
                        {"<=": [{"var": "last_interaction_days"}, 60]}
                    ]
                },
                "data": {},
                "description": "60天內有購買的用戶"
            },
            "tm:c_2980_c_005": {
                "rule": {
                    "and": [
                        {">=": [{"var": "purchase_count"}, 2]},
                        {"<=": [{"var": "last_interaction_days"}, 90]}
                    ]
                },
                "data": {},
                "description": "90天內多次購買的用戶"
            },
            "tm:c_2980_c_006": {
                "rule": {
                    "and": [
                        {">=": [{"var": "purchase_count"}, 1]},
                        {">": [{"var": "last_interaction_days"}, 90]}
                    ]
                },
                "data": {},
                "description": "超過90天前有購買的用戶"
            }
        }


    @pytest.fixture
    def limited_time_range_data(self):
        """建立只有 0-13 天時間範圍的測試資料（模擬 EC ID 2980 的實際情況）"""
        np.random.seed(42)  # 固定隨機種子以確保結果一致

        data = []

        # 建立 1000 個用戶的資料，last_interaction_days 範圍為 0-13 天
        for i in range(1000):
            user_data = {
                'permanent': f'user_{i:04d}',
                'ec_id': 2980,
                'last_interaction_days': np.random.randint(0, 14),  # 0-13 天
                'last_view_item_days': np.random.randint(0, 14),
                'last_add_to_cart_days': np.random.randint(0, 14),
                'last_purchase_days': np.random.randint(0, 14) if np.random.random() > 0.5 else -1,
                'purchase_count': np.random.randint(0, 5),
                'total_purchase_amount': np.random.uniform(0, 10000) if np.random.random() > 0.3 else 0,
                'view_item_count': np.random.randint(1, 50),
                'add_to_cart_count': np.random.randint(0, 10)
            }
            data.append(user_data)

        return pd.DataFrame(data)

    def test_analyze_data_time_range(self, limited_time_range_data):
        """測試資料時間範圍分析功能"""
        analysis = analyze_data_time_range(limited_time_range_data)

        print("資料時間範圍分析結果:")
        for col, info in analysis.items():
            print(f"  {col}: {info}")

        # 驗證 last_interaction_days 的範圍
        assert analysis['last_interaction_days']['min_days'] == 0
        assert analysis['last_interaction_days']['max_days'] == 13
        assert analysis['last_interaction_days']['valid_count'] == 1000

    def test_check_rule_data_requirement(self, ec_2980_rules, limited_time_range_data):
        """測試規則資料需求檢查"""
        data_time_range = analyze_data_time_range(limited_time_range_data)

        # 檢查每個規則的資料需求
        for rule_id, rule_data in ec_2980_rules.items():
            requirement_check = check_rule_data_requirement(rule_data, data_time_range)
            print(f"\n規則 {rule_id}:")
            print(f"  有足夠資料: {requirement_check['has_sufficient_data']}")
            if not requirement_check['has_sufficient_data']:
                print(f"  原因: {requirement_check['reason']}")
                print(f"  缺失需求: {requirement_check['missing_requirements']}")

        # 驗證規則 003 和 006 應該被標記為資料不足
        rule_003_check = check_rule_data_requirement(ec_2980_rules["tm:c_2980_c_003"], data_time_range)
        rule_006_check = check_rule_data_requirement(ec_2980_rules["tm:c_2980_c_006"], data_time_range)

        assert not rule_003_check['has_sufficient_data'], "規則 003 應該被標記為資料不足"
        assert not rule_006_check['has_sufficient_data'], "規則 006 應該被標記為資料不足"

        # 驗證其他規則應該有足夠資料
        rule_001_check = check_rule_data_requirement(ec_2980_rules["tm:c_2980_c_001"], data_time_range)
        rule_002_check = check_rule_data_requirement(ec_2980_rules["tm:c_2980_c_002"], data_time_range)

        assert rule_001_check['has_sufficient_data'], "規則 001 應該有足夠資料"
        assert rule_002_check['has_sufficient_data'], "規則 002 應該有足夠資料"


        # 驗證規則 003 和 006 應該被標記為資料不足
        rule_003_check = check_rule_data_requirement(ec_2980_rules["tm:c_2980_c_003"], data_time_range)
        rule_006_check = check_rule_data_requirement(ec_2980_rules["tm:c_2980_c_006"], data_time_range)

        assert not rule_003_check['has_sufficient_data'], "規則 003 應該被標記為資料不足"
        assert not rule_006_check['has_sufficient_data'], "規則 006 應該被標記為資料不足"

        # 驗證其他規則應該有足夠資料
        rule_001_check = check_rule_data_requirement(ec_2980_rules["tm:c_2980_c_001"], data_time_range)
        rule_002_check = check_rule_data_requirement(ec_2980_rules["tm:c_2980_c_002"], data_time_range)

        assert rule_001_check['has_sufficient_data'], "規則 001 應該有足夠資料"
        assert rule_002_check['has_sufficient_data'], "規則 002 應該有足夠資料"

    def test_vectorized_evaluate_with_insufficient_data(self, ec_2980_rules, limited_time_range_data):
        """測試向量化評估在資料不足情況下的處理"""
        print(f"\n測試資料筆數: {len(limited_time_range_data)}")
        print(f"規則數量: {len(ec_2980_rules)}")

        # 執行評估
        results = vectorized_evaluate(limited_time_range_data, ec_2980_rules)

        print(f"\n評估結果:")
        for rule_id, users in results.items():
            print(f"  {rule_id}: {len(users)} 個用戶")

        # 驗證所有規則都有結果記錄
        assert len(results) == len(ec_2980_rules), f"應該有 {len(ec_2980_rules)} 個規則結果，實際有 {len(results)} 個"

        # 驗證所有規則 ID 都存在於結果中
        for rule_id in ec_2980_rules.keys():
            assert rule_id in results, f"規則 {rule_id} 應該在結果中"

        # 驗證規則 003 和 006 應該有 0 個用戶（因為資料不足）
        assert len(results["tm:c_2980_c_003"]) == 0, "規則 003 應該因為資料不足而有 0 個用戶"
        assert len(results["tm:c_2980_c_006"]) == 0, "規則 006 應該因為資料不足而有 0 個用戶"

        # 驗證其他規則可能有符合的用戶
        total_users_with_results = sum(len(users) for rule_id, users in results.items()
                                     if rule_id not in ["tm:c_2980_c_003", "tm:c_2980_c_006"])
        print(f"除了規則 003 和 006 外，其他規則總共有 {total_users_with_results} 個用戶符合")


        # 執行評估
        results = vectorized_evaluate(limited_time_range_data, ec_2980_rules)

        print(f"\n評估結果:")
        for rule_id, users in results.items():
            print(f"  {rule_id}: {len(users)} 個用戶")

        # 驗證所有規則都有結果記錄
        assert len(results) == len(ec_2980_rules), f"應該有 {len(ec_2980_rules)} 個規則結果，實際有 {len(results)} 個"

        # 驗證所有規則 ID 都存在於結果中
        for rule_id in ec_2980_rules.keys():
            assert rule_id in results, f"規則 {rule_id} 應該在結果中"

        # 驗證規則 003 和 006 應該有 0 個用戶（因為資料不足）
        assert len(results["tm:c_2980_c_003"]) == 0, "規則 003 應該因為資料不足而有 0 個用戶"
        assert len(results["tm:c_2980_c_006"]) == 0, "規則 006 應該因為資料不足而有 0 個用戶"

        # 驗證其他規則可能有符合的用戶
        total_users_with_results = sum(len(users) for rule_id, users in results.items()
                                     if rule_id not in ["tm:c_2980_c_003", "tm:c_2980_c_006"])
        print(f"除了規則 003 和 006 外，其他規則總共有 {total_users_with_results} 個用戶符合")

        # 檢查重複問題 - 現在 0 用戶的重複應該被認為是正常的
        user_counts = {rule_id: len(users) for rule_id, users in results.items()}
        duplicate_counts = {}
        for count in user_counts.values():
            rules_with_this_count = [rule_id for rule_id, c in user_counts.items() if c == count]
            if len(rules_with_this_count) > 1:
                duplicate_counts[count] = rules_with_this_count

        print(f"重複的用戶數量: {duplicate_counts}")


        print(f"重複的用戶數量: {duplicate_counts}")

        # 0 用戶的重複是正常的（由於資料不足）
        if 0 in duplicate_counts:
            zero_user_rules = duplicate_counts[0]
            expected_zero_rules = ["tm:c_2980_c_003", "tm:c_2980_c_006"]
            assert all(rule in zero_user_rules for rule in expected_zero_rules), \
                f"規則 003 和 006 都應該在 0 用戶的列表中: {zero_user_rules}"


    def test_edge_cases(self):
        """測試邊界情況"""
        # 測試空資料
        empty_data = pd.DataFrame(columns=['permanent', 'last_interaction_days', 'purchase_count'])
        rules = {"test_rule": {"rule": {"<=": [{"var": "purchase_count"}, 0]}, "data": {}}}

        results = vectorized_evaluate(empty_data, rules)
        assert results == {}, "空資料應該返回空結果"


        results = vectorized_evaluate(empty_data, rules)
        assert results == {}, "空資料應該返回空結果"

        # 測試空規則
        test_data = pd.DataFrame({
            'permanent': ['user_001'],
            'last_interaction_days': [5],
            'purchase_count': [0]
        })


        results = vectorized_evaluate(test_data, {})
        assert results == {}, "空規則應該返回空結果"

if __name__ == "__main__":
    # 直接運行測試
    test = TestDataInsufficientHandling()

    # 建立測試資料
    limited_data = test.limited_time_range_data()
    ec_rules = test.ec_2980_rules()

    print("=== 測試資料時間範圍分析 ===")
    test.test_analyze_data_time_range(limited_data)

    print("\n=== 測試規則資料需求檢查 ===")
    test.test_check_rule_data_requirement(ec_rules, limited_data)

    print("\n=== 測試向量化評估處理資料不足情況 ===")
    test.test_vectorized_evaluate_with_insufficient_data(ec_rules, limited_data)

    print("\n=== 測試邊界情況 ===")
    test.test_edge_cases()


    # 建立測試資料
    limited_data = test.limited_time_range_data()
    ec_rules = test.ec_2980_rules()

    print("=== 測試資料時間範圍分析 ===")
    test.test_analyze_data_time_range(limited_data)

    print("\n=== 測試規則資料需求檢查 ===")
    test.test_check_rule_data_requirement(ec_rules, limited_data)

    print("\n=== 測試向量化評估處理資料不足情況 ===")
    test.test_vectorized_evaluate_with_insufficient_data(ec_rules, limited_data)

    print("\n=== 測試邊界情況 ===")
    test.test_edge_cases()

    print("\n✅ 所有測試完成！")
