#!/usr/bin/env python3
"""
自動修復流程集成測試

整合 test_auto_repair.py 的功能，測試自動修復缺失天數的完整流程。
"""

import os
import sys
import json
import logging
import pytest
import pandas as pd
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, MagicMock

# 添加 src 目錄到 Python 路徑
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', '..', 'src'))

from src.main import calculate_user_stats_via_bigquery_wrapper, parse_bool


class TestAutoRepairFlow:
    """自動修復流程測試類別"""

    @pytest.fixture
    def test_ec_id(self):
        """測試用的 EC ID"""
        return 107

    @pytest.fixture
    def test_date_range(self):
        """測試用的日期範圍"""
        end_time = datetime(2024, 1, 5, tzinfo=timezone.utc)
        start_time = datetime(2024, 1, 1, tzinfo=timezone.utc)
        return [start_time, end_time]

    @pytest.fixture
    def mock_snapshot_data(self, test_ec_id):
        """創建模擬的快照資料"""
        user_count = 100
        return pd.DataFrame({
            'ec_id': [test_ec_id] * user_count,
            'permanent': [f'user_{i:06d}' for i in range(user_count)],
            'total_sessions': [i % 20 + 1 for i in range(user_count)],
            'total_revenue': [float(i * 100) for i in range(user_count)],
            'total_orders': [i % 10 + 1 for i in range(user_count)],
            'registration_time': [
                datetime(2023, 12, 1, tzinfo=timezone.utc) + timedelta(days=i % 30)
                for i in range(user_count)
            ],
            'first_interaction_time': [
                datetime(2023, 12, 1, tzinfo=timezone.utc) + timedelta(days=i % 30)
                for i in range(user_count)
            ],
            'last_interaction_time': [
                datetime(2023, 12, 31, tzinfo=timezone.utc) - timedelta(days=i % 7)
                for i in range(user_count)
            ],
            'first_purchase_time': [
                datetime(2023, 12, 5, tzinfo=timezone.utc) + timedelta(days=i % 25)
                if i % 3 != 0 else None
                for i in range(user_count)
            ],
            'last_purchase_time': [
                datetime(2023, 12, 25, tzinfo=timezone.utc) - timedelta(days=i % 5)
                if i % 3 != 0 else None
                for i in range(user_count)
            ],
            'purchase_count': [i % 10 if i % 3 != 0 else 0 for i in range(user_count)],
            'total_purchase_amount': [
                float(i * 100) if i % 3 != 0 else 0.0
                for i in range(user_count)
            ]
        })

    @pytest.fixture
    def mock_daily_data(self, test_ec_id):
        """創建模擬的每日資料"""
        daily_data = {}

        # 為每一天創建模擬資料
        for day_offset in range(1, 5):  # 2024-01-01 到 2024-01-04
            date = datetime(2024, 1, day_offset, tzinfo=timezone.utc)
            user_count = 20

            daily_data[date.date()] = pd.DataFrame({
                'ec_id': [test_ec_id] * user_count,
                'permanent': [f'user_{i:06d}' for i in range(user_count)],
                'daily_sessions': [1 + (i % 3) for i in range(user_count)],
                'daily_revenue': [float(i * 10 + day_offset * 5) for i in range(user_count)],
                'daily_orders': [1 if i % 2 == 0 else 0 for i in range(user_count)],
                'last_interaction_time': [date] * user_count,
                'last_purchase_time': [
                    date if i % 2 == 0 else None
                    for i in range(user_count)
                ]
            })

        return daily_data

    def test_auto_repair_parameter_parsing(self):
        """測試自動修復參數解析"""

        # 測試各種布林值輸入
        test_cases = [
            (True, True),
            (False, False),
            ('true', True),
            ('false', False),
            ('True', True),
            ('False', False),
            ('1', True),
            ('0', False),
            (1, True),
            (0, False),
            ('yes', True),
            ('no', False),
        ]

        for input_val, expected in test_cases:
            result = parse_bool(input_val)
            assert result == expected, f"parse_bool({input_val}) 返回 {result}，期望 {expected}"

        logging.info("✓ 自動修復參數解析測試通過")

    def test_repair_date_calculation(self):
        """測試修復日期計算邏輯"""

        # 測試不同的日期範圍
        test_cases = [
            # (快照日期, 結束日期, 最大修復天數, 期望修復天數)
            (datetime(2024, 1, 1, tzinfo=timezone.utc), datetime(2024, 1, 5, tzinfo=timezone.utc), 30, 4),
            (datetime(2024, 1, 1, tzinfo=timezone.utc), datetime(2024, 1, 10, tzinfo=timezone.utc), 5, 5),
            (datetime(2024, 1, 1, tzinfo=timezone.utc), datetime(2024, 2, 1, tzinfo=timezone.utc), 30, 30),
            (datetime(2024, 1, 1, tzinfo=timezone.utc), datetime(2024, 1, 1, tzinfo=timezone.utc), 30, 0),
        ]

        for snapshot_date, end_date, max_repair_days, expected_days in test_cases:
            # 計算需要修復的天數
            calculated_days = (end_date - snapshot_date).days
            actual_repair_days = min(calculated_days, max_repair_days)

            assert actual_repair_days == expected_days, \
                f"快照 {snapshot_date.date()} 到 {end_date.date()}，" \
                f"最大 {max_repair_days} 天，期望 {expected_days}，實際 {actual_repair_days}"

        logging.info("✓ 修復日期計算邏輯測試通過")

    def test_mock_api_call_parsing(self, test_ec_id):
        """測試模擬 API 調用的參數解析"""

        # 模擬 Cloud Function 請求資料
        mock_request_data = {
            'ec_ids': [test_ec_id],
            'start_time': '2024-01-01T00:00:00Z',
            'end_time': '2024-01-03T23:59:59Z',
            'auto_repair_missing_days': True,
            'max_repair_days': 30,
            'use_bigquery_compute': True,
            'should_save_snapshot': False,
            'should_write_lta': False,
            'should_update_user_stats_table': False
        }

        # 解析參數
        auto_repair = parse_bool(mock_request_data.get('auto_repair_missing_days', True))
        use_bigquery = parse_bool(mock_request_data.get('use_bigquery_compute', False))
        max_days = int(mock_request_data.get('max_repair_days', 30))
        ec_ids = mock_request_data.get('ec_ids', [])

        # 驗證解析結果
        assert auto_repair is True
        assert use_bigquery is True
        assert max_days == 30
        assert test_ec_id in ec_ids

        logging.info("✓ 模擬 API 調用參數解析正確")

    def test_edge_cases(self):
        """測試邊緣情況"""

        # 測試 max_repair_days = 0
        assert parse_bool(True) is True

        # 測試負數天數（應該被處理為 0）
        past_date = datetime.now(timezone.utc) + timedelta(days=-5)
        future_date = datetime.now(timezone.utc)
        days_diff = (future_date - past_date).days
        repair_days = max(0, min(days_diff, 0))  # max_repair_days = 0
        assert repair_days == 0

        # 測試相同日期
        same_date = datetime.now(timezone.utc)
        days_diff = (same_date - same_date).days
        assert days_diff == 0

        logging.info("✓ 邊緣情況測試通過")

    @pytest.mark.integration
    def test_end_to_end_auto_repair(self, test_ec_id, test_date_range):
        """端到端自動修復測試（需要實際環境）"""

        # 這個測試需要真實的 GCP 環境
        pytest.skip("需要真實的 GCP 環境，跳過端到端測試")


if __name__ == '__main__':
    # 設定測試日誌
    logging.basicConfig(level=logging.INFO)

    # 執行測試
    pytest.main([__file__, '-v', '--tb=short'])