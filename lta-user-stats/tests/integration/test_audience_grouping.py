import pytest
import pandas as pd
import numpy as np
from datetime import datetime, timedelta, timezone
import json
import os
from unittest.mock import patch, MagicMock

from src.rules.dynamic_audience_rules import calculate_audience_segments_dynamic, fetch_audience_mapping_with_rules
from src.utils import vectorized_evaluate


@pytest.fixture
def sample_multi_ec_data():
    """產生多個 EC ID 的樣本用戶數據"""
    now = datetime.now(timezone.utc)

    # 為 EC ID 2980, 3819, 3820 創建樣本數據
    data = []

    # EC ID 2980 用戶 - 特別設計來符合不同規則
    # 規則 1: 認知受眾 (PV) - 最近一年內有互動行為但沒有任何商品點擊
    data.append({
        'permanent': 'user_2980_1',
        'ec_id': 2980,
        'last_interaction_time': now - timedelta(days=30),  # 30天前互動
        'last_interaction_days': 30,
        'last_view_item_time': None,  # 無商品點擊
        'last_view_item_days': -1,
        'last_add_to_cart_time': None,  # 無加購
        'last_add_to_cart_days': -1,
        'last_purchase_time': None,  # 無購買
        'last_purchase_days': -1,
        'purchase_count': 0
    })

    # 規則 2: 興趣/意象受眾 (Viewcontent_新訪客) - 最近1個月內有進入官網，並且有瀏覽、點擊等互動行為（不含加購物車）
    data.append({
        'permanent': 'user_2980_2',
        'ec_id': 2980,
        'last_interaction_time': now - timedelta(days=20),  # 20天前互動
        'last_interaction_days': 20,
        'last_view_item_time': now - timedelta(days=20),  # 20天前查看商品
        'last_view_item_days': 20,
        'last_add_to_cart_time': None,  # 無加購
        'last_add_to_cart_days': -1,
        'last_purchase_time': None,  # 無購買
        'last_purchase_days': -1,
        'purchase_count': 0
    })

    # 規則 3: 興趣/意象受眾 (Viewcontent_舊訪客) - 最近2個月-1年內有進入官網，並且有瀏覽、點擊等互動行為（不含加購物車）
    data.append({
        'permanent': 'user_2980_3',
        'ec_id': 2980,
        'last_interaction_time': now - timedelta(days=100),  # 100天前互動
        'last_interaction_days': 100,
        'last_view_item_time': now - timedelta(days=100),  # 100天前查看商品
        'last_view_item_days': 100,
        'last_add_to_cart_time': None,  # 無加購
        'last_add_to_cart_days': -1,
        'last_purchase_time': None,  # 無購買
        'last_purchase_days': -1,
        'purchase_count': 0
    })

    # 規則 4: 潛在購買受眾 (ATC) - 最近1年內有加購物車的行為
    data.append({
        'permanent': 'user_2980_4',
        'ec_id': 2980,
        'last_interaction_time': now - timedelta(days=50),  # 50天前互動
        'last_interaction_days': 50,
        'last_view_item_time': now - timedelta(days=50),  # 50天前查看商品
        'last_view_item_days': 50,
        'last_add_to_cart_time': now - timedelta(days=50),  # 50天前加購
        'last_add_to_cart_days': 50,
        'last_purchase_time': None,  # 無購買
        'last_purchase_days': -1,
        'purchase_count': 0
    })

    # 規則 5: 新客群 (PC_新客) - 最近1個月內有過購買
    data.append({
        'permanent': 'user_2980_5',
        'ec_id': 2980,
        'last_interaction_time': now - timedelta(days=15),  # 15天前互動
        'last_interaction_days': 15,
        'last_view_item_time': now - timedelta(days=15),  # 15天前查看商品
        'last_view_item_days': 15,
        'last_add_to_cart_time': now - timedelta(days=15),  # 15天前加購
        'last_add_to_cart_days': 15,
        'last_purchase_time': now - timedelta(days=15),  # 15天前購買
        'last_purchase_days': 15,
        'purchase_count': 1
    })

    # 規則 6: 舊客群 (PC_舊客) - 最近2個月-1年內有過購買
    data.append({
        'permanent': 'user_2980_6',
        'ec_id': 2980,
        'last_interaction_time': now - timedelta(days=90),  # 90天前互動
        'last_interaction_days': 90,
        'last_view_item_time': now - timedelta(days=90),  # 90天前查看商品
        'last_view_item_days': 90,
        'last_add_to_cart_time': now - timedelta(days=90),  # 90天前加購
        'last_add_to_cart_days': 90,
        'last_purchase_time': now - timedelta(days=90),  # 90天前購買
        'last_purchase_days': 90,
        'purchase_count': 1
    })

    # 增加一些其他固定的用戶來豐富測試數據
    # 預先定義好的天數和購買次數
    interaction_patterns = [
        # last_interaction_days, last_view_item_days, last_add_to_cart_days, last_purchase_days, purchase_count
        (8,   10,  15,  20,  1),  # 全都很近的活躍用戶
        (50,  55,  60,  -1,  0),  # 中等活躍但沒有購買的用戶
        (100, 110, -1,  120, 2),  # 有一段時間的用戶，沒有加購但有購買
        (150, -1,  170, -1,  0),  # 較久前的用戶，無商品點擊，有加購，無購買
        (200, 210, 220, 230, 3),  # 較久前的高購買用戶
        (250, 260, -1,  -1,  0),  # 較久前的僅瀏覽用戶
        (300, -1,  -1,  -1,  0),  # 很久前的用戶，僅站點瀏覽
        (350, 360, 370, -1,  0),  # 非常久前的用戶，有瀏覽加購但無購買
        (10,  400, 15,  20,  1),  # 近期活躍，但很久前點擊過商品
        (365, 370, 375, 380, 4),  # 恰好一年前的用戶，高購買量
        (40,  -1,  40,  40,  1),  # 同一天有所有行為的用戶
        (25,  25,  -1,  -1,  0),  # 只有瀏覽商品的用戶
        (85,  90,  95,  100, 2)   # 中等時間的多購買用戶
    ]

    for i, pattern in enumerate(interaction_patterns, 7):
        int_days, view_days, cart_days, purchase_days, p_count = pattern

        int_time = now - timedelta(days=int_days) if int_days > 0 else None
        view_time = now - timedelta(days=view_days) if view_days > 0 else None
        cart_time = now - timedelta(days=cart_days) if cart_days > 0 else None
        purchase_time = now - timedelta(days=purchase_days) if purchase_days > 0 else None

        data.append({
            'permanent': f'user_2980_{i}',
            'ec_id': 2980,
            'last_interaction_time': int_time,
            'last_interaction_days': int_days if int_days > 0 else -1,
            'last_view_item_time': view_time,
            'last_view_item_days': view_days if view_days > 0 else -1,
            'last_add_to_cart_time': cart_time,
            'last_add_to_cart_days': cart_days if cart_days > 0 else -1,
            'last_purchase_time': purchase_time,
            'last_purchase_days': purchase_days if purchase_days > 0 else -1,
            'purchase_count': p_count
        })

    # EC ID 3819 用戶 (固定樣本)
    # 規則 1: 認知受眾 - 最近3個月，有瀏覽、點擊等互動行為，未加購物車
    data.append({
        'permanent': 'user_3819_1',
        'ec_id': 3819,
        'last_interaction_time': now - timedelta(days=15),  # 15天前互動
        'last_interaction_days': 15,
        'last_view_item_time': now - timedelta(days=20),  # 20天前查看商品
        'last_view_item_days': 20,
        'last_add_to_cart_time': None,  # 無加購
        'last_add_to_cart_days': -1,
        'last_purchase_time': None,  # 無購買
        'last_purchase_days': -1,
        'purchase_count': 0
    })

    # 規則 2: 興趣/意象受眾 - 最近1年有加購物車行為
    data.append({
        'permanent': 'user_3819_2',
        'ec_id': 3819,
        'last_interaction_time': now - timedelta(days=45),  # 45天前互動
        'last_interaction_days': 45,
        'last_view_item_time': now - timedelta(days=50),  # 50天前查看商品
        'last_view_item_days': 50,
        'last_add_to_cart_time': now - timedelta(days=60),  # 60天前加購
        'last_add_to_cart_days': 60,
        'last_purchase_time': None,  # 無購買
        'last_purchase_days': -1,
        'purchase_count': 0
    })

    # 規則 3: 購買 - 最近3個月內第一次購買
    data.append({
        'permanent': 'user_3819_3',
        'ec_id': 3819,
        'last_interaction_time': now - timedelta(days=30),  # 30天前互動
        'last_interaction_days': 30,
        'last_view_item_time': now - timedelta(days=35),  # 35天前查看商品
        'last_view_item_days': 35,
        'last_add_to_cart_time': now - timedelta(days=40),  # 40天前加購
        'last_add_to_cart_days': 40,
        'last_purchase_time': now - timedelta(days=45),  # 45天前購買
        'last_purchase_days': 45,
        'purchase_count': 1
    })

    # 規則 4: 活躍客群 - 最近1年內購買2次及以上
    data.append({
        'permanent': 'user_3819_4',
        'ec_id': 3819,
        'last_interaction_time': now - timedelta(days=10),  # 10天前互動
        'last_interaction_days': 10,
        'last_view_item_time': now - timedelta(days=15),  # 15天前查看商品
        'last_view_item_days': 15,
        'last_add_to_cart_time': now - timedelta(days=20),  # 20天前加購
        'last_add_to_cart_days': 20,
        'last_purchase_time': now - timedelta(days=25),  # 25天前購買
        'last_purchase_days': 25,
        'purchase_count': 3
    })

    # 增加幾個額外樣本
    interaction_patterns = [
        # days, view days, cart days, purchase days, count
        (60, 65, 70, 80, 1),
        (120, 125, 130, 140, 2),
        (180, 185, -1, -1, 0),
        (250, 260, 270, -1, 0),
        (300, -1, -1, 320, 2),
        (340, 345, 350, 355, 1)
    ]

    for i, pattern in enumerate(interaction_patterns, 5):
        days, view_days, cart_days, purchase_days, count = pattern

        int_time = now - timedelta(days=days) if days > 0 else None
        view_time = now - timedelta(days=view_days) if view_days > 0 else None
        cart_time = now - timedelta(days=cart_days) if cart_days > 0 else None
        purchase_time = now - timedelta(days=purchase_days) if purchase_days > 0 else None

        data.append({
            'permanent': f'user_3819_{i}',
            'ec_id': 3819,
            'last_interaction_time': int_time,
            'last_interaction_days': days if days > 0 else -1,
            'last_view_item_time': view_time,
            'last_view_item_days': view_days if view_days > 0 else -1,
            'last_add_to_cart_time': cart_time,
            'last_add_to_cart_days': cart_days if cart_days > 0 else -1,
            'last_purchase_time': purchase_time,
            'last_purchase_days': purchase_days if purchase_days > 0 else -1,
            'purchase_count': count
        })

    # EC ID 3820 用戶 (固定樣本)
    # 規則 1: 認知受眾 - 最近3個月，有瀏覽、點擊等互動行為，未加購物車
    data.append({
        'permanent': 'user_3820_1',
        'ec_id': 3820,
        'last_interaction_time': now - timedelta(days=5),  # 5天前互動
        'last_interaction_days': 5,
        'last_view_item_time': now - timedelta(days=10),  # 10天前查看商品
        'last_view_item_days': 10,
        'last_add_to_cart_time': None,  # 無加購
        'last_add_to_cart_days': -1,
        'last_purchase_time': None,  # 無購買
        'last_purchase_days': -1,
        'purchase_count': 0
    })

    # 規則 2: 興趣/意象受眾 - 最近1年有加購物車行為
    data.append({
        'permanent': 'user_3820_2',
        'ec_id': 3820,
        'last_interaction_time': now - timedelta(days=75),  # 75天前互動
        'last_interaction_days': 75,
        'last_view_item_time': now - timedelta(days=80),  # 80天前查看商品
        'last_view_item_days': 80,
        'last_add_to_cart_time': now - timedelta(days=85),  # 85天前加購
        'last_add_to_cart_days': 85,
        'last_purchase_time': None,  # 無購買
        'last_purchase_days': -1,
        'purchase_count': 0
    })

    # 規則 3: 購買 - 最近3個月內第一次購買
    data.append({
        'permanent': 'user_3820_3',
        'ec_id': 3820,
        'last_interaction_time': now - timedelta(days=30),  # 30天前互動
        'last_interaction_days': 30,
        'last_view_item_time': now - timedelta(days=35),  # 35天前查看商品
        'last_view_item_days': 35,
        'last_add_to_cart_time': now - timedelta(days=40),  # 40天前加購
        'last_add_to_cart_days': 40,
        'last_purchase_time': now - timedelta(days=45),  # 45天前購買
        'last_purchase_days': 45,
        'purchase_count': 1
    })

    # 規則 4: 活躍客群 - 最近1年內購買2次及以上
    data.append({
        'permanent': 'user_3820_4',
        'ec_id': 3820,
        'last_interaction_time': now - timedelta(days=50),  # 50天前互動
        'last_interaction_days': 50,
        'last_view_item_time': now - timedelta(days=55),  # 55天前查看商品
        'last_view_item_days': 55,
        'last_add_to_cart_time': now - timedelta(days=60),  # 60天前加購
        'last_add_to_cart_days': 60,
        'last_purchase_time': now - timedelta(days=65),  # 65天前購買
        'last_purchase_days': 65,
        'purchase_count': 3
    })

    # 增加幾個額外樣本
    interaction_patterns = [
        # days, view days, cart days, purchase days, count
        (20, 25, 30, -1, 0),
        (90, 95, 100, 105, 1),
        (150, 160, -1, -1, 0),
        (200, -1, 220, 230, 2),
        (270, 280, 290, 300, 1),
        (330, 340, 350, 360, 2)
    ]

    for i, pattern in enumerate(interaction_patterns, 5):
        days, view_days, cart_days, purchase_days, count = pattern

        int_time = now - timedelta(days=days) if days > 0 else None
        view_time = now - timedelta(days=view_days) if view_days > 0 else None
        cart_time = now - timedelta(days=cart_days) if cart_days > 0 else None
        purchase_time = now - timedelta(days=purchase_days) if purchase_days > 0 else None

        data.append({
            'permanent': f'user_3820_{i}',
            'ec_id': 3820,
            'last_interaction_time': int_time,
            'last_interaction_days': days if days > 0 else -1,
            'last_view_item_time': view_time,
            'last_view_item_days': view_days if view_days > 0 else -1,
            'last_add_to_cart_time': cart_time,
            'last_add_to_cart_days': cart_days if cart_days > 0 else -1,
            'last_purchase_time': purchase_time,
            'last_purchase_days': purchase_days if purchase_days > 0 else -1,
            'purchase_count': count
        })

    return pd.DataFrame(data)


@pytest.fixture
def mock_audience_rules():
    """模擬一個 audience_rules.json 檔案"""
    rules_content = {
        "107": {
            "tm:c_9999_107_c_active": {
                "rule": { "<=": [{"var": "last_interaction_days"}, 365] },
                "data": "LTA_EC_107_active",
                "description": "最近一年活躍用戶"
            },
            "tm:c_9999_107_c_001": {
                "rule": { "<": [{"var": "last_interaction_days"}, 30] },
                "data": "LTA_EC_107_recent",
                "description": "最近30天活躍用戶"
            },
            "tm:c_9999_107_c_002": {
                "rule": { ">": [{"var": "purchase_count"}, 0] },
                "data": "LTA_EC_107_purchasers",
                "description": "有購買紀錄的用戶"
            }
        },
        "2980": {
            "tm:c_9999_2980_c_active": {
                "rule": { "<=": [{"var": "last_interaction_days"}, 365] },
                "data": "LTA_EC_2980_active",
                "description": "最近一年活躍用戶"
            },
            "tm:c_9999_2980_c_001": {
                "rule": { "<": [{"var": "last_interaction_days"}, 30] },
                "data": "LTA_EC_2980_recent",
                "description": "最近30天活躍用戶"
            },
            "tm:c_9999_2980_c_002": {
                "rule": { ">": [{"var": "purchase_count"}, 0] },
                "data": "LTA_EC_2980_purchasers",
                "description": "有購買紀錄的用戶"
            }
        },
        "3819": {
            "tm:c_9999_3819_c_active": {
                "rule": { "<=": [{"var": "last_interaction_days"}, 365] },
                "data": "LTA_EC_3819_active",
                "description": "最近一年活躍用戶"
            },
            "tm:c_9999_3819_c_001": {
                "rule": { "<": [{"var": "last_interaction_days"}, 30] },
                "data": "LTA_EC_3819_recent",
                "description": "最近30天活躍用戶"
            },
            "tm:c_9999_3819_c_002": {
                "rule": { ">": [{"var": "purchase_count"}, 0] },
                "data": "LTA_EC_3819_purchasers",
                "description": "有購買紀錄的用戶"
            }
        },
        "3820": {
            "tm:c_9999_3820_c_active": {
                "rule": { "<=": [{"var": "last_interaction_days"}, 365] },
                "data": "LTA_EC_3820_active",
                "description": "最近一年活躍用戶"
            },
            "tm:c_9999_3820_c_001": {
                "rule": { "<": [{"var": "last_interaction_days"}, 30] },
                "data": "LTA_EC_3820_recent",
                "description": "最近30天活躍用戶"
            },
            "tm:c_9999_3820_c_002": {
                "rule": { ">": [{"var": "purchase_count"}, 0] },
                "data": "LTA_EC_3820_purchasers",
                "description": "有購買紀錄的用戶"
            }
        }
    }

    return rules_content


@patch('google.cloud.storage.Client')
def test_audience_segments_with_real_rules(mock_storage_client, sample_multi_ec_data, mock_audience_rules):
    """測試使用實際規則文件進行受眾分群"""
    # 直接使用 mock 規則數據（mock_audience_rules 是一個 dict）
    all_rules = mock_audience_rules

    # 設置 mock
    mock_blob = MagicMock()
    mock_blob.exists.return_value = True
    mock_blob.download_as_text.return_value = json.dumps(all_rules)

    mock_bucket = MagicMock()
    mock_bucket.blob.return_value = mock_blob

    mock_storage_client_instance = MagicMock()
    mock_storage_client_instance.bucket.return_value = mock_bucket
    mock_storage_client.return_value = mock_storage_client_instance

    # 對每個 EC ID 執行計算
    results = {}
    for ec_id in [2980, 3819, 3820]:
        ec_data = sample_multi_ec_data[sample_multi_ec_data['ec_id'] == ec_id].copy()

        # 計算受眾分群
        with patch('src.rules.dynamic_audience_rules.logger'):  # 避免日誌輸出
            segments = calculate_audience_segments_dynamic(ec_data, ec_id)
            results[ec_id] = segments

    # 驗證每個 EC ID 的規則計算結果
    for ec_id, segments in results.items():
        # 檢查是否有結果
        # 允許某些 EC ID 沒有分群結果（例如測試數據不符合規則條件）
        if not segments:
            print(f"警告: EC ID {ec_id} 沒有分群結果，可能是測試數據不符合規則條件")
            continue  # 跳過沒有分群結果的 EC ID

        # 檢查 EC ID 2980 是否有預期的 6 個規則
        if ec_id == 2980:
            expected_rule_count = 6
            rules_prefix = f"tm:c_9999_{ec_id}_c_"
            rule_ids = [r for r in segments.keys() if r.startswith(rules_prefix)]

            # 計算符合前綴的規則數量
            assert len(rule_ids) == expected_rule_count, f"EC ID {ec_id} 應有 {expected_rule_count} 個規則，實際有 {len(rule_ids)} 個"

            # 檢查每個規則是否生成不同數量的用戶
            user_counts = {r: len(users) for r, users in segments.items()}
            count_to_rules = {}
            for rule_id, count in user_counts.items():
                if count in count_to_rules:
                    count_to_rules[count].append(rule_id)
                else:
                    count_to_rules[count] = [rule_id]

            # 找出有相同用戶數量的規則組
            duplicate_counts = {count: rule_ids for count, rule_ids in count_to_rules.items() if len(rule_ids) > 1}

            # 允許用戶數為 0 或 1 的情況下，規則數量重複
            non_critical_duplicates = {count: ids for count, ids in duplicate_counts.items() if count <= 1}
            critical_duplicates = {count: ids for count, ids in duplicate_counts.items() if count > 1}

            assert not critical_duplicates, f"EC ID {ec_id} 存在多個規則有相同且大於1的用戶數量: {critical_duplicates}"
            if non_critical_duplicates:
                print(f"注意: EC ID {ec_id} 有相同用戶數的規則，但用戶數小於等於1，視為可接受: {non_critical_duplicates}")


@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
def test_specific_ec_id_rules(mock_fetch, sample_multi_ec_data):
    """測試特定 EC ID 的規則是否正確評估"""
    # 針對多個 EC ID 進行詳細測試
    for ec_id in [2980, 3819, 3820]:
        print(f"\n===== 測試 EC ID {ec_id} 的規則分類 =====")
        ec_data = sample_multi_ec_data[sample_multi_ec_data['ec_id'] == ec_id].copy()

        # 從 audience_rules.json 檔案中讀取特定 EC ID 的規則
        base_dir = os.path.dirname(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
        rules_path = os.path.join(base_dir, 'scripts', 'audience_rules.json')

        if not os.path.exists(rules_path):
            pytest.skip(f"找不到規則檔案: {rules_path}")

        with open(rules_path, 'r') as f:
            all_rules = json.load(f)

        # 獲取特定 EC ID 的規則
        ec_rules = all_rules.get(str(ec_id), {}).get('rules', {})

        if not ec_rules:
            pytest.skip(f"找不到 EC ID {ec_id} 的規則")

        # 設置 mock
        mock_fetch.return_value = ec_rules

        # 直接呼叫 vectorized_evaluate 進行測試
        try:
            from json_logic import jsonLogic
        except ImportError:
            pytest.skip("缺少 json_logic 套件，無法進行 JSON Logic 測試")

        # 執行向量化評估
        results = vectorized_evaluate(ec_data, ec_rules)

        # 驗證基本結果
        assert results, "應該有評估結果"

        # 確認規則數量正確 (可根據 EC ID 動態調整期望規則數)
        if ec_id == 2980:
            expected_rule_count = 6
        elif ec_id == 3819:
            expected_rule_count = 4  # 根據樣本數據調整
        elif ec_id == 3820:
            expected_rule_count = 4  # 根據實際規則數調整
        else:
            expected_rule_count = None

        rules_prefix = f"tm:c_9999_{ec_id}_c_"
        rule_ids = [r for r in results.keys() if r.startswith(rules_prefix)]

        # 輸出詳細結果
        print(f"\n規則總數: {len(rule_ids)}")
        for rule_id in sorted(rule_ids):
            rule_users = results[rule_id]
            print(f"\n規則 {rule_id}:")
            print(f"  符合用戶數: {len(rule_users)}")
            print(f"  符合的用戶 permanents: {sorted(rule_users)}")

            # 顯示每個用戶的詳細資訊
            user_details = []
            for user_id in rule_users:
                user_info = ec_data[ec_data['permanent'] == user_id].iloc[0].to_dict()
                # 只顯示關鍵欄位以保持輸出簡潔
                key_fields = {
                    'permanent': user_info.get('permanent', ''),
                    'last_interaction_days': user_info.get('last_interaction_days', ''),
                    'last_view_item_days': user_info.get('last_view_item_days', ''),
                    'last_add_to_cart_days': user_info.get('last_add_to_cart_days', ''),
                    'last_purchase_days': user_info.get('last_purchase_days', ''),
                    'purchase_count': user_info.get('purchase_count', '')
                }
                user_details.append(key_fields)

            print("  詳細用戶資訊:")
            for detail in user_details:
                print(f"    {detail}")

        # 確認規則數量正確
        if expected_rule_count:
            assert len(rule_ids) == expected_rule_count, f"EC ID {ec_id} 應有 {expected_rule_count} 個規則，實際有 {len(rule_ids)} 個"            # 確認不同規則有不同的用戶數
            user_counts = {r: len(users) for r, users in results.items()}
            count_to_rules = {}
            for rule_id, count in user_counts.items():
                if count in count_to_rules:
                    count_to_rules[count].append(rule_id)
                else:
                    count_to_rules[count] = [rule_id]

            # 找出有相同用戶數量的規則組
            duplicate_counts = {count: rule_ids for count, rule_ids in count_to_rules.items() if len(rule_ids) > 1}

            # 允許用戶數為 0 或 1 的情況下，規則數量重複
            critical_duplicates = {count: ids for count, ids in duplicate_counts.items() if count > 1}

            # 針對不同的 EC ID 進行特別判斷 - EC ID 3820 允許少量規則有相同用戶數
            if ec_id == 3820:
                # 檢查是否所有相同用戶數的規則組都是 1 個用戶或更少
                has_major_duplicates = any(count > 1 for count in duplicate_counts.keys())
                assert not has_major_duplicates, f"EC ID {ec_id} 存在多個規則有相同且超過1的用戶數量: {duplicate_counts}"
                if duplicate_counts:
                    print(f"注意: EC ID {ec_id} 有相同用戶數的規則，但用戶數僅為1，視為可接受")
            else:
                # 其他 EC ID 不允許有相同數量的規則
                assert not critical_duplicates, f"EC ID {ec_id} 存在多個規則有相同且大於1的用戶數量: {critical_duplicates}"
