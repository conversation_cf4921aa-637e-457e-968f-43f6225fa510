"""
整合測試的共用 Pytest Fixtures
"""
import os
import logging
import pytest
import google.auth
from google.cloud import bigquery

# 將 src 目錄加入 Python 路徑，這樣才能正確 import
import sys
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..', '..', 'src')))

from core.cloud_integration import BigQueryClient

logger = logging.getLogger(__name__)

@pytest.fixture(scope="session")
def check_gcp_credentials():
    """
    檢查 GCP 認證是否可用。
    使用 session scope，在整個測試會話中只檢查一次。
    """
    service_account_path = "/app/tagtoo-ml-workflow-kubeflow.json"
    if os.path.exists(service_account_path):
        os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = service_account_path
        logger.info(f"使用服務帳戶文件: {service_account_path}")
    else:
        logger.warning(f"服務帳戶文件不存在: {service_account_path}，將嘗試使用預設認證。")

    try:
        credentials, project = google.auth.default()
        logger.info(f"✅ 成功獲取 GCP 認證，項目: {project}")
        return {'credentials': credentials, 'project': project}
    except Exception as e:
        pytest.skip(f"❌ 跳過真實 BigQuery 測試：無法獲取 GCP 認證 - {str(e)}")

@pytest.fixture(scope="session")
def bigquery_setup_client(check_gcp_credentials):
    """
    提供一個在 session scope 內用於設定/清理的 BigQuery Client。
    """
    if not check_gcp_credentials:
        pytest.skip("跳過：無 GCP 認證")
    return bigquery.Client()

@pytest.fixture(scope="session")
def persistent_test_table_id():
    """
    返回固定的測試表格 ID。
    """
    return "tagtoo-tracking.event_test.user_stats"

@pytest.fixture(scope="session")
def ensure_persistent_test_table(bigquery_setup_client, persistent_test_table_id):
    """
    確保共用的測試表格存在，其 schema 與生產環境一致。
    在整個測試會話 (session) 開始時執行一次，結束時不處理。
    """
    prod_table_id = "tagtoo-tracking.event_prod.user_stats"
    logger.info(f"檢查共用測試表格: {persistent_test_table_id}")

    try:
        bigquery_setup_client.get_table(persistent_test_table_id)
        logger.info(f"✅ 共用測試表格 {persistent_test_table_id} 已存在。")
    except Exception:
        logger.warning(f"共用測試表格 {persistent_test_table_id} 不存在，將根據生產表格 {prod_table_id} 進行創建...")
        try:
            prod_table = bigquery_setup_client.get_table(prod_table_id)
            test_table = bigquery.Table(persistent_test_table_id, schema=prod_table.schema)
            if prod_table.time_partitioning:
                test_table.time_partitioning = prod_table.time_partitioning
            if prod_table.clustering_fields:
                test_table.clustering_fields = prod_table.clustering_fields
            bigquery_setup_client.create_table(test_table)
            logger.info(f"✅ 共用測試表格 {persistent_test_table_id} 創建成功。")
        except Exception as e:
            pytest.fail(f"❌ 創建共用測試表格 {persistent_test_table_id} 失敗: {e}")

    return persistent_test_table_id

@pytest.fixture(scope="function")
def cleanup_persistent_test_table(bigquery_setup_client, persistent_test_table_id):
    """
    在每個測試函數執行後，清理共用測試表格中的數據。
    使用 function scope 確保每個測試都在乾淨的狀態下開始。
    """
    yield persistent_test_table_id

    logger.info(f"🧹 清理測試表格 {persistent_test_table_id} 中的數據...")
    try:
        # 刪除所有數據，但保留表格結構
        delete_query = f"DELETE FROM `{persistent_test_table_id}` WHERE TRUE"
        bigquery_setup_client.query(delete_query).result()
        logger.info(f"✅ 已清空測試表格 {persistent_test_table_id} 中的數據。")
    except Exception as e:
        logger.warning(f"清理測試表格 {persistent_test_table_id} 時發生錯誤: {e}")