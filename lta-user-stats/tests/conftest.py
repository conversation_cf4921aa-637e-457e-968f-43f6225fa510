"""
pytest 測試配置文件
提供 CI 環境設置、測試夾具和性能監控
"""
import pytest
import os
import sys
import tempfile
import time
import platform
import psutil
from datetime import datetime, timedelta, timezone
from unittest.mock import patch, MagicMock
import pandas as pd
import json

# 確保 src 模組路徑正確設置
current_dir = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))  # 上一層目錄（專案根目錄）
src_path = os.path.join(current_dir, 'src')
if src_path not in sys.path:
    sys.path.insert(0, src_path)

# 確保專案根目錄在 Python 路徑中
if current_dir not in sys.path:
    sys.path.insert(0, current_dir)


# =============================================================================
# CI 環境和基礎設置
# =============================================================================

@pytest.fixture(scope="session", autouse=True)
def ci_environment_setup():
    """CI 環境設置夾具，只在 CI 環境中自動啟動 mock"""
    if not os.getenv('CI'):
        yield
        return

    # 只在 CI 環境中進行 mock 設置
    patches = []

    try:
        # Mock Google Cloud 服務，但允許被其他 mock 覆蓋
        # 主要 patch 模組內部的導入路徑
        bq_patch_core = patch('src.core.cloud_integration.bigquery.Client', autospec=False)
        storage_patch_core = patch('src.core.cloud_integration.storage.Client', autospec=False)
        # 也 patch 全局路徑以捕獲可能的直接導入
        bq_patch_google = patch('google.cloud.bigquery.Client', autospec=False)
        storage_patch_google = patch('google.cloud.storage.Client', autospec=False)

        mock_bq_core = bq_patch_core.start()
        mock_storage_core = storage_patch_core.start()
        mock_bq_google = bq_patch_google.start()
        mock_storage_google = storage_patch_google.start()

        # 統一設置 mock 實例的行為
        # 確保所有 mock 路徑返回的 mock client 實例行為一致
        shared_bq_mock_instance = MagicMock()
        shared_bq_mock_instance._http = MagicMock()
        shared_bq_mock_instance._http.adapters = {}

        mock_bq_core.return_value = shared_bq_mock_instance
        mock_bq_google.return_value = shared_bq_mock_instance

        shared_storage_mock_instance = MagicMock()
        # 為 storage mock 添加默認的 audience rules JSON 配置
        mock_bucket = MagicMock()
        mock_blob = MagicMock()

        # 配置一個基本的 audience rules JSON 用於測試
        # 添加符合測試預期的規則 ID 前綴 "tm:c_9999_107_c_" 並使用正確的格式
        default_audience_rules = {
            "107": {
                "rules": {
                    "tm:c_9999_107_c_active": {
                        "rule": {"==": [{"var": "last_interaction_days"}, {"var": "last_interaction_days"}]},
                        "data": {},
                        "description": "active users",
                        "segments": {"tm:c_9999_107_c_active": "active users segment"}
                    },
                    "tm:c_9999_107_c_001": {
                        "rule": {"<": [{"var": "last_interaction_days"}, 30]},
                        "data": {"min_days": 0, "max_days": 30},
                        "description": "recent users",
                        "segments": {"tm:c_9999_107_c_001": "recent users segment"}
                    },
                    "tm:c_9999_107_c_002": {
                        "rule": {">": [{"var": "purchase_count"}, 0]},
                        "data": {},
                        "description": "purchasers",
                        "segments": {"tm:c_9999_107_c_002": "purchasers segment"}
                    }
                }
            }
        }

        mock_blob.exists.return_value = True
        mock_blob.download_as_text.return_value = json.dumps(default_audience_rules)
        mock_bucket.blob.return_value = mock_blob
        shared_storage_mock_instance.bucket.return_value = mock_bucket

        mock_storage_core.return_value = shared_storage_mock_instance
        mock_storage_google.return_value = shared_storage_mock_instance

        patches.extend([bq_patch_core, storage_patch_core, bq_patch_google, storage_patch_google])

        # Mock 其他可能的外部依賴
        logging_patch = patch('google.cloud.logging.Client', autospec=False)
        mock_logging = logging_patch.start()
        patches.append(logging_patch)

        print("✅ CI 環境 mock 設置完成 (已 patch core 和 google 路徑)")
        yield

    except Exception as e:
        print(f"⚠️  CI mock 設置警告: {e}")
        yield
    finally:
        # 清理所有 patch
        for p in patches:
            try:
                p.stop()
            except Exception:
                pass


@pytest.fixture(autouse=True)
def setup_temp_directories():
    """自動創建測試需要的臨時目錄"""
    temp_dirs = [
        '/tmp/test_lta_user_stats_cache',
        '/tmp/test_snapshots'
    ]

    created_dirs = []
    for temp_dir in temp_dirs:
        if not os.path.exists(temp_dir):
            os.makedirs(temp_dir, exist_ok=True)
            created_dirs.append(temp_dir)

    yield

    # 清理創建的目錄
    for temp_dir in created_dirs:
        try:
            import shutil
            if os.path.exists(temp_dir):
                shutil.rmtree(temp_dir)
        except Exception:
            pass


# =============================================================================
# Google Cloud Mock Fixtures
# =============================================================================

@pytest.fixture
def mock_bigquery_client():
    """建立 BigQuery client 的 mock - 兼容 CI 和本地環境"""
    # 在 CI 環境中，mock 可能已經存在，直接創建 mock instance
    if os.environ.get('CI'):
        # 直接創建一個 MagicMock 作為 mock instance
        mock_instance = MagicMock()
    else:
        # 在非 CI 環境中，創建正常的 patch
        mock_instance = MagicMock()

    # 設定基本的 mock 回傳值
    mock_job = MagicMock()
    mock_job.total_bytes_processed = 1000
    # 設定查詢結果
    mock_result = MagicMock()
    mock_result.to_dataframe.return_value = pd.DataFrame({
        'ec_id': [107],
        'count': [10]
    })
    mock_job.result.return_value = mock_result

    # 設定 job 配置
    mock_instance.query.return_value = mock_job
    # 在 CI 環境中直接返回 mock instance，在非 CI 環境中需要在 context manager 中
    if os.environ.get('CI'):
        yield mock_instance
    else:
        with patch('src.core.cloud_integration.bigquery.Client', autospec=False) as mock:
            mock.return_value = mock_instance
            yield mock_instance


@pytest.fixture
def mock_storage_client():
    """建立 Storage client 的 mock - 兼容 CI 和本地環境"""
    # 在 CI 環境中，mock 可能已經存在，直接創建 mock instance
    if os.environ.get('CI'):
        # 直接創建一個 MagicMock 作為 mock instance
        mock_instance = MagicMock()
    else:
        # 在非 CI 環境中，創建正常的 patch
        mock_instance = MagicMock()

    mock_bucket = MagicMock()
    mock_blob = MagicMock()

    # 不再驗證 blob 路徑
    mock_bucket.blob.return_value = mock_blob
    mock_instance.bucket.return_value = mock_bucket
    # 設定下載內容
    mock_blob.download_as_text.return_value = json.dumps({
        "107": {
            "rules": {
                "tm:c_9999_107_c_001": {
                    "data": {"min_days": 30, "max_days": 365},
                    "rule": {
                        "and": [
                            {"!=": [{"var": "last_interaction"}, None]},
                            {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                            {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                        ]
                    }
                }
            }
        }
    })

    # 在 CI 環境中直接返回 mock instance，在非 CI 環境中需要在 context manager 中
    if os.environ.get('CI'):
        yield mock_instance
    else:
        with patch('src.core.cloud_integration.storage.Client', autospec=False) as mock:
            mock.return_value = mock_instance
            yield mock_instance


@pytest.fixture
def mock_gcsfs():
    """Mock GCSFS 檔案系統"""
    with patch('gcsfs.GCSFileSystem') as mock_fs:
        mock_fs.return_value.exists.return_value = False
        mock_fs.return_value.ls.return_value = []
        yield mock_fs


# =============================================================================
# 測試資料 Fixtures
# =============================================================================

@pytest.fixture
def mock_dataframe():
    """提供測試用的模擬 DataFrame"""
    return pd.DataFrame({
        'ec_id': [107, 107, 107],
        'permanent': ['user1', 'user2', 'user3'],
        'purchase_count': [1, 2, 3],
        'last_interaction_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'registration_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'first_interaction_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'first_purchase_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'last_purchase_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'total_purchase_amount': [100.0, 200.0, 300.0],
        'total_sessions': [10, 20, 30],
        'daily_purchase_count': [1, 2, 3],
        'last_interaction_days': [10, 20, 30],
        'daily_purchase_amount': [100.0, 200.0, 300.0],
        'daily_sessions': [1, 2, 3],
        'first_interaction_of_day': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'last_interaction_of_day': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'first_purchase_of_day': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'last_purchase_of_day': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')]
    })


@pytest.fixture
def mock_audience_rules():
    """產生測試用的規則資料"""
    return {
        "tm:c_9999_107_c_001": {
            "description": "前2個月~1年有互動的用戶",
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"!=": [{"var": "last_interaction"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        }
    }


@pytest.fixture
def sample_user_data():
    """提供測試用的用戶數據"""
    return [
        {
            'permanent': 'user1',
            'last_interaction_time': '2024-01-01',
            'last_interaction_days': 30,
            'total_sessions': 10
        },
        {
            'permanent': 'user2',
            'last_interaction_time': '2024-01-15',
            'last_interaction_days': 15,
            'total_sessions': 5
        }
    ]


@pytest.fixture
def sample_rules():
    """提供測試用的規則數據"""
    return {
        'rule1': {
            'rule': {
                'and': [
                    {'!=': [{'var': 'last_interaction_time'}, None]},
                    {'>': [{'var': 'last_interaction_days'}, 10]},
                    {'<=': [{'var': 'last_interaction_days'}, 60]}
                ]
            },
            'min_days': 10,
            'max_days': 60
        }
    }


# =============================================================================
# 性能監控系統
# =============================================================================

# 性能追蹤變數
performance_metrics = {
    'benchmark_results': {},
    'memory_usage': {},
    'start_time': None,
    'end_time': None,
    'process': psutil.Process(os.getpid()),
    'platform': platform.system(),  # 添加平台識別
    'in_cloud_function': os.environ.get('K_SERVICE') is not None or os.environ.get('FUNCTION_TARGET') is not None
}

def format_bytes(bytes):
    """將位元組數格式化為人類可讀格式"""
    for unit in ['B', 'KB', 'MB', 'GB', 'TB']:
        if bytes < 1024 or unit == 'TB':
            return f"{bytes:.2f} {unit}"
        bytes /= 1024

@pytest.hookimpl(tryfirst=True)
def pytest_sessionstart(session):
    """在測試會話開始時記錄初始狀態"""
    performance_metrics['start_time'] = time.time()
    performance_metrics['initial_memory'] = performance_metrics['process'].memory_info().rss

@pytest.hookimpl(hookwrapper=True)
def pytest_runtest_call(item):
    """在每個測試執行時收集性能數據"""
    start_time = time.time()
    start_memory = performance_metrics['process'].memory_info().rss

    # 執行測試
    yield

    end_time = time.time()
    end_memory = performance_metrics['process'].memory_info().rss

    # 僅收集 benchmark 和 memory 測試的指標
    if 'benchmark' in item.name or 'memory' in item.name:
        test_name = item.name
        performance_metrics['benchmark_results'][test_name] = {
            'duration': end_time - start_time,
            'memory_diff': end_memory - start_memory
        }

@pytest.hookimpl(trylast=True)
def pytest_sessionfinish(session, exitstatus):
    """在測試會話結束時輸出性能報告"""
    performance_metrics['end_time'] = time.time()
    total_duration = performance_metrics['end_time'] - performance_metrics['start_time']
    final_memory = performance_metrics['process'].memory_info().rss
    memory_diff = final_memory - performance_metrics['initial_memory']

    # 僅在測試成功時輸出性能報告
    if exitstatus == 0:
        print("\n\n" + "="*80)
        print("【性能測試報告】".center(76))
        print("="*80)

        # 顯示環境信息
        env_type = "Cloud Functions" if performance_metrics['in_cloud_function'] else "本地開發環境"
        print(f"\n🖥️ 運行環境: {performance_metrics['platform']} ({platform.version()}) - {env_type}")
        print(f"🐍 Python 版本: {platform.python_version()}")
        print(f"🧠 CPU 核心數: {psutil.cpu_count(logical=False)} 物理核心 / {psutil.cpu_count()} 邏輯核心")
        print(f"🕒 總執行時間: {total_duration:.2f} 秒")
        print(f"📊 最終記憶體使用: {format_bytes(final_memory)} (增加: {format_bytes(memory_diff)})")

        # 顯示系統記憶體狀態
        try:
            sys_mem = psutil.virtual_memory()
            print(f"💻 系統記憶體: {format_bytes(sys_mem.used)}/{format_bytes(sys_mem.total)} "
                  f"({sys_mem.percent}% 已使用)")
        except Exception as e:
            print(f"無法獲取系統記憶體信息: {str(e)}")

        # 輸出基準測試結果
        if performance_metrics['benchmark_results']:
            print("\n📈 【性能基準測試詳細結果】")
            print("-" * 80)
            print(f"{'測試名稱':<50} {'執行時間(秒)':<15} {'記憶體增加':<15}")
            print("-" * 80)

            for test_name, metrics in performance_metrics['benchmark_results'].items():
                formatted_name = test_name.replace('test_', '').replace('_', ' ').capitalize()
                print(f"{formatted_name:<50} {metrics['duration']:<15.4f} {format_bytes(metrics['memory_diff']):<15}")

            # 分析資料量與性能的關係
            scale_results = {}
            for test_name, metrics in performance_metrics['benchmark_results'].items():
                if 'scale' in test_name:
                    # 嘗試從測試名稱中提取比例因子
                    for scale in [1, 10, 100, 1000]:
                        if f"scale={scale}" in test_name:
                            if scale not in scale_results:
                                scale_results[scale] = []
                            scale_results[scale].append(metrics['duration'])

            if scale_results:
                print("\n📊 【資料量與執行時間關係】")
                print("-" * 80)
                print(f"{'資料量(千筆)':<15} {'平均執行時間(秒)':<20} {'相對效能':<15}")
                print("-" * 80)

                # 以最小比例因子為基準計算相對效能
                base_scale = min(scale_results.keys())
                base_time = sum(scale_results[base_scale]) / len(scale_results[base_scale])

                for scale in sorted(scale_results.keys()):
                    avg_time = sum(scale_results[scale]) / len(scale_results[scale])
                    relative_perf = base_time / avg_time * (scale / base_scale)
                    print(f"{scale:<15} {avg_time:<20.4f} {relative_perf:<15.2%}")

        # 輸出記憶體使用分析
        print("\n💾 【記憶體使用分析】")
        print("-" * 80)
        print(f"{'資源類型':<30} {'使用量':<20} {'占比':<10}")
        print("-" * 80)

        # 獲取詳細的記憶體使用情況
        try:
            mem_info = performance_metrics['process'].memory_info()
            total_mem = mem_info.rss

            # 展示不同記憶體類型的使用情況（跨平台兼容）
            mem_types = [
                ('物理記憶體 (RSS)', mem_info.rss),
                ('虛擬記憶體 (VMS)', mem_info.vms),
            ]

            # 根據系統平台添加額外資訊
            if hasattr(mem_info, 'shared'):
                mem_types.append(('共享記憶體', mem_info.shared))
            if hasattr(mem_info, 'text'):
                mem_types.append(('純文本記憶體', mem_info.text))
            if hasattr(mem_info, 'data'):
                mem_types.append(('資料段記憶體', mem_info.data))
            if hasattr(mem_info, 'lib'):
                mem_types.append(('庫記憶體', mem_info.lib))

            # Linux 特有的屬性
            if performance_metrics['platform'] == 'Linux':
                if hasattr(mem_info, 'swap'):
                    mem_types.append(('交換記憶體', mem_info.swap))
                if hasattr(mem_info, 'dirty'):
                    mem_types.append(('髒頁記憶體', mem_info.dirty))

            for name, value in mem_types:
                percentage = value / total_mem * 100 if total_mem > 0 else 0
                print(f"{name:<30} {format_bytes(value):<20} {percentage:<10.2f}%")

        except Exception as e:
            print(f"\n⚠️ 記憶體分析時發生錯誤: {str(e)}")
            print(f"平台: {performance_metrics['platform']}, Python: {platform.python_version()}")

        # 獲取主要記憶體消耗函數
        if 'benchmark_results' in performance_metrics and performance_metrics['benchmark_results']:
            memory_intensive_tests = sorted(
                performance_metrics['benchmark_results'].items(),
                key=lambda x: x[1]['memory_diff'],
                reverse=True
            )[:3]

            print("\n🔬 【主要記憶體消耗部分】")
            print("-" * 80)
            for test_name, metrics in memory_intensive_tests:
                formatted_name = test_name.replace('test_', '').replace('_', ' ').capitalize()
                print(f"{formatted_name}: {format_bytes(metrics['memory_diff'])}")

        # 提供性能改進建議
        print("\n🔍 【性能改進建議】")
        print("-" * 80)

        suggestions = [
            "考慮使用 PyArrow 或 NumPy 進行資料處理以減少記憶體使用",
            "優化過濾操作，先過濾再聚合可減少中間結果的大小",
            "對大資料集使用分塊處理 (chunking) 搭配並行處理",
            "使用懶惰評估 (lazy evaluation) 推遲資料處理",
            "只載入必要的欄位以減少記憶體佔用",
            "資料序列化至磁碟可釋放記憶體，但會增加 I/O 操作",
            "針對關鍵計算使用向量化操作而非迭代",
            "使用 Pandas UDF 或 numba 加速計算密集型操作",
            "考慮使用漸進式處理流程避免一次性載入全部資料"
        ]

        # 根據平台添加特定建議
        if performance_metrics['platform'] == 'Linux':
            suggestions.append("在 Linux 環境中，考慮使用 mmap 減少實際記憶體使用")

            if performance_metrics['in_cloud_function']:
                suggestions.append("設定 Cloud Functions 記憶體上限以優化成本與效能")
                suggestions.append("考慮使用 Cloud Storage 臨時存儲中間結果以減少記憶體使用")
                suggestions.append("針對大型處理任務，考慮使用 Cloud Run 或 Dataflow 替代 Cloud Functions")

        elif performance_metrics['platform'] == 'Darwin':  # macOS
            suggestions.append("在 macOS 上開發時注意 Python 記憶體釋放可能與 Linux 環境有差異")
            suggestions.append("本地測試後，建議在類似 Cloud Functions 的 Docker 容器中進行最終驗證")

        for i, suggestion in enumerate(suggestions, 1):
            print(f"{i}. {suggestion}")

        # 如果在 Cloud Functions 環境中，提供特別警告
        if performance_metrics['in_cloud_function']:
            cf_mem_limit = os.environ.get('FUNCTION_MEMORY_MB', '未知')
            print(f"\n⚠️ 【Cloud Functions 特別提醒】")
            print(f"當前函數記憶體限制: {cf_mem_limit} MB")
            print(f"當前記憶體使用: {format_bytes(final_memory)} (約 {final_memory / (1024*1024):.2f} MB)")

            # 警告是否接近限制
            if cf_mem_limit != '未知':
                mem_limit = int(cf_mem_limit)
                mem_usage_mb = final_memory / (1024*1024)
                usage_percent = mem_usage_mb / mem_limit * 100

                if usage_percent > 80:
                    print(f"⛔ 警告：記憶體使用率已達 {usage_percent:.1f}%，接近限制！")
                    print("建議增加函數記憶體配置或優化代碼以減少記憶體使用")
                elif usage_percent > 50:
                    print(f"⚠️ 注意：記憶體使用率已達 {usage_percent:.1f}%")
                else:
                    print(f"✅ 記憶體使用率為 {usage_percent:.1f}%，狀況良好")

        print("\n" + "="*80)
        print("【測試完成】".center(76))
        print("="*80 + "\n")