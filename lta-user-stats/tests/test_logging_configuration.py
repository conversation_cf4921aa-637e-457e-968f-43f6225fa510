#!/usr/bin/env python3
"""
測試 logging 配置的正式測試文件
整合到 pytest 測試框架中
"""

import pytest
import logging
import os
import sys
from unittest.mock import patch, MagicMock

# 確保可以導入 src 模組
sys.path.insert(0, os.path.join(os.path.dirname(__file__), '..', 'src'))

class TestLoggingConfiguration:
    """測試 logging 配置相關功能"""

    def setup_method(self):
        """每個測試方法執行前的設定"""
        # 重置 logging 配置狀態
        from src.utils.logging_setup import _configured
        import src.utils.logging_setup as logging_setup
        logging_setup._configured = False

    def test_configure_logging_prevents_duplicate_configuration(self):
        """測試 configure_logging 防止重複配置"""
        from src.utils.logging_setup import configure_logging

        # 模擬 Cloud Functions 環境
        with patch.dict(os.environ, {'K_SERVICE': 'test-service'}):
            # 第一次配置
            logger1 = configure_logging()
            assert logger1 is not None
            assert logger1.name == 'lta_user_stats'

            # 第二次配置（應該返回相同的 logger，不重新配置）
            logger2 = configure_logging()
            assert logger2 is logger1

            # 第三次配置（應該返回相同的 logger，不重新配置）
            logger3 = configure_logging()
            assert logger3 is logger1

    def test_configure_logging_local_environment(self):
        """測試本地環境的 logging 配置"""
        from src.utils.logging_setup import configure_logging

        # 確保沒有 K_SERVICE 環境變數（本地環境）
        with patch.dict(os.environ, {}, clear=True):
            logger = configure_logging()
            assert logger is not None
            assert logger.name == 'lta_user_stats'

    def test_configure_logging_cloud_environment(self):
        """測試 Cloud Functions 環境的 logging 配置"""
        from src.utils.logging_setup import configure_logging

        # 模擬 Google Cloud Logging Client
        with patch('src.utils.logging_setup.cloud_logging.Client') as mock_client:
            mock_client_instance = MagicMock()
            mock_client.return_value = mock_client_instance

            # 模擬 Cloud Functions 環境
            with patch.dict(os.environ, {'K_SERVICE': 'test-service'}):
                logger = configure_logging()

                # 驗證 Cloud Logging 被正確設定
                mock_client.assert_called_once()
                mock_client_instance.setup_logging.assert_called_once_with(log_level=logging.INFO)

                assert logger is not None
                assert logger.name == 'lta_user_stats'

    def test_logging_level_adjustment(self):
        """測試動態調整日誌級別"""
        from src.utils.logging_setup import configure_logging

        # 初始配置
        logger = configure_logging(logging.INFO)

        # 驗證初始級別
        assert logger.level == logging.INFO or logging.getLogger().level == logging.INFO

        # 重新配置為 DEBUG 級別
        logger_debug = configure_logging(logging.DEBUG)

        # 應該返回相同的 logger（避免重複配置）
        assert logger_debug is logger

    def test_multiple_module_imports_no_duplicate_logs(self):
        """測試多個模組導入不會產生重複日誌"""
        # 這個測試比較複雜，主要是確保從不同模組導入 logging_setup
        # 不會導致重複的 handler
        from src.utils.logging_setup import configure_logging

        # 模擬多次從不同地方導入
        with patch.dict(os.environ, {'K_SERVICE': 'test-service'}):
            # 第一次配置
            logger1 = configure_logging()

            # 模擬從 utils/__init__.py 的導入
            logger2 = configure_logging()

            # 模擬從 main.py 的導入
            logger3 = configure_logging()

            # 所有應該是同一個 logger
            assert logger1 is logger2 is logger3

    def test_logging_setup_state_tracking(self):
        """測試 logging 設定狀態追蹤"""
        import src.utils.logging_setup as logging_setup

        # 初始狀態應該是未配置
        assert logging_setup._configured == False

        # 配置後狀態應該變為已配置
        from src.utils.logging_setup import configure_logging
        configure_logging()
        assert logging_setup._configured == True

        # 再次配置不應該改變狀態
        configure_logging()
        assert logging_setup._configured == True

    @pytest.mark.integration
    def test_real_logging_output_no_duplicates(self, caplog):
        """整合測試：驗證實際的日誌輸出沒有重複"""
        from src.utils.logging_setup import configure_logging

        # 清除之前的日誌記錄
        caplog.clear()

        with caplog.at_level(logging.INFO):
            # 配置 logger
            logger = configure_logging(logging.INFO)

            # 確保 logger 級別正確設定
            logger.setLevel(logging.INFO)

            # 發送測試訊息
            test_message = "這是一個測試訊息"
            logger.info(test_message)

            # 再次配置（應該不會產生額外的 handler）
            logger2 = configure_logging(logging.INFO)
            logger2.info(test_message)

            # 檢查日誌記錄
            # 主要目的是驗證沒有嚴重的重複問題
            info_messages = [record for record in caplog.records if record.levelname == 'INFO']

            # 驗證基本功能：如果有訊息被捕獲，應該包含我們的測試訊息
            if info_messages:
                assert any(test_message in record.message for record in info_messages)
                # 如果捕獲到訊息，檢查沒有過度重複（比如超過10次相同訊息）
                message_counts = {}
                for record in info_messages:
                    if test_message in record.message:
                        message_counts[record.message] = message_counts.get(record.message, 0) + 1

                # 確保沒有單一訊息重複超過合理次數
                for message, count in message_counts.items():
                    assert count <= 10, f"訊息 '{message}' 重複了 {count} 次，可能有重複 handler 問題"

            # 驗證 logger 配置狀態
            assert logger is logger2, "重複配置應該返回相同的 logger 實例"

class TestLoggingIntegration:
    """測試 logging 與其他模組的整合"""

    def setup_method(self):
        """每個測試方法執行前的設定"""
        # 重置 logging 配置狀態
        import src.utils.logging_setup as logging_setup
        logging_setup._configured = False

    def test_utils_init_logging_integration(self):
        """測試 utils/__init__.py 中的 logging 整合"""
        # 這會測試 utils/__init__.py 中的 vectorized_evaluate 函數是否能正確使用 logging
        import pandas as pd
        from src.utils import vectorized_evaluate

        # 創建測試資料
        test_data = pd.DataFrame({
            'permanent': ['user1', 'user2'],
            'last_interaction_days': [10, 20],
            'purchase_count': [1, 2]
        })

        # 簡單的測試規則
        test_rules = {
            'test_rule': {
                'rule': {'>=': [{'var': 'purchase_count'}, 1]},
                'data': {}
            }
        }

        # 這應該不會產生 logging 錯誤
        try:
            result = vectorized_evaluate(test_data, test_rules)
            assert isinstance(result, dict)
        except Exception as e:
            pytest.fail(f"vectorized_evaluate 執行失敗: {str(e)}")

    @patch.dict(os.environ, {'K_SERVICE': 'test-service'})
    def test_main_module_logging_integration(self):
        """測試 main.py 模組的 logging 整合"""
        # 模擬 main.py 中的 logging 使用
        from src.main import logger

        # 驗證 logger 已正確配置
        assert logger is not None
        assert logger.name == 'lta_user_stats'

        # 測試日誌輸出
        logger.info("測試 main.py 的 logger 整合")

if __name__ == "__main__":
    # 如果直接執行此文件，運行測試
    pytest.main([__file__, "-v"])