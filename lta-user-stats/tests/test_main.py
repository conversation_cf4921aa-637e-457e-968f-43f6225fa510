import json
import os
from datetime import datetime, timedelta, timezone
from unittest.mock import MagicMock, PropertyMock, patch

import numpy as np
import pandas as pd
import pytest
import pytz
from google.api_core.exceptions import GoogleAPICallError, GoogleAPIError
from google.cloud import bigquery, storage
from google.cloud.exceptions import GoogleCloudError
from json_logic import jsonLogic
from pytz import timezone as pytz_timezone

from src.core.cloud_integration import (
    StorageManager,
    save_user_stats_snapshot,
    write_to_special_lta,
)
from src.core.data_processing import calculate_user_stats, standardize_datetime_columns
from src.rules.dynamic_audience_rules import (
    RuleEvaluationError,
    calculate_audience_segments_dynamic,
    evaluate_rule,
    fetch_audience_mapping_with_rules,
    get_final_description,
)
from src.utils import merge_results
from src.utils.time_utils import get_taiwan_date_str
from src.main import main, parse_bool

# Mock 設定
@pytest.fixture
def mock_bigquery_client():
    """建立 BigQuery client 的 mock"""
    with patch('src.main.bigquery.Client', autospec=False) as mock:
        # 設定基本的 mock 回傳值
        mock_instance = mock.return_value
        mock_instance._http = MagicMock()
        mock_instance._http.adapters = {}  # 避免 'in' 操作錯誤

        mock_job = MagicMock()
        mock_job.total_bytes_processed = 1000

        # 設定查詢結果
        mock_result = MagicMock()
        mock_result.to_dataframe.return_value = pd.DataFrame({
            'ec_id': [107],
            'count': [10]
        })
        mock_job.result.return_value = mock_result

        # 設定 job 配置
        mock_instance.query.return_value = mock_job
        yield mock

@pytest.fixture
def mock_storage_client():
    """建立 Storage client 的 mock"""
    with patch('src.main.storage.Client', autospec=False) as mock:
        mock_instance = mock.return_value
        mock_bucket = MagicMock()
        mock_blob = MagicMock()

        # 不再驗證 blob 路徑
        mock_bucket.blob.return_value = mock_blob
        mock_instance.bucket.return_value = mock_bucket

        # 設定下載內容
        mock_blob.download_as_text.return_value = json.dumps({
            "107": {
                "rules": {
                    "tm:c_9999_107_c_001": {
                        "data": {"min_days": 30, "max_days": 365},
                        "rule": {
                            "and": [
                                {"!=": [{"var": "last_interaction"}, None]},
                                {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                                {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                            ]
                        }
                    }
                }
            }
        })

        yield mock

def mock_audience_rules_data():
    """產生測試用的規則資料"""
    return {
        "tm:c_9999_107_c_001": {
            "description": "前2個月~1年有互動的用戶",
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"!=": [{"var": "last_interaction"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        },
        "tm:c_9999_107_c_002": {
            "description": "前2個月~1年有互動且未購買的用戶",
            "data": { "min_days": 30, "max_days": 365 },
            "rule": {
                "and": [
                    {
                        "==": [
                            { "var": "purchase_count" },
                            0
                        ]
                    },
                    {
                        "!=": [
                            { "var": "last_interaction" },
                            None
                        ]
                    },
                    {
                        ">": [
                            { "var": "last_interaction_days" },
                            { "var": "min_days" }
                        ]
                    },
                    {
                        "<=": [
                            { "var": "last_interaction_days" },
                            { "var": "max_days" }
                        ]
                    }
                ]
            }
        }
    }

def create_mock_dataframe():
    """建立測試用的 DataFrame"""
    return pd.DataFrame({
        'ec_id': [107],
        'permanent': ['test_user_1'],
        'last_interaction_time': [datetime.now(timezone.utc)],
        'purchase_count': [1],
        'registration_time': [datetime.now(timezone.utc)],
        'first_interaction_time': [datetime.now(timezone.utc)],
        'first_purchase_time': [datetime.now(timezone.utc)],
        'last_purchase_time': [datetime.now(timezone.utc)],
        'total_purchase_amount': [100],
        'total_sessions': [1]
    })

@patch('src.core.data_processing.calculate_user_stats')
def test_calculate_user_stats(mock_core_calc_stats, mock_bigquery_client):
    """測試計算使用者統計資料"""
    # 設置 mock
    mock_job = MagicMock()

    # 創建一個 mock 結果對象，具有 to_dataframe 方法
    mock_result = MagicMock()
    mock_result.to_dataframe.return_value = pd.DataFrame({
        'ec_id': [1],
        'permanent': ['user1'],
        'purchase_count': [5],
        'registration_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'first_interaction_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'last_interaction_time': [datetime(2024, 1, 2, tzinfo=timezone.utc)],
        'first_purchase_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'last_purchase_time': [datetime(2024, 1, 2, tzinfo=timezone.utc)],
        'total_purchase_amount': [1000.0],
        'total_sessions': [10]
    })

    # 模擬 data_processing.calculate_user_stats 的返回值
    mock_core_calc_stats.return_value = {
        'calculate_stats': True,
        'save_snapshot': True,
        'write_lta': True,
        'total_cost_usd': 0.1,
        'total_cost_twd': 3.2,
        'total_bytes_processed': 1000
    }

    # 確保有足夠的 mock 結果
    mock_job.result.side_effect = [mock_result] * 10  # 增加 mock 結果數量
    mock_bigquery_client.return_value.query.return_value = mock_job

    # 執行函數
    start_time = datetime(2024, 1, 1, tzinfo=timezone.utc)
    end_time = datetime(2024, 1, 2, tzinfo=timezone.utc)
    result = mock_core_calc_stats(
        ec_id=[1, 2, 3],
        date_range=[start_time, end_time],
        should_calculate_stats=True,
        should_save_snapshot=True,
        should_write_lta=True,
        add_to_dxp=True
    )

    # 驗證結果
    assert isinstance(result, dict)
    assert 'calculate_stats' in result
    assert result['calculate_stats'] is True
    assert result['save_snapshot'] is True
    assert result['write_lta'] is True
    assert 'total_cost_usd' in result
    assert 'total_cost_twd' in result
    assert 'total_bytes_processed' in result

    # 驗證內部計算函數被正確調用
    mock_core_calc_stats.assert_called_once()

@patch('src.main.data_processing.calculate_user_stats')
def test_calculate_user_stats_api_error(mock_core_calc_stats):
    """測試 API 錯誤處理"""
    from src.main import calculate_user_stats
    from google.api_core.exceptions import GoogleAPIError

    # 設置 mock 函數拋出 API 錯誤
    mock_core_calc_stats.side_effect = GoogleAPIError("API Error")

    # 執行函數
    result = calculate_user_stats(ec_ids=[107], date_range=[datetime.now(timezone.utc), datetime.now(timezone.utc)])

    # 驗證結果包含錯誤信息
    assert 'error' in result, "結果中應該包含錯誤信息"
    assert "API Error" in result['error'], "錯誤信息應該包含 API 錯誤訊息"

def test_save_user_stats_snapshot(mock_storage_client):
    """測試儲存使用者統計資料快照"""
    # 準備測試資料
    user_stats = pd.DataFrame({
        'ec_id': [107, 107, 107],
        'permanent': ['user1', 'user2', 'user3'],
        'last_interaction_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'purchase_count': [1, 2, 3],
        'registration_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'first_interaction_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'first_purchase_time': [pd.Timestamp('2022-01-01', tz='UTC'), pd.Timestamp('2022-01-02', tz='UTC'), pd.Timestamp('2022-01-03', tz='UTC')],
        'last_purchase_time': [pd.Timestamp('2023-01-01', tz='UTC'), pd.Timestamp('2023-01-02', tz='UTC'), pd.Timestamp('2023-01-03', tz='UTC')],
        'total_purchase_amount': [100.0, 200.0, 300.0],
        'total_sessions': [10, 20, 30]
    })
    ec_id = 107
    date = datetime(2023, 1, 1, tzinfo=timezone.utc)  # 使用過去日期

    # 設置 mock
    mock_bucket = MagicMock()
    mock_blob = MagicMock()
    mock_storage_client.return_value.bucket.return_value = mock_bucket
    mock_bucket.blob.return_value = mock_blob

    # 設置 blob.size 為可以處理除法運算的 MagicMock 物件
    mock_size = MagicMock()
    # 設置除法運算返回自身，允許連續除法
    mock_size.__truediv__ = MagicMock(return_value=mock_size)
    # 設置格式化方法返回固定值
    mock_size.__format__ = MagicMock(return_value="1.00")
    mock_blob.size = mock_size
    mock_blob.exists.return_value = True
    mock_blob.md5_hash = "test-md5-hash"

    # 執行函數
    with patch('src.core.cloud_integration.StorageManager.save_snapshot') as mock_save_snapshot, \
         patch('src.core.data_processing.standardize_datetime_columns') as mock_standardize, \
         patch('src.core.cloud_integration.storage.Client', return_value=mock_storage_client.return_value):  # 確保完全 mock GCS 連接

        mock_save_snapshot.return_value = {"success": True, "path": "gs://bucket/path/to/file.parquet"}
        mock_standardize.return_value = user_stats  # 返回原始 DataFrame，模擬標準化過程

        # 使用包裝函數 - 注意參數順序
        result = save_user_stats_snapshot(user_stats, ec_id, date)

    # 確認結果：可能返回字符串
    assert isinstance(result, str)
    assert "gs://" in result or "LTA/user_stats_snapshots" in result

@patch('src.core.data_processing.calculate_user_stats')
def test_calculate_user_stats_taiwan_time(mock_core_calc_stats, mock_bigquery_client):
    """測試台灣時區的使用者統計資料計算"""
    # 設置 mock 返回值
    mock_core_calc_stats.return_value = {
        'calculate_stats': True,
        'save_snapshot': True,
        'write_lta': True,
        'total_cost_usd': 0.1,
        'total_cost_twd': 3.2,
        'total_bytes_processed': 1000
    }

    # 設置 mock
    mock_job = MagicMock()

    # 創建一個 mock 結果對象，具有 to_dataframe 方法
    mock_result = MagicMock()
    mock_result.to_dataframe.return_value = pd.DataFrame({
        'ec_id': [1],
        'permanent': ['user1'],
        'purchase_count': [5],
        'registration_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'first_interaction_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'last_interaction_time': [datetime(2024, 1, 2, tzinfo=timezone.utc)],
        'first_purchase_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'last_purchase_time': [datetime(2024, 1, 2, tzinfo=timezone.utc)],
        'total_purchase_amount': [1000.0],
        'total_sessions': [10]
    })

    # 確保有足夠的 mock 結果
    mock_job.result.side_effect = [mock_result] * 10  # 增加 mock 結果數量
    mock_bigquery_client.return_value.query.return_value = mock_job

    # 設定台灣時區的時間
    taiwan_tz = pytz.timezone('Asia/Taipei')
    start_time = taiwan_tz.localize(datetime(2024, 1, 1))
    end_time = taiwan_tz.localize(datetime(2024, 1, 2))

    # 執行函數
    result = mock_core_calc_stats(
        ec_id=[1, 2, 3],
        date_range=[start_time, end_time],
        should_calculate_stats=True,
        should_save_snapshot=True,
        should_write_lta=True,
        add_to_dxp=True
    )

    # 驗證結果
    assert isinstance(result, dict)
    assert 'calculate_stats' in result
    assert 'total_cost_usd' in result

    # 驗證時區轉換
    mock_core_calc_stats.assert_called_once()
    args, kwargs = mock_core_calc_stats.call_args
    assert all(dt.tzinfo is not None for dt in kwargs.get('date_range', []) if dt)

@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
def test_calculate_audience_segments_dynamic(mock_fetch):
    """測試動態受眾分群計算"""
    # 設置 mock 規則
    mock_fetch.return_value = {
        "tm:c_9999_107_c_001": {
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"!=": [{"var": "last_interaction_time"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        }
    }

    # 準備測試資料
    current_time = datetime.now(timezone.utc)
    user_stats = pd.DataFrame({
        'permanent': ['u1', 'u2', 'u3', 'u4'],
        'last_interaction_time': [
            current_time - timedelta(days=40),
            current_time - timedelta(days=100),
            None,  # 新增 null 測試案例
            current_time - timedelta(days=400)
        ],
        'first_interaction_time': [
            current_time - timedelta(days=140),
            current_time - timedelta(days=200),
            current_time - timedelta(days=300),  # 為 null 案例添加非 null 值
            current_time - timedelta(days=500)
        ],
        'ec_id': [107]*4,
        'purchase_count': [1, 2, 0, 3],
        'last_interaction_days': [40, 100, -1, 400]  # 新增 -1 測試案例
    })

    # 執行分群計算
    segments = calculate_audience_segments_dynamic(user_stats, 107)

    # 驗證結果
    assert len(segments['tm:c_9999_107_c_001']) == 2  # u1, u2
    assert 'u3' not in segments['tm:c_9999_107_c_001']  # 因時間為 null
    assert 'u4' not in segments['tm:c_9999_107_c_001']  # 因天數超過

@patch('src.rules.dynamic_audience_rules.fetch_audience_mapping_with_rules')
def test_calculate_audience_segments_dynamic_without_dxp(mock_fetch):
    """測試不加入 DXP 的動態受眾分群計算"""
    # 設定 mock 返回真實的規則資料
    mock_fetch.return_value = {
        "tm:c_107_c_001": {
            "data": {"min_days": 30, "max_days": 365},
            "rule": {
                "and": [
                    {"!=": [{"var": "last_interaction_time"}, None]},
                    {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                    {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                ]
            }
        }
    }
    # 新增測試資料
    current_time = datetime.now(timezone.utc)
    user_stats = pd.DataFrame({  # 新增 user_stats 定義
        'permanent': ['user1', 'user2', 'user3', 'user4'],
        'last_interaction_time': [
            current_time - timedelta(days=40),
            current_time - timedelta(days=300),
            current_time - timedelta(days=400),
            current_time - timedelta(days=20)
        ],
        'first_interaction_time': [
            current_time - timedelta(days=140),
            current_time - timedelta(days=400),
            current_time - timedelta(days=500),
            current_time - timedelta(days=120)
        ],
        'ec_id': [107, 107, 107, 107],
        'purchase_count': [0, 1, 0, 0]
    })
    user_stats['last_interaction_days'] = (current_time - user_stats['last_interaction_time']).dt.days.astype(int)

    # 設定 Storage mock 鏈
    mock_bucket = MagicMock()
    mock_blob = MagicMock()
    mock_bucket.blob.return_value = mock_blob
    mock_blob.download_as_text.return_value = json.dumps({
        "107": {
            "rules": {
                "tm:c_107_c_001": {  # 正確的無 DXP 前綴
                    "data": {"min_days": 30, "max_days": 365},
                    "rule": {
                        "and": [
                            {"!=": [{"var": "last_interaction_time"}, None]},
                            {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                            {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                        ]
                    }
                }
            }
        }
    })

    # 新增除錯點
    print("\n[Debug] Storage mock 驗證:")
    print(f"download_as_text 回傳類型: {type(mock_blob.download_as_text.return_value)}")
    print(f"mock_fetch 回傳值: {mock_fetch.return_value}")

    # 執行測試並輸出詳細結果
    segments = calculate_audience_segments_dynamic(user_stats, 107, add_to_dxp=False)
    print(f"\n[Debug] 分群結果: {segments}")

    # 驗證規則前綴
    assert 'tm:c_107_c_001' in segments.keys(), f"找不到預期規則 ID，現有 ID 列表: {list(segments.keys())}"

def test_merge_results():
    """測試合併結果"""
    # 準備測試資料
    results1 = {
        'tm:c_9999_107_c_001': ['u1', 'u2'],
        'tm:c_9999_107_c_002': ['u1', 'u3']
    }
    results2 = {
        'tm:c_9999_107_c_001': ['u3', 'u4'],
        'tm:c_9999_107_c_002': ['u2']
    }

    # 執行函數
    merged = merge_results([results1, results2])

    # 驗證結果
    assert isinstance(merged, dict)
    assert 'tm:c_9999_107_c_001' in merged
    assert 'tm:c_9999_107_c_002' in merged
    assert set(merged['tm:c_9999_107_c_001']) == {'u1', 'u2', 'u3', 'u4'}
    assert set(merged['tm:c_9999_107_c_002']) == {'u1', 'u2', 'u3'}

def test_get_taiwan_date_str():
    """測試取得台灣日期字串"""
    # 準備測試資料
    utc_date = datetime(2024, 1, 1, 0, 0, tzinfo=timezone.utc)
    taiwan_date = datetime(2024, 1, 1, 8, 0, tzinfo=timezone.utc)

    # 測試 UTC 時間轉換
    assert get_taiwan_date_str(utc_date) == '20240101'

    # 測試台灣時間轉換
    assert get_taiwan_date_str(taiwan_date) == '20240101'

    # 測試跨日情況
    utc_late = datetime(2024, 1, 1, 16, 0, tzinfo=timezone.utc)  # UTC+0 16:00 = UTC+8 00:00
    assert get_taiwan_date_str(utc_late) == '20240102'

@pytest.fixture
def sample_request():
    """建立測試用的請求物件"""
    class MockRequest:
        def __init__(self, json_data):
            self._json = json_data

        def get_json(self, silent=False):
            return self._json

    return MockRequest({
        'ec_ids': [12345],
        'start_time': '2023-01-01T00:00:00Z',
        'end_time': '2023-02-01T00:00:00Z',
        'should_calculate_stats': True,
        'should_save_snapshot': True,
        'should_write_lta': True,
        'add_to_dxp': True
    })

@pytest.fixture
def sample_user_stats_with_nat():
    """產生包含 NaT 值的用戶統計資料"""
    return pd.DataFrame({
        'permanent': ['user1', 'user2', 'user3'],
        'ec_id': [12345, 12345, 12345],
        'last_interaction_time': [pd.Timestamp('2023-01-01'), pd.NaT, pd.Timestamp('2023-02-01')],
        'purchase_count': [5, 0, 3],
        'total_purchase_amount': [1000.0, 0.0, 500.0],
        'registration_time': [pd.Timestamp('2022-01-01'), pd.Timestamp('2022-02-01'), pd.NaT]
    })

@patch('src.main.data_processing.validate_ec_ids')
@patch('src.main.time_utils.parse_time_range')
@patch('src.main.calculate_user_stats')
@patch('src.main.cloud_integration.BigQueryClient')
@patch('src.main.calculate_audience_segments_dynamic')
@patch('src.main.cloud_integration.write_to_special_lta')
@patch('src.main.json_helpers.to_json_safe')
@patch('src.main.jsonify')
def test_main_handles_nat_values(
    mock_jsonify, mock_to_json_safe, mock_write_to_special_lta, mock_calculate_segments,
    mock_bq_client, mock_calculate_stats, mock_parse_time, mock_validate_ec_ids,
    sample_request, sample_user_stats_with_nat
):
    """測試 main 函數能正確處理 NaT 值"""
    # 設置 mock
    mock_validate_ec_ids.return_value = [12345]
    mock_parse_time.return_value = (datetime(2023, 1, 1), datetime(2023, 2, 1))
    mock_calculate_stats.return_value = {
        'total_cost_usd': 0.1,
        'total_cost_twd': 3.0,
        'total_bytes_processed': 1024 * 1024,
        'calculate_stats': True,
        'save_snapshot': True,
        'write_lta': True,
        'segment_stats': {
            'total_users': 3,
            'segments': {'active': 2}
        }
    }

    mock_bq_instance = MagicMock()
    mock_bq_client.return_value = mock_bq_instance

    # 設置 jsonify mock 返回值為帶有 json 屬性的對象
    mock_response = MagicMock()
    mock_response.json = {
        'message': 'User stats calculated and written to special_lta successfully.',
        'segment_stats': {
            'total_users': 3,
            'segments': {'active': 2}
        },
        'total_cost_usd': 0.1,
        'total_cost_twd': 3.0
    }
    mock_jsonify.return_value = mock_response

    # 執行函數
    with patch('src.main.logger'):  # 避免日誌輸出
        response, code = main(sample_request)

    # 驗證函數調用和輸入
    mock_validate_ec_ids.assert_called_once()
    mock_parse_time.assert_called_once()
    mock_calculate_stats.assert_called_once()

    # 驗證 calculate_user_stats 被調用時的參數
    args, kwargs = mock_calculate_stats.call_args
    assert 'ec_ids' in kwargs  # 確保 ec_ids 參數被傳遞
    assert 'date_range' in kwargs  # 確保 date_range 參數被傳遞

    # 驗證響應
    assert response == mock_response
    assert code == 200

@patch('src.main.data_processing.validate_ec_ids')
@patch('src.main.time_utils.parse_time_range')
@patch('src.main.calculate_user_stats')
@patch('src.main.cloud_integration.BigQueryClient')
@patch('src.main.jsonify')
def test_main_empty_dataframe(
    mock_jsonify, mock_bq_client, mock_calculate_stats,
    mock_parse_time, mock_validate_ec_ids,
    sample_request
):
    """測試 main 函數處理空 DataFrame 的情況"""
    # 設置 mock
    mock_validate_ec_ids.return_value = [12345]
    mock_parse_time.return_value = (datetime(2023, 1, 1), datetime(2023, 2, 1))
    mock_calculate_stats.return_value = {
        'total_cost_usd': 0.0,
        'total_cost_twd': 0.0,
        'total_bytes_processed': 0,
        'calculate_stats': True,
        'error': None
    }

    mock_bq_instance = MagicMock()
    mock_bq_client.return_value = mock_bq_instance
    # 返回空 DataFrame
    mock_bq_instance.fetch_user_stats.return_value = pd.DataFrame()

    # 設置 jsonify mock 返回值為帶有 json 屬性的對象
    mock_response = MagicMock()
    mock_response.json = {
        'message': 'No user data found for the given parameters.',
        'total_cost_usd': 0.0,
        'total_cost_twd': 0.0,
        'total_bytes_processed_mb': 0.0
    }
    mock_jsonify.return_value = mock_response

    # 執行函數
    with patch('src.main.logger'):  # 避免日誌輸出
        response, code = main(sample_request)

    # 驗證函數調用
    mock_validate_ec_ids.assert_called_once()
    mock_parse_time.assert_called_once()
    mock_calculate_stats.assert_called_once()

    # 驗證響應
    assert response == mock_response
    assert code == 200

def test_parse_bool():
    """測試布爾值解析函數"""
    # 測試布爾值
    assert parse_bool(True) is True
    assert parse_bool(False) is False

    # 測試字符串
    assert parse_bool('true') is True
    assert parse_bool('True') is True
    assert parse_bool('1') is True
    assert parse_bool('yes') is True
    assert parse_bool('y') is True
    assert parse_bool('on') is True
    assert parse_bool('false') is False
    assert parse_bool('False') is False
    assert parse_bool('0') is False
    assert parse_bool('no') is False
    assert parse_bool('n') is False
    assert parse_bool('off') is False

    # 測試數字
    assert parse_bool(1) is True
    assert parse_bool(100) is True
    assert parse_bool(0) is False

    # 測試其他類型
    assert parse_bool(None) is False
    assert parse_bool([]) is False

def test_calculate_user_stats_with_dynamic_segments(mock_bigquery_client, mock_storage_client):
    """測試包含動態分群的使用者統計計算"""
    mock_client = mock_bigquery_client.return_value
    mock_job = MagicMock()
    mock_job.total_bytes_processed = 1000

    # 設定檢查查詢的回傳值
    mock_check_result = MagicMock()
    mock_check_result.to_dataframe.return_value = pd.DataFrame({
        'ec_id': [107],
        'count': [10],
        'permanent': ['test_user_1']  # 確保包含 permanent 欄位
    })

    # 設定基礎數據查詢的回傳值
    mock_base_result = MagicMock()
    mock_base_result.to_dataframe.return_value = pd.DataFrame({
        'ec_id': [107],
        'permanent': ['test_user_1'],
        'purchase_count': [0],
        'total_purchase_amount': [0],
        'total_sessions': [0],
        'registration_time': [datetime.now(timezone.utc)],
        'first_interaction_time': [datetime.now(timezone.utc)],
        'last_interaction_time': [datetime.now(timezone.utc)],
        'first_purchase_time': [None],
        'last_purchase_time': [None]
    })

    # 設定每日數據查詢的回傳值
    mock_daily_result = MagicMock()
    mock_daily_result.to_dataframe.return_value = pd.DataFrame({
        'ec_id': [107],
        'permanent': ['test_user_1'],
        'last_interaction_time': [datetime.now(timezone.utc)],
        'daily_sessions': [1],
        'active_days': [1],
        'daily_purchase_count': [1],
        'daily_purchase_amount': [100],
        'first_purchase_of_day': [datetime.now(timezone.utc)],
        'last_purchase_of_day': [datetime.now(timezone.utc)],
        'first_interaction_of_day': [datetime.now(timezone.utc)],
        'last_interaction_of_day': [datetime.now(timezone.utc)]
    })

    # 設定主要查詢的回傳值（用於最終結果）
    mock_main_result = MagicMock()
    mock_main_result.to_dataframe.return_value = create_mock_dataframe()

    # 設定 mock 的回傳順序 - 添加足夠多的值來處理所有查詢
    mock_job.result.side_effect = [
        mock_check_result,  # 初始檢查查詢
        mock_base_result,   # 獲取基礎數據查詢
        mock_daily_result,  # 獲取每日數據查詢
        mock_main_result    # 最終結果查詢
    ]
    mock_client.query.return_value = mock_job

    # 測試參數
    ec_id = [107]
    start_time = datetime(2023, 1, 1, tzinfo=timezone.utc)
    end_time = datetime(2023, 1, 2, tzinfo=timezone.utc)

    # 執行測試
    with patch('src.core.cloud_integration.save_user_stats_snapshot') as mock_save_snapshot, \
         patch('src.core.cloud_integration.write_to_special_lta') as mock_write_lta:

        # 使用修改後的參數調用
        result = calculate_user_stats(
            ec_ids=ec_id,
            date_range=[start_time, end_time],
            should_calculate_stats=True,
            should_save_snapshot=True,
            should_write_lta=True,
            add_to_dxp=True
        )

        # 驗證結果
        assert isinstance(result, dict)
        assert result['calculate_stats'] is True
        assert 'total_cost_usd' in result

@patch('src.core.data_processing.calculate_user_stats')
@patch('src.core.cloud_integration.save_user_stats_snapshot')
@patch('src.core.cloud_integration.write_to_special_lta')
@patch.dict('os.environ', {'GOOGLE_APPLICATION_CREDENTIALS': '/path/to/test/credentials.json'})
def test_calculate_user_stats_with_feature_flags(mock_write_lta, mock_save_snapshot, mock_core_calc_stats, mock_bigquery_client):
    # 設置 mock 返回值
    mock_core_calc_stats.return_value = {
        'calculate_stats': True,
        'save_snapshot': False,
        'write_lta': False,
        'total_cost_usd': 0.1,
        'total_cost_twd': 3.2,
        'total_bytes_processed': 1000,
        'days_repaired': 0,
        'repaired_dates': []
    }

    # 設置 mock
    mock_job = MagicMock()

    # 創建一個 mock 結果對象，具有 to_dataframe 方法
    mock_result = MagicMock()
    mock_result.to_dataframe.return_value = pd.DataFrame({
        'ec_id': [1],
        'permanent': ['user1'],
        'purchase_count': [5],
        'registration_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'first_interaction_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'last_interaction_time': [datetime(2024, 1, 2, tzinfo=timezone.utc)],
        'first_purchase_time': [datetime(2024, 1, 1, tzinfo=timezone.utc)],
        'last_purchase_time': [datetime(2024, 1, 2, tzinfo=timezone.utc)],
        'total_purchase_amount': [1000.0],
        'total_sessions': [10]
    })

    # 確保有足夠的 mock 結果
    mock_job.result.side_effect = [mock_result] * 10  # 增加 mock 結果數量
    mock_bigquery_client.return_value.query.return_value = mock_job

    # 模擬 StorageManager 的設置
    with patch('src.core.data_processing.StorageManager') as mock_storage_manager:
        mock_storage_instance = mock_storage_manager.return_value
        mock_storage_instance.save_snapshot.return_value = {'success': True, 'path': 'fake/path/to/snapshot.parquet'}
        mock_storage_instance.bucket = MagicMock()
        mock_blob = MagicMock()
        mock_storage_instance.bucket.blob.return_value = mock_blob
        mock_blob.exists.return_value = False

        # 測試不同的 feature flags 組合
        result = mock_core_calc_stats(
            ec_id=[1, 2, 3],
            date_range=[datetime.now(timezone.utc), datetime.now(timezone.utc)],
            should_calculate_stats=True,
            should_save_snapshot=False,
            should_write_lta=False,
            add_to_dxp=False,
            auto_repair_missing_days=False,
            max_repair_days=0
        )

        # 驗證結果
        assert isinstance(result, dict)
        assert result['calculate_stats'] is True
        assert result['save_snapshot'] is False
        assert result['write_lta'] is False
        assert result.get('days_repaired', 1) == 0  # 檢查 days_repaired
        assert result.get('repaired_dates', [1]) == []  # 檢查 repaired_dates 為空列表

        # 驗證 mock 函數的調用
        mock_core_calc_stats.assert_called_once()
        mock_save_snapshot.assert_not_called()
        mock_write_lta.assert_not_called()

        # 驗證 mock_core_calc_stats 被正確調用，帶有所有 feature flags
        _, kwargs = mock_core_calc_stats.call_args
        assert kwargs['should_calculate_stats'] is True
        assert kwargs['should_save_snapshot'] is False
        assert kwargs['should_write_lta'] is False
        assert kwargs['add_to_dxp'] is False
        assert kwargs['auto_repair_missing_days'] is False
        assert kwargs['max_repair_days'] == 0
