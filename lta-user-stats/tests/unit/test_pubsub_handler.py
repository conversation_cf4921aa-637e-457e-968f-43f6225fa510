import pytest
import json
import base64
from datetime import datetime, timezone
from unittest.mock import patch, MagicMock, PropertyMock

import pandas as pd
from src.main import update_user_stats_pubsub


@pytest.fixture
def mock_cloud_event():
    """產生模擬的 Cloud Event 物件"""
    class MockCloudEvent:
        def __init__(self, message_data):
            self.data = {
                "message": {
                    "data": base64.b64encode(message_data.encode("utf-8")).decode("utf-8")
                }
            }

    return MockCloudEvent(json.dumps({
        "ec_id": [107],
        "start_time": "2023-10-01T00:00:00Z",
        "end_time": "2023-10-02T00:00:00Z",
        "calculate_stats": True,
        "save_snapshot": True,
        "write_lta": True
    }))


@patch('src.main.calculate_user_stats')
def test_update_user_stats_pubsub_parameter_naming(mock_calculate_stats, mock_cloud_event):
    """測試 Pub/Sub 處理函數的參數命名一致性"""
    # 設置模擬返回值
    mock_calculate_stats.return_value = {"status": "success"}

    # 創建 jsonify 的模擬
    with patch('src.main.jsonify') as mock_jsonify:
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response

        # 執行測試
        update_user_stats_pubsub(mock_cloud_event)

        # 檢查 calculate_user_stats 被調用時的參數
        # 這將驗證是否使用了正確的參數名稱（ec_ids而非ec_id）
        args, kwargs = mock_calculate_stats.call_args
        assert 'ec_ids' in kwargs, "應該使用'ec_ids'參數而非'ec_id'"

        # 驗證參數值正確
        assert kwargs['ec_ids'] == [107]
        assert isinstance(kwargs['date_range'], list)
        assert len(kwargs['date_range']) == 2
        assert all(isinstance(dt, datetime) for dt in kwargs['date_range'])


@patch('src.main.calculate_user_stats')
def test_update_user_stats_pubsub_error_handling(mock_calculate_stats, mock_cloud_event):
    """測試 Pub/Sub 處理函數的錯誤處理"""
    # 設置模擬拋出異常
    mock_calculate_stats.side_effect = ValueError("測試錯誤")

    # 創建 jsonify 的模擬
    with patch('src.main.jsonify') as mock_jsonify:
        mock_response = MagicMock()
        mock_jsonify.return_value = mock_response

        # 執行測試
        result, status_code = update_user_stats_pubsub(mock_cloud_event)

        # 驗證錯誤處理
        assert status_code == 500
        mock_jsonify.assert_called_once()
        jsonify_args, _ = mock_jsonify.call_args
        assert 'error' in jsonify_args[0]
        assert '測試錯誤' in jsonify_args[0]['error']