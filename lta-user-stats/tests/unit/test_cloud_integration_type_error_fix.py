import unittest
import pandas as pd
import numpy as np
from datetime import datetime, timezone, timedelta
import pytz
from unittest.mock import patch, MagicMock, ANY
from src.core.cloud_integration import BigQueryClient
from src.utils.data_cleaner import load_bigquery_schema  # 導入 load_bigquery_schema
import pytest

# 簡化 schema 定義
MINIMAL_SCHEMA = [
    {'name': 'permanent', 'type': 'STRING'},
    {'name': 'ec_id', 'type': 'INTEGER'},
    {'name': 'purchase_count', 'type': 'INTEGER'},
]

class TestBigQueryClient(unittest.TestCase):

    @patch('src.core.cloud_integration.bigquery.Client')
    @patch('src.core.cloud_integration.storage.Client')
    @patch('src.core.cloud_integration.load_bigquery_schema')
    @patch('src.core.cloud_integration.clean_parquet_dataframe_dynamic')
    def test_time_field_conversion(self, mock_clean_dataframe, mock_load_schema, mock_storage_client, mock_bq_client_class):
        """
        測試 update_user_stats_table 的流程整合性。
        驗證它能正確調用 data_cleaner，並處理後續的 JSON 轉換流程，而不會因數據問題引發 TypeError。
        """
        # 1. 準備 Mock
        # Mock BigQuery Client 的行為
        mock_bq_instance = MagicMock()
        mock_query_job = MagicMock()
        mock_query_job.result.return_value = [MagicMock(count=5)]
        mock_bq_instance.query.return_value = mock_query_job
        mock_bq_client_class.return_value = mock_bq_instance

        # 關鍵：Mock data_cleaner 的返回值。我們假設它已經正確清理了數據。
        # 我們提供一個已知乾淨的 DataFrame 作為其輸出。
        now = datetime.now(timezone.utc)
        clean_data = {
            'ec_id': [1, 1, 1, 1, 1],
            'permanent': ['user1', 'user2', 'user3', 'user4', 'user5'],
            'first_purchase_date': [now, now, None, now, now] # 模擬一個時間欄位
        }
        mock_clean_dataframe.return_value = pd.DataFrame(clean_data)
        mock_load_schema.return_value = MINIMAL_SCHEMA

        # 2. 準備測試輸入數據 (這個數據將被 mock_clean_dataframe 忽略，但仍需作為輸入)
        ec_id = 1
        input_df = pd.DataFrame({'ec_id': [ec_id] * 5}) # 內容不重要

        # 3. 執行被測函數
        client = BigQueryClient()
        try:
            # 由於我們的 data_cleaner mock 返回的 df 不包含所有 schema 欄位，
            # 後續的 to_json 可能會因為缺少欄位而報錯。
            # 我們的核心是測試在 JSON 序列化之前沒有 TypeError，所以我們捕獲後續的錯誤。
            client.update_user_stats_table(input_df, ec_id, now)
        except KeyError as e:
            # 我們預期可能會因為 Mock 的 DataFrame 欄位不全而觸發 KeyError。
            # 但如果我們能走到這一步，就說明我們已經成功繞過了之前的 TypeError。
            print(f"Successfully bypassed TypeError, caught expected downstream error: {e}")
            pass
        except TypeError as e:
            self.fail(f"測試失敗，不應再出現 TypeError: {e}")

        # 4. 驗證依賴是否被正確調用
        mock_clean_dataframe.assert_called_once_with(ANY, ec_id)


if __name__ == '__main__':
    unittest.main()