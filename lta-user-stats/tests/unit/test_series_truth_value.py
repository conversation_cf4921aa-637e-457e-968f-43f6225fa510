import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock
from src.utils import vectorized_evaluate


def test_series_truth_value_in_vectorized_evaluate():
    """
    測試 vectorized_evaluate 函數中如何正確處理 Series 的真值問題

    這個測試確保當我們在 vectorized_evaluate 函數中處理 pandas Series 時，
    不會出現 'the truth value of a Series is ambiguous' 錯誤
    """
    # 創建測試數據
    test_data = pd.DataFrame({
        'last_interaction_days': [10, 20, 30, 40, 50, -1, np.nan],
        'purchase_count': [0, 1, 2, 3, 4, 5, np.nan],
        'permanent': [True, True, False, True, False, True, True]
    })

    # 創建測試規則
    rules = {
        'test_rule_1': {
            'id': 'test_rule_1',
            'name': 'Test Rule Min Days',
            'data': {'min_days': 25},
            'rule': {'and': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]}
        },
        'test_rule_2': {
            'id': 'test_rule_2',
            'name': 'Test Rule Min Purchases',
            'data': {'min_purchases': 3},
            'rule': {'and': [{'var': 'purchase_count'}, {'var': 'min_purchases'}]}
        },
        'test_rule_3': {
            'id': 'test_rule_3',
            'name': 'Test Rule Special Values',
            'data': {},
            'rule': {'and': [{'!=': [{'var': 'last_interaction_days'}, -1]}]}
        }
    }

    # 執行 vectorized_evaluate 函數
    result = vectorized_evaluate(test_data, rules)

    # 驗證結果是字典類型，而不是拋出錯誤
    assert isinstance(result, dict)

    # 驗證每個規則下的結果是列表
    for rule_id, users in result.items():
        assert isinstance(users, list)


def test_unhashable_series_fix():
    """
    測試索引列表替代布爾掩碼的方法來解決 'unhashable type: Series' 錯誤

    這個測試直接模擬了在處理 Series 時可能導致 unhashable 錯誤的場景，
    以及使用 .tolist() 將索引轉換為列表來解決這個問題的方法
    """
    # 創建測試數據
    df = pd.DataFrame({
        'value': [10, 20, 30, 40, 50],
        'timestamp': pd.Series(['2023-01-01', '2023-02-01', None, '2023-03-01', pd.NaT])
    })

    # 創建 Series 布爾掩碼
    valid_mask = df['timestamp'].notna()

    # 測試問題：直接嘗試使用布爾掩碼作為鍵會失敗
    # 以下代碼在實際執行時會引發 TypeError: unhashable type: 'Series'
    # bad_dict = {valid_mask: "這會失敗"}

    # 解決方案：將掩碼轉換為索引列表
    valid_indices = df.index[valid_mask].tolist()

    # 使用索引列表不會引發錯誤
    working_dict = {tuple(valid_indices): "這可以工作"}

    # 驗證索引列表正確識別了非空值
    assert len(valid_indices) == 3
    assert valid_indices == [0, 1, 3]

    # 使用索引列表訪問原始數據
    selected_values = df.loc[valid_indices, 'value']
    assert selected_values.tolist() == [10, 20, 40]

    # 確認索引列表與布爾掩碼選擇的結果相同
    assert df.loc[valid_indices, 'value'].equals(df.loc[valid_mask, 'value'])


def test_series_boolean_operations():
    """
    測試 pandas Series 在布爾運算中的正確處理方式

    確保我們在程式碼中正確使用 Series 的布爾運算，而不是在條件表達式中直接使用 Series
    """
    # 創建測試數據
    test_data = pd.DataFrame({
        'value': [10, 20, 30, 40, 50]
    })

    # 測試正確的布爾運算方式
    condition = test_data['value'] > 30

    # 使用 .any() 和 .all() 方法來進行評估
    assert condition.any() == True
    assert condition.all() == False

    # 計算符合條件的數量
    assert condition.sum() == 2

    # 測試布爾掩碼的正確使用
    filtered_data = test_data[condition]
    assert len(filtered_data) == 2
    assert filtered_data['value'].tolist() == [40, 50]

    # 測試多條件組合（使用 & 和 | 而不是 and 和 or）
    condition1 = test_data['value'] > 20
    condition2 = test_data['value'] < 50
    combined_condition = condition1 & condition2

    assert combined_condition.sum() == 2
    assert test_data[combined_condition]['value'].tolist() == [30, 40]

    # 測試正確的條件評估方式
    # 以下是錯誤的方式，可能會導致 ValueError: The truth value of a Series is ambiguous
    # if condition:  # 這會失敗
    #    pass

    # 正確的方式是使用 .any() 或 .all()
    if condition.any():
        assert True  # 這是正確的方式

    # 測試結合 & 和 | 的複雜條件
    complex_condition = ((test_data['value'] > 20) & (test_data['value'] < 50)) | (test_data['value'] == 10)
    assert complex_condition.sum() == 3
    assert test_data[complex_condition]['value'].tolist() == [10, 30, 40]