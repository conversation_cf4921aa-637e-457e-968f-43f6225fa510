"""
測試動態數據清理功能
"""
import pytest
import pandas as pd
import numpy as np
import logging
import json
import tempfile
import os
from unittest.mock import Mock, patch, MagicMock

from src.utils.data_cleaner import (
    load_bigquery_schema,
    clean_parquet_dataframe_dynamic,
    clean_numeric_column_dynamic,
    clean_timestamp_column_dynamic,
    clean_string_column_dynamic,
    validate_dataframe_for_bigquery_dynamic,
    create_bigquery_schema_fields
)

logger = logging.getLogger(__name__)


class TestDynamicDataCleaner:
    """測試動態數據清理功能"""

    def create_test_schema_file(self, temp_dir):
        """創建測試用的 schema 檔案"""
        test_schema = [
            {"name": "ec_id", "type": "INTEGER", "mode": "REQUIRED"},
            {"name": "permanent", "type": "STRING", "mode": "REQUIRED"},
            {"name": "purchase_count", "type": "INTEGER", "mode": "REQUIRED"},
            {"name": "total_sessions", "type": "INTEGER", "mode": "REQUIRED"},
            {"name": "total_purchase_amount", "type": "FLOAT", "mode": "REQUIRED"},
            {"name": "registration_time", "type": "TIMESTAMP", "mode": "NULLABLE"},
            {"name": "first_interaction_time", "type": "TIMESTAMP", "mode": "REQUIRED"},
            {"name": "last_interaction_time", "type": "TIMESTAMP", "mode": "REQUIRED"},
            {"name": "first_purchase_time", "type": "TIMESTAMP", "mode": "NULLABLE"}
        ]

        schema_path = os.path.join(temp_dir, 'test_schema.json')
        with open(schema_path, 'w', encoding='utf-8') as f:
            json.dump(test_schema, f)

        return schema_path

    def test_load_bigquery_schema(self):
        """測試載入 BigQuery schema"""
        with tempfile.TemporaryDirectory() as temp_dir:
            schema_path = self.create_test_schema_file(temp_dir)

            schema_dict = load_bigquery_schema(schema_path)

            # 驗證基本結構
            assert isinstance(schema_dict, dict)
            assert len(schema_dict) == 9

            # 驗證具體欄位
            assert schema_dict['ec_id']['type'] == 'INTEGER'
            assert schema_dict['ec_id']['mode'] == 'REQUIRED'
            assert schema_dict['registration_time']['type'] == 'TIMESTAMP'
            assert schema_dict['registration_time']['mode'] == 'NULLABLE'

    def test_clean_parquet_dataframe_dynamic(self):
        """測試基於 schema 的完整 DataFrame 清理"""
        with tempfile.TemporaryDirectory() as temp_dir:
            schema_path = self.create_test_schema_file(temp_dir)

            # 創建包含各種問題的測試數據
            test_df = pd.DataFrame({
                'ec_id': [107, '', 107],
                'permanent': ['user1', 'user2', ''],
                'purchase_count': ['', '5', '10'],
                'total_sessions': ['3', '', '7'],
                'total_purchase_amount': ['', '200.5', '300.0'],
                'registration_time': pd.to_datetime(['2023-01-01', pd.NaT, '2023-01-03']),
                'first_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
                'last_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
                'first_purchase_time': pd.to_datetime(['2023-01-01', pd.NaT, '2023-01-03'])
            })

            # 應用動態清理
            cleaned_df = clean_parquet_dataframe_dynamic(test_df, ec_id=107, schema_path=schema_path)

            # 驗證結果
            # 必填整數欄位的空字符串應該轉為 0
            assert cleaned_df['purchase_count'].tolist() == [0, 5, 10]
            assert cleaned_df['total_sessions'].tolist() == [3, 0, 7]

            # 必填浮點數欄位的空字符串應該轉為 0.0
            assert cleaned_df['total_purchase_amount'].tolist() == [0.0, 200.5, 300.0]

            # 必填字符串欄位的空值應該轉為空字符串
            assert cleaned_df['permanent'].tolist() == ['user1', 'user2', '']

            # 檢查數據類型
            assert cleaned_df['ec_id'].dtype == 'int64'
            assert cleaned_df['purchase_count'].dtype == 'int64'
            assert cleaned_df['total_sessions'].dtype == 'int64'
            assert cleaned_df['total_purchase_amount'].dtype == 'float64'

    def test_edge_cases_with_real_problematic_data(self):
        """測試真實世界中遇到的邊界情況"""
        with tempfile.TemporaryDirectory() as temp_dir:
            schema_path = self.create_test_schema_file(temp_dir)

            # 模擬 EC ID 2808 和其他 EC IDs 遇到的具體問題
            problematic_df = pd.DataFrame({
                'ec_id': [2808, 107, 2808],
                'permanent': ['user1', 'user2', 'user3'],
                'purchase_count': ['', '5', ''],  # 空字符串導致 Int64 錯誤
                'total_sessions': ['3', '', '7'],
                'total_purchase_amount': [100.0, 200.0, 300.0],
                'registration_time': pd.to_datetime(['2023-01-01', pd.NaT, '2023-01-03']),  # NaT 導致序列化錯誤
                'first_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
                'last_interaction_time': pd.to_datetime(['2023-01-01', '2023-01-02', '2023-01-03']),
                'first_purchase_time': pd.to_datetime(['2023-01-01', pd.NaT, pd.NaT])  # 多個 NaT 值
            })

            # 應用動態清理
            cleaned_df = clean_parquet_dataframe_dynamic(problematic_df, ec_id=2808, schema_path=schema_path)

            # 驗證空字符串問題已修復
            assert cleaned_df['purchase_count'].tolist() == [0, 5, 0]
            assert cleaned_df['total_sessions'].tolist() == [3, 0, 7]

            # 驗證數據類型正確
            assert cleaned_df['purchase_count'].dtype == 'int64'
            assert cleaned_df['total_sessions'].dtype == 'int64'

            # 驗證 NaT 值被保留（供後續處理決定如何處理）
            assert cleaned_df['registration_time'].isna().sum() == 1
            assert cleaned_df['first_purchase_time'].isna().sum() == 2