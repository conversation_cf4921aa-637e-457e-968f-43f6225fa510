import pytest
from datetime import datetime, timedelta, timezone
from src.rules.dynamic_audience_rules import (
    evaluate_rule,
    get_final_description
)
from src.utils import merge_results

def test_evaluate_rule():
    """測試規則評估功能"""
    # 準備測試資料
    user_data = {
        'last_interaction_time': datetime(2024, 1, 1, tzinfo=timezone.utc),
        'last_interaction_days': 45,
        'purchase_count': 5
    }

    rule = {
        'data': {'min_days': 30, 'max_days': 365},
        'rule': {
            'and': [
                {'!=': [{'var': 'last_interaction_time'}, None]},
                {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]},
                {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]},
                {'>=': [{'var': 'purchase_count'}, 3]}
            ]
        }
    }

    # 測試有效規則
    assert evaluate_rule(rule, user_data) is True

    # 測試無效規則 - 購買次數不足
    user_data['purchase_count'] = 2
    assert evaluate_rule(rule, user_data) is False

    # 測試無效規則 - 互動日期超出範圍
    user_data['last_interaction_days'] = 400
    assert evaluate_rule(rule, user_data) is False

def test_rule_evaluation_with_variables():
    """測試帶有變數的規則評估"""
    # 準備測試資料
    user_data = {
        'last_interaction_time': datetime(2024, 1, 1, tzinfo=timezone.utc),
        'last_interaction_days': 45,
        'purchase_count': 5
    }

    rule = {
        'data': {
            'min_days': 30,
            'max_days': 365,
            'min_purchases': 3,
            'max_purchases': 10
        },
        'rule': {
            'and': [
                {'!=': [{'var': 'last_interaction_time'}, None]},
                {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]},
                {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]},
                {'>=': [{'var': 'purchase_count'}, {'var': 'min_purchases'}]},
                {'<=': [{'var': 'purchase_count'}, {'var': 'max_purchases'}]}
            ]
        }
    }

    # 測試有效規則
    assert evaluate_rule(rule, user_data) is True

    # 測試無效規則 - 購買次數超出上限
    user_data['purchase_count'] = 15
    assert evaluate_rule(rule, user_data) is False

def test_multiple_rule_types():
    """測試多種規則類型的評估"""
    # 準備測試資料
    user_data = {
        'last_interaction_time': datetime(2024, 1, 1, tzinfo=timezone.utc),
        'last_interaction_days': 45,
        'purchase_count': 5,
        'min_days': 7,
        'max_days': 60,
        'min_purchases': 3
    }

    # 數值比較規則
    numeric_rule = {
        'data': {'min_purchases': 3},
        'rule': {'>=': [{'var': 'purchase_count'}, {'var': 'min_purchases'}]}
    }
    assert evaluate_rule(numeric_rule, user_data) is True

    # 時間比較規則
    time_rule = {
        'data': {'min_days': 30, 'max_days': 365},
        'rule': {
            'and': [
                {'!=': [{'var': 'last_interaction_time'}, None]},
                {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]},
                {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]}
            ]
        }
    }
    assert evaluate_rule(time_rule, user_data) is True

    # 複合規則
    composite_rule = {
        'data': {
            'min_purchases': 3,
            'min_days': 30,
            'max_days': 365
        },
        'rule': {
            'and': [
                {'>=': [{'var': 'purchase_count'}, {'var': 'min_purchases'}]},
                {'!=': [{'var': 'last_interaction_time'}, None]},
                {'>': [{'var': 'last_interaction_days'}, {'var': 'min_days'}]},
                {'<=': [{'var': 'last_interaction_days'}, {'var': 'max_days'}]}
            ]
        }
    }
    assert evaluate_rule(composite_rule, user_data) is True

    # 測試不符合條件的情況
    user_data['purchase_count'] = 2  # 低於最小購買次數
    assert evaluate_rule(composite_rule, user_data) is False

def test_get_final_description():
    """測試獲取最終描述的功能"""
    # 準備測試資料
    mapping = {
        'description': '測試規則 1',
        'data': {'min_days': 30, 'max_days': 365}
    }

    # 測試存在的規則
    assert get_final_description(mapping) == '測試規則 1'

    # 測試帶參數的規則
    mapping = {
        'description': '最近 {min_days} 天內有互動的用戶',
        'data': {'min_days': 30}
    }
    assert get_final_description(mapping) == '最近 30 天內有互動的用戶'

def test_merge_results():
    """測試合併結果的功能"""
    # 準備測試資料
    results1 = {
        'key1': ['user1', 'user2'],
        'key2': ['user3']
    }
    results2 = {
        'key1': ['user2', 'user4'],
        'key3': ['user5']
    }

    # 執行測試
    merged = merge_results([results1, results2])

    # 驗證結果
    assert isinstance(merged, dict)
    assert sorted(merged['key1']) == sorted(['user1', 'user2', 'user2', 'user4'])  # 允許重複
    assert merged['key2'] == ['user3']
    assert merged['key3'] == ['user5']

def test_null_check_validation():
    """測試 null 檢查防護機制"""
    user_data = {
        'last_interaction_time': None,
        'last_interaction_days': 100,
        'purchase_count': 2
    }

    rule = {
        "data": {"min_days": 30, "max_days": 365},
        "rule": {
            "and": [
                {"!=": [{"var": "last_interaction_time"}, None]},
                {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
            ]
        }
    }

    # 測試時間為 null 的情況
    assert evaluate_rule(rule, user_data) is False

    # 測試時間存在但天數不符合的情況
    user_data['last_interaction_time'] = datetime.now(timezone.utc)
    user_data['last_interaction_days'] = 20
    assert evaluate_rule(rule, user_data) is False

    # 測試完全符合條件的情況
    user_data['last_interaction_days'] = 100
    assert evaluate_rule(rule, user_data) is True