import pytest
import pandas as pd
import numpy as np
from unittest.mock import patch, MagicMock

from src.core.cloud_integration import StorageManager


@pytest.fixture
def mock_storage_client():
    """建立 Storage client 的 mock"""
    with patch('src.core.cloud_integration.storage.Client', autospec=False) as mock:
        mock_instance = mock.return_value
        mock_bucket = MagicMock()
        mock_blob = MagicMock()
        mock_bucket.blob.return_value = mock_blob
        mock_instance.bucket.return_value = mock_bucket
        yield mock


@pytest.fixture
def sample_dataframe_with_different_dtypes():
    """創建包含不同數據類型的測試 DataFrame"""
    return pd.DataFrame({
        'ec_id': [107, 108, 109],  # 標準 int64
        'permanent': ['user1', 'user2', 'user3'],
        'purchase_count': pd.Series([1, 2, 3], dtype=pd.Int64Dtype()),  # pandas Int64
        'total_purchase_amount': [100.0, 200.0, 300.0],
        'total_sessions': pd.Series([10, 20, 30], dtype='int32'),  # int32
        'last_interaction_time': [pd.Timestamp('2023-01-01'), pd.Timestamp('2023-01-02'), pd.Timestamp('2023-01-03')]
    })


@pytest.fixture
def sample_dataframe_with_nullable_types():
    """創建包含 nullable 類型的測試 DataFrame"""
    return pd.DataFrame({
        'ec_id': pd.Series([107, 108, None], dtype=pd.Int64Dtype()),  # pandas Int64 with NULL
        'permanent': ['user1', 'user2', 'user3'],
        'purchase_count': pd.Series([1, 2, None], dtype=pd.Int64Dtype()),  # pandas Int64 with NULL
        'total_purchase_amount': [100.0, 200.0, None],
        'total_sessions': pd.Series([10, 20, None], dtype=pd.Int64Dtype()),  # Int64 with NULL
        'last_interaction_time': [pd.Timestamp('2023-01-01'), pd.Timestamp('2023-01-02'), None]
    })


def test_storage_manager_save_snapshot_handles_different_dtypes(mock_storage_client, sample_dataframe_with_different_dtypes):
    """測試 StorageManager 能夠處理不同的數據類型"""
    with patch('src.core.cloud_integration.gcsfs.GCSFileSystem') as mock_gcsfs:
        # 設定 mock
        storage_manager = StorageManager()
        storage_manager.bucket_name = 'test-bucket'

        # Mock gcsfs
        mock_fs = MagicMock()
        mock_fs.exists.return_value = False
        mock_fs.open.return_value.__enter__.return_value = MagicMock()
        mock_gcsfs.return_value = mock_fs

        # 執行函數
        current_date = pd.Timestamp('2023-01-01', tz='UTC')
        with patch('pandas.DataFrame.to_parquet') as mock_to_parquet:
            result = storage_manager.save_snapshot(
                sample_dataframe_with_different_dtypes,
                date=current_date,
                ec_id=107
            )

            # 驗證 to_parquet 被調用
            mock_to_parquet.assert_called_once()

            # 驗證結果
            assert result['success'] is True
            assert 'path' in result


def test_storage_manager_save_snapshot_handles_nullable_types(mock_storage_client, sample_dataframe_with_nullable_types):
    """測試 StorageManager 能夠處理含有 NULL 值的 Nullable 類型"""
    with patch('src.core.cloud_integration.gcsfs.GCSFileSystem') as mock_gcsfs:
        # 設定 mock
        storage_manager = StorageManager()
        storage_manager.bucket_name = 'test-bucket'

        # Mock gcsfs
        mock_fs = MagicMock()
        mock_fs.exists.return_value = False
        mock_fs.open.return_value.__enter__.return_value = MagicMock()
        mock_gcsfs.return_value = mock_fs

        # 執行函數
        current_date = pd.Timestamp('2023-01-01', tz='UTC')
        with patch('pandas.DataFrame.to_parquet') as mock_to_parquet, \
             patch('src.core.storage.logger') as mock_logger:

            result = storage_manager.save_snapshot(
                sample_dataframe_with_nullable_types,
                date=current_date,
                ec_id=107
            )

            # 驗證 NULL 值處理
            mock_logger.warning.assert_any_call("ec_id 欄位包含 1 個 NULL 值，這些值將被過濾掉")

            # 驗證 to_parquet 被調用
            mock_to_parquet.assert_called_once()

            # 驗證結果
            assert result['success'] is True
            assert 'path' in result


def test_bigquery_client_dtype_conversion_direct(sample_dataframe_with_different_dtypes):
    """直接測試 BigQueryClient 數據類型轉換邏輯，而不執行完整的 update_user_stats_table 方法"""
    from src.core.cloud_integration import BigQueryClient
    import pandas.api.types as types

    with patch('src.core.cloud_integration.logger') as mock_logger:
        # 創建 BigQueryClient 的實例
        bq_client = BigQueryClient()

        # 獲取測試用的 DataFrame 副本
        df = sample_dataframe_with_different_dtypes.copy()

        # 只測試 ec_id 的類型轉換部分
        original_dtype = df['ec_id'].dtype

        # 檢查是否為整數類型
        assert types.is_integer_dtype(df['ec_id']), "ec_id 應該是整數類型"

        # 手動執行轉換邏輯
        if str(original_dtype) != 'int64':
            # 使用更穩健的轉換方式，檢查是否為整數類型
            if types.is_integer_dtype(df['ec_id']):
                df['ec_id'] = df['ec_id'].astype('int64')
            else:
                df['ec_id'] = df['ec_id'].astype(np.int64)

        # 驗證轉換結果
        assert str(df['ec_id'].dtype) == 'int64', "ec_id 應該已經轉換為 int64 類型"

        # 測試 Int64Dtype 轉換
        assert str(df['purchase_count'].dtype) == 'Int64', "purchase_count 應該是 Int64 類型"
        if types.is_integer_dtype(df['purchase_count']):
            df['purchase_count'] = df['purchase_count'].fillna(0).astype(int)

        # 確認成功轉換
        assert df['purchase_count'].dtype == np.dtype('int64'), "purchase_count 應該已經轉換為 int64 類型"


def test_bigquery_client_nullable_handling_direct(sample_dataframe_with_nullable_types):
    """直接測試 BigQueryClient 對 NULL 值的處理邏輯"""
    import pandas.api.types as types

    # 獲取測試用的 DataFrame 副本
    df = sample_dataframe_with_nullable_types.copy()

    # 確認 NULL 值的存在
    assert df['ec_id'].isna().sum() > 0, "測試資料應該包含 NULL 值"

    # 簡化版的 NULL 處理邏輯測試
    # 1. 過濾 ec_id 中的 NULL 值
    df_filtered = df.dropna(subset=['ec_id'])
    assert len(df_filtered) < len(df), "應該過濾掉含有 NULL ec_id 的行"

    # 2. 測試 Int64 轉 int64
    ec_ids = df_filtered['ec_id'].copy()
    assert types.is_integer_dtype(ec_ids), "ec_id 應該是整數類型"
    ec_ids = ec_ids.astype('int64')
    assert str(ec_ids.dtype) == 'int64', "轉換後應該是 int64 類型"

    # 3. 測試整數欄位 NULL 值處理
    int_col = df['purchase_count'].copy()
    assert int_col.isna().sum() > 0, "purchase_count 應該包含 NULL 值"
    int_col = int_col.fillna(0).astype(int)
    assert int_col.isna().sum() == 0, "轉換後不應該有 NULL 值"
    assert str(int_col.dtype) == 'int64', "轉換後應該是 int64 類型"