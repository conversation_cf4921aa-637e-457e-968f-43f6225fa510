#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
測試 BigQueryClient 類別方法的存在性和正確性
防止方法被錯誤地定義在類別外部的問題再次發生
"""

import pytest
import inspect
from unittest.mock import Mock, patch
from src.core.cloud_integration import BigQueryClient


class TestBigQueryClientMethods:
    """測試 BigQueryClient 類別方法的完整性"""

    def test_required_methods_exist_in_class(self):
        """測試必要的方法確實存在於 BigQueryClient 類別中"""
        # 定義必要的方法列表
        required_methods = [
            '_process_single_batch',
            '_merge_single_batch_to_final_table',
            '_merge_batches_to_final_table'
        ]
        # 檢查每個方法是否在類別定義中
        for method_name in required_methods:
            assert hasattr(BigQueryClient, method_name), \
                f"BigQueryClient 類別缺少必要方法: {method_name}"
            # 確認方法是函數類型
            method = getattr(BigQueryClient, method_name)
            assert callable(method), \
                f"BigQueryClient.{method_name} 不是可調用的方法"
            # 確認方法是在類別字典中（不是繼承的）
            assert method_name in BigQueryClient.__dict__, \
                f"方法 {method_name} 不在 BigQueryClient.__dict__ 中，可能被定義在類別外部"

    @patch('src.core.cloud_integration.bigquery.Client')
    def test_prevent_attribute_error_regression(self, mock_bigquery_client):
        """
        回歸測試：防止 'BigQueryClient' object has no attribute '_process_single_batch' 錯誤
        """

        # 模擬 BigQuery 客戶端
        mock_client = Mock()
        mock_bigquery_client.return_value = mock_client

        try:
            # 創建實例
            bq_client = BigQueryClient()

            # 嘗試訪問之前出問題的方法
            process_method = getattr(bq_client, '_process_single_batch', None)
            merge_single_method = getattr(bq_client, '_merge_single_batch_to_final_table', None)
            merge_batches_method = getattr(bq_client, '_merge_batches_to_final_table', None)

            # 確保方法存在
            assert process_method is not None, \
                "回歸錯誤：_process_single_batch 方法不存在於實例中"

            assert merge_single_method is not None, \
                "回歸錯誤：_merge_single_batch_to_final_table 方法不存在於實例中"

            assert merge_batches_method is not None, \
                "回歸錯誤：_merge_batches_to_final_table 方法不存在於實例中"

        except AttributeError as e:
            pytest.fail(f"回歸錯誤：BigQueryClient 實例化或方法訪問失敗: {str(e)}")

    def test_merge_batches_method_signature(self):
        """測試 _merge_batches_to_final_table 方法的簽名"""
        # 獲取方法
        method = getattr(BigQueryClient, '_merge_batches_to_final_table', None)
        assert method is not None, "方法 _merge_batches_to_final_table 不存在"

        # 檢查方法簽名
        sig = inspect.signature(method)
        params = list(sig.parameters.keys())

        # 檢查參數（排除 self）
        expected_params = ['base_temp_table_id', 'total_records', 'batch_size', 'target_table_id']
        actual_params = params[1:]  # 排除 self

        assert actual_params == expected_params, \
            f"方法簽名不匹配，期望: {expected_params}，實際: {actual_params}"

    @patch('src.core.cloud_integration.bigquery.Client')
    @patch('src.core.cloud_integration.create_bigquery_schema_fields')
    def test_merge_batches_method_implementation(self, mock_schema_fields, mock_bigquery_client):
        """測試 _merge_batches_to_final_table 方法的實作邏輯（安全優化版本）"""
        # 模擬 schema fields
        from google.cloud import bigquery
        mock_schema_fields.return_value = [
            bigquery.SchemaField("ec_id", "INT64", mode="REQUIRED"),
            bigquery.SchemaField("permanent", "STRING", mode="REQUIRED"),
            bigquery.SchemaField("purchase_count", "INT64", mode="REQUIRED")
        ]

        # 模擬 BigQuery 客戶端
        mock_client = Mock()
        mock_bigquery_client.return_value = mock_client

        # 模擬查詢結果
        mock_query_job = Mock()
        mock_client.query.return_value = mock_query_job

        # 模擬計數查詢結果
        mock_count_result = Mock()
        mock_count_row = Mock()
        mock_count_row.total = 1000
        # 正確模擬迭代器
        mock_count_result.__iter__ = Mock(return_value=iter([mock_count_row]))
        mock_query_job.result.return_value = mock_count_result

        # 創建測試實例
        client = BigQueryClient()
        client.client = mock_client
        client.dataset_id = "test_dataset"

        # 模擬 table_exists 方法
        def mock_table_exists(table_id):
            # 模擬批次表存在
            if "batch" in table_id:
                return True
            return False
        client.table_exists = mock_table_exists

        # 模擬 _merge_single_batch_to_final_table 方法
        mock_merge_method = Mock()
        client._merge_single_batch_to_final_table = mock_merge_method

        try:
            # 執行測試
            client._merge_batches_to_final_table(
                base_temp_table_id="test_temp",
                total_records=1000,
                batch_size=200,
                target_table_id="test_target"
            )

            # 驗證方法被正確調用
            assert mock_client.query.called, "應該調用 BigQuery 查詢"

            # 驗證合併查詢被執行
            call_args = mock_client.query.call_args_list
            assert len(call_args) >= 1, "應該至少執行一次查詢"

            # 驗證第一個查詢是 CREATE TABLE AS SELECT
            first_query = call_args[0][0][0]
            assert "CREATE TABLE" in first_query, "應該執行 CREATE TABLE 查詢"
            assert "UNION ALL" in first_query, "應該使用 UNION ALL 合併批次"

            # 驗證 MERGE 操作被調用
            assert mock_merge_method.called, "應該執行 MERGE 操作"

        except Exception as e:
            pytest.fail(f"方法執行失敗: {str(e)}")
