import pytest
from datetime import datetime, timezone
import pandas as pd
import numpy as np
from src.core.queries import base_user_stats_query, daily_user_stats_query
from src.core.data_processing import update_user_stats_with_daily_data, standardize_datetime_columns

def test_base_user_stats_query_cart_view_item_fields():
    """測試基礎用戶統計查詢是否包含購物車和瀏覽商品欄位"""
    current_time = datetime.now(timezone.utc)
    query = base_user_stats_query(107, current_time, current_time)

    # 檢查查詢中是否包含購物車相關欄位
    assert "first_add_to_cart_time" in query
    assert "last_add_to_cart_time" in query

    # 檢查查詢中是否包含查看商品相關欄位
    assert "first_view_item_time" in query
    assert "last_view_item_time" in query

    # 檢查查詢中的 CASE WHEN 語句
    assert "CASE WHEN event.name = 'add_to_cart' THEN event_time ELSE NULL END" in query
    assert "CASE WHEN event.name = 'view_item' THEN event_time ELSE NULL END" in query

def test_daily_user_stats_query_cart_view_item_fields():
    """測試每日用戶統計查詢是否包含購物車和瀏覽商品欄位"""
    current_time = datetime.now(timezone.utc)
    query = daily_user_stats_query(107, current_time, current_time)

    # 檢查查詢中是否包含購物車相關欄位
    assert "first_add_to_cart_of_day" in query
    assert "last_add_to_cart_of_day" in query

    # 檢查查詢中是否包含查看商品相關欄位
    assert "first_view_item_of_day" in query
    assert "last_view_item_of_day" in query

    # 檢查查詢中的 CASE WHEN 語句
    assert "CASE WHEN event.name = 'add_to_cart' THEN event_time ELSE NULL END" in query
    assert "CASE WHEN event.name = 'view_item' THEN event_time ELSE NULL END" in query

def test_update_user_stats_with_daily_data_cart_view_item_fields():
    """測試更新用戶統計資料時是否正確處理購物車和瀏覽商品欄位"""
    # 創建基礎資料
    base_stats = pd.DataFrame({
        'ec_id': [107, 107],
        'permanent': ['user1', 'user2'],
        'first_interaction_time': [
            datetime(2025, 1, 1, tzinfo=timezone.utc),
            datetime(2025, 1, 1, tzinfo=timezone.utc)
        ],
        'last_interaction_time': [
            datetime(2025, 1, 1, tzinfo=timezone.utc),
            datetime(2025, 1, 1, tzinfo=timezone.utc)
        ],
        'first_add_to_cart_time': [
            datetime(2025, 1, 1, tzinfo=timezone.utc),
            None
        ],
        'last_add_to_cart_time': [
            datetime(2025, 1, 1, tzinfo=timezone.utc),
            None
        ],
        'first_view_item_time': [
            datetime(2025, 1, 1, tzinfo=timezone.utc),
            None
        ],
        'last_view_item_time': [
            datetime(2025, 1, 1, tzinfo=timezone.utc),
            None
        ],
        'purchase_count': [1, 0],
        'total_purchase_amount': [100.0, 0.0],
        'total_sessions': [1, 1]
    })

    # 創建每日資料
    daily_stats = pd.DataFrame({
        'ec_id': [107, 107],
        'permanent': ['user1', 'user2'],
        'first_add_to_cart_of_day': [
            datetime(2025, 1, 2, tzinfo=timezone.utc),
            datetime(2025, 1, 2, tzinfo=timezone.utc)
        ],
        'last_add_to_cart_of_day': [
            datetime(2025, 1, 2, tzinfo=timezone.utc),
            datetime(2025, 1, 2, tzinfo=timezone.utc)
        ],
        'first_view_item_of_day': [
            datetime(2025, 1, 2, tzinfo=timezone.utc),
            datetime(2025, 1, 2, tzinfo=timezone.utc)
        ],
        'last_view_item_of_day': [
            datetime(2025, 1, 2, tzinfo=timezone.utc),
            datetime(2025, 1, 2, tzinfo=timezone.utc)
        ],
        'first_interaction_of_day': [
            datetime(2025, 1, 2, tzinfo=timezone.utc),
            datetime(2025, 1, 2, tzinfo=timezone.utc)
        ],
        'last_interaction_of_day': [
            datetime(2025, 1, 2, tzinfo=timezone.utc),
            datetime(2025, 1, 2, tzinfo=timezone.utc)
        ],
        'daily_sessions': [1, 1],
        'daily_purchase_count': [0, 0],
        'daily_purchase_amount': [0.0, 0.0]
    })

    # 更新用戶統計資料
    result = update_user_stats_with_daily_data(base_stats, daily_stats)

    # 標準化時間欄位
    result = standardize_datetime_columns(result)

    # 檢查結果
    assert 'first_add_to_cart_time' in result.columns
    assert 'last_add_to_cart_time' in result.columns
    assert 'first_view_item_time' in result.columns
    assert 'last_view_item_time' in result.columns

    # 檢查 user1 的資料（應該更新 last_add_to_cart_time 和 last_view_item_time）
    user1 = result[result['permanent'] == 'user1'].iloc[0]
    assert user1['first_add_to_cart_time'] == datetime(2025, 1, 1, tzinfo=timezone.utc)
    assert user1['last_add_to_cart_time'] == datetime(2025, 1, 2, tzinfo=timezone.utc)
    assert user1['first_view_item_time'] == datetime(2025, 1, 1, tzinfo=timezone.utc)
    assert user1['last_view_item_time'] == datetime(2025, 1, 2, tzinfo=timezone.utc)

    # 檢查 user2 的資料（應該設置 first_add_to_cart_time、last_add_to_cart_time、first_view_item_time 和 last_view_item_time）
    user2 = result[result['permanent'] == 'user2'].iloc[0]
    assert user2['first_add_to_cart_time'] == datetime(2025, 1, 2, tzinfo=timezone.utc)
    assert user2['last_add_to_cart_time'] == datetime(2025, 1, 2, tzinfo=timezone.utc)
    assert user2['first_view_item_time'] == datetime(2025, 1, 2, tzinfo=timezone.utc)
    assert user2['last_view_item_time'] == datetime(2025, 1, 2, tzinfo=timezone.utc)
