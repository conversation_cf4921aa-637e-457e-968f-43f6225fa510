# LTA User Stats Cloud Function 和 Cloud Scheduler Terraform 配置
# 使用一份檔案管理 Cloud Function 和所有 EC ID 的 Scheduler

terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
  }

  # GCS Remote Backend 配置
  backend "gcs" {
    bucket = "tagtoo-ml-workflow-cloud-functions"
    prefix = "terraform-state/lta-user-stats"  # 每個 cloud function 有獨立的 state 路徑
  }
}

# GCP Provider 配置
provider "google" {
  project = var.project_id
  region  = var.region
}

# 變數定義
variable "ec_ids" {
  description = "EC IDs to process"
  type        = list(number)
  default     = [107, 2808, 2980, 3819, 3820]
}

variable "project_id" {
  description = "GCP Project ID"
  type        = string
  default     = "tagtoo-ml-workflow"
}

variable "region" {
  description = "GCP Region"
  type        = string
  default     = "asia-east1"
}

variable "schedule" {
  description = "Cron schedule for all schedulers"
  type        = string
  default     = "30 01 * * *"  # 每天 01:30 (台北時間)
}

variable "time_zone" {
  description = "Time zone for scheduling"
  type        = string
  default     = "Asia/Taipei"
}

variable "cloud_scheduler_timeout" {
  description = "Timeout for Cloud Scheduler to wait for a response, in seconds. Max is 1800s (30m) due to Cloud Scheduler API limits. The triggered Cloud Function itself has a longer timeout of 3600s (60m)."
  type        = number
  default     = 1800  # 30 分鐘，Cloud Scheduler API 最大值限制
}

# Cloud Function 相關變數 - 參考 cloudbuild.yaml 設定
variable "function_name" {
  description = "Cloud Function 名稱"
  type        = string
  default     = "lta-user-stats"
}

variable "runtime" {
  description = "Runtime 版本"
  type        = string
  default     = "python311"
}

variable "memory" {
  description = "記憶體大小 (MB) - 參考 cloudbuild.yaml 的 16Gi"
  type        = number
  default     = 16384  # 16Gi = 16384 MB
}

variable "timeout" {
  description = "超時時間 (秒) - 參考 cloudbuild.yaml 的 3600"
  type        = number
  default     = 3600  # 60 分鐘
}

variable "cpu" {
  description = "vCPU 數量 - 參考 cloudbuild.yaml 的 4"
  type        = string
  default     = "4"
}

variable "environment_variables" {
  description = "環境變數 - 參考 cloudbuild.yaml"
  type        = map(string)
  default = {
    ENV = "production"
    GOOGLE_APPLICATION_CREDENTIALS = "tagtoo-ml-workflow-kubeflow.json"
  }
}

variable "max_instances" {
  description = "最大執行個體數量 - 參考 cloudbuild.yaml 的 10"
  type        = number
  default     = 10
}

variable "min_instances" {
  description = "最小執行個體數量 - 參考 cloudbuild.yaml 的 0"
  type        = number
  default     = 0
}

variable "vpc_connector" {
  description = "VPC 連接器名稱 (可選)"
  type        = string
  default     = null
}

# 啟用必要的 API
resource "google_project_service" "scheduler_api" {
  project = var.project_id
  service = "cloudscheduler.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "functions_api" {
  project = var.project_id
  service = "cloudfunctions.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "bigquery_api" {
  project = var.project_id
  service = "bigquery.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "bigquerydatatransfer_api" {
  project = var.project_id
  service = "bigquerydatatransfer.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudrun_api" {
  project = var.project_id
  service = "run.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudbuild_api" {
  project = var.project_id
  service = "cloudbuild.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "storage_api" {
  project = var.project_id
  service = "storage.googleapis.com"
  disable_on_destroy = false
}

# 引用現有的 BigQuery Dataset（不管理它）
data "google_bigquery_dataset" "tagtoo_export_results" {
  dataset_id = "tagtoo_export_results"
  project    = var.project_id
}

# 引用共用的 Storage Bucket
data "google_storage_bucket" "shared_bucket" {
  name = "tagtoo-ml-workflow-cloud-functions"
}

# 壓縮源代碼 - 只包含必要的檔案
data "archive_file" "source_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../"
  output_path = "${path.module}/source.zip"

  # 使用更精確的排除規則
  excludes = [
    # 大檔案資料夾
    "local_data/",
    "local_output/",
    "init_snapshot/",
    "logs/",

    # Terraform 相關
    "terraform/",
    ".terraform/",
    "*.tf",
    "*.tfstate*",
    "source.zip",

    # Git 相關
    ".git/",
    ".gitignore",

    # Python 相關
    "__pycache__/",
    "*.pyc",
    "*.pyo",
    "*.pyd",
    "venv/",
    ".venv/",
    "env/",
    "ENV/",
    "*.egg-info/",
    "build/",
    "dist/",
    "downloads/",
    "eggs/",
    "lib/",
    "lib64/",
    "parts/",
    "sdist/",
    "var/",
    "wheels/",
    "*.egg",

    # IDE 相關
    ".idea/",
    ".vscode/",
    "*.swp",
    "*.swo",

    # 測試和覆蓋率
    "htmlcov/",
    ".tox/",
    ".coverage",
    ".coverage.*",
    ".cache",
    "nosetests.xml",
    "coverage.xml",
    "*.cover",
    ".hypothesis/",
    ".pytest_cache/",
    "test-reports/",

    # 日誌
    "*.log",

    # 建置相關
    "Dockerfile",
    ".dockerignore",
    "cloudbuild.yaml",
    "Makefile",
    "requirements-test.txt",
    "pytest.ini",
    "setup.py",

    # 文檔
    "docs/_build/",
    "docs/",
    "examples/",

    # macOS
    ".DS_Store",

    # 環境變數
    ".env",
    ".env.local",

    # 其他
    "payload.json"
  ]
}

# 上傳源代碼到共用存儲桶的 source/ 目錄下
resource "google_storage_bucket_object" "source_archive" {
  name   = "source/${var.function_name}/${var.function_name}-${data.archive_file.source_zip.output_md5}.zip"
  bucket = data.google_storage_bucket.shared_bucket.name
  source = data.archive_file.source_zip.output_path
}

# 使用統一的服務帳戶
locals {
  service_account_email = "<EMAIL>"
}

# Cloud Function 2nd Gen - 參考 cloudbuild.yaml 設定
resource "google_cloudfunctions2_function" "function" {
  name        = var.function_name
  description = "Cloud Function 2nd Gen for ${var.function_name}"
  location    = var.region

  build_config {
    runtime     = var.runtime
    entry_point = "main"

    source {
      storage_source {
        bucket = data.google_storage_bucket.shared_bucket.name
        object = google_storage_bucket_object.source_archive.name
      }
    }
  }

  service_config {
    max_instance_count    = var.max_instances
    min_instance_count    = var.min_instances
    available_memory      = "${var.memory}Mi"
    available_cpu         = var.cpu
    timeout_seconds       = var.timeout
    service_account_email = local.service_account_email

    environment_variables = var.environment_variables

    # VPC 連接器 (可選)
    vpc_connector = var.vpc_connector
  }

  labels = {
    environment = "production"
    function    = var.function_name
    managed_by  = "terraform"
  }

  depends_on = [
    google_project_service.functions_api,
    google_project_service.cloudrun_api,
    google_project_service.cloudbuild_api,
    google_project_service.storage_api
  ]
}

# IAM 策略 - 允許未經身份驗證的調用 (參考 cloudbuild.yaml 的 --allow-unauthenticated)
resource "google_cloud_run_service_iam_member" "invoker" {
  project  = google_cloudfunctions2_function.function.project
  location = google_cloudfunctions2_function.function.location
  service  = google_cloudfunctions2_function.function.name

  role   = "roles/run.invoker"
  member = "allUsers"

  depends_on = [google_cloudfunctions2_function.function]
}

# EC 107 產品購買者分群 Scheduled Query
resource "google_bigquery_data_transfer_config" "ec_107_product_segments" {
  display_name   = "tm:c_9999_107_c_020~023 product purchasing audiences (by Terraform)"
  location       = "asia-east1"
  data_source_id = "scheduled_query"

  schedule = "every day 17:30"  # UTC 時間，對應台灣時間 01:30 (UTC+8)

  destination_dataset_id = data.google_bigquery_dataset.tagtoo_export_results.dataset_id
  service_account_name   = "<EMAIL>"

  params = {
    destination_table_name_template = "special_lta_temp_for_update_{run_time+8h|\"%Y%m%d\"}"
    write_disposition               = "WRITE_APPEND"  # 使用 APPEND 模式
    query                          = file("${path.module}/../sql/scheduled_queries/ec_107_product_segments.sql")
  }

  depends_on = [
    google_project_service.bigquerydatatransfer_api
  ]
}

# 主要資源：為每個 EC ID 創建 Cloud Scheduler
resource "google_cloud_scheduler_job" "lta_user_stats" {
  for_each = { for ec_id in var.ec_ids : ec_id => ec_id }

  name     = "lta-user-stats-ec-${each.value}_by-terraform"
  project  = var.project_id
  region   = var.region

  description = "LTA User Stats Calculator for EC ID ${each.value}"
  schedule    = var.schedule
  time_zone   = var.time_zone

  http_target {
    uri         = google_cloudfunctions2_function.function.service_config[0].uri
    http_method = "POST"

    headers = {
      "Content-Type" = "application/json"
    }

    body = base64encode(jsonencode({
      ec_ids                           = [each.value]
      use_bigquery_compute            = true
      skip_time_standardization       = false
      only_standardize_required_columns = true
      should_calculate_stats          = true
      should_save_snapshot            = true
      should_write_lta                = true
      add_to_dxp                      = true
      should_update_user_stats_table  = true
      auto_repair_missing_days        = true
      max_repair_days                 = 30
    }))
  }

  retry_config {
    retry_count            = 0      # 設為 0 避免重複執行
    max_backoff_duration   = "3600s"
    max_doublings          = 5
    max_retry_duration     = "0s"
    min_backoff_duration   = "5s"
  }

  attempt_deadline = "${var.cloud_scheduler_timeout}s"

  depends_on = [
    google_project_service.scheduler_api,
    google_cloudfunctions2_function.function
  ]
}

# 輸出結果
output "function_url" {
  description = "Cloud Function 2nd Gen 的 HTTP 觸發器 URL"
  value       = google_cloudfunctions2_function.function.service_config[0].uri
}

output "function_name" {
  description = "Cloud Function 2nd Gen 名稱"
  value       = google_cloudfunctions2_function.function.name
}

output "service_account_email" {
  description = "Cloud Function 使用的服務帳戶"
  value       = local.service_account_email
}

output "source_bucket" {
  description = "源代碼存儲桶"
  value       = data.google_storage_bucket.shared_bucket.name
}

output "scheduler_jobs" {
  description = "Created Cloud Scheduler jobs"
  value = {
    for k, v in google_cloud_scheduler_job.lta_user_stats : k => {
      name        = v.name
      schedule    = v.schedule
      ec_id       = k
      next_run    = "Check GCP Console for next run time"
      function_url = v.http_target[0].uri
    }
  }
}

output "bigquery_scheduled_query" {
  description = "BigQuery Scheduled Query information"
  value = {
    name                = google_bigquery_data_transfer_config.ec_107_product_segments.display_name
    schedule            = google_bigquery_data_transfer_config.ec_107_product_segments.schedule
    destination_dataset = google_bigquery_data_transfer_config.ec_107_product_segments.destination_dataset_id
    table_template      = google_bigquery_data_transfer_config.ec_107_product_segments.params.destination_table_name_template
  }
}

output "summary" {
  description = "Deployment summary"
  value = {
    function_name        = var.function_name
    function_url         = google_cloudfunctions2_function.function.service_config[0].uri
    total_jobs           = length(var.ec_ids)
    ec_ids               = var.ec_ids
    schedule             = var.schedule
    time_zone            = var.time_zone
    project_id           = var.project_id
    region               = var.region
    bigquery_dataset     = data.google_bigquery_dataset.tagtoo_export_results.dataset_id
    bigquery_schedule    = google_bigquery_data_transfer_config.ec_107_product_segments.schedule
  }
}
