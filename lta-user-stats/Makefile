# Makefile for unified local dev/test/build with Docker buildx (linux/amd64)

# 與 cloudbuild.yaml 部署一致的 image 名稱
IMAGE_NAME=asia-east1-docker.pkg.dev/tagtoo-ml-workflow/gcf-artifacts/tagtoo--ml--workflow__asia--east1__lta--user--stats
PLATFORM=linux/amd64
IMAGE_TAG?=latest

# 取得預設 tag（branch-commit8碼），可被 IMAGE_TAG 覆寫
ifeq ($(IMAGE_TAG),latest)
  GIT_BRANCH := $(shell git rev-parse --abbrev-ref HEAD | tr '/' '-')
  GIT_SHA := $(shell git rev-parse --short=8 HEAD)
  TAG := $(GIT_BRANCH)-$(GIT_SHA)
else
  TAG := $(IMAGE_TAG)
endif

# 定義不同的 image tag
PROD_TAG := $(TAG)
TEST_TAG := $(TAG)-test

.PHONY: build build-test test test-all benchmark shell clean clean-cache-only logs lint format push help test-json-logic terraform-init terraform-plan terraform-apply terraform-destroy terraform-fmt terraform-validate test-file test-func test-class test-pattern test-quick test-failed test-debug-func

# Build docker image for production (只有生產依賴)
build:
	@echo "Building production image $(IMAGE_NAME):$(PROD_TAG)"
	docker buildx use orbstack || true
	docker buildx build --platform=$(PLATFORM) \
		--build-arg INSTALL_TEST_DEPS=false \
		-t $(IMAGE_NAME):$(PROD_TAG) --load .

# Build docker image for testing (包含測試依賴)
build-test:
	@echo "Building test image $(IMAGE_NAME):$(TEST_TAG)"
	docker buildx use orbstack || true
	docker buildx build --platform=$(PLATFORM) \
		--build-arg INSTALL_TEST_DEPS=true \
		-t $(IMAGE_NAME):$(TEST_TAG) --load .

# 執行單元測試（不包含 performance 和 real_bigquery）
test: build-test
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear -n auto -k 'not performance and not real_bigquery' -v

# 執行所有 pytest 測試（包含 performance，不含 real_bigquery）
test-all: build-test
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear -n auto -k 'not real_bigquery' -v

# 測試當前 json-logic 套件的兼容性
test-json-logic: build-test
	@echo "Testing current json-logic package compatibility..."
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear tests/test_json_logic_compatibility.py -v

# 執行真實 BigQuery 測試（需要 GCP 認證，成本較高）
test-real-bigquery: build-test
	@echo "執行真實 BigQuery 測試 - 警告：這將產生真實的 BigQuery 費用"
	@echo "確保已有 tagtoo-ml-workflow-kubeflow.json 認證文件"
	docker run --rm --platform=$(PLATFORM) \
		-v $(PWD)/tagtoo-ml-workflow-kubeflow.json:/app/tagtoo-ml-workflow-kubeflow.json:ro \
		-e GOOGLE_APPLICATION_CREDENTIALS=/app/tagtoo-ml-workflow-kubeflow.json \
		$(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear -m real_bigquery -v -s

# 執行特定的真實 BigQuery 測試 pattern
# 用法: make test-pattern-real-bigquery PATTERN="empty_strings"
test-pattern-real-bigquery: build-test
ifndef PATTERN
	@echo "錯誤: 請指定搜尋 pattern"
	@echo "用法: make test-pattern-real-bigquery PATTERN=\"empty_strings\""
	@exit 1
endif
	@echo "執行真實 BigQuery pattern 測試: $(PATTERN) - 警告：這將產生真實的 BigQuery 費用"
	@echo "確保已有 tagtoo-ml-workflow-kubeflow.json 認證文件"
	docker run --rm --platform=$(PLATFORM) \
		-v $(PWD)/tagtoo-ml-workflow-kubeflow.json:/app/tagtoo-ml-workflow-kubeflow.json:ro \
		-e GOOGLE_APPLICATION_CREDENTIALS=/app/tagtoo-ml-workflow-kubeflow.json \
		$(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear -k "$(PATTERN)" -v -s

# 監控檔案變更自動執行測試
watch-test:
	find . -name "*.py" | entr -c make test

# 互動式除錯（使用 pdb）
test-debug: build-test
	docker run --rm -it --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear --pdb -k 'not performance and not real_bigquery' -v

# 生成 HTML 測試報告
test-report: build-test
	mkdir -p test-reports
	docker run --rm --platform=$(PLATFORM) -v $(PWD)/test-reports:/app/test-reports --env CI=true $(IMAGE_NAME):$(TEST_TAG) \
		pytest --cache-clear -k 'not performance and not real_bigquery' -v --html=test-reports/report.html --self-contained-html

# 測試覆蓋率報告
test-cov: build-test
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) \
		pytest --cache-clear -k 'not performance and not real_bigquery' -v --cov=src --cov-report=term-missing

# 分析測試時間
test-slow: build-test
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear -k 'not performance and not real_bigquery' -v --durations=10

# 生成測試文件
test-doc: build-test
	mkdir -p docs/test-docs
	docker run --rm --platform=$(PLATFORM) -v $(PWD)/docs/test-docs:/app/docs/test-docs --env CI=true $(IMAGE_NAME):$(TEST_TAG) \
		pytest --cache-clear --doctest-modules --doctest-report=html --doctest-report-dir=docs/test-docs

# 執行性能基準測試
benchmark: build-test
	@echo "執行性能基準測試..."
	mkdir -p local_output
	docker run --rm --platform=$(PLATFORM) \
		-v $(PWD):/app \
		-v $(PWD)/local_output:/app/local_output \
		-v $(PWD)/tagtoo-ml-workflow-kubeflow.json:/app/tagtoo-ml-workflow-kubeflow.json:ro \
		-e GOOGLE_APPLICATION_CREDENTIALS=/app/tagtoo-ml-workflow-kubeflow.json \
		$(IMAGE_NAME):$(TEST_TAG) python scripts/benchmark_performance.py

# 清理建置快取
clean-cache:
	docker builder prune -f
	docker system df

# 進入 container shell（debug 用，使用測試 image，掛載本地目錄和 GCP 認證）
shell: build-test
	docker run --rm -it --platform=$(PLATFORM) \
		-v $(PWD):/app \
		-v $(PWD)/tagtoo-ml-workflow-kubeflow.json:/app/tagtoo-ml-workflow-kubeflow.json:ro \
		-e GOOGLE_APPLICATION_CREDENTIALS=/app/tagtoo-ml-workflow-kubeflow.json \
		$(IMAGE_NAME):$(TEST_TAG) /bin/bash

# 進入生產 container shell（掛載本地目錄和 GCP 認證）
shell-prod: build
	docker run --rm -it --platform=$(PLATFORM) \
		-v $(PWD):/app \
		-v $(PWD)/tagtoo-ml-workflow-kubeflow.json:/app/tagtoo-ml-workflow-kubeflow.json:ro \
		-e GOOGLE_APPLICATION_CREDENTIALS=/app/tagtoo-ml-workflow-kubeflow.json \
		$(IMAGE_NAME):$(PROD_TAG) /bin/bash

# 清除本地 images 和開發快取檔案
clean:
	@echo "清理 Docker images..."
	docker rmi $(IMAGE_NAME):$(PROD_TAG) || true
	docker rmi $(IMAGE_NAME):$(TEST_TAG) || true
	@echo "清理 Python 快取檔案..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type f -name "*.pyo" -delete 2>/dev/null || true
	@echo "清理測試和覆蓋率報告..."
	rm -rf htmlcov/ .coverage .coverage.* .pytest_cache/ .cache/ || true
	@echo "清理建置檔案..."
	rm -rf build/ dist/ *.egg-info/ .eggs/ || true
	@echo "清理完成！"

# 只清理 Python 快取檔案（不清理 Docker images）
clean-cache-only:
	@echo "清理 Python 快取檔案..."
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	find . -type f -name "*.pyo" -delete 2>/dev/null || true
	@echo "清理測試和覆蓋率報告..."
	rm -rf htmlcov/ .coverage .coverage.* .pytest_cache/ .cache/ || true
	@echo "清理建置檔案..."
	rm -rf build/ dist/ *.egg-info/ .eggs/ || true
	@echo "快取清理完成！"

# 顯示 pytest 測試 log（假設 log 輸出到 logs/pytest.log）
logs:
	cat logs/pytest.log || echo "No log file found."

# ==================== Terraform 相關指令 ====================

# 初始化 Terraform (使用 GCS remote backend)
terraform-init:
	@echo "Initializing Terraform with GCS remote backend..."
	@echo "State will be stored in: gs://tagtoo-ml-workflow-cloud-functions/terraform-state/lta-user-stats/"
	terraform init

# Terraform plan (預覽變更)
terraform-plan: terraform-init
	@echo "Running Terraform plan..."
	terraform plan

# 套用 Terraform 變更
terraform-apply: terraform-plan
	@echo "Applying Terraform changes..."
	terraform apply

# 銷毀 Terraform 資源 (小心使用!)
terraform-destroy:
	@echo "WARNING: This will destroy all Terraform-managed resources!"
	@read -p "Are you sure? (yes/no): " confirm && [ "$confirm" = "yes" ] || (echo "Cancelled." && exit 1)
	terraform destroy

# 格式化 Terraform 檔案
terraform-fmt:
	@echo "Formatting Terraform files..."
	terraform fmt

# 驗證 Terraform 配置
terraform-validate: terraform-init
	@echo "Validating Terraform configuration..."
	terraform validate

# 部署完整流程：測試 → Terraform → Cloud Function
deploy-all: test terraform-apply
	@echo "Running full deployment: tests passed, infrastructure updated"
	gcloud builds submit

# 僅部署基礎設施 (Cloud Schedulers)
deploy-infrastructure: terraform-apply
	@echo "Infrastructure deployment completed"

# 程式碼靜態檢查（需 requirements-test.txt 有 flake8）
lint: build-test
	docker run --rm --platform=$(PLATFORM) $(IMAGE_NAME):$(TEST_TAG) flake8 . || true

# 自動格式化（需 requirements-test.txt 有 black）
format: build-test
	docker run --rm --platform=$(PLATFORM) $(IMAGE_NAME):$(TEST_TAG) black .

# 推送 production image 到 registry（需先 docker login）
push: build
	@echo "Pushing production image $(IMAGE_NAME):$(PROD_TAG)"
	docker push $(IMAGE_NAME):$(PROD_TAG)

# 推送 test image 到 registry (通常不需要)
push-test: build-test
	@echo "Pushing test image $(IMAGE_NAME):$(TEST_TAG)"
	docker push $(IMAGE_NAME):$(TEST_TAG)

# 顯示所有常用指令
help:
	@echo "可用指令："
	@echo "=== Docker & Testing ==="
	@echo "  make build                    # build production docker image (linux/amd64)"
	@echo "  make build-test               # build test docker image (包含測試依賴)"
	@echo "  make test                     # 執行單元測試（不含 performance 和 real_bigquery）"
	@echo "  make test-all                 # 執行所有 pytest 測試（包含 performance，不含 real_bigquery）"
	@echo "  make benchmark                # 執行性能基準測試，測量分群計算性能"
	@echo "  make test-json-logic          # 測試當前 json-logic 套件兼容性"
	@echo "  make test-real-bigquery       # 執行真實 BigQuery 測試（需 GCP 認證，有成本）"
	@echo ""
	@echo "=== 特定測試指令（開發專用）==="
	@echo "  make test-file FILE=path      # 測試特定檔案"
	@echo "  make test-func FUNC=path::fn  # 測試特定函數"
	@echo "  make test-class CLASS=path::C # 測試特定類別"
	@echo "  make test-pattern PATTERN=str # 測試符合 pattern 的測試"
	@echo "  make test-pattern-real-bigquery PATTERN=str # 測試真實 BigQuery（需認證）"
	@echo "  make test-quick               # 快速測試（最近修改的檔案）"
	@echo "  make test-failed              # 重新執行上次失敗的測試"
	@echo "  make test-debug-func FUNC=... # 使用 pdb 除錯特定函數"
	@echo ""
	@echo "=== 其他 ==="
	@echo "  make shell                    # 進入測試 container shell (debug，掛載本地目錄和 kubeflow 認證)"
	@echo "  make shell-prod               # 進入生產 container shell (掛載本地目錄和 kubeflow 認證)"
	@echo "  make clean                    # 清除本地 images 和開發快取檔案"
	@echo "  make clean-cache-only         # 只清理 Python 快取檔案（不清理 Docker images）"
	@echo "  make logs                     # 顯示 pytest 測試 log"
	@echo "  make lint                     # 程式碼靜態檢查 (flake8)"
	@echo "  make format                   # 自動格式化 (black)"
	@echo "  make push                     # 推送 production image 到 registry"
	@echo ""
	@echo "=== Terraform & Infrastructure ==="
	@echo "  make terraform-init           # 初始化 Terraform"
	@echo "  make terraform-plan           # 預覽 Terraform 變更"
	@echo "  make terraform-apply          # 套用 Terraform 變更"
	@echo "  make terraform-destroy        # 銷毀 Terraform 資源 (小心!)"
	@echo "  make terraform-fmt            # 格式化 Terraform 檔案"
	@echo "  make terraform-validate       # 驗證 Terraform 配置"
	@echo "  make deploy-infrastructure    # 僅部署基礎設施"
	@echo "  make deploy-all               # 完整部署 (測試+基礎設施+Cloud Function)"
	@echo ""
	@echo "  make help                     # 顯示本說明"

# ==================== 特定測試指令 ====================

# 測試特定檔案
# 用法: make test-file FILE=tests/core/test_cloud_integration.py
test-file: build-test
ifndef FILE
	@echo "錯誤: 請指定檔案路徑"
	@echo "用法: make test-file FILE=tests/core/test_cloud_integration.py"
	@exit 1
endif
	@echo "執行檔案測試: $(FILE)"
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear $(FILE) -v

# 測試特定函數或方法
# 用法: make test-func FUNC=tests/core/test_cloud_integration.py::test_write_to_special_lta_logic
test-func: build-test
ifndef FUNC
	@echo "錯誤: 請指定函數路徑"
	@echo "用法: make test-func FUNC=tests/core/test_cloud_integration.py::test_write_to_special_lta_logic"
	@exit 1
endif
	@echo "執行函數測試: $(FUNC)"
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear $(FUNC) -v -s

# 測試特定類別
# 用法: make test-class CLASS=tests/integration/test_configuration_validation.py::TestConfigurationValidation
test-class: build-test
ifndef CLASS
	@echo "錯誤: 請指定類別路徑"
	@echo "用法: make test-class CLASS=tests/integration/test_configuration_validation.py::TestConfigurationValidation"
	@exit 1
endif
	@echo "執行類別測試: $(CLASS)"
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear $(CLASS) -v

# 測試符合特定 pattern 的測試
# 用法: make test-pattern PATTERN="cloud_integration"
test-pattern: build-test
ifndef PATTERN
	@echo "錯誤: 請指定搜尋 pattern"
	@echo "用法: make test-pattern PATTERN=\"cloud_integration\""
	@exit 1
endif
	@echo "執行 pattern 測試: $(PATTERN)"
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear -k "$(PATTERN)" -v

# 快速測試（只執行最近修改的檔案，適合開發時使用）
test-quick: build-test
	@echo "執行快速測試（最近修改的檔案）"
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear --lf -x -v

# 重新執行失敗的測試
test-failed: build-test
	@echo "重新執行上次失敗的測試"
	docker run --rm --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear --lf -v

# 使用 pdb 除錯特定函數
# 用法: make test-debug-func FUNC=tests/core/test_cloud_integration.py::test_write_to_special_lta_logic
test-debug-func: build-test
ifndef FUNC
	@echo "錯誤: 請指定函數路徑"
	@echo "用法: make test-debug-func FUNC=tests/core/test_cloud_integration.py::test_write_to_special_lta_logic"
	@exit 1
endif
	@echo "除錯函數測試: $(FUNC)"
	docker run --rm -it --platform=$(PLATFORM) --env CI=true $(IMAGE_NAME):$(TEST_TAG) pytest --cache-clear $(FUNC) --pdb -v -s
