#!/bin/bash
# 此腳本用於初始化特定 EC ID 的用戶統計資料
# 用法: ./init_ec_id.sh [EC_ID] [開始日期(可選, 格式為YYYY-MM-DD)] [選項]

# 獲取當前腳本目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
PROJECT_ROOT="$(dirname "$SCRIPT_DIR")"

# 顯示幫助訊息
show_help() {
    echo "用法: $0 [EC_ID] [開始日期(可選，格式為YYYY-MM-DD)] [選項]"
    echo
    echo "選項:"
    echo "  -h, --help          顯示此幫助訊息"
    echo "  --skip-bigquery     跳過寫入 BigQuery"
    echo "  --skip-gcs          跳過寫入 GCS"
    echo "  --enable-bigquery-upload  啟用寫入 BigQuery (默認不啟用)"
    echo "  --enable-gcs-upload      啟用寫入 GCS (默認不啟用)"
    echo "  --debug             啟用 debug 模式 (同時跳過寫入 BigQuery 和 GCS)"
    echo "  --local-parquet     產生本地 parquet 檔案 (debug 模式下默認啟用)"
    echo "  --local-path PATH   指定本地 parquet 檔案的儲存路徑 (默認為 ./local_data)"
    echo "  --force-query       自動確認執行高成本查詢，跳過費用確認提示"
    echo "  --use-bigquery-compute  使用 BigQuery 直接計算統計資料 (預設啟用)"
    echo
    echo "範例:"
    echo "  $0 107              # 初始化 EC ID 107，時間從 2022-01-01 開始"
    echo "  $0 107 2023-06-01   # 初始化 EC ID 107，時間從 2023-06-01 開始"
    echo "  $0 107 --enable-bigquery-upload  # 初始化 EC ID 107，並寫入 BigQuery"
    echo "  $0 107 --enable-gcs-upload       # 初始化 EC ID 107，並寫入 GCS"
    echo "  $0 107 --enable-bigquery-upload --enable-gcs-upload  # 初始化 EC ID 107，並寫入 BigQuery 和 GCS"
    echo "  $0 107 --debug      # 測試模式，不寫入任何資料庫，但會產生本地 parquet 檔案"
    echo "  $0 107 --local-parquet  # 初始化 EC ID 107，同時產生本地 parquet 檔案"
    echo "  $0 107 --local-parquet --local-path /tmp/data  # 指定本地 parquet 檔案儲存路徑"
    echo "  $0 107 --force-query    # 自動確認執行查詢，不提示費用確認"
}

# 檢查參數
if [ $# -lt 1 ]; then
    show_help
    exit 1
fi

# 解析參數
EC_ID=
START_DATE="2022-01-01"
SKIP_BIGQUERY=
SKIP_GCS=
DEBUG=
LOCAL_PARQUET=
LOCAL_PATH=
FORCE_QUERY=

while [ $# -gt 0 ]; do
    case "$1" in
        -h|--help)
            show_help
            exit 0
            ;;
        --skip-bigquery)
            SKIP_BIGQUERY="--skip-bigquery"
            ;;
        --skip-gcs)
            SKIP_GCS="--skip-gcs"
            ;;
        --enable-bigquery-upload)
            SKIP_BIGQUERY="--enable-bigquery-upload"
            ;;
        --enable-gcs-upload)
            SKIP_GCS="--enable-gcs-upload"
            ;;
        --debug)
            DEBUG="--debug"
            ;;
        --local-parquet)
            LOCAL_PARQUET="--local-parquet"
            ;;
        --force-query)
            FORCE_QUERY="true"
            ;;
        --local-path)
            if [ -n "$2" ] && [ "${2:0:1}" != "-" ]; then
                LOCAL_PATH="--local-path $2"
                shift
            else
                echo "錯誤：--local-path 選項需要一個參數"
                exit 1
            fi
            ;;
        *)
            if [ -z "$EC_ID" ]; then
                # 第一個位置參數視為 EC ID
                EC_ID=$1
            elif [[ "$1" =~ ^[0-9]{4}-[0-9]{2}-[0-9]{2}$ ]]; then
                # 格式為 YYYY-MM-DD 的參數視為開始日期
                START_DATE=$1
            else
                echo "錯誤：無法解析參數 '$1'"
                show_help
                exit 1
            fi
            ;;
    esac
    shift
done

# 檢查必要參數
if [ -z "$EC_ID" ]; then
    echo "錯誤：必須提供 EC ID"
    show_help
    exit 1
fi

# 執行 Python 腳本
echo "初始化 EC ID $EC_ID 的用戶統計資料，時間從 $START_DATE 開始..."

# 檢查是否在 Docker 環境中運行
if [ -f /.dockerenv ] || [ -n "$DOCKER_CONTAINER" ]; then
    echo "檢測到 Docker 環境，跳過虛擬環境檢查"

    # 設置 Google Cloud 認證
    if [ -f "$PROJECT_ROOT/tagtoo-ml-workflow-kubeflow.json" ]; then
        export GOOGLE_APPLICATION_CREDENTIALS="$PROJECT_ROOT/tagtoo-ml-workflow-kubeflow.json"
        echo "已設置 Google Cloud 認證: $GOOGLE_APPLICATION_CREDENTIALS"
    else
        echo "警告：找不到 Google Cloud 服務帳戶金鑰文件 ($PROJECT_ROOT/tagtoo-ml-workflow-kubeflow.json)"
        echo "請確保服務帳戶金鑰文件存在，或設置 GOOGLE_APPLICATION_CREDENTIALS 環境變數"
    fi
else
    # 確保虛擬環境已激活（僅在非 Docker 環境中）
    if [[ "$VIRTUAL_ENV" == "" ]]; then
        echo "警告：虛擬環境未激活，將自動嘗試激活"

        if [ -d "$PROJECT_ROOT/venv" ]; then
            source "$PROJECT_ROOT/venv/bin/activate"
            echo "已激活虛擬環境: $VIRTUAL_ENV"
        else
            echo "錯誤：找不到虛擬環境 ($PROJECT_ROOT/venv)"
            echo "請先建立並激活虛擬環境："
            echo "  cd $PROJECT_ROOT"
            echo "  python -m venv venv"
            echo "  source venv/bin/activate"
            echo "  pip install -r requirements.txt"
            exit 1
        fi
    fi
fi

# 切換到項目根目錄
cd "$PROJECT_ROOT"

# 設定環境變數，用於費用確認
if [ -n "$FORCE_QUERY" ]; then
    export FORCE_QUERY="true"
    echo "已設置自動確認查詢，將跳過費用確認提示"
fi

# 獲取結束日期（預設為台灣時間今天）
END_DATE=$(TZ=Asia/Taipei date +%Y-%m-%d)

# 構建命令
CMD="python \"$SCRIPT_DIR/init_user_stats_for_ec_id.py\" --ec-id \"$EC_ID\" --start-date \"$START_DATE\" --end-date \"$END_DATE\" --use-bigquery-compute $SKIP_BIGQUERY $SKIP_GCS $DEBUG $LOCAL_PARQUET $LOCAL_PATH"
echo "執行命令: $CMD"

# 執行 Python 腳本
eval $CMD

# 檢查執行結果
if [ $? -eq 0 ]; then
    echo "✅ EC ID $EC_ID 的用戶統計資料初始化完成"

    # 如果使用了本地 parquet 選項，顯示檔案位置
    if [ -n "$LOCAL_PARQUET" ] || [ -n "$DEBUG" ]; then
        local_path_value=$(echo $LOCAL_PATH | sed -E 's/--local-path\s+//g')
        if [ -z "$local_path_value" ]; then
            local_path_value="./local_data"
        fi

        echo "📊 本地 parquet 檔案已產生於: $local_path_value"
        if command -v ls &> /dev/null; then
            echo "最新檔案:"
            ls -lth "$local_path_value" | head -n 3
        fi
    fi
else
    echo "❌ EC ID $EC_ID 的用戶統計資料初始化失敗"
    exit 1
fi

# 清除環境變數
if [ -n "$FORCE_QUERY" ]; then
    unset FORCE_QUERY
fi