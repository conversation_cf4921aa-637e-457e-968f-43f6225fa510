#!/bin/bash

# =============================================================================
# LTA User Stats 批次重算腳本
# =============================================================================
# 此腳本用於在指定日期範圍內批次執行 local_user_stats_script.py
# 支援自動日期分割、Docker 環境執行和錯誤處理
#
# 使用方式:
#   ./scripts/recalculate_lta.sh
#   或自訂參數: EC_IDS=107 START_TIME=2025-05-31T16:00:00Z END_TIME=2025-06-23T16:00:00Z ./scripts/recalculate_lta.sh
# =============================================================================

set -euo pipefail  # 嚴格錯誤處理

# =============================================================================
# 配置變數 (可透過環境變數覆寫)
# =============================================================================

# EC 店家 IDs (逗號分隔，例如: "107" 或 "107,108,109")
EC_IDS="${EC_IDS:-107}"

# 日期範圍 (ISO 8601 格式，必須包含時區)
START_TIME="${START_TIME:-2025-05-31T16:00:00Z}"
END_TIME="${END_TIME:-2025-06-23T16:00:00Z}"

# 執行選項
USE_BIGQUERY_COMPUTE="${USE_BIGQUERY_COMPUTE:-true}"
VERBOSE="${VERBOSE:-true}"
DRY_RUN="${DRY_RUN:-false}"  # 設為 true 時只顯示要執行的指令，不實際執行
IN_DOCKER="${IN_DOCKER:-false}"  # 設為 true 時直接執行 Python 指令（假設已在 Docker 環境內）

# Docker 相關設定
DOCKER_IMAGE_NAME="asia-east1-docker.pkg.dev/tagtoo-ml-workflow/gcf-artifacts/tagtoo--ml--workflow__asia--east1__lta--user--stats"
DOCKER_TAG="${DOCKER_TAG:-latest}"
GCP_CREDENTIALS_FILE="${GCP_CREDENTIALS_FILE:-tagtoo-ml-workflow-kubeflow.json}"

# 日誌設定
LOG_DIR="logs/batch_recalculate"
LOG_FILE="${LOG_DIR}/recalculate_$(date +%Y%m%d_%H%M%S).log"

# =============================================================================
# 函數定義
# =============================================================================

# 顯示使用說明
show_usage() {
    cat << EOF
LTA User Stats 批次重算腳本

使用方式:
    $0 [選項]

環境變數:
    EC_IDS                  店家 IDs (預設: 107)
    START_TIME              開始時間 (ISO 8601, 預設: 2025-05-31T16:00:00Z)
    END_TIME                結束時間 (ISO 8601, 預設: 2025-06-23T16:00:00Z)
    USE_BIGQUERY_COMPUTE    是否使用 BigQuery 計算 (預設: true)
    VERBOSE                 詳細模式 (預設: true)
    DRY_RUN                 乾跑模式，只顯示指令不執行 (預設: false)
    IN_DOCKER               是否已在 Docker 環境內，直接執行 Python 指令 (預設: false)
    DOCKER_TAG              Docker image tag (預設: latest)
    GCP_CREDENTIALS_FILE    GCP 認證檔案路徑 (預設: tagtoo-ml-workflow-kubeflow.json)

範例:
    # 使用預設設定
    $0

    # 自訂日期範圍
    START_TIME=2025-06-01T16:00:00Z END_TIME=2025-06-05T16:00:00Z $0

    # 乾跑模式 (只顯示要執行的指令)
    DRY_RUN=true $0

    # 多個店家 ID
    EC_IDS="107,108,109" $0

    # 在 Docker 環境內執行（直接執行 Python 指令）
    IN_DOCKER=true $0

選項:
    -h, --help              顯示此說明
    --dry-run               乾跑模式 (等同 DRY_RUN=true)
    --verbose               詳細模式 (等同 VERBOSE=true)
    --quiet                 安靜模式 (等同 VERBOSE=false)

EOF
}

# 日誌函數
log() {
    local level="$1"
    shift
    local message="$*"
    local timestamp=$(date '+%Y-%m-%d %H:%M:%S')
    # 確保日誌目錄存在
    mkdir -p "$(dirname "$LOG_FILE")"
    # 避免 tee 造成的輸出混亂，分開處理
    echo "[$timestamp] [$level] $message"
    echo "[$timestamp] [$level] $message" >> "$LOG_FILE"
}

log_info() { log "INFO" "$@"; }
log_warn() { log "WARN" "$@"; }
log_error() { log "ERROR" "$@"; }

# 檢查必要的檔案和工具
check_prerequisites() {
    log_info "檢查必要條件..."

    # 檢查 Docker（僅在非 Docker 環境內時檢查）
    if [[ "$IN_DOCKER" != "true" ]] && ! command -v docker &> /dev/null; then
        log_error "Docker 未安裝或不在 PATH 中"
        exit 1
    fi

    # 檢查 GCP 認證檔案
    if [[ ! -f "$GCP_CREDENTIALS_FILE" ]]; then
        log_error "GCP 認證檔案不存在: $GCP_CREDENTIALS_FILE"
        log_info "請確保檔案存在於當前目錄或設定正確的 GCP_CREDENTIALS_FILE 環境變數"
        exit 1
    fi

    # 檢查是否在正確的目錄
    if [[ ! -f "scripts/local_user_stats_script.py" ]]; then
        log_error "找不到 scripts/local_user_stats_script.py"
        log_info "請在 lta-user-stats 專案根目錄執行此腳本"
        exit 1
    fi

    log_info "✓ 必要條件檢查通過"
}

# 建置 Docker image（如果需要）
build_docker_image() {
    if [[ "$IN_DOCKER" == "true" ]]; then
        log_info "已在 Docker 環境內，跳過 Docker image 建置"
        return 0
    fi

    log_info "建置 Docker image..."

    if [[ "$DRY_RUN" == "true" ]]; then
        log_info "[DRY-RUN] 將執行: make build"
        return 0
    fi

    if ! make build; then
        log_error "Docker image 建置失敗"
        exit 1
    fi

    log_info "✓ Docker image 建置完成"
}

# 解析 ISO 8601 日期為 Unix timestamp（保持原始時間）
parse_date() {
    local date_str="$1"

    # 使用 date 指令解析 ISO 8601 格式（不修改時間）
    if command -v gdate &> /dev/null; then
        # macOS 上使用 GNU date (需要安裝 coreutils: brew install coreutils)
        gdate -d "$date_str" +%s
    else
        # Linux 上使用標準 date
        date -d "$date_str" +%s
    fi
}

# 將 Unix timestamp 轉換為 ISO 8601 格式（保持原始時間，使用 UTC）
format_date() {
    local timestamp="$1"

    if command -v gdate &> /dev/null; then
        TZ=UTC gdate -d "@$timestamp" '+%Y-%m-%dT%H:%M:%SZ'
    else
        TZ=UTC date -d "@$timestamp" '+%Y-%m-%dT%H:%M:%SZ'
    fi
}

# 產生日期範圍列表
generate_date_ranges() {
    local start_time="$1"
    local end_time="$2"

    # 解析開始和結束時間（不輸出日誌避免干擾）
    local start_timestamp
    local end_timestamp

    if ! start_timestamp=$(parse_date "$start_time" 2>/dev/null); then
        log_error "無法解析開始時間: $start_time"
        exit 1
    fi

    if ! end_timestamp=$(parse_date "$end_time" 2>/dev/null); then
        log_error "無法解析結束時間: $end_time"
        exit 1
    fi

    # 檢查時間順序
    if [[ $start_timestamp -ge $end_timestamp ]]; then
        log_error "開始時間必須早於結束時間"
        exit 1
    fi

        # 產生每日的時間範圍
    local current_timestamp=$start_timestamp
    local one_day=86400  # 24 * 60 * 60 秒
    local ranges=()

    while [[ $current_timestamp -lt $end_timestamp ]]; do
        local current_start=$(format_date $current_timestamp 2>/dev/null)
        local next_timestamp=$((current_timestamp + one_day))

        # 如果下一個時間戳超過結束時間，則使用結束時間作為當前範圍的結束
        if [[ $next_timestamp -gt $end_timestamp ]]; then
            local current_end=$(format_date $end_timestamp 2>/dev/null)
            ranges+=("$current_start,$current_end")
            break
        else
            local current_end=$(format_date $next_timestamp 2>/dev/null)
            ranges+=("$current_start,$current_end")
        fi

        current_timestamp=$next_timestamp
    done

    # 輸出範圍到 stdout，不混入日誌
    printf '%s\n' "${ranges[@]}"
}

# 執行單一日期範圍的腳本
execute_single_range() {
    local ec_ids="$1"
    local start_time="$2"
    local end_time="$3"
    local range_index="$4"
    local total_ranges="$5"

    log_info "[$range_index/$total_ranges] 處理日期範圍: $start_time 到 $end_time (EC IDs: $ec_ids)"

    # 建構執行指令
    local cmd="python scripts/local_user_stats_script.py"
    cmd+=" --ec-ids $ec_ids"
    cmd+=" --start-time $start_time"
    cmd+=" --end-time $end_time"

    if [[ "$USE_BIGQUERY_COMPUTE" == "true" ]]; then
        cmd+=" --use-bigquery-compute"
    fi

    if [[ "$VERBOSE" == "true" ]]; then
        cmd+=" --verbose"
    fi

    if [[ "$IN_DOCKER" == "true" ]]; then
        # 在 Docker 環境內直接執行
        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[DRY-RUN] 將執行: $cmd"
            return 0
        fi

        # 設定 GCP 認證環境變數
        export GOOGLE_APPLICATION_CREDENTIALS="$(pwd)/$GCP_CREDENTIALS_FILE"

        # 執行指令
        log_info "執行指令: $cmd"

        if eval "$cmd"; then
            log_info "✓ [$range_index/$total_ranges] 完成: $start_time 到 $end_time"
        else
            local exit_code=$?
            log_error "✗ [$range_index/$total_ranges] 失敗: $start_time 到 $end_time (exit code: $exit_code)"
            return $exit_code
        fi
    else
        # 使用 Docker 容器執行
        local docker_cmd="docker run --rm"
        docker_cmd+=" -v $(pwd):/app"
        docker_cmd+=" -v $(pwd)/$GCP_CREDENTIALS_FILE:/app/$GCP_CREDENTIALS_FILE:ro"
        docker_cmd+=" -e GOOGLE_APPLICATION_CREDENTIALS=/app/$GCP_CREDENTIALS_FILE"
        docker_cmd+=" $DOCKER_IMAGE_NAME:$DOCKER_TAG"
        docker_cmd+=" $cmd"

        if [[ "$DRY_RUN" == "true" ]]; then
            log_info "[DRY-RUN] 將執行: $docker_cmd"
            return 0
        fi

        # 執行指令
        log_info "執行指令: $cmd"

        if eval "$docker_cmd"; then
            log_info "✓ [$range_index/$total_ranges] 完成: $start_time 到 $end_time"
        else
            local exit_code=$?
            log_error "✗ [$range_index/$total_ranges] 失敗: $start_time 到 $end_time (exit code: $exit_code)"
            return $exit_code
        fi
    fi
}

# 主要執行函數
main() {
    # 建立日誌目錄
    mkdir -p "$LOG_DIR"

    log_info "=== LTA User Stats 批次重算開始 ==="
    log_info "EC IDs: $EC_IDS"
    log_info "日期範圍: $START_TIME 到 $END_TIME"
    log_info "BigQuery 計算: $USE_BIGQUERY_COMPUTE"
    log_info "詳細模式: $VERBOSE"
    log_info "乾跑模式: $DRY_RUN"
    log_info "在 Docker 環境內: $IN_DOCKER"
    log_info "Docker Tag: $DOCKER_TAG"
    log_info "日誌檔案: $LOG_FILE"
    log_info ""

    # 檢查必要條件
    check_prerequisites

    # 建置 Docker image
    build_docker_image

    # 產生日期範圍
    log_info "解析日期範圍: $START_TIME 到 $END_TIME"
    local date_ranges_output
    if ! date_ranges_output=$(generate_date_ranges "$START_TIME" "$END_TIME"); then
        log_error "無法產生日期範圍"
        exit 1
    fi

    # 將輸出轉換為陣列
    local date_ranges=()
    while IFS= read -r line; do
        [[ -n "$line" ]] && date_ranges+=("$line")
    done <<< "$date_ranges_output"

    log_info "共產生 ${#date_ranges[@]} 個日期範圍"

    local total_ranges=${#date_ranges[@]}
    local failed_ranges=()

    log_info "開始批次處理 $total_ranges 個日期範圍..."
    log_info ""

    # 執行每個日期範圍
    for i in "${!date_ranges[@]}"; do
        local range="${date_ranges[$i]}"
        local start_time="${range%,*}"
        local end_time="${range#*,}"
        local range_index=$((i + 1))

        if ! execute_single_range "$EC_IDS" "$start_time" "$end_time" "$range_index" "$total_ranges"; then
            failed_ranges+=("$range")
            log_warn "記錄失敗範圍: $start_time 到 $end_time"
        fi

        # 在範圍之間添加短暫延遲，避免過度負載
        if [[ "$DRY_RUN" != "true" && $range_index -lt $total_ranges ]]; then
            log_info "等待 3 秒後繼續下一個範圍..."
            sleep 3
        fi

        log_info ""
    done

    # 總結報告
    log_info "=== 批次處理完成 ==="
    log_info "總計處理: $total_ranges 個日期範圍"
    log_info "成功: $((total_ranges - ${#failed_ranges[@]})) 個"
    log_info "失敗: ${#failed_ranges[@]} 個"

    if [[ ${#failed_ranges[@]} -gt 0 ]]; then
        log_warn ""
        log_warn "失敗的日期範圍:"
        for range in "${failed_ranges[@]}"; do
            local start_time="${range%,*}"
            local end_time="${range#*,}"
            log_warn "  - $start_time 到 $end_time"
        done
        log_warn ""
        log_warn "建議重新執行失敗的範圍"
        exit 1
    else
        log_info "🎉 所有日期範圍都處理成功！"
    fi
}

# =============================================================================
# 參數解析和入口點
# =============================================================================

# 解析命令列參數
while [[ $# -gt 0 ]]; do
    case $1 in
        -h|--help)
            show_usage
            exit 0
            ;;
        --dry-run)
            DRY_RUN=true
            ;;
        --verbose)
            VERBOSE=true
            ;;
        --quiet)
            VERBOSE=false
            ;;
        *)
            log_error "未知選項: $1"
            log_info "使用 $0 --help 查看使用說明"
            exit 1
            ;;
    esac
    shift
done

# 執行主函數
main "$@"