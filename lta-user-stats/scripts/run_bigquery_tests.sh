#!/bin/bash
# 執行 BigQuery 計算功能的測試腳本

# 設置顏色輸出
GREEN='\033[0;32m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

PROJECT_ROOT=$(dirname "$(dirname "$(readlink -f "$0")")")
cd "$PROJECT_ROOT"

echo -e "${BLUE}開始執行 BigQuery 直接計算功能測試${NC}"
echo -e "${BLUE}===============================================${NC}"

# 運行單元測試
echo -e "${BLUE}1. 執行單元測試 (使用模擬)${NC}"
python -m unittest tests/test_bigquery_compute.py
UNIT_TEST_RESULT=$?

# 運行 SQL 查詢測試
echo -e "\n${BLUE}2. 執行 SQL 查詢結構測試${NC}"
python -m unittest tests/test_bigquery_sql.py
SQL_TEST_RESULT=$?

# 輸出摘要
echo -e "\n${BLUE}===============================================${NC}"
echo -e "${BLUE}測試摘要:${NC}"
if [ $UNIT_TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}✓ 單元測試通過${NC}"
else
    echo -e "${RED}✗ 單元測試失敗${NC}"
fi

if [ $SQL_TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}✓ SQL 查詢測試通過${NC}"
else
    echo -e "${RED}✗ SQL 查詢測試失敗${NC}"
fi

echo -e "${BLUE}===============================================${NC}"

# 檢查所有測試是否通過
if [ $UNIT_TEST_RESULT -eq 0 ] && [ $SQL_TEST_RESULT -eq 0 ]; then
    echo -e "${GREEN}所有測試都通過了！🎉${NC}"
    exit 0
else
    echo -e "${RED}部分測試失敗，請查看上面的錯誤訊息${NC}"
    exit 1
fi