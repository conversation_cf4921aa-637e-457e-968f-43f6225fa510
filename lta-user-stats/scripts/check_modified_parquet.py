import pandas as pd
import sys
import os

def check_parquet(file_path):
    """檢查 parquet 文件的結構和內容"""
    print(f"檢查文件: {file_path}")

    if not os.path.exists(file_path):
        print(f"錯誤: 文件不存在 - {file_path}")
        return

    try:
        df = pd.read_parquet(file_path)

        # 檢查基本信息
        print(f"總記錄數: {len(df)}")
        print(f"欄位數量: {len(df.columns)}")
        print(f"欄位列表: {', '.join(df.columns)}")

        # 檢查數據類型
        print("\n欄位數據類型:")
        for col, dtype in df.dtypes.items():
            print(f"  {col}: {dtype}")

        # 檢查是否有需要刪除的欄位
        removed_columns = ['total_interaction_count', 'average_purchase_amount', 'customer_status']
        for col in removed_columns:
            if col in df.columns:
                print(f"\n警告: 應該移除的欄位 '{col}' 仍然存在")

        # 檢查是否有必要的欄位
        required_columns = [
            'ec_id', 'permanent', 'total_sessions',
            'registration_time', 'first_interaction_time', 'last_interaction_time',
            'first_purchase_time', 'last_purchase_time',
            'purchase_count', 'total_purchase_amount',
            'created_at', 'updated_at'
        ]

        missing_columns = [col for col in required_columns if col not in df.columns]
        if missing_columns:
            print(f"\n警告: 缺少必要欄位: {', '.join(missing_columns)}")

        # 檢查內容
        print("\n資料摘要:")

        # 檢查時間欄位
        time_columns = [col for col in df.columns if 'time' in col.lower()]
        for col in time_columns:
            valid_count = df[col].notna().sum()
            print(f"  {col}: {valid_count} 有效值 ({valid_count/len(df)*100:.2f}%)")

        # 檢查 total_sessions
        if 'total_sessions' in df.columns:
            print(f"  總會話數: {df['total_sessions'].sum()}")
            print(f"  平均每用戶會話數: {df['total_sessions'].mean():.2f}")

            # 檢查會話數分布
            session_counts = df['total_sessions'].value_counts().sort_index()
            print(f"  會話數分布: ")
            for session_count, count in session_counts.items():
                print(f"    {session_count} 個會話: {count} 個用戶 ({count/len(df)*100:.2f}%)")

        # 檢查購買計數和金額
        if 'purchase_count' in df.columns:
            print(f"  購買用戶數: {(df['purchase_count'] > 0).sum()} ({(df['purchase_count'] > 0).mean()*100:.2f}%)")
            print(f"  總購買次數: {df['purchase_count'].sum()}")

        if 'total_purchase_amount' in df.columns:
            print(f"  總購買金額: {df['total_purchase_amount'].sum():.2f}")

    except Exception as e:
        print(f"錯誤: {str(e)}")

if __name__ == "__main__":
    # 檢查原始檔案和修改後的檔案
    check_parquet("./local_data/test_modified.parquet")

    # 檢查半年資料的檔案
    print("\n" + "-"*50)
    check_parquet("./local_data/test_modified_half_year.parquet")

    # 檢查完整半年資料的檔案
    print("\n" + "-"*50)
    check_parquet("./local_data/test_full_half_year.parquet")