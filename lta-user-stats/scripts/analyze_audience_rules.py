#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
深度分析受眾規則邏輯和數量問題
"""

import json
import sys
import os
sys.path.insert(0, os.path.abspath(os.path.join(os.path.dirname(__file__), '..')))

def analyze_rules():
    """分析受眾規則的詳細內容"""

    # 讀取本地的 audience_rules.json
    with open('/Users/<USER>/tagtoo/ml-workflow-cloud-functions/lta-user-stats/scripts/audience_rules.json', 'r', encoding='utf-8') as f:
        rules_data = json.load(f)

    target_ec_ids = ['2980', '3819', '3820']

    for ec_id in target_ec_ids:
        print(f"\n{'='*60}")
        print(f"EC ID {ec_id} 規則分析")
        print(f"{'='*60}")

        if ec_id not in rules_data:
            print(f"❌ EC ID {ec_id} 不存在於規則檔案中")
            continue

        ec_rules = rules_data[ec_id]['rules']
        print(f"📊 總規則數量: {len(ec_rules)}")
        print(f"🏷️  EC 名稱: {rules_data[ec_id]['metadata']['name']}")

        # 按照規則 ID 排序
        sorted_rules = sorted(ec_rules.items(), key=lambda x: x[0])

        for rule_id, rule_data in sorted_rules:
            print(f"\n🔸 規則: {rule_id}")
            print(f"   描述: {rule_data['description']}")
            print(f"   參數: {rule_data.get('data', {})}")

            # 檢查規則邏輯
            rule_logic = rule_data.get('rule', {})
            print(f"   規則邏輯: {json.dumps(rule_logic, indent=4, ensure_ascii=False)}")

            # 檢查規則是否有效（是否為空或無效）
            if not rule_logic or (isinstance(rule_logic, dict) and len(rule_logic) == 0):
                print(f"   ⚠️  警告: 規則邏輯為空！")
            elif isinstance(rule_logic, dict) and 'and' in rule_logic and len(rule_logic['and']) == 0:
                print(f"   ⚠️  警告: 'and' 條件為空陣列！")
        print(f"\n🔍 規則ID清單: {list(ec_rules.keys())}")

if __name__ == "__main__":
    analyze_rules()
