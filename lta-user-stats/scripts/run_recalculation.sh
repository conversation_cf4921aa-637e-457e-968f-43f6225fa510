#!/bin/bash
# 重新計算特定 EC IDs 的受眾分群
# 此腳本啟動所有重新計算的流程

set -e

# 獲取當前腳本目錄
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
cd $SCRIPT_DIR/..

# 將腳本設為可執行
chmod +x scripts/recalculate_audience_segments.sh
chmod +x scripts/recalculate_segments.py
chmod +x scripts/backup_and_validate.sh

# 確保本地目錄存在
mkdir -p ./local_data

# 首先備份並驗證現有的 parquet 文件
# 備份分析腳本只檢查 xxx_time 欄位，不檢查 xxx_days 欄位
echo "========================================================="
echo "備份並驗證當前的 20250522 數據"
echo "========================================================="
bash scripts/backup_and_validate.sh

echo "========================================================="
echo "開始為 EC ID 2980, 3819, 3820 重新計算 20250522 數據"
echo "========================================================="

# 第一步：執行 recalculate_audience_segments.sh
# 執行重新計算流程...
bash scripts/recalculate_audience_segments.sh || echo "警告: recalculate_audience_segments.sh 執行時有錯誤，請檢查 log"

# 第二步：執行 Python 重新計算腳本
for EC_ID in 2980 3819 3820; do
    echo "詳細重新計算 EC ID $EC_ID..."
    python -m scripts.recalculate_segments --ec-id $EC_ID --base-date 20250521 --target-date 20250522 || echo "警告: recalculate_segments $EC_ID 執行失敗，請檢查 log"
done

echo "========================================================="
echo "所有 EC IDs 的受眾分群重新計算已完成"
echo "========================================================="
echo "請驗證以下 GCS 路徑的 parquet 檔案是否更新："
echo "gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_2980/20250522.parquet"
echo "gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_3819/20250522.parquet"
echo "gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_3820/20250522.parquet"
echo ""
echo "也請驗證這些受眾分群是否已正確計算。"
