#!/bin/bash
# 用法: ./restore_user_stats.sh [備份日期 yyyymmdd] [電商ID]

BACKUP_DATE=${1:-$(date -d "yesterday" +%Y%m%d)}
EC_ID=${2}

# 若提供了電商ID，使用新格式路徑；否則使用舊格式路徑
if [ -n "$EC_ID" ]; then
    GCS_PATH="gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_${EC_ID}/${BACKUP_DATE}.parquet"
    echo "使用新格式路徑 (依電商ID分類)"
else
    GCS_PATH="gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/user_stats_${BACKUP_DATE}.parquet"
    echo "使用舊格式路徑 (所有電商合併)"
fi

LOCAL_PATH="/tmp/user_stats_${BACKUP_DATE}.parquet"

# 動態獲取原表叢集欄位
CLUSTER_FIELDS=$(bq show --format=json tagtoo-tracking:event_prod.user_stats | jq -r '.clustering.fields | join(",")')

# 在執行前加入結構驗證
ORIG_CLUSTER=$(bq show --format=json tagtoo-tracking:event_prod.user_stats | jq -r '.clustering.fields | join(",")')

if [[ "$ORIG_CLUSTER" != "ec_id,permanent" ]]; then
  echo "錯誤：原表叢集順序已變更為 $ORIG_CLUSTER，請同步更新指令稿"
  exit 1
fi

# 在執行前加入檢查
if ! gsutil ls ${GCS_PATH} >/dev/null 2>&1; then
    echo "錯誤：備份檔案不存在 ${GCS_PATH}"

    # 如果新格式不存在，嘗試檢查舊格式
    if [ -n "$EC_ID" ]; then
        OLD_GCS_PATH="gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/user_stats_${BACKUP_DATE}.parquet"
        echo "嘗試檢查舊格式路徑: ${OLD_GCS_PATH}"

        if gsutil ls ${OLD_GCS_PATH} >/dev/null 2>&1; then
            echo "找到舊格式備份檔案，將使用此檔案"
            GCS_PATH=${OLD_GCS_PATH}
        else
            echo "錯誤：新舊格式備份檔案皆不存在"
            exit 1
        fi
    else
        exit 1
    fi
fi

# 下載 parquet 檔案到本地，以便進行處理
echo "下載檔案 ${GCS_PATH} 到 ${LOCAL_PATH}"
gsutil cp "${GCS_PATH}" "${LOCAL_PATH}"

if [ $? -ne 0 ]; then
    echo "錯誤：無法從 GCS 下載檔案"
    exit 1
fi

# 使用臨時表並通過 MERGE 操作更新數據，而不是完全替換
TEMP_TABLE="event_prod.user_stats_temp_$(date +%s)"
echo "建立臨時表 ${TEMP_TABLE} 並載入資料..."

# 首先創建一個中間表，用於過濾和清理數據
INTERMEDIATE_TABLE="event_prod.user_stats_intermediate_$(date +%s)"
echo "創建中間表進行數據預處理..."

# 1. 先載入數據到中間表
bq load \
--project_id=tagtoo-tracking \
--time_partitioning_field=last_interaction_time \
--time_partitioning_type=DAY \
--clustering_fields=${CLUSTER_FIELDS} \
--source_format=PARQUET \
tagtoo-tracking:${INTERMEDIATE_TABLE} \
"${LOCAL_PATH}"

if [ $? -ne 0 ]; then
    echo "錯誤：無法將資料載入到中間表"
    bq rm -f tagtoo-tracking:${INTERMEDIATE_TABLE}
    exit 1
fi

# 2. 從中間表清理數據並寫入臨時表
echo "清理數據並寫入臨時表..."

# 創建臨時表，使用與主表相同的結構
bq mk --table \
    --time_partitioning_field=last_interaction_time \
    --time_partitioning_type=DAY \
    --clustering_fields=${CLUSTER_FIELDS} \
    --schema="ec_id:INTEGER,permanent:STRING,purchase_count:INTEGER,registration_time:TIMESTAMP,first_interaction_time:TIMESTAMP,last_interaction_time:TIMESTAMP,first_purchase_time:TIMESTAMP,last_purchase_time:TIMESTAMP,total_purchase_amount:FLOAT,total_sessions:INTEGER,created_at:TIMESTAMP,updated_at:TIMESTAMP" \
    tagtoo-tracking:${TEMP_TABLE}

if [ $? -ne 0 ]; then
    echo "錯誤：無法創建臨時表"
    bq rm -f tagtoo-tracking:${INTERMEDIATE_TABLE}
    exit 1
fi

# 從中間表提取數據，處理 NULL 和 NaT 值，然後插入到臨時表
bq query --use_legacy_sql=false "
    INSERT INTO \`tagtoo-tracking.${TEMP_TABLE}\`
    (ec_id, permanent, purchase_count, registration_time, first_interaction_time,
    last_interaction_time, first_purchase_time, last_purchase_time,
    total_purchase_amount, total_sessions, created_at, updated_at)
    SELECT
        ec_id,
        permanent,
        IFNULL(purchase_count, 0) as purchase_count,
        registration_time,
        first_interaction_time,
        last_interaction_time,
        first_purchase_time,
        last_purchase_time,
        IFNULL(total_purchase_amount, 0.0) as total_purchase_amount,
        IFNULL(total_sessions, 0) as total_sessions,
        CURRENT_TIMESTAMP() as created_at,
        CURRENT_TIMESTAMP() as updated_at
    FROM \`tagtoo-tracking.${INTERMEDIATE_TABLE}\`
    WHERE last_interaction_time IS NOT NULL
"

if [ $? -ne 0 ]; then
    echo "錯誤：無法從中間表提取清理後的數據"
    bq rm -f tagtoo-tracking:${INTERMEDIATE_TABLE}
    bq rm -f tagtoo-tracking:${TEMP_TABLE}
    exit 1
fi

# 刪除中間表
bq rm -f tagtoo-tracking:${INTERMEDIATE_TABLE}

# 3. 使用 MERGE 語句將臨時表數據合併到主表
echo "執行 MERGE 操作..."
MERGE_QUERY="
MERGE \`tagtoo-tracking.event_prod.user_stats\` T
USING \`tagtoo-tracking.${TEMP_TABLE}\` S
ON T.ec_id = S.ec_id AND T.permanent = S.permanent
WHEN MATCHED THEN
  UPDATE SET
    purchase_count = S.purchase_count,
    registration_time = S.registration_time,
    first_interaction_time = S.first_interaction_time,
    last_interaction_time = S.last_interaction_time,
    first_purchase_time = S.first_purchase_time,
    last_purchase_time = S.last_purchase_time,
    total_purchase_amount = S.total_purchase_amount,
    total_sessions = S.total_sessions,
    updated_at = CURRENT_TIMESTAMP()
WHEN NOT MATCHED THEN
  INSERT (ec_id, permanent, purchase_count, registration_time, first_interaction_time,
          last_interaction_time, first_purchase_time, last_purchase_time,
          total_purchase_amount, total_sessions, created_at, updated_at)
  VALUES (S.ec_id, S.permanent, S.purchase_count, S.registration_time, S.first_interaction_time,
          S.last_interaction_time, S.first_purchase_time, S.last_purchase_time,
          S.total_purchase_amount, S.total_sessions, CURRENT_TIMESTAMP(), CURRENT_TIMESTAMP())
"

bq query --use_legacy_sql=false "${MERGE_QUERY}"

if [ $? -ne 0 ]; then
    echo "錯誤：MERGE 操作失敗"
    bq rm -f tagtoo-tracking:${TEMP_TABLE}
    exit 1
fi

# 獲取更新記錄數
RECORD_COUNT=$(bq query --use_legacy_sql=false --format=json "SELECT COUNT(*) as count FROM \`tagtoo-tracking.${TEMP_TABLE}\`" | jq -r '.[] | .count')

# 4. 刪除臨時表
echo "刪除臨時表..."
bq rm -f tagtoo-tracking:${TEMP_TABLE}

echo "資料已成功通過 MERGE 操作更新到 tagtoo-tracking:event_prod.user_stats"
echo "更新記錄數：${RECORD_COUNT}"
echo "清理臨時檔案..."
rm -f "${LOCAL_PATH}"

echo "完成！"
