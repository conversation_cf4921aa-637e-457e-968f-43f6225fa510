#!/usr/bin/env python
# -*- coding: utf-8 -*-
"""
驗證腳本 1：比較兩個 parquet 檔案

此腳本用於比較兩個 parquet 檔案，確認兩者資料是否一致。
支援兩種模式：
1. 比較本地 parquet 與 GCS 快照
2. 比較兩個本地 parquet 檔案

使用方式 (與 GCS 快照比較):
    python scripts/validation/validate_with_snapshot.py \
        --local-file ./local_data/user_stats_ec107_20250311_154703.parquet \
        --ec-id 107 \
        --snapshot-date 20250310 \
        --report-file ./validation_snapshot_report.html

使用方式 (比較兩個本地檔案):
    python scripts/validation/validate_with_snapshot.py \
        --local-file ./scripts/local_data/user_stats_ec107_20250311_154703.parquet \
        --second-file ./scripts/local_data/20250310.parquet \
        --report-file ./validation_snapshot_report.html

選項:
    --local-file PATH: 必填，第一個本地 parquet 檔案路徑
    --ec-id EC_ID: 與 GCS 快照比較時必填，電商 ID
    --snapshot-date DATE: 與 GCS 快照比較時必填，快照日期 (YYYYMMDD 格式)
    --second-file PATH: 比較兩個本地檔案時必填，第二個本地 parquet 檔案路徑
    --report-file PATH: 選填，HTML 報告輸出路徑
    --sample-rate PERCENT: 選填，抽樣比率 (1-100)，默認為 100 (全部比較)
    --max-compare INT: 選填，最大比較數量，默認為 10000
"""

import argparse
import os
import sys
import time
from datetime import datetime
from pathlib import Path
import logging
import pandas as pd
import numpy as np
from google.cloud import storage
import multiprocessing
from concurrent.futures import ProcessPoolExecutor, as_completed
import tqdm

# 將專案根目錄加入 sys.path
ROOT_DIR = Path(__file__).parent.parent.parent.absolute()
sys.path.append(str(ROOT_DIR))

from src.utils import logging_setup

# 設定 logger
logger = logging.getLogger()
formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s')

if not logger.handlers:
    handler = logging.StreamHandler()
    handler.setFormatter(formatter)
    logger.addHandler(handler)
else:
    logger.handlers[0].setFormatter(formatter)

logger.setLevel(logging.INFO)

def parse_args():
    """解析命令行參數"""
    parser = argparse.ArgumentParser(description='比較兩個 parquet 檔案')
    parser.add_argument('--local-file', type=str, required=True, help='第一個本地 parquet 檔案路徑')
    parser.add_argument('--ec-id', type=int, help='電商 ID (與 GCS 快照比較時必填)')
    parser.add_argument('--snapshot-date', type=str, help='快照日期 (YYYYMMDD 格式，與 GCS 快照比較時必填)')
    parser.add_argument('--second-file', type=str, help='第二個本地 parquet 檔案路徑 (比較兩個本地檔案時必填)')
    parser.add_argument('--report-file', type=str, default='./validation_snapshot_report.html', help='HTML 報告輸出路徑')
    parser.add_argument('--sample-rate', type=float, default=100.0, help='抽樣比率 (1-100)，默認為 100 (全部比較)')
    parser.add_argument('--max-compare', type=int, default=10000, help='最大比較數量，默認為 10000')
    return parser.parse_args()

def download_snapshot(ec_id, snapshot_date):
    """從 GCS 下載快照

    Args:
        ec_id: 電商 ID
        snapshot_date: 快照日期 (YYYYMMDD 格式)

    Returns:
        pd.DataFrame: 快照的 DataFrame
    """
    # 設定 GCS 路徑
    bucket_name = "mlfoundation-demo"
    blob_name = f"lta_user_stats/snapshots/ec_{ec_id}/user_stats_{snapshot_date}.parquet"
    local_path = f"/tmp/snapshot_{ec_id}_{snapshot_date}.parquet"

    logger.info(f"開始從 GCS 下載快照: gs://{bucket_name}/{blob_name}")

    # 下載檔案
    try:
        client = storage.Client()
        bucket = client.bucket(bucket_name)
        blob = bucket.blob(blob_name)

        if not blob.exists():
            # 嘗試找其他可能的路徑
            alternative_path = f"lta_user_stats/snapshots/ec{ec_id}/user_stats_{snapshot_date}.parquet"
            logger.info(f"找不到快照，嘗試備用路徑: gs://{bucket_name}/{alternative_path}")
            blob = bucket.blob(alternative_path)
            if not blob.exists():
                logger.error(f"找不到快照: gs://{bucket_name}/{blob_name} 或 gs://{bucket_name}/{alternative_path}")
                sys.exit(1)

        blob.download_to_filename(local_path)
        logger.info(f"快照已下載到: {local_path}")

        # 讀取 parquet 檔案
        df = pd.read_parquet(local_path)
        logger.info(f"成功讀取快照: {len(df)} 筆用戶資料")

        return df
    except Exception as e:
        logger.error(f"下載或讀取快照時發生錯誤: {str(e)}")
        sys.exit(1)

# 將 compare_batch 函數移至外部，以便多處理程序可以使用
def compare_batch(batch, df1_indexed, df2_indexed, numeric_columns, datetime_columns, other_columns):
    """比較一批用戶"""
    batch_results = []

    for user_id in batch:
        if user_id not in df1_indexed.index or user_id not in df2_indexed.index:
            continue

        row1 = df1_indexed.loc[user_id]
        row2 = df2_indexed.loc[user_id]

        user_result = {
            'permanent': user_id,
            'match': True,
            'mismatches': []
        }

        # 比較數值欄位
        for col in numeric_columns:
            if col in row1 and col in row2:
                val1 = float(row1.get(col, 0) or 0)
                val2 = float(row2.get(col, 0) or 0)

                # 檢查數值是否相等（容許微小差異）
                if abs(val1 - val2) > 1e-6:
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': val1,
                        'file2_value': val2,
                        'difference': val1 - val2,
                        'pct_difference': ((val1 - val2) / val2 * 100) if val2 != 0 else None
                    })

        # 比較時間欄位
        for col in datetime_columns:
            if col in row1 and col in row2:
                val1 = row1.get(col)
                val2 = row2.get(col)

                # 如果兩者都是 None 或 NaT，視為相等
                if pd.isna(val1) and pd.isna(val2):
                    continue

                # 如果只有一個是 None 或 NaT，視為不等
                if pd.isna(val1) != pd.isna(val2):
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': str(val1),
                        'file2_value': str(val2),
                        'difference': 'NULL 值不一致'
                    })
                    continue

                # 否則，比較時間值
                try:
                    dt1 = pd.Timestamp(val1).tz_convert(None) if hasattr(val1, 'tz_convert') else pd.Timestamp(val1)
                    dt2 = pd.Timestamp(val2).tz_convert(None) if hasattr(val2, 'tz_convert') else pd.Timestamp(val2)

                    if dt1 != dt2:
                        time_diff_seconds = (dt1 - dt2).total_seconds()

                        # 如果時差超過 1 秒，視為不等
                        if abs(time_diff_seconds) > 1:
                            user_result['match'] = False
                            user_result['mismatches'].append({
                                'column': col,
                                'file1_value': str(val1),
                                'file2_value': str(val2),
                                'difference': f"{time_diff_seconds} 秒"
                            })
                except Exception as e:
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': str(val1),
                        'file2_value': str(val2),
                        'difference': f"比較錯誤: {str(e)}"
                    })

        # 比較其他欄位 (直接比較)
        for col in other_columns:
            if col in row1 and col in row2:
                val1 = row1.get(col)
                val2 = row2.get(col)

                # 如果兩者都是 None，視為相等
                if pd.isna(val1) and pd.isna(val2):
                    continue

                # 如果只有一個是 None，視為不等
                if pd.isna(val1) != pd.isna(val2):
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': str(val1),
                        'file2_value': str(val2),
                        'difference': 'NULL 值不一致'
                    })
                    continue

                # 直接比較值
                if str(val1) != str(val2):
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': str(val1),
                        'file2_value': str(val2),
                        'difference': '值不同'
                    })

        batch_results.append(user_result)

    return batch_results

def compare_dataframes(df1, df2, sample_rate=100.0, max_compare=10000):
    """比較兩個 DataFrame

    Args:
        df1: 第一個 DataFrame
        df2: 第二個 DataFrame
        sample_rate: 抽樣比率 (1-100)，默認為 100 (全部比較)
        max_compare: 最大比較數量，默認為 10000

    Returns:
        dict: 比較結果
    """
    # 確保兩個 DataFrame 都有 permanent 欄位
    if 'permanent' not in df1.columns:
        logger.error(f"第一個檔案缺少 permanent 欄位，無法進行比較")
        sys.exit(1)
    if 'permanent' not in df2.columns:
        logger.error(f"第二個檔案缺少 permanent 欄位，無法進行比較")
        sys.exit(1)

    logger.info(f"正在比較兩個 DataFrame...")
    logger.info(f"第一個檔案: {len(df1)} 筆資料")
    logger.info(f"第二個檔案: {len(df2)} 筆資料")

    # 找出共同的用戶 ID
    common_users = set(df1['permanent']) & set(df2['permanent'])

    logger.info(f"共同用戶: {len(common_users)} 筆")

    if len(common_users) == 0:
        logger.error("兩個檔案中沒有共同的用戶，無法進行比較")
        sys.exit(1)

    # 根據抽樣比率限制比較數量
    if sample_rate < 100 or len(common_users) > max_compare:
        if sample_rate < 100:
            sample_size = int(len(common_users) * sample_rate / 100)
            logger.info(f"抽樣比較: {sample_rate}% = {sample_size} 筆用戶")
        else:
            sample_size = min(len(common_users), max_compare)
            logger.info(f"限制最大比較數量: {sample_size} 筆用戶")

        # 隨機抽樣
        import random
        comparison_users = list(random.sample(list(common_users), sample_size))
    else:
        comparison_users = list(common_users)
        logger.info(f"比較全部共同用戶: {len(comparison_users)} 筆")

    # 識別共同欄位
    common_columns = set(df1.columns) & set(df2.columns)
    logger.info(f"共同欄位: {len(common_columns)}")

    # 區分不同類型的欄位
    numeric_columns = []
    datetime_columns = []
    other_columns = []

    for col in common_columns:
        if col == 'permanent':
            continue  # 排除用戶 ID 欄位

        # 檢查兩個 DataFrame 中的欄位類型
        dtype1 = df1[col].dtype
        dtype2 = df2[col].dtype

        if pd.api.types.is_numeric_dtype(dtype1) and pd.api.types.is_numeric_dtype(dtype2):
            numeric_columns.append(col)
        elif 'datetime' in str(dtype1) or 'datetime' in str(dtype2):
            datetime_columns.append(col)
        else:
            other_columns.append(col)

    logger.info(f"數值欄位: {len(numeric_columns)} 個")
    logger.info(f"時間欄位: {len(datetime_columns)} 個")
    logger.info(f"其他欄位: {len(other_columns)} 個")

    # 初始化結果
    results = {
        'users': [],
        'numeric_columns': numeric_columns,
        'datetime_columns': datetime_columns,
        'other_columns': other_columns,
        'summary': {
            'total_users': len(comparison_users),
            'matching_users': 0,
            'mismatching_users': 0,
            'column_mismatches': {}
        }
    }

    # 初始化欄位不匹配計數
    for col in numeric_columns + datetime_columns + other_columns:
        results['summary']['column_mismatches'][col] = 0

    # 建立索引加速比對
    logger.info("建立索引加速比對...")
    df1_indexed = df1.set_index('permanent')
    df2_indexed = df2.set_index('permanent')

    # 由於多處理程序速度較慢，改用單線程處理
    logger.info("開始比較用戶資料...")

    # 直接循環處理每個用戶
    all_user_results = []

    # 使用進度條
    from tqdm import tqdm
    for user_id in tqdm(comparison_users, desc="比對進度"):
        if user_id not in df1_indexed.index or user_id not in df2_indexed.index:
            continue

        row1 = df1_indexed.loc[user_id]
        row2 = df2_indexed.loc[user_id]

        user_result = {
            'permanent': user_id,
            'match': True,
            'mismatches': []
        }

        # 比較數值欄位
        for col in numeric_columns:
            if col in row1 and col in row2:
                val1 = float(row1.get(col, 0) or 0)
                val2 = float(row2.get(col, 0) or 0)

                # 檢查數值是否相等（容許微小差異）
                if abs(val1 - val2) > 1e-6:
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': val1,
                        'file2_value': val2,
                        'difference': val1 - val2,
                        'pct_difference': ((val1 - val2) / val2 * 100) if val2 != 0 else None
                    })

        # 比較時間欄位
        for col in datetime_columns:
            if col in row1 and col in row2:
                val1 = row1.get(col)
                val2 = row2.get(col)

                # 如果兩者都是 None 或 NaT，視為相等
                if pd.isna(val1) and pd.isna(val2):
                    continue

                # 如果只有一個是 None 或 NaT，視為不等
                if pd.isna(val1) != pd.isna(val2):
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': str(val1),
                        'file2_value': str(val2),
                        'difference': 'NULL 值不一致'
                    })
                    continue

                # 否則，比較時間值
                try:
                    dt1 = pd.Timestamp(val1).tz_convert(None) if hasattr(val1, 'tz_convert') else pd.Timestamp(val1)
                    dt2 = pd.Timestamp(val2).tz_convert(None) if hasattr(val2, 'tz_convert') else pd.Timestamp(val2)

                    if dt1 != dt2:
                        time_diff_seconds = (dt1 - dt2).total_seconds()

                        # 如果時差超過 1 秒，視為不等
                        if abs(time_diff_seconds) > 1:
                            user_result['match'] = False
                            user_result['mismatches'].append({
                                'column': col,
                                'file1_value': str(val1),
                                'file2_value': str(val2),
                                'difference': f"{time_diff_seconds} 秒"
                            })
                except Exception as e:
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': str(val1),
                        'file2_value': str(val2),
                        'difference': f"比較錯誤: {str(e)}"
                    })

        # 比較其他欄位 (直接比較)
        for col in other_columns:
            if col in row1 and col in row2:
                val1 = row1.get(col)
                val2 = row2.get(col)

                # 如果兩者都是 None，視為相等
                if pd.isna(val1) and pd.isna(val2):
                    continue

                # 如果只有一個是 None，視為不等
                if pd.isna(val1) != pd.isna(val2):
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': str(val1),
                        'file2_value': str(val2),
                        'difference': 'NULL 值不一致'
                    })
                    continue

                # 直接比較值
                if str(val1) != str(val2):
                    user_result['match'] = False
                    user_result['mismatches'].append({
                        'column': col,
                        'file1_value': str(val1),
                        'file2_value': str(val2),
                        'difference': '值不同'
                    })

        all_user_results.append(user_result)

    # 整合結果
    results['users'] = all_user_results

    # 計算統計資料
    for user_result in results['users']:
        if user_result['match']:
            results['summary']['matching_users'] += 1
        else:
            results['summary']['mismatching_users'] += 1

            # 更新欄位不匹配計數
            for mismatch in user_result['mismatches']:
                col = mismatch['column']
                if col in results['summary']['column_mismatches']:
                    results['summary']['column_mismatches'][col] += 1

    logger.info(f"比較完成: {results['summary']['matching_users']} 筆匹配，{results['summary']['mismatching_users']} 筆不匹配")

    return results

def generate_html_report(results, file1_path, file2_path, report_file):
    """生成 HTML 比較報告

    Args:
        results: 比較結果
        file1_path: 第一個檔案路徑
        file2_path: 第二個檔案路徑
        report_file: 報告輸出路徑

    Returns:
        str: 報告檔案路徑
    """
    # 摘要統計
    summary = results['summary']
    matching_percent = (summary['matching_users'] / summary['total_users'] * 100) if summary['total_users'] > 0 else 0

    # 計算總體匹配度
    overall_status = 'success' if matching_percent >= 95 else 'warning' if matching_percent >= 80 else 'error'

    # 生成欄位不匹配的表格
    col_mismatch_rows = ""
    for col, count in summary['column_mismatches'].items():
        if count > 0:
            mismatch_percent = (count / summary['total_users'] * 100)
            status_class = 'success' if mismatch_percent < 5 else 'warning' if mismatch_percent < 20 else 'error'

            col_mismatch_rows += f"""
            <tr>
                <td>{col}</td>
                <td>{count}</td>
                <td class="{status_class}">{mismatch_percent:.2f}%</td>
            </tr>
            """

    # 生成不匹配用戶的表格
    mismatched_users_html = ""
    if summary['mismatching_users'] > 0:
        mismatched_users = [user for user in results['users'] if not user['match']]
        mismatched_users = sorted(mismatched_users, key=lambda u: len(u['mismatches']), reverse=True)  # 按不匹配數排序

        for user in mismatched_users[:10]:  # 限制只顯示前 10 個，避免報告太長
            mismatches_html = ""
            for mismatch in user['mismatches']:
                file1_value = mismatch.get('file1_value', 'N/A')
                file2_value = mismatch.get('file2_value', 'N/A')
                difference = mismatch.get('difference', 'N/A')
                pct_difference = f"{mismatch.get('pct_difference', 'N/A'):.2f}%" if mismatch.get('pct_difference') is not None else 'N/A'

                mismatches_html += f"""
                <tr>
                    <td>{mismatch['column']}</td>
                    <td>{file1_value}</td>
                    <td>{file2_value}</td>
                    <td>{difference}</td>
                    <td>{pct_difference}</td>
                </tr>
                """

            mismatched_users_html += f"""
            <div class="user-mismatch">
                <h4>用戶 ID: {user['permanent']}</h4>
                <table>
                    <tr>
                        <th>欄位</th>
                        <th>檔案 1 值</th>
                        <th>檔案 2 值</th>
                        <th>差異</th>
                        <th>差異百分比</th>
                    </tr>
                    {mismatches_html}
                </table>
            </div>
            """

        if len(mismatched_users) > 10:
            mismatched_users_html += f"<p>還有 {len(mismatched_users) - 10} 個不匹配的用戶未顯示...</p>"

    # 完整 HTML
    html_content = f"""
    <html>
    <head>
        <title>Parquet 檔案比較報告</title>
        <style>
            body {{ font-family: Arial, sans-serif; margin: 20px; }}
            h1, h2, h3, h4 {{ color: #333; }}
            .summary {{ background-color: #f9f9f9; padding: 15px; border-radius: 5px; margin-bottom: 20px; }}
            .warning {{ color: orange; }}
            .error {{ color: red; }}
            .success {{ color: green; }}
            table {{ border-collapse: collapse; margin: 15px 0; width: 100%; }}
            th, td {{ border: 1px solid #ddd; padding: 8px; text-align: left; }}
            th {{ background-color: #f2f2f2; }}
            .user-mismatch {{ margin-bottom: 30px; }}
        </style>
    </head>
    <body>
        <h1>Parquet 檔案比較報告</h1>
        <p>生成時間: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}</p>

        <h2>檔案資訊</h2>
        <p>檔案 1: {file1_path}</p>
        <p>檔案 2: {file2_path}</p>

        <div class="summary">
            <h2>比較摘要</h2>
            <p>總比較用戶數: {summary['total_users']}</p>
            <p>完全匹配用戶數: {summary['matching_users']} ({matching_percent:.2f}%)</p>
            <p>不匹配用戶數: {summary['mismatching_users']} ({100 - matching_percent:.2f}%)</p>
            <p>整體匹配狀態: <span class="{overall_status}">
                {
                    "完全匹配" if overall_status == 'success' else
                    "部分匹配" if overall_status == 'warning' else
                    "顯著差異"
                }
            </span></p>

            <h3>欄位不匹配統計</h3>
            <table>
                <tr>
                    <th>欄位</th>
                    <th>不匹配用戶數</th>
                    <th>不匹配百分比</th>
                </tr>
                {col_mismatch_rows if col_mismatch_rows else "<tr><td colspan='3'>所有欄位都完全匹配</td></tr>"}
            </table>
        </div>

        <h2>不匹配用戶詳情</h2>
        {mismatched_users_html if summary['mismatching_users'] > 0 else "<p>所有用戶都完全匹配！</p>"}
    </body>
    </html>
    """

    # 寫入檔案
    with open(report_file, 'w') as f:
        f.write(html_content)

    logger.info(f"已生成報告: {report_file}")
    return report_file

def main():
    """主函數"""
    # 解析命令列參數
    args = parse_args()

    # 檢查本地檔案是否存在
    if not os.path.exists(args.local_file):
        logger.error(f"第一個本地檔案不存在: {args.local_file}")
        sys.exit(1)

    start_time = time.time()

    try:
        # 讀取第一個本地 parquet 檔案
        logger.info(f"正在讀取第一個本地檔案: {args.local_file}")
        df1 = pd.read_parquet(args.local_file)
        logger.info(f"檔案 1 共有 {len(df1)} 筆資料")

        # 確定比較模式 (GCS 快照或本地檔案)
        if args.second_file:
            # 模式 2: 比較兩個本地檔案
            if not os.path.exists(args.second_file):
                logger.error(f"第二個本地檔案不存在: {args.second_file}")
                sys.exit(1)

            logger.info(f"正在讀取第二個本地檔案: {args.second_file}")
            df2 = pd.read_parquet(args.second_file)
            file2_path = args.second_file
        elif args.ec_id and args.snapshot_date:
            # 模式 1: 比較本地檔案與 GCS 快照
            df2 = download_snapshot(args.ec_id, args.snapshot_date)
            file2_path = f"GCS 快照: ec_{args.ec_id}/user_stats_{args.snapshot_date}.parquet"
        else:
            logger.error("必須指定 --second-file 或同時指定 --ec-id 和 --snapshot-date")
            sys.exit(1)

        logger.info(f"檔案 2 共有 {len(df2)} 筆資料")

        # 比較兩個 DataFrame
        results = compare_dataframes(df1, df2, args.sample_rate, args.max_compare)

        # 生成報告
        report_path = generate_html_report(results, args.local_file, file2_path, args.report_file)

        # 計算總耗時
        total_time = time.time() - start_time
        logger.info(f"完成驗證，總耗時: {total_time:.2f} 秒")
        logger.info(f"匹配率: {results['summary']['matching_users']} / {results['summary']['total_users']} ({results['summary']['matching_users'] / results['summary']['total_users'] * 100:.2f}%)")

        # 嘗試在瀏覽器中打開報告
        if sys.stdout.isatty():
            try:
                import webbrowser
                logger.info(f"嘗試在瀏覽器中打開報告...")
                webbrowser.open(f"file://{os.path.abspath(report_path)}")
            except Exception as e:
                logger.warning(f"無法自動打開報告: {str(e)}")

        # 根據匹配度設置退出碼
        if results['summary']['matching_users'] == results['summary']['total_users']:
            logger.info("驗證成功：所有用戶都完全匹配！")
            sys.exit(0)
        elif results['summary']['matching_users'] / results['summary']['total_users'] >= 0.95:
            logger.warning("驗證通過：大部分用戶匹配，但有少量差異")
            sys.exit(0)
        else:
            logger.error("驗證失敗：存在顯著差異")
            sys.exit(1)
    except Exception as e:
        logger.error(f"驗證過程中發生錯誤: {str(e)}")
        import traceback
        logger.error(traceback.format_exc())
        sys.exit(1)

if __name__ == "__main__":
    main()