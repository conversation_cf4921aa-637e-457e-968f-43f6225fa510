# EC ID 用戶統計初始化與管理腳本

這個目錄包含了用於管理特定 EC ID 的用戶統計資料的相關腳本。

## 使用情境

### 新增電商時的初始化流程

當需要為新的電商加入用戶統計功能時，需要執行以下步驟：

1. **初始化歷史資料**：
   - 使用 `init_ec_id.sh` 初始化該電商的歷史用戶統計資料
   - 預設會處理從 2022/01/01 00:00:00 到執行當天 00:00:00 的資料
   - 例如：在 2025/03/18 執行時，會處理 [2022/01/01 00:00:00, 2025/03/18 00:00:00) 的資料

2. **設定排程任務**：
   - 初始化完成後，需要設定 Cloud Scheduler
   - 使用新的 EC ID 作為參數來觸發 Cloud Function
   - 建議設定在每日台灣時間 01:30 執行
   - 這樣可以確保每日的資料都能被正確計算

### 時間規劃說明

以 2025/03/18 新增電商為例：

1. **初始化階段**：
   - 執行 `init_ec_id.sh`
   - 處理時間範圍：[2022/01/01 00:00:00, 2025/03/18 00:00:00)
   - 資料來源：tagtoo_event 表

2. **每日排程階段**：
   - 第一次執行時間：2025/03/19 01:30
   - 會處理 2025/03/18 的資料
   - 之後每天同一時間執行，處理前一天的資料

### 其他使用情境

1. **資料修復**：
   - 當發現某個時間區間的資料有誤時
   - 可以使用 `--start-date` 和 `--end-date` 參數指定要重新計算的時間範圍

2. **資料驗證**：
   - 使用 `--debug` 或 `--local-parquet` 選項
   - 可以在不影響生產環境的情況下驗證計算結果

## 初始化用戶統計資料

### 腳本簡介

- `init_user_stats_for_ec_id.py` - Python 主腳本，負責查詢數據、計算統計資料並儲存結果
- `init_ec_id.sh` - Shell 包裝腳本，提供更簡易的使用方式
  - 記得要加上以下兩個參數才會真的影響到 production 的資料
    - `--enable-bigquery-upload` 啟用寫入 BigQuery (默認不啟用)
    - `--enable-gcs-upload` 啟用寫入 GCS (默認不啟用)
  - 如果需要測試，可以加上 `--debug` 參數，這樣不會真的寫入資料庫，但會產生本地 parquet 檔案
- `run_bigquery_tests.sh` - 測試腳本，用於驗證 BigQuery 直接計算功能的正確性

### 使用方式

#### 使用 Shell 腳本（推薦）

```bash
# 初始化 EC ID 107 的用戶統計資料，時間從 2022-01-01 開始
./init_ec_id.sh 107

# 初始化 EC ID 107 的用戶統計資料，時間從 2023-06-01 開始
./init_ec_id.sh 107 2023-06-01

# 初始化 EC ID 107 的用戶統計資料，但不寫入 BigQuery
./init_ec_id.sh 107 --skip-bigquery

# 初始化 EC ID 107 的用戶統計資料，但不寫入 GCS
./init_ec_id.sh 107 --skip-gcs

# 測試模式，不寫入任何資料庫，但會產生本地 parquet 檔案
./init_ec_id.sh 107 --debug

# 初始化 EC ID 107 的用戶統計資料，並產生本地 parquet 檔案
./init_ec_id.sh 107 --local-parquet

# 初始化 EC ID 107 的用戶統計資料，並指定本地 parquet 檔案儲存路徑
./init_ec_id.sh 107 --local-parquet --local-path /tmp/my_data

# 指定自定義輸出檔案名稱
./init_ec_id.sh 107 --local-parquet --output-filename custom_name.parquet

# 指定日期範圍並自定義輸出檔案名稱
./init_ec_id.sh 107 --start-date 2022-01-01 --end-date 2025-03-12 --local-parquet --output-filename 20250311.parquet

# 自動確認執行高成本查詢，跳過費用確認提示
./init_ec_id.sh 107 --force-query

# 使用 BigQuery 直接計算用戶統計資料（無需下載原始數據）
./init_ec_id.sh 107 --use-bigquery-compute

# 查看所有可用選項
./init_ec_id.sh --help
```

#### 直接使用 Python 腳本

```bash
# 初始化 EC ID 107 的用戶統計資料，時間從 2022-01-01 開始
python init_user_stats_for_ec_id.py --ec-id 107

# 初始化 EC ID 107 的用戶統計資料，時間從 2023-06-01 開始
python init_user_stats_for_ec_id.py --ec-id 107 --start-date 2023-06-01

# 初始化 EC ID 107 的用戶統計資料，但不寫入 BigQuery
python init_user_stats_for_ec_id.py --ec-id 107 --skip-bigquery

# 初始化 EC ID 107 的用戶統計資料，但不寫入 GCS
python init_user_stats_for_ec_id.py --ec-id 107 --skip-gcs

# 測試模式，不寫入任何資料庫，但會產生本地 parquet 檔案
python init_user_stats_for_ec_id.py --ec-id 107 --debug

# 初始化 EC ID 107 的用戶統計資料，並產生本地 parquet 檔案
python init_user_stats_for_ec_id.py --ec-id 107 --local-parquet

# 初始化 EC ID 107 的用戶統計資料，並指定本地 parquet 檔案儲存路徑
python init_user_stats_for_ec_id.py --ec-id 107 --local-parquet --local-path /tmp/my_data

# 指定自定義輸出檔案名稱
python init_user_stats_for_ec_id.py --ec-id 107 --local-parquet --output-filename custom_name.parquet

# 使用 BigQuery 直接計算用戶統計資料（無需下載原始數據）
python init_user_stats_for_ec_id.py --ec-id 107 --use-bigquery-compute

# 指定日期範圍並自定義輸出檔案名稱
python init_user_stats_for_ec_id.py \
  --ec-id 107 \
  --start-date 2022-01-01 \
  --end-date 2025-03-12 \
  --local-parquet \
  --output-filename 20250311.parquet \
  --skip-bigquery \
  --skip-gcs
```

### 腳本功能

這些腳本會：

1. 從 BigQuery 的 `tagtoo-tracking.event_prod.tagtoo_event` 表中查詢指定 EC ID 從特定日期到前一天的所有資料
2. 計算該 EC ID 的用戶統計資料（可選擇本地計算或 BigQuery 直接計算）
3. 將統計資料寫入 BigQuery 的 `event_prod.user_stats` 表（除非使用 `--skip-bigquery` 選項）
4. 將統計資料保存為 parquet 檔案並上傳到 GCS（除非使用 `--skip-gcs` 選項）
5. 選擇性地將統計資料保存為本地 parquet 檔案（使用 `--local-parquet` 選項，或在 `--debug` 模式下自動啟用）
6. **多進程平行處理**：使用多進程並行處理大量資料，提高處理效率（自動根據 CPU 核心數調整）

### BigQuery 直接計算功能

腳本現在支援直接使用 BigQuery 進行用戶統計數據計算，無需下載原始資料：

1. **使用方式**：添加 `--use-bigquery-compute` 參數即可啟用
2. **優點**：
   - 大幅減少數據傳輸量，適合處理大型資料集
   - 利用 BigQuery 的分布式計算能力，提高計算效率
   - 減少本地記憶體使用，避免記憶體溢出問題
3. **工作原理**：
   - 使用精心優化的 SQL 查詢在 BigQuery 中執行統計計算
   - 利用 Common Table Expressions (CTEs) 結構化計算流程
   - 直接返回計算結果，而非原始事件數據
4. **測試與驗證**：
   - 使用 `run_bigquery_tests.sh` 腳本進行功能測試
   - 包含單元測試和 SQL 查詢結構測試
   - 確保計算結果與本地處理一致

### 平行處理功能

腳本內建多進程平行處理功能，提供以下優勢：

1. **顯著提升處理速度**：使用多核心處理大型資料集，比單線程處理快數倍
2. **自動優化**：根據可用 CPU 核心數自動調整工作進程數（最多使用 8 個進程）
3. **批次處理**：將資料分成多個批次（預設每批 50,000 行），由不同進程平行處理
4. **動態展示進度**：顯示各批次處理進度和預估完成時間
5. **錯誤隔離**：單一批次的處理錯誤不會影響其他批次

平行處理功能尤其適合處理大型查詢結果（百萬行級別），可以大幅縮短數據處理時間。

### 查詢費用估算功能

腳本現在會在執行 BigQuery 查詢前自動估算查詢費用，以防止意外執行高成本查詢：

1. 在查詢執行前，會顯示預估的數據處理量（位元組數和 GB 數）以及新台幣成本
2. 若估算費用超過 NT$30.00，則會顯示警告訊息
3. 若估算費用超過 NT$150.00，則會提示用戶確認是否繼續查詢
4. 可使用 `--force-query` 選項跳過確認提示，自動執行任何查詢（適用於無人值守情境）

費用估算使用 BigQuery 的 `dry_run` 功能，不會實際執行查詢，因此不會產生任何實際費用。

#### BigQuery 直接計算的費用估算

當使用 `--use-bigquery-compute` 參數時，系統會在執行實際計算前自動進行查詢費用估算：

1. **即時顯示查詢成本**：會顯示預估處理的資料量（以位元組為單位，並轉換為易讀格式，如 GB）
2. **自動成本警告**：若預估費用超過閾值，系統會自動提供警告
3. **互動式確認**：對於高成本查詢（NT$150.00 以上），系統會要求用戶手動確認後才繼續執行
4. **自動化支援**：可以使用 `--force-query` 參數跳過確認提示，適合用於自動化腳本或無人值守環境

這個功能可以：
- 為用戶提供透明的成本預估
- 避免意外執行高成本查詢
- 在保持自動化能力的同時確保成本控制
- 提高工作流程的可靠性和可預測性

### 時間範圍說明

腳本默認會處理從 2022-01-01 00:00:00 (台灣時間) 到查詢當下台灣時間的**前一天** 00:00:00 的資料。

例如：
- 如果今天是 2024-03-07，腳本會處理 2022-01-01 到 2024-03-06 00:00:00 之間的資料
- 這樣可以確保使用完整的昨天資料進行計算

### 選項說明

| 選項 | 說明 |
| --- | --- |
| `--ec-id EC_ID` | 必填，電商 ID |
| `--start-date DATE` | 選填，開始日期，默認為 2022-01-01，格式為 YYYY-MM-DD |
| `--end-date DATE` | 選填，結束日期，預設為查詢當下的前一天，格式為 YYYY-MM-DD |
| `--skip-bigquery` | 選填，跳過寫入 BigQuery |
| `--skip-gcs` | 選填，跳過寫入 GCS |
| `--debug` | 選填，測試模式，相當於同時使用 `--skip-bigquery` 和 `--skip-gcs`，自動啟用本地 parquet 產生 |
| `--local-parquet` | 選填，產生本地 parquet 檔案，便於驗算 |
| `--local-path PATH` | 選填，指定本地 parquet 檔案的儲存路徑，默認為 `./local_data` |
| `--output-filename FILENAME` | 選填，指定輸出的 parquet 檔案名稱，默認為自動生成的時間戳檔名 |
| `--force-query` | 選填，自動確認執行高成本查詢，跳過費用確認提示（與 `--use-bigquery-compute` 搭配使用時特別有用） |
| `--use-bigquery-compute` | 選填，使用 BigQuery 直接計算用戶統計資料，無需下載原始數據 |

## 受眾規則管理

### 腳本簡介

- `audience_rules.json` - 包含各電商的受眾規則定義
- `upload_rules.py` - 負責將受眾規則上傳至 GCS，並更新 CDN 快取

### 使用方式

```bash
# 更新受眾規則到 GCS
python upload_rules.py
```

(上傳之前記得先更新 `audience_rules.json` 檔案且確認無誤，運算條件的部分是使用 JSON Logic 格式表達條件。 )

### 功能說明

這組腳本的功能包括：

1. `audience_rules.json` 定義各電商的受眾分群規則，支援 JSON Logic 格式表達條件
2. `upload_rules.py` 將規則檔案上傳至 GCS（`gs://tagtoo-ml-workflow/LTA/user_stats_configs/audience_rules.json`）
3. 自動加入部署時間與來源的 metadata

**提醒**：記得要:

1. 去 TTD Taxonomy 的 All 大表：[TTD Taxonomy All 大表](https://docs.google.com/spreadsheets/d/1BtG2GdoV7B2Cu5Ej7B49VU8z6MZyFqGRyjOSX5emp2Q/edit?gid=*********#gid=*********)，並在取得上傳受眾的 Audience_id 之後回填到 All 大表的 Audience_id 欄位

2. 再使用 [semi-automation-tools/create_meta_audience](https://github.com/Tagtoo/ml-workflow-semi-automation-tools/tree/main/create_meta_audience) 把受眾相關資料上傳到 Meta 和更新資料到「分析報告」表單

3. 將新的 ec_id 加入 terraform.tf 的 ec_ids default 中。

## 更新特定欄位資料

### 腳本簡介

- `update_fields.sh` - 更新特定 EC ID 的特定欄位資料
- `update_fields_for_ec_id.py` - Python 主腳本，負責更新特定欄位的資料

### 使用方式

```bash
# 更新 EC ID 107 的購物車相關欄位
./update_fields.sh --ec-ids=107 --fields=first_add_to_cart_time,last_add_to_cart_time

# 更新 EC ID 107 的瀏覽商品相關欄位
./update_fields.sh --ec-ids=107 --fields=first_view_item_time,last_view_item_time

# 更新多個 EC ID 的多個欄位
./update_fields.sh --ec-ids=107,108,109 --fields=first_add_to_cart_time,last_add_to_cart_time,first_view_item_time,last_view_item_time

# 指定時間範圍更新欄位
./update_fields.sh --ec-ids=107 --fields=first_add_to_cart_time,last_add_to_cart_time --start-date=2023-01-01 --end-date=2023-12-31

# 先執行 dry run 估算查詢成本
./update_fields.sh --ec-ids=107 --fields=first_add_to_cart_time,last_add_to_cart_time --dry-run

# 不使用 BigQuery 直接計算統計資料（預設是使用）
./update_fields.sh --ec-ids=107 --fields=first_add_to_cart_time,last_add_to_cart_time --no-use-bigquery-compute

# 設定日誌級別
./update_fields.sh --ec-ids=107 --fields=first_add_to_cart_time,last_add_to_cart_time --log-level=DEBUG
```

### 功能說明

此腳本用於更新特定 EC ID 的特定欄位資料，特別適用於新增欄位後的資料填充：

1. 支援選擇性更新特定欄位，無需重新計算所有統計資料
2. 可以指定時間範圍，只處理特定時間段的資料
3. 支援更新多個 EC ID 的資料
4. 提供 dry run 模式，可以先估算查詢成本
5. 支援使用 BigQuery 直接計算統計資料，無需下載原始數據
6. 可以設定日誌級別，方便調試和監控

### 選項說明

| 選項 | 說明 |
| --- | --- |
| `--ec-ids=EC_IDS` | 必填，要更新的電商 ID，多個 ID 以逗號分隔 |
| `--fields=FIELDS` | 必填，要更新的欄位，多個欄位以逗號分隔 |
| `--start-date=DATE` | 選填，開始日期，默認為兩年前，格式為 YYYY-MM-DD |
| `--end-date=DATE` | 選填，結束日期，默認為今天，格式為 YYYY-MM-DD |
| `--dry-run` | 選填，僅執行查詢估算，不實際更新資料 |
| `--max-rows=ROWS` | 選填，最大處理資料列數 |
| `--use-bigquery-compute` | 選填，使用 BigQuery 直接計算統計資料（預設啟用） |
| `--no-use-bigquery-compute` | 選填，不使用 BigQuery 直接計算統計資料 |
| `--log-level=LEVEL` | 選填，日誌級別，可選值為 DEBUG、INFO、WARNING、ERROR，默認為 INFO |

## 用戶統計資料還原

### 腳本簡介

- `restore_user_stats.sh` - 從 GCS 的 snapshot 檔案還原用戶統計資料到 BigQuery

### 使用方式

```bash
# 從昨天的快照還原所有電商的用戶統計資料
./restore_user_stats.sh

# 從指定日期的快照還原所有電商的用戶統計資料
./restore_user_stats.sh 20240301

# 從昨天的快照還原特定電商 (107) 的用戶統計資料
./restore_user_stats.sh $(date -d "yesterday" +%Y%m%d) 107

# 從指定日期的快照還原特定電商 (107) 的用戶統計資料
./restore_user_stats.sh 20240301 107
```

### 功能說明

此腳本用於在資料出現問題時，從事先儲存的快照中還原用戶統計資料：

1. 支援從舊格式（所有電商合併的單一檔案）或新格式（依電商 ID 分類的檔案）中還原
2. 自動檢查並驗證目標 BigQuery 表的結構是否符合預期
3. 提供完整的錯誤處理與日誌記錄
4. 支援從指定日期的快照還原，也支援僅還原特定電商的資料
5. 還原前會自動檢查快照檔案是否存在，確保操作安全性