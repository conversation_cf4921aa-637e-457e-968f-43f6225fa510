# 第一階段：建置階段
FROM python:3.12-slim AS builder

WORKDIR /app

# 1. 確保 /root/.local 目錄存在
RUN mkdir -p /root/.local

# 2. 安裝建置依賴（僅 builder 階段）
RUN apt-get update && apt-get install -y --no-install-recommends \
    build-essential \
    && rm -rf /var/lib/apt/lists/*

# 3. 複製依賴文件
COPY requirements.txt ./
# 分開安裝測試依賴以更好的利用 docker layer cache
COPY requirements-test.txt ./

# 4. 安裝生產依賴
RUN pip install --user --prefer-binary -r requirements.txt

# 5. 條件性安裝測試依賴（只在需要時）
ARG INSTALL_TEST_DEPS=false
RUN if [ "$INSTALL_TEST_DEPS" = "true" ]; then \
        pip install --user --prefer-binary -r requirements-test.txt; \
    fi

# 第二階段：運行階段
FROM python:3.12-slim

WORKDIR /app

# 6. 移除運行時依賴安裝 curl (暫時性，用於排查 CI 問題)
# RUN apt-get update && apt-get install -y --no-install-recommends \
#     curl \
#     && rm -rf /var/lib/apt/lists/*

# 7. 確保目標目錄存在
RUN mkdir -p /root/.local

# 8. 從 builder 複製已安裝的套件
COPY --from=builder /root/.local /root/.local

# 9. 確保腳本在 PATH 中
ENV PATH=/root/.local/bin:$PATH

# 10. 複製應用程式代碼
COPY . .

# 11. 暴露端口 8080 供 Cloud Run 使用
EXPOSE 8080

# 12. 使用 Functions Framework 啟動 HTTP 函數
CMD ["functions-framework", "--target=main_router", "--port=8080", "--source=main.py"]