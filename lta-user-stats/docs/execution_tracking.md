# Execution Tracking 執行追蹤設計

本文檔說明 LTA User Stats 專案中的執行追蹤設計，包含 execution_id 的統一設計、時間格式標準，以及如何進行工作流事件分析。

## 概述

執行追蹤系統旨在為所有資料處理操作提供一致的識別標準，便於監控、調試和工作流分析。每個執行任務都會生成一個唯一的 execution_id，用於關聯相關的資料處理結果。

## Execution ID 設計原則

### 1. 時間相關性
- 所有 execution_id 都包含時間資訊，便於追蹤和排序
- 使用人類可讀的時間格式，便於理解和調試
- 確保不同時間點的執行具有唯一性

### 2. 業務語義化
- 包含業務邏輯標識，明確表示執行任務的類型
- 便於從 execution_id 直接識別執行的業務功能
- 支援跨系統的業務邏輯追蹤

### 3. 執行一致性
- 單次執行中的所有相關操作使用相同的 execution_id
- 確保可以完整追蹤一次執行的所有影響範圍
- 避免同一次執行產生多個不相關的 ID

## BigQuery Scheduled Query 實現

### EC 107 產品分群查詢

**檔案位置**: `sql/scheduled_queries/ec_107_product_segments.sql`

**execution_id 格式**: `ec_107_segments_YYYYMMDD_HHMMSS`

**範例**: `ec_107_segments_20250619_143052`

### 實現方式

```sql
-- 統一 execution_id 設計
WITH execution_info AS (
  SELECT
    CONCAT('ec_107_segments_', FORMAT_TIMESTAMP('%Y%m%d_%H%M%S', CURRENT_TIMESTAMP())) AS execution_id,
    CURRENT_TIMESTAMP() AS created_at
)

-- 在所有 SELECT 中使用相同的 execution_id
SELECT
  permanent,
  'tm:c_9999_107_c_020' AS segment_id,
  execution_info.created_at,
  'keyword' AS source_type,
  'ec_107_product_segments_scheduler' AS source_entity,
  execution_info.execution_id
FROM
  `tagtoo-tracking.event_prod.tagtoo_event`,
  execution_info
-- ... 其他查詢邏輯
```

### 關鍵特點

1. **WITH 子句統一生成**: 確保所有 UNION ALL 分段使用相同的 execution_id
2. **交叉連接**: 透過 `FROM table, execution_info` 確保每行都包含執行資訊
3. **GROUP BY 包含**: 在 GROUP BY 中包含 execution 相關欄位

## 時間格式標準

### 格式規範
- **模式**: `YYYYMMDD_HHMMSS`
- **範例**: `20250619_143052`
- **時區**: UTC 時間
- **精度**: 秒級精度

### BigQuery 實現
```sql
FORMAT_TIMESTAMP('%Y%m%d_%H%M%S', CURRENT_TIMESTAMP())
```

### 替代格式選項

如需要更高精度或不同格式，可使用：

```sql
-- 毫秒精度
FORMAT_TIMESTAMP('%Y%m%d_%H%M%S_%3f', CURRENT_TIMESTAMP())
-- 結果: 20250619_143052_123

-- ISO 格式
FORMAT_TIMESTAMP('%Y-%m-%dT%H:%M:%S', CURRENT_TIMESTAMP())
-- 結果: 2025-06-19T14:30:52

-- Unix 時間戳
CAST(UNIX_SECONDS(CURRENT_TIMESTAMP()) AS STRING)
-- 結果: 1718808652
```

## Cloud Functions 實現

### 現有實現
在 Cloud Functions 中，execution_id 透過環境變數或 UUID 生成：

```python
# 生成執行 ID (使用環境變數或生成 UUID)
execution_id = os.environ.get('CLOUD_RUN_EXECUTION',
                             os.environ.get('FUNCTION_EXECUTION_ID',
                                           str(uuid.uuid4())))
```

### 統一建議
為保持一致性，建議 Cloud Functions 也採用時間相關的格式：

```python
import datetime

def generate_execution_id(prefix: str = "lta_user_stats") -> str:
    """生成統一格式的 execution_id"""
    timestamp = datetime.datetime.utcnow().strftime('%Y%m%d_%H%M%S')
    return f"{prefix}_{timestamp}"

# 使用範例
execution_id = generate_execution_id("ec_107_segments")
# 結果: ec_107_segments_20250619_143052
```

## 工作流事件分析

### 分析場景

1. **執行追蹤**: 根據 execution_id 找到同一次執行的所有分群結果
2. **時間分析**: 從 execution_id 直接提取執行時間進行時間序列分析
3. **故障排查**: 快速定位特定時間點的執行問題
4. **效能監控**: 分析不同時間執行的效能差異

### 查詢範例

```sql
-- 查詢特定執行的所有分群結果
SELECT *
FROM `tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_20250619`
WHERE execution_id = 'ec_107_segments_20250619_143052';

-- 分析每小時的執行數量
SELECT
  EXTRACT(HOUR FROM PARSE_TIMESTAMP('%Y%m%d_%H%M%S',
    SUBSTR(execution_id, STRPOS(execution_id, '_') + 1))) AS hour,
  COUNT(*) AS execution_count
FROM `tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_*`
WHERE execution_id LIKE 'ec_107_segments_%'
GROUP BY hour
ORDER BY hour;

-- 找出執行時間異常的任務
SELECT
  execution_id,
  COUNT(*) AS records_count,
  PARSE_TIMESTAMP('%Y%m%d_%H%M%S',
    SUBSTR(execution_id, STRPOS(execution_id, '_') + 1)) AS execution_time
FROM `tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_*`
WHERE execution_id LIKE 'ec_107_segments_%'
GROUP BY execution_id
HAVING records_count < 1000  -- 假設正常執行應該有超過 1000 筆記錄
ORDER BY execution_time DESC;
```

## 監控與告警

### 監控指標

1. **執行頻率**: 確保 scheduled query 按預期頻率執行
2. **資料量**: 監控每次執行產生的資料量是否正常
3. **執行時間**: 監控執行時長是否在合理範圍
4. **錯誤率**: 監控失敗的執行比例

### Cloud Monitoring 設定

```yaml
# 範例告警配置
alertPolicy:
  displayName: "EC 107 Segments Execution Monitoring"
  conditions:
    - displayName: "No execution in last 2 hours"
      conditionThreshold:
        filter: 'resource.type="bigquery_dataset"'
        comparison: COMPARISON_LESS_THAN
        thresholdValue: 1
        duration: "7200s"  # 2 hours
```

## 最佳實踐

### 1. 命名規範
- 使用一致的前綴表示業務功能
- 包含 EC ID 或其他業務標識
- 避免使用特殊字符，確保跨系統兼容性

### 2. 時間處理
- 統一使用 UTC 時間避免時區問題
- 保持秒級精度平衡可讀性和唯一性
- 考慮未來可能的精度需求

### 3. 資料庫設計
- 將 execution_id 設為索引欄位提升查詢效能
- 考慮分區策略便於時間範圍查詢
- 保留足夠的欄位長度支援未來格式變更

### 4. 錯誤處理
- 在 execution_id 生成失敗時提供回退機制
- 記錄詳細的錯誤日誌便於排查
- 確保部分失敗不影響整體執行流程

## 未來擴展

### 支援其他業務場景
- 可擴展到其他 EC ID 的分群查詢
- 支援不同類型的 scheduled queries
- 整合到 Cloud Functions 的其他執行場景

### 跨系統整合
- 與 Cloud Logging 整合實現結構化日誌
- 與 Cloud Monitoring 整合實現自動化監控
- 與業務系統整合支援端到端追蹤

### 效能優化
- 考慮使用更高效的時間戳生成方式
- 優化查詢效能避免影響業務邏輯
- 實現並行執行時的 ID 衝突處理

---

## 參考資料

- [BigQuery 時間函數文檔](https://cloud.google.com/bigquery/docs/reference/standard-sql/timestamp_functions)
- [Cloud Functions 環境變數](https://cloud.google.com/functions/docs/env-var)
- [Terraform BigQuery Data Transfer](https://registry.terraform.io/providers/hashicorp/google/latest/docs/resources/bigquery_data_transfer_config)
