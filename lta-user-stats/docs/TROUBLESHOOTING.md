# LTA User Stats 故障排除指南

## 目錄

1. [分群結果異常問題](#分群結果異常問題)
2. [BigQuery JSON 解析錯誤](#bigquery-json-解析錯誤)
3. [Cloud Function 執行問題](#cloud-function-執行問題)
4. [數據完整性問題](#數據完整性問題)
5. [性能問題](#性能問題)
6. [監控和診斷工具](#監控和診斷工具)

---

## 分群結果異常問題

### 問題描述

Cloud Function 執行日誌顯示很多分群結果為 0，但實際應該有分群結果。

**症狀**:
- 執行日誌顯示 `規則 tm:c_107_c_001 符合的用戶數量: 0`
- 多個分群規則都返回 0 個用戶
- 但用戶數據確實存在且符合分群條件

### 根本原因

**暫存 Parquet 檔案異常**:
- 快照檔案缺少分群欄位 (`tm:c_` 開頭的欄位)
- 快照檔案大小異常 (例如只有 138KB 而非正常的 100MB+)
- 快照檔案記錄數量異常 (例如只有 2,094 筆而非正常的 200 萬筆)

### 診斷步驟

#### 1. 檢查暫存檔案狀態

```bash
# 使用 Docker 執行檢查腳本
docker run --rm --platform=linux/amd64 \
  -v $(pwd):/app \
  -v $(pwd)/tagtoo-ml-workflow-kubeflow.json:/app/tagtoo-ml-workflow-kubeflow.json:ro \
  -e GOOGLE_APPLICATION_CREDENTIALS=/app/tagtoo-ml-workflow-kubeflow.json \
  asia-east1-docker.pkg.dev/tagtoo-ml-workflow/gcf-artifacts/tagtoo--ml--workflow__asia--east1__lta--user--stats:main-35bd2b6a-test \
  python check_snapshot_content.py
```

#### 2. 驗證 LTA 輸出表格

```bash
# 檢查今天的分群結果
bq query --use_legacy_sql=false "
SELECT segment_id, COUNT(*) as user_count
FROM \`tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_$(date +%Y%m%d)\`
WHERE segment_id LIKE 'tm:c_107_c_%'
GROUP BY segment_id ORDER BY segment_id"
```

#### 3. 監控快照健康狀態

```bash
# 執行健康檢查
python scripts/monitor_snapshot_health.py
```

### 解決方案

#### 1. 清理異常暫存檔案

```bash
# 刪除異常的快照檔案 (替換 YYYYMMDD 為實際日期)
gsutil rm gs://tagtoo-ml-workflow/LTA/user_stats_snapshots/ec_107/YYYYMMDD.parquet
```

#### 2. 重新執行計算

系統會在下次執行時自動重新生成正確的快照檔案。

#### 3. 預防措施

- 定期執行 `scripts/monitor_snapshot_health.py` 監控快照健康狀態
- 監控快照檔案大小，正常應該 > 50MB
- 監控記錄數量，正常應該 > 100,000 筆
- 確保快照包含必要的分群欄位

### 已知案例

**2025-07-30**: 發現 `20250729.parquet` 檔案異常
- 檔案大小: 138KB (正常應為 112MB)
- 記錄數量: 2,094 筆 (正常應為 200 萬筆)
- 缺少分群欄位
- **解決**: 刪除異常檔案，系統自動重新生成

---

## BigQuery JSON 解析錯誤

### 問題描述

BigQuery 載入作業失敗，出現 JSON 解析錯誤：
```
JSON parsing error in row starting at position XXXXXXX: Parser terminated before end of string
```

### 根本原因

1. **JSON 序列化問題**: numpy 陣列和特殊數據類型序列化不當
2. **文件截斷**: JSON 文件在寫入過程中被截斷
3. **編碼問題**: 特殊字符導致的編碼錯誤

### 解決方案 (已於 2025-07-30 修復)

#### 1. 增強 JSON 序列化 (`json_helpers.py`)

```python
def json_serializer(obj: Any) -> Any:
    # 先處理 numpy 陣列（避免在 pd.isna 時出現 ambiguous truth value 錯誤）
    if hasattr(obj, 'tolist'):  # numpy 陣列
        return obj.tolist()
    # 處理 numpy 數值類型
    if hasattr(obj, 'item'):  # numpy 標量
        return obj.item()

    # 處理 pandas 的 NaT 和 Timestamp
    if pd.isna(obj):
        return None
```

#### 2. 安全文件寫入

```python
def safe_write_json_lines(data: List[Dict], file_path: str) -> None:
    """原子性寫入 JSON Lines 文件並驗證完整性"""
    temp_path = f"{file_path}.tmp"
    try:
        with open(temp_path, 'w', encoding='utf-8') as f:
            for record in data:
                json_line = dumps(record)
                # 驗證 JSON 完整性
                json.loads(json_line)
                f.write(json_line + '\n')
        # 原子性移動
        os.rename(temp_path, file_path)
    except Exception as e:
        if os.path.exists(temp_path):
            os.remove(temp_path)
        raise e
```

#### 3. 修復縮排錯誤 (`cloud_integration.py`)

**問題**: `job_config` 創建代碼被錯誤縮排，導致 `load_table_from_file` 永遠不被調用。

**修復**: 將縮排從錯誤的 4 個空格調整為正確的縮排層級。

### 驗證修復效果

```bash
# 檢查最近的 JSON 解析錯誤
gcloud logging read "resource.labels.service_name=lta-user-stats AND textPayload:\"JSON parsing error\" AND timestamp>=\"2025-07-30T00:00:00Z\"" --limit=10
```

**預期結果**: 2025-07-30 之後應該沒有 JSON 解析錯誤。

---

## Cloud Function 執行問題

### 常見問題

#### 1. 超時錯誤

**症狀**: Cloud Function 執行超過 60 分鐘後被終止

**解決方案**:
- 檢查數據量是否過大
- 啟用 `use_bigquery_compute=true` 使用 BigQuery 計算模式
- 調整 `max_repair_days` 參數減少修復範圍

#### 2. 記憶體不足

**症狀**: 出現 OOM (Out of Memory) 錯誤

**解決方案**:
- 啟用 Polars 高效能模式 (`use_polars=true`)
- 使用 BigQuery 計算模式避免本地處理大數據
- 檢查是否有記憶體洩漏

#### 3. 權限問題

**症狀**: BigQuery 或 Cloud Storage 存取被拒絕

**解決方案**:
```bash
# 檢查服務帳戶權限
gcloud projects get-iam-policy tagtoo-ml-workflow --flatten="bindings[].members" --filter="bindings.members:<EMAIL>"
```

### 診斷指令

```bash
# 檢查 Cloud Function 狀態
gcloud functions describe lta-user-stats --gen2 --region=asia-east1

# 檢查最近的執行記錄
gcloud logging read "resource.labels.service_name=lta-user-stats AND timestamp>=\"$(date -u -d '1 hour ago' --iso-8601)\"" --limit=20
```

---

## 數據完整性問題

### 檢查清單

#### 1. user_stats 表格驗證

```sql
-- 檢查數據完整性
SELECT
  ec_id,
  COUNT(*) as user_count,
  MAX(updated_at) as latest_update,
  COUNT(CASE WHEN first_interaction_time IS NULL THEN 1 END) as null_first_interaction,
  COUNT(CASE WHEN last_interaction_time IS NULL THEN 1 END) as null_last_interaction
FROM `tagtoo-tracking.event_prod.user_stats`
GROUP BY ec_id
ORDER BY ec_id;
```

#### 2. 時間欄位邏輯檢查

```sql
-- 檢查時間邏輯錯誤
SELECT
  COUNT(CASE WHEN first_interaction_time > last_interaction_time THEN 1 END) as invalid_interaction_order,
  COUNT(CASE WHEN first_purchase_time > last_purchase_time THEN 1 END) as invalid_purchase_order
FROM `tagtoo-tracking.event_prod.user_stats`
WHERE ec_id = 107;
```

#### 3. 統計欄位合理性檢查

```sql
-- 檢查統計欄位
SELECT
  COUNT(CASE WHEN total_sessions < 0 THEN 1 END) as negative_sessions,
  COUNT(CASE WHEN purchase_count < 0 THEN 1 END) as negative_purchases,
  AVG(total_sessions) as avg_sessions,
  AVG(purchase_count) as avg_purchases
FROM `tagtoo-tracking.event_prod.user_stats`
WHERE ec_id = 107;
```

---

## 性能問題

### Polars vs Pandas 性能比較

#### 啟用 Polars 高效能模式

```python
# 在 Cloud Function 調用中設置
{
  "ec_ids": [107],
  "use_polars": true,  # 啟用 Polars
  "use_bigquery_compute": true
}
```

#### 性能監控

```bash
# 檢查執行時間
gcloud logging read "resource.labels.service_name=lta-user-stats AND textPayload:\"耗時\"" --limit=10
```

### 記憶體優化建議

1. **使用 BigQuery 計算模式**: 避免本地處理大數據集
2. **啟用 Polars**: 更高效的記憶體管理
3. **調整批次大小**: 減少單次處理的數據量

---

## 監控和診斷工具

### 日常監控指令

#### 1. 檢查 Cloud Function 健康狀態

```bash
# 檢查最近 24 小時的錯誤
gcloud logging read "resource.labels.service_name=lta-user-stats AND severity>=ERROR AND timestamp>=\"$(date -u -d '1 day ago' --iso-8601)\"" --limit=10
```

#### 2. 檢查 BigQuery 作業狀態

```bash
# 檢查最近的 BigQuery 作業
bq ls -j --max_results=10 --project_id=tagtoo-tracking
```

#### 3. 檢查數據更新狀態

```sql
-- 檢查最近更新的數據
SELECT
  ec_id,
  COUNT(*) as updated_users,
  MAX(updated_at) as latest_update
FROM `tagtoo-tracking.event_prod.user_stats`
WHERE updated_at >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 1 DAY)
GROUP BY ec_id
ORDER BY latest_update DESC;
```

### 警報設置建議

1. **JSON 解析錯誤**: 設置 Cloud Monitoring 警報
2. **執行失敗**: 監控 Cloud Function 錯誤率
3. **數據延遲**: 監控 user_stats 表格更新時間
4. **性能異常**: 監控執行時間和記憶體使用

### 緊急聯絡流程

1. **檢查錯誤日誌**: 使用上述診斷指令
2. **確認數據完整性**: 執行數據驗證查詢
3. **手動觸發測試**: 使用小範圍數據測試
4. **回滾策略**: 如有必要，切換回 pandas 實現

---

## 版本歷史

- **2025-07-30**: 修復 BigQuery JSON 解析錯誤，完成生產環境驗證
- **2025-07-29**: 實現 Polars 高效能模式，修復 sessions 累加邏輯
- **2025-07-28**: 修復 BigQuery 100MB 限制問題

---

## 相關文檔

- [DEVELOPMENT.md](../DEVELOPMENT.md) - 開發狀態和技術架構
- [README.md](../README.md) - 使用說明和 API 文檔
- [POLARS_IMPLEMENTATION.md](./POLARS_IMPLEMENTATION.md) - Polars 實現詳細說明
