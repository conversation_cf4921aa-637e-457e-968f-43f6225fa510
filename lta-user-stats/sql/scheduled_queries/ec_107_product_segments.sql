-- EC 107 產品購買者分群查詢
-- 包含新增的追蹤欄位：created_at, source_type, source_entity, execution_id
-- 單次執行中所有分群使用相同的時間相關 execution_id

WITH execution_info AS (
  SELECT
    CONCAT('ec_107_segments_', FORMAT_TIMESTAMP('%Y%m%d_%H%M%S', CURRENT_TIMESTAMP())) AS execution_id,
    CURRENT_TIMESTAMP() AS created_at
)

-- 澄淨卸妝露購買者
SELECT
  permanent,
  'tm:c_9999_107_c_020' AS segment_id,
  execution_info.created_at,
  'keyword' AS source_type,
  'ec_107_product_segments_scheduler' AS source_entity,
  execution_info.execution_id
FROM
  `tagtoo-tracking.event_prod.tagtoo_event`,
  execution_info
WHERE
  event.name = 'purchase'
  AND TIMESTAMP_TRUNC(event_time, DAY) BETWEEN TIMESTAMP(DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 YEAR))
  AND TIMESTAMP(DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY))
  AND (
  SELECT
    COUNT(1)
  FROM
    UNNEST(event.items) item
  WHERE
    LOWER(item.name) LIKE '%澄淨卸妝露%' ) > 0
GROUP BY
  permanent, execution_info.created_at, execution_info.execution_id

UNION ALL

-- 雙重酵素潔顏粉購買者
SELECT
  permanent,
  'tm:c_9999_107_c_021' AS segment_id,
  execution_info.created_at,
  'keyword' AS source_type,
  'ec_107_product_segments_scheduler' AS source_entity,
  execution_info.execution_id
FROM
  `tagtoo-tracking.event_prod.tagtoo_event`,
  execution_info
WHERE
  event.name = 'purchase'
  AND TIMESTAMP_TRUNC(event_time, DAY) BETWEEN TIMESTAMP(DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 YEAR))
  AND TIMESTAMP(DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY))
  AND (
  SELECT
    COUNT(1)
  FROM
    UNNEST(event.items) item
  WHERE
    LOWER(item.name) LIKE '%雙重酵素潔顏粉%' ) > 0
GROUP BY
  permanent, execution_info.created_at, execution_info.execution_id

UNION ALL

-- 妍潤色隔離霜-極清爽型購買者
SELECT
  permanent,
  'tm:c_9999_107_c_022' AS segment_id,
  execution_info.created_at,
  'keyword' AS source_type,
  'ec_107_product_segments_scheduler' AS source_entity,
  execution_info.execution_id
FROM
  `tagtoo-tracking.event_prod.tagtoo_event`,
  execution_info
WHERE
  event.name = 'purchase'
  AND TIMESTAMP_TRUNC(event_time, DAY) BETWEEN TIMESTAMP(DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 YEAR))
  AND TIMESTAMP(DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY))
  AND (
  SELECT
    COUNT(1)
  FROM
    UNNEST(event.items) item
  WHERE
    LOWER(item.name) LIKE '%妍潤色隔離霜-極清爽型%' ) > 0
GROUP BY
  permanent, execution_info.created_at, execution_info.execution_id

UNION ALL

-- 透妍光肌隔離霜購買者
SELECT
  permanent,
  'tm:c_9999_107_c_023' AS segment_id,
  execution_info.created_at,
  'keyword' AS source_type,
  'ec_107_product_segments_scheduler' AS source_entity,
  execution_info.execution_id
FROM
  `tagtoo-tracking.event_prod.tagtoo_event`,
  execution_info
WHERE
  event.name = 'purchase'
  AND TIMESTAMP_TRUNC(event_time, DAY) BETWEEN TIMESTAMP(DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 YEAR))
  AND TIMESTAMP(DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY))
  AND (
  SELECT
    COUNT(1)
  FROM
    UNNEST(event.items) item
  WHERE
    LOWER(item.name) LIKE '%透妍光肌隔離霜%' ) > 0
GROUP BY
  permanent, execution_info.created_at, execution_info.execution_id
