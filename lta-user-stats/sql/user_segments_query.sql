DECLARE ec_id_param INT64 DEFAULT 107;

WITH active_users AS (
  -- 計算各時段的購買次數
  SELECT
    permanent,
    COUNT(IF(event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 60 DAY), 1, NULL)) as purchase_count_2m,
    COUNT(IF(event_time BETWEEN
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 180 DAY) AND
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 90 DAY), 1, NULL)) as purchase_count_3m_6m,
    COUNT(IF(event_time BETWEEN
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY) AND
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 210 DAY), 1, NULL)) as purchase_count_7m_12m,
    COUNT(IF(event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 120 DAY), 1, NULL)) as purchase_count_4m,
    COUNT(IF(event_time BETWEEN
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 270 DAY) AND
        TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 150 DAY), 1, NULL)) as purchase_count_5m_9m,
    COUNT(IF(event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 270 DAY), 1, NULL)) as purchase_count_9m
  FROM `tagtoo-tracking.event_prod.tagtoo_event`
  WHERE
    ec_id = ec_id_param
    AND event.name = 'purchase'
  GROUP BY permanent
),

user_activity_base AS (
  SELECT
    s.permanent as permanent_id,
    s.purchase_count,
    s.last_interaction_time,
    s.last_purchase_time,
    DATE_DIFF(CURRENT_DATE(), DATE(s.last_interaction_time), DAY) as days_since_last_interaction,
    DATE_DIFF(CURRENT_DATE(), DATE(s.last_purchase_time), DAY) as days_since_last_purchase,
    a.*,
    CASE
      WHEN DATE_DIFF(CURRENT_DATE(), DATE(s.last_interaction_time), DAY) <= 30
        THEN '最近1個月內有互動行為'
      WHEN DATE_DIFF(CURRENT_DATE(), DATE(s.last_interaction_time), DAY) BETWEEN 31 AND 365
        THEN '前2個月~1年，有互動行為'
      ELSE '超過1年無互動行為'
    END as interaction_period
  FROM `tagtoo-tracking.event_prod.user_stats` s
  LEFT JOIN active_users a ON s.permanent = a.permanent
  WHERE s.ec_id = ec_id_param
),

user_segments AS (
  SELECT
    permanent_id,
    interaction_period,
    CASE
      WHEN purchase_count = 0 THEN 'F0'
      -- F1 新客細分
      WHEN purchase_count = 1 AND days_since_last_purchase <= 60 THEN 'F1新客(AY)'
      WHEN purchase_count = 1 AND days_since_last_purchase BETWEEN 90 AND 180 THEN 'F1新客(BY)'
      WHEN purchase_count = 1 AND days_since_last_purchase BETWEEN 210 AND 365 THEN 'F1新客(ES)'
      -- 淺層客戶細分
      WHEN purchase_count_4m BETWEEN 2 AND 5 THEN 'F淺層客(Z淺)'
      WHEN purchase_count_5m_9m BETWEEN 2 AND 5 THEN 'F淺層客(X)'
      -- 深層客戶
      WHEN purchase_count_9m >= 6 THEN 'F深層客(Z深)'
      -- 睡眠客和脫離預備客細分（修正重複計算問題）
      WHEN purchase_count >= 2 AND days_since_last_purchase > 365 THEN '睡眠客（多次購買）'
      WHEN purchase_count = 1 AND days_since_last_purchase > 365 THEN '睡眠客（單次購買）'
      WHEN purchase_count >= 2 AND days_since_last_purchase BETWEEN 300 AND 365 THEN '脫離預備客（多次購買）'
      WHEN purchase_count = 1 AND days_since_last_purchase BETWEEN 300 AND 365 THEN '脫離預備客（單次購買）'
    END as segment
  FROM user_activity_base
),

activity_stats AS (
  -- 活躍度統計
  SELECT
    '活躍度分布' as stat_type,
    interaction_period as segment,
    COUNT(DISTINCT permanent) as user_count,
    ROUND(COUNT(DISTINCT permanent) * 100.0 /
      SUM(COUNT(DISTINCT permanent)) OVER(), 2) as percentage
  FROM user_activity_base
  GROUP BY interaction_period
),

segment_stats_by_activity AS (
  -- 各活躍度下的分群統計
  SELECT
    CONCAT(u.interaction_period, ' 用戶分布') as stat_type,
    s.segment,
    COUNT(DISTINCT s.permanent_id) as user_count,
    ROUND(COUNT(DISTINCT s.permanent_id) * 100.0 /
      SUM(COUNT(DISTINCT s.permanent_id)) OVER(PARTITION BY u.interaction_period), 2) as percentage
  FROM user_segments s
  JOIN user_activity_base u ON s.permanent_id = u.permanent
  WHERE s.segment IS NOT NULL
  GROUP BY u.interaction_period, s.segment
),

overall_segment_stats AS (
  -- 整體分群統計
  SELECT
    '整體用戶分布' as stat_type,
    segment,
    COUNT(DISTINCT permanent_id) as user_count,
    ROUND(COUNT(DISTINCT permanent_id) * 100.0 /
      SUM(COUNT(DISTINCT permanent_id)) OVER(), 2) as percentage
  FROM user_segments
  WHERE segment IS NOT NULL
  GROUP BY segment
)
-- 合併所有統計結果
SELECT
  stat_type,
  segment,
  user_count,
  percentage,
  CONCAT(CAST(percentage as STRING), '%') as percentage_display,
  REPEAT('█', CAST(ROUND(percentage / 2) as INT64)) as distribution_bar
FROM (
  SELECT * FROM activity_stats
  UNION ALL
  SELECT * FROM segment_stats_by_activity
  UNION ALL
  SELECT * FROM overall_segment_stats
)ORDER BY
  CASE stat_type
    WHEN '活躍度分布' THEN 1
    WHEN '最近1個月內有互動行為 用戶分布' THEN 2
    WHEN '前2個月~1年，有互動行為 用戶分布' THEN 3
    WHEN '超過1年無互動行為 用戶分布' THEN 4
    WHEN '整體用戶分布' THEN 5
    ELSE 6
  END,
  CASE segment
    WHEN 'F0' THEN 1
    WHEN 'F1新客(AY)' THEN 2
    WHEN 'F1新客(BY)' THEN 3
    WHEN 'F1新客(ES)' THEN 4
    WHEN 'F淺層客(Z淺)' THEN 5
    WHEN 'F淺層客(X)' THEN 6
    WHEN 'F深層客(Z深)' THEN 7
    WHEN '脫離預備客（單次購買）' THEN 8
    WHEN '脫離預備客（多次購買）' THEN 9
    WHEN '睡眠客（單次購買）' THEN 10
    WHEN '睡眠客（多次購買）' THEN 11
    ELSE 12
  END;
