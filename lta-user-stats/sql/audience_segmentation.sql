WITH user_interactions AS (
  -- 計算每個使用者的互動資訊
  SELECT
    permanent,  -- 用戶唯一識別碼
    MAX(event_time) as last_interaction_time,  -- 最後互動時間
    MIN(CASE
      WHEN event.name = 'purchase' THEN event_time
      ELSE NULL
    END) as first_purchase_time,  -- 第一次購買時間
    MAX(CASE
      WHEN event.name = 'purchase' THEN event_time
      ELSE NULL
    END) as last_purchase_time,  -- 最後一次購買時間
    COUNT(DISTINCT CASE
      WHEN event.name = 'purchase' THEN session.id
      ELSE NULL
    END) as purchase_count  -- 購買次數
  FROM `tagtoo-tracking.event_prod.tagtoo_event`
  WHERE
    ec_id = 107  -- 指定電商ID
  GROUP BY permanent
),

user_segments AS (
  -- 根據購買次數和時間進行分類
  SELECT
    permanent,
    last_interaction_time,
    first_purchase_time,
    last_purchase_time,
    purchase_count,
    CASE
      -- F0: 從未購買過的用戶
      WHEN purchase_count = 0 THEN 'F0'

      -- F1: 只購買過一次的新客
      WHEN purchase_count = 1 THEN
        CASE
          -- AY: 近2個月內第一次購買的新客
          WHEN first_purchase_time >= TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -60 DAY)
            THEN 'F1新客(AY)'
          -- BY: 3-6個月內第一次購買的新客
          WHEN first_purchase_time BETWEEN
            TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -180 DAY) AND
            TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -90 DAY)
            THEN 'F1新客(BY)'
          -- ES: 7個月-1年內第一次購買的新客
          WHEN first_purchase_time BETWEEN
            TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -365 DAY) AND
            TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -210 DAY)
            THEN 'F1新客(ES)'
        END

      -- 淺層客: 購買2-5次的客戶
      WHEN purchase_count BETWEEN 2 AND 5 THEN
        CASE
          -- Z淺: 近4個月內有購買的淺層客
          WHEN last_interaction_time >= TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -120 DAY)
            THEN 'F淺層客(Z淺)'
          -- X: 5-9個月內有購買的淺層客
          WHEN last_interaction_time BETWEEN
            TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -270 DAY) AND
            TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -150 DAY)
            THEN 'F淺層客(X)'
        END

      -- Z深: 購買6次以上且9個月內有互動的深度客戶
      WHEN purchase_count >= 6 AND
        last_interaction_time >= TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -270 DAY)
        THEN 'F深層客(Z深)'

      -- 睡眠客: 購買2次以上但超過1年未回購
      WHEN purchase_count >= 2 AND
        last_interaction_time < TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -365 DAY)
        THEN '睡眠客'

      -- 脫離預備客: 購買2次以上，10-12個月未回購
      WHEN purchase_count >= 2 AND
        last_interaction_time BETWEEN
          TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -365 DAY) AND
          TIMESTAMP_ADD(CURRENT_TIMESTAMP(), INTERVAL -300 DAY)
        THEN '脫離預備客'
    END as segment
  FROM user_interactions
),

segment_stats AS (
  -- 計算各區段的統計數據
  SELECT
    segment,
    COUNT(DISTINCT permanent) as user_count,  -- 該區段的用戶數
    -- 計算該區段佔總體的百分比
    COUNT(DISTINCT permanent) * 100.0 / SUM(COUNT(DISTINCT permanent)) OVER() as percentage
  FROM user_segments
  WHERE segment IS NOT NULL
  GROUP BY segment
)

-- 最終輸出結果
SELECT
  segment,  -- 客戶區段
  user_count,  -- 區段人數
  ROUND(percentage, 2) as percentage,  -- 百分比(數字)
  CONCAT(CAST(ROUND(percentage, 2) as STRING), '%') as percentage_display,  -- 百分比(顯示)
  -- 使用ASCII方塊視覺化區段分布
  REPEAT('█', CAST(ROUND(percentage / 2) as INT64)) as distribution_bar
FROM segment_stats
ORDER BY
  -- 依照區段類型排序
  CASE
    WHEN segment = 'F0' THEN 1  -- 未購買客戶優先
    WHEN segment LIKE 'F1%' THEN 2  -- 其次是新客
    WHEN segment LIKE 'F淺層客%' THEN 3  -- 再來是淺層客
    WHEN segment = 'F深層客(Z深)' THEN 4  -- 接著是深層客
    WHEN segment = '睡眠客' THEN 5  -- 然後是睡眠客
    WHEN segment = '脫離預備客' THEN 6  -- 最後是脫離預備客
    ELSE 7
  END,
  segment;