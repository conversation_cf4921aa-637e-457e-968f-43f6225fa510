-- 用於計算指定時間範圍的用戶統計資料並寫入 user_stats
-- 參數：
-- start_timestamp: 開始時間（含），格式：YYYY-MM-DD HH:MI:SS
-- end_timestamp: 結束時間（不含），格式：YYYY-MM-DD HH:MI:SS
-- ec_id: 電商 ID
-- is_incremental: 是否使用增量更新模式 (TRUE: 增量更新, FALSE: 直接覆蓋)
DECLARE start_timestamp TIMESTAMP DEFAULT TIMESTAMP('2024-01-01 00:00:00');
DECLARE end_timestamp TIMESTAMP DEFAULT TIMESTAMP('2024-03-15 00:00:00');
DECLARE ec_id_param INT64 DEFAULT 107;
DECLARE is_incremental BOOL DEFAULT FALSE;

MERGE INTO `tagtoo-tracking.event_prod.user_stats` T
USING (
  SELECT
    ec_id,
    permanent,
    COUNT(IF(event.name = 'purchase', 1, NULL)) as purchase_count,
    MIN(IF(event.name = 'register', event_time, NULL)) as registration_time,
    MIN(event_time) as first_interaction_time,
    MAX(event_time) as last_interaction_time,
    MIN(IF(event.name = 'purchase', event_time, NULL)) as first_purchase_time,
    MAX(IF(event.name = 'purchase', event_time, NULL)) as last_purchase_time,
    SUM(IF(event.name = 'purchase', event.value, 0)) as total_purchase_amount,
    COUNT(DISTINCT session.id) as total_sessions
  FROM `tagtoo-tracking.event_prod.tagtoo_event`
  WHERE
    ec_id = ec_id_param
    AND event_time >= start_timestamp
    AND event_time < end_timestamp
  GROUP BY
    ec_id,
    permanent
) S
ON T.ec_id = S.ec_id AND T.permanent = S.permanent
WHEN MATCHED THEN
  UPDATE SET
    purchase_count = IF(is_incremental,
      T.purchase_count + S.purchase_count,
      S.purchase_count),
    registration_time = IF(is_incremental,
      LEAST(T.registration_time, S.registration_time),
      S.registration_time),
    first_interaction_time = IF(is_incremental,
      LEAST(T.first_interaction_time, S.first_interaction_time),
      S.first_interaction_time),
    last_interaction_time = IF(is_incremental,
      GREATEST(T.last_interaction_time, S.last_interaction_time),
      S.last_interaction_time),
    first_purchase_time = IF(is_incremental,
      LEAST(COALESCE(T.first_purchase_time, S.first_purchase_time),
            COALESCE(S.first_purchase_time, T.first_purchase_time)),
      S.first_purchase_time),
    last_purchase_time = IF(is_incremental,
      GREATEST(COALESCE(T.last_purchase_time, S.last_purchase_time),
               COALESCE(S.last_purchase_time, T.last_purchase_time)),
      S.last_purchase_time),
    total_purchase_amount = IF(is_incremental,
      T.total_purchase_amount + S.total_purchase_amount,
      S.total_purchase_amount),
    total_sessions = IF(is_incremental,
      T.total_sessions + S.total_sessions,
      S.total_sessions),
    updated_at = CURRENT_TIMESTAMP()
WHEN NOT MATCHED THEN
  INSERT (
    ec_id,
    permanent,
    purchase_count,
    registration_time,
    first_interaction_time,
    last_interaction_time,
    first_purchase_time,
    last_purchase_time,
    total_purchase_amount,
    total_sessions,
    created_at,
    updated_at
  )
  VALUES (
    S.ec_id,
    S.permanent,
    S.purchase_count,
    S.registration_time,
    S.first_interaction_time,
    S.last_interaction_time,
    S.first_purchase_time,
    S.last_purchase_time,
    S.total_purchase_amount,
    S.total_sessions,
    CURRENT_TIMESTAMP(),
    CURRENT_TIMESTAMP()
  );