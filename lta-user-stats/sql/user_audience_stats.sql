DECLARE ec_id_param INT64 DEFAULT 107;

WITH active_users AS (
  -- 計算各時段的購買次數
  SELECT
    permanent,
    COUNT(IF(event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 60 DAY), 1, NULL)) as purchase_count_2m,
    COUNT(IF(event_time >= TIMESTAMP_SUB(CURRENT_TIMESTAMP(), INTERVAL 365 DAY), 1, NULL)) as purchase_count_1y
  FROM `tagtoo-tracking.event_prod.tagtoo_event`
  WHERE
    ec_id = ec_id_param
    AND event.name = 'purchase'
  GROUP BY permanent
),

user_activity_base AS (
  SELECT
    s.permanent as permanent_id,
    s.purchase_count,
    s.last_interaction_time,
    s.last_purchase_time,
    DATE_DIFF(CURRENT_DATE(), DATE(s.last_interaction_time), DAY) as days_since_last_interaction,
    DATE_DIFF(CURRENT_DATE(), DATE(s.last_purchase_time), DAY) as days_since_last_purchase,
    a.*,
    CASE
      WHEN DATE_DIFF(CURRENT_DATE(), DATE(s.last_interaction_time), DAY) <= 30
        THEN '最近1個月內有進入網站、瀏覽、點擊等互動行為'
      WHEN DATE_DIFF(CURRENT_DATE(), DATE(s.last_interaction_time), DAY) BETWEEN 31 AND 365
        THEN '前2個月~1年，有經進入網站、瀏覽、點擊等互動行為'
      ELSE '超過1年無互動行為'
    END as interaction_period
  FROM `tagtoo-tracking.event_prod.user_stats` s
  LEFT JOIN active_users a ON s.permanent = a.permanent
  WHERE s.ec_id = ec_id_param
),

user_segments AS (
  SELECT
    permanent_id,
    interaction_period,
    CASE
      WHEN purchase_count = 0 THEN 'F0 (未購買)'
      WHEN purchase_count = 1 THEN 'F1 (只購買過1次)'
      WHEN purchase_count_1y >= 2 THEN '活動客 (最近1年購買2次及以上)'
      ELSE NULL
    END as audience_type
  FROM user_activity_base
),

interaction_audience_stats AS (
  -- 各互動期間的受眾統計
  SELECT
    interaction_period as `受眾類型`,
    '全部受眾' as `目標受眾`,
    COUNT(DISTINCT permanent_id) as `用戶數`,
    100.0 as `百分比`,
    '100%' as `百分比顯示`
  FROM user_segments
  GROUP BY interaction_period

  UNION ALL

  SELECT
    interaction_period as `受眾類型`,
    audience_type as `目標受眾`,
    COUNT(DISTINCT permanent_id) as `用戶數`,
    ROUND(COUNT(DISTINCT permanent_id) * 100.0 /
      SUM(COUNT(DISTINCT permanent_id)) OVER(PARTITION BY interaction_period), 2) as `百分比`,
    CONCAT(CAST(ROUND(COUNT(DISTINCT permanent_id) * 100.0 /
      SUM(COUNT(DISTINCT permanent_id)) OVER(PARTITION BY interaction_period), 2) as STRING), '%') as `百分比顯示`
  FROM user_segments
  WHERE audience_type IS NOT NULL
  GROUP BY interaction_period, audience_type
)

-- 輸出結果
SELECT *
FROM interaction_audience_stats
ORDER BY
  CASE `受眾類型`
    WHEN '最近1個月內有進入網站、瀏覽、點擊等互動行為' THEN 1
    WHEN '前2個月~1年，有經進入網站、瀏覽、點擊等互動行為' THEN 2
    ELSE 3
  END,
  CASE `目標受眾`
    WHEN '全部受眾' THEN 1
    WHEN 'F0 (未購買)' THEN 2
    WHEN 'F1 (只購買過1次)' THEN 3
    WHEN '活動客 (最近1年購買2次及以上)' THEN 4
    ELSE 5
  END;