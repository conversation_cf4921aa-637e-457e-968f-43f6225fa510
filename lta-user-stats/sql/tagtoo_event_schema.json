[{"description": "The Tagtoo ecommerce ID.", "mode": "REQUIRED", "name": "ec_id", "type": "INTEGER"}, {"description": "A long-lived user ID.", "mode": "REQUIRED", "name": "permanent", "type": "STRING"}, {"description": "The selected language on ecommerce webpage.", "mode": "REQUIRED", "name": "language", "type": "STRING"}, {"description": "The current page link.", "mode": "REQUIRED", "name": "link", "type": "STRING"}, {"description": "The previous page link.", "mode": "NULLABLE", "name": "referrer", "type": "STRING"}, {"description": "The time an event is triggered. In UTC+0 format.", "mode": "REQUIRED", "name": "event_time", "type": "TIMESTAMP"}, {"description": "Client IP Address.", "mode": "NULLABLE", "name": "ip_address", "type": "STRING"}, {"description": "GEO location information resolved from client IP address.", "fields": [{"description": "Two-character country code based on ISO 3166.", "mode": "REQUIRED", "name": "country_code", "type": "STRING"}, {"description": "Region or state name.", "mode": "NULLABLE", "name": "region_name", "type": "STRING"}, {"description": "City name.", "mode": "NULLABLE", "name": "city_name", "type": "STRING"}, {"description": "City latitude. Defaults to capital city latitude if city is unknown.", "mode": "NULLABLE", "name": "latitude", "type": "FLOAT"}, {"description": "City longitude. Defaults to capital city longitude if city is unknown.", "mode": "NULLABLE", "name": "longitude", "type": "FLOAT"}, {"description": "ZIP code or Postal code.", "mode": "NULLABLE", "name": "zip_code", "type": "STRING"}], "mode": "NULLABLE", "name": "location", "type": "RECORD"}, {"description": "The parsed user agent information.", "fields": [{"description": "Browser name.", "mode": "REQUIRED", "name": "browser", "type": "STRING"}, {"description": "Browser version.", "mode": "REQUIRED", "name": "browser_version", "type": "STRING"}, {"description": "Operation system name.", "mode": "REQUIRED", "name": "os", "type": "STRING"}, {"description": "Operation system version.", "mode": "REQUIRED", "name": "os_version", "type": "STRING"}, {"description": "Device name.", "mode": "REQUIRED", "name": "device", "type": "STRING"}, {"description": "Is mobile.", "mode": "REQUIRED", "name": "is_mobile", "type": "BOOLEAN"}, {"description": "Is tablet.", "mode": "REQUIRED", "name": "is_tablet", "type": "BOOLEAN"}, {"description": "Is PC.", "mode": "REQUIRED", "name": "is_pc", "type": "BOOLEAN"}, {"description": "Is touch capable.", "mode": "REQUIRED", "name": "is_touch_capable", "type": "BOOLEAN"}, {"description": "Is bot.", "mode": "REQUIRED", "name": "is_bot", "type": "BOOLEAN"}], "mode": "REQUIRED", "name": "user_agent", "type": "RECORD"}, {"description": "The detail of an event.", "fields": [{"description": "Event name.", "mode": "REQUIRED", "name": "name", "type": "STRING"}, {"description": "The total price of an event.", "mode": "NULLABLE", "name": "value", "type": "FLOAT"}, {"description": "Sale currency.", "mode": "NULLABLE", "name": "currency", "type": "STRING"}, {"description": "Product item details.", "fields": [{"description": "Product ID.", "mode": "REQUIRED", "name": "id", "type": "STRING"}, {"description": "Product name.", "mode": "REQUIRED", "name": "name", "type": "STRING"}, {"description": "Sale price.", "mode": "NULLABLE", "name": "price", "type": "FLOAT"}, {"description": "Product quantity.", "mode": "NULLABLE", "name": "quantity", "type": "FLOAT"}, {"description": "Product availability.", "mode": "NULLABLE", "name": "availability", "type": "STRING"}], "mode": "REPEATED", "name": "items", "type": "RECORD"}, {"description": "Additional data for an event.", "fields": [{"description": "Payment method e.g. credit_card.", "mode": "NULLABLE", "name": "payment_method", "type": "STRING"}, {"description": "Shipping method e.g. 711.", "mode": "NULLABLE", "name": "shipping_method", "type": "STRING"}, {"description": "The web context copied by the user.", "mode": "NULLABLE", "name": "copy_string", "type": "STRING"}, {"description": "To show the on/off or entry/exit status of an event.", "mode": "NULLABLE", "name": "status", "type": "BOOLEAN"}, {"description": "To show how long an user stays on one page.", "mode": "NULLABLE", "name": "focus_minutes", "type": "FLOAT"}, {"description": "Ecommerce campaign or form title.", "mode": "NULLABLE", "name": "campaign_name", "type": "STRING"}, {"description": "Auth method, e.g. google.", "mode": "NULLABLE", "name": "auth_method", "type": "STRING"}, {"description": "The unique ID for an purchase.", "mode": "NULLABLE", "name": "order_id", "type": "STRING"}, {"description": "The height of a web page.", "mode": "NULLABLE", "name": "document_height", "type": "FLOAT"}, {"description": "The scroll top of a web page.", "mode": "NULLABLE", "name": "scroll_top", "type": "FLOAT"}, {"description": "The string entered by the user for the search.", "mode": "NULLABLE", "name": "search_string", "type": "STRING"}, {"description": "The breadcrumb for a product page.", "mode": "NULLABLE", "name": "breadcrumb", "type": "STRING"}], "mode": "NULLABLE", "name": "custom_data", "type": "RECORD"}], "mode": "REQUIRED", "name": "event", "type": "RECORD"}, {"description": "User information", "fields": [{"description": "A hashed email.", "mode": "NULLABLE", "name": "em", "type": "STRING"}, {"description": "A hashed phone number.", "mode": "NULLABLE", "name": "ph", "type": "STRING"}, {"description": "A hashed username.", "mode": "NULLABLE", "name": "un", "type": "STRING"}, {"description": "A hashed user gender.", "mode": "NULLABLE", "name": "gd", "type": "STRING"}, {"description": "A hashed date of birth.", "mode": "NULLABLE", "name": "db", "type": "STRING"}, {"description": "A short-lived Facebook session ID.", "mode": "NULLABLE", "name": "fbp", "type": "STRING"}, {"description": "A short-lived Facebook click ID.", "mode": "NULLABLE", "name": "fbc", "type": "STRING"}, {"description": "A short-lived Google Analytics session ID.", "mode": "NULLABLE", "name": "ga", "type": "STRING"}, {"description": "A short-lived Google Analytics user ID.", "mode": "NULLABLE", "name": "gid", "type": "STRING"}, {"description": "Line user ID.", "mode": "NULLABLE", "name": "lmid", "type": "STRING"}], "mode": "NULLABLE", "name": "user", "type": "RECORD"}, {"description": "Session information.", "fields": [{"description": "A short-lived session ID.", "mode": "REQUIRED", "name": "id", "type": "STRING"}, {"description": "The session source or utm_source query param.", "mode": "NULLABLE", "name": "source", "type": "STRING"}, {"description": "The session medium or utm_medium query param.", "mode": "NULLABLE", "name": "medium", "type": "STRING"}, {"description": "The session campaign or utm_campaign query param.", "mode": "NULLABLE", "name": "campaign", "type": "STRING"}, {"description": "The session keyword or utm_term query param\n.", "mode": "NULLABLE", "name": "term", "type": "STRING"}, {"description": "The session content or utm_content query param.", "mode": "NULLABLE", "name": "content", "type": "STRING"}], "mode": "REQUIRED", "name": "session", "type": "RECORD"}]