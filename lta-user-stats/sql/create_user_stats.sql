CREATE TABLE IF NOT EXISTS `tagtoo-tracking.event_prod.user_stats` (
  ec_id INT64 NOT NULL,
  permanent STRING NOT NULL,
  purchase_count INT64 NOT NULL,
  registration_time TIMESTAMP,  -- 可以為 NULL，因為不是所有用戶都有註冊事件
  first_interaction_time TIMESTAMP NOT NULL,
  last_interaction_time TIMESTAMP NOT NULL,
  first_purchase_time TIMESTAMP,  -- 可以為 NULL，因為不是所有用戶都有購買
  last_purchase_time TIMESTAMP,  -- 可以為 NULL，同上
  total_purchase_amount FLOAT64 NOT NULL,
  total_sessions INT64 NOT NULL,
  created_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP(),
  updated_at TIMESTAMP NOT NULL DEFAULT CURRENT_TIMESTAMP()
)
PARTITION BY DATE(last_interaction_time)  -- 按最後互動時間分區
CLUSTER BY ec_id, permanent;  -- 使用叢集索引來優化查詢效能