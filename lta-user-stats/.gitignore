# Python 相關檔案
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# 測試和覆蓋率報告
.coverage
.coverage.*
.pytest_cache/
htmlcov/
.tox/
.nox/
.cache
nosetests.xml
coverage.xml
*.cover
.hypothesis/

# 開發環境和 IDE
.DS_Store
.cursor/
.cursorignore
.vscode/
.idea/
*.swp
*.swo
*~

# 專案特定
.benchmarks/
.gcloudignore
lta_user_stats.egg-info/
py/
spec/

# Terraform files
.terraform/
*.tfstate
*.tfstate.*
*.tfplan
*.tfplan.*
.terraformrc
terraform.rc
*.tfvars
*.tfvars.json
.terraform.lock.hcl
crash.log
crash.*.log
override.tf
override.tf.json
*_override.tf
*_override.tf.json
