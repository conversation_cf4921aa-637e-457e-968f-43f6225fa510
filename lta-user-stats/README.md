# LTA User Stats Cloud Function

這個 Cloud Function 用於計算和更新使用者統計資料，並將結果寫入到不同的 GCP 服務中。

## 目錄

- [背景說明](#背景說明)
- [功能簡介](#功能簡介)
- [流程說明](#流程說明)
  - [資料流向](#資料流向)
  - [程式執行流程圖](#程式執行流程圖)
  - [冪等性設計](#冪等性設計)
- [安裝方式](#安裝方式)
- [使用說明](#使用說明)
  - [避免 OOM 問題的 BigQuery 計算模式](#%EF%B8%8F-避免-oom-問題的-bigquery-計算模式)
  - [批次重算腳本](#批次重算腳本)
- [必要配置](#必要配置)
- [規則設定檔結構](#規則設定檔結構)
- [資料處理特性](#資料處理特性)
- [監控與維護](#監控與維護)
- [執行追蹤系統](#執行追蹤系統)
- [維護者](#維護者)
- [如何貢獻](#如何貢獻)
- [開發工具](#開發工具)
  - [Parquet 時間範圍檢查工具](#parquet-時間範圍檢查工具)
- [最近變更](#最近變更)

## 背景說明

此專案為 Tagtoo 廣告投放系統的一部分，用於計算用戶的統計資料，並將這些資料寫入 LTA 大表，以便進行更精準的廣告投放和分析報告的產出。

## 功能簡介

主要功能包括:
1. 接收 `ec_ids`、`start_time`、`end_time` 和其他控制參數。
2. 計算用戶的統計資料，包括購買次數、互動時間等。
3. 將計算結果保存到 Google Cloud Storage。
4. 將用戶的受眾資料寫入 BigQuery 的 LTA 臨時表格。
5. 支援自訂受眾編號格式（是否加入 DXP 平台）。
6. 在 Cloud Logging 中記錄執行過程。
7. **自動處理 NaT 值**：透過自定義序列化邏輯，確保所有日期時間欄位可正確序列化。
8. **過濾 NULL 值**：確保關鍵欄位（如 `last_interaction_time`）沒有 NULL 值寫入 BigQuery。
9. **空資料處理**：改進了對空資料框的處理，確保即使資料為空也能正常處理後續流程。
10. **錯誤處理增強**：
    - 改進日誌格式化，防止 MagicMock 物件格式化錯誤
    - 捕獲並記錄各種異常，避免中斷整體處理流程
    - 提供詳細的錯誤堆疊資訊，便於問題診斷
11. **動態分群改進**：優化動態分群計算，確保即使某個規則評估失敗，也不會影響其他規則的評估。
12. **自動修復機制**：
    - 可自動檢測並修復指定時間範圍內缺失的每日資料
    - 支援限制最大修復天數，避免效能問題
    - 修復過程會確保資料完整性和連續性
    - 針對每個缺失的日期分別執行資料查詢和更新
    - 可透過 `auto_repair_missing_days` 和 `max_repair_days` 參數控制修復行為
13. **日誌級別控制**：
    - 支援通過 `log_level` 參數動態設定日誌級別
    - 可選值包括 DEBUG、INFO、WARNING、ERROR，預設為 INFO
    - 便於在開發或調試過程中獲取更詳細的執行資訊
14. **批次重算腳本**：
    - 提供 `scripts/recalculate_lta.sh` 腳本，支援跨多日的批次資料重算
    - 自動按日分割長時間範圍，確保資料完整性和處理穩定性
    - 支援 Docker 環境執行、乾跑模式、詳細進度追蹤和錯誤處理
    - 適用於大規模歷史資料修復和定期批次處理需求
15. **Polars 高效能資料處理**：
    - **預設啟用 Polars 實現**：使用 `polars-lts-cpu==1.31.0` 提供高效能資料處理
    - **雙實現架構**：保留 pandas 實現作為備用方案，確保向後兼容性
    - **效能優化**：向量化操作、懶惰評估和更高效的記憶體管理
    - **一致性保證**：兩種實現產生完全相同的結果，通過嚴格測試驗證
    - **靈活切換**：可透過 `use_polars` 參數選擇實現方式

## 流程說明

### 資料流向

1. **資料獲取**：
   - 從 BigQuery `tagtoo-tracking.event_prod.tagtoo_event` 讀取原始事件資料
   - 或從 Cloud Storage 讀取最近的用戶統計快照作為基礎資料

2. **資料處理**：
   - 計算/更新用戶統計數據（若 `should_calculate_stats=true`）
   - 處理 NaT 值和日期時間格式一致性
   - 資料類型轉換與驗證
   - 智能欄位優先順序處理，優先使用帶 `_daily` 後綴的最新互動時間欄位

3. **資料寫入**：
   - 更新 BigQuery `tagtoo-tracking.event_prod.user_stats` 表（若 `should_update_user_stats_table=true`）
   - 儲存當日快照到 Cloud Storage（若 `should_save_snapshot=true`）
   - 路徑格式：`LTA/user_stats_snapshots/ec_{EC_ID}/{YYYYMMDD}.parquet`

4. **受眾分群**：
   - 從 Cloud Storage 讀取受眾分群規則 (`LTA/user_stats_configs/audience_rules.json`)
   - 動態評估受眾規則
   - 寫入分群結果到特殊 LTA 表格 `tagtoo-ml-workflow.tagtoo_export_results.special_lta_YYYYMMDD`（若 `should_write_lta=true`）

5. **自動修復**（若 `auto_repair_missing_days=true`）：
   - 逐日檢查並修復缺失的資料
   - 對每個缺失的日期重複步驟 1-4

### 程式執行流程圖

```mermaid
sequenceDiagram
    participant Client as HTTP Client
    participant CF as Cloud Functions
    participant BQ as BigQuery
    participant GCS as Cloud Storage

    Client->>CF: POST / (含 JSON body)

    Note over CF: 解析請求參數
    Note over CF: 處理布林標誌設定 (should_calculate_stats, should_save_snapshot 等)

    CF->>CF: 驗證 EC IDs 及時間範圍

    CF->>GCS: 尋找最近可用的快照

    alt 找到可用快照
        GCS-->>CF: 回傳快照資料
        Note over CF: 載入快照作為基礎資料

        alt 啟用自動修復功能
            Note over CF: 計算需修復天數 (最大限制 max_repair_days)

            loop 逐日修復
                CF->>BQ: 查詢該天的使用者資料
                BQ-->>CF: 返回日活動資料
                CF->>CF: 更新使用者統計資料

                alt should_save_snapshot=true
                    CF->>GCS: 儲存該天快照
                end

                alt should_write_lta=true
                    Note over CF: 計算受眾分群
                    CF->>BQ: 寫入特殊 LTA 臨時表格
                end
            end
        end
    else 沒有找到可用快照
        Note over CF: 從頭計算統計資料
        CF->>BQ: 查詢時間範圍內所有資料
        BQ-->>CF: 返回查詢結果
    end

    alt should_update_user_stats_table=true
        CF->>BQ: 更新 user_stats 表格
    end

    alt should_save_snapshot=true and 未在日修復中保存
        CF->>GCS: Snapshot 儲存至 GCS
    end

    alt should_write_lta=true and 未在日修復中寫入
        Note over CF: 計算受眾分群
        CF->>GCS: 獲取受眾分群規則
        GCS-->>CF: 返回規則配置
        CF->>CF: 動態評估受眾規則
        CF->>BQ: 寫入 LTA 臨時表格
    end

    Note over CF: 計算處理成本及統計資料

    CF-->>Client: 回傳 HTTP 200 含處理結果
```

### 冪等性設計

此 Cloud Function 設計為具有冪等性，意味著對相同時間範圍內的資料重複執行不會產生不同結果，具體表現在：

1. **快照機制**：
   - 系統會先尋找可用的最近快照作為基礎資料
   - 若找到快照，只會處理快照日期之後的增量資料
   - 這確保了即使重複運行，也不會重複計算已處理過的資料

2. **自動修復功能**：
   - 當啟用 `auto_repair_missing_days` 時，系統會逐日處理資料
   - 每個日期的資料處理是獨立的，確保重複執行只會修復相同的日期資料
   - 資料修復不會超過 `max_repair_days` 設定的天數限制

3. **BigQuery 資料更新**：
   - 每次對 `user_stats` 表格的更新是基於最新快照和新增的資料
   - 表格採用 MERGE 操作，確保同一個 permanent 只會被最新資料更新

4. **LTA 特殊臨時表格**：
   - 寫入 LTA 表格時使用日期作為表格名稱（`special_lta_temp_for_update_YYYYMMDD`）
   - 重複執行只會覆蓋同一日期的表格，不會產生重複資料
   - 動態受眾分群基於當前的用戶統計資料計算，確保結果一致

### 冪等性參數控制

通過以下參數可以控制冪等性行為：

- `should_calculate_stats`：設為 false 時不重新計算統計資料，直接使用 BigQuery 中已有的資料
- `should_save_snapshot`：設為 false 時不儲存快照，避免覆蓋已有快照
- `should_write_lta`：設為 false 時不寫入 LTA 特殊臨時表格，避免重複更新受眾資料
- `should_update_user_stats_table`：設為 false 時不更新 BigQuery 的 user_stats 表格
- `auto_repair_missing_days`：設為 false 時不自動修復缺失的日期資料

## 安裝方式

此專案透過 GitHub Actions 自動部署至 Google Cloud Functions，無需手動安裝。

### 部署流程
1. 當程式碼推送到 `main` 分支時，會自動觸發部署。
2. GitHub Actions 會執行 `.github/workflows/deploy.yml` 中定義的部署步驟。
3. 部署完成後，Cloud Function 會自動更新。

### 本地開發與測試

#### 環境設定
1. 建立虛擬環境並安裝依賴：
```bash
python -m venv venv

source venv/bin/activate  # Linux/Mac
# 或
.\venv\Scripts\activate  # Windows

pip install -r requirements.txt
```

2. 設定 Google Cloud 認證：
```bash
# 方法一：使用服務帳號金鑰
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"

# 方法二：使用 gcloud CLI 登入
gcloud auth application-default login
```

#### 本地運行
使用 functions-framework 啟動本地伺服器：
```bash
functions-framework --target=main_router --debug
```
伺服器會在 http://localhost:8080 啟動。

#### 本地測試
使用 curl 發送測試請求：
```bash
curl -X POST http://localhost:8080 \
-H "Content-Type: application/json" \
-d '{
    "ec_ids": [107],
    "start_time": "2023-10-01T00:00:00Z",
    "end_time": "2023-10-02T00:00:00Z"
}'
```

#### 單元測試
執行所有測試：
```bash
python -m pytest tests/ -v
```

執行特定測試：
```bash
python -m pytest tests/test_main.py -k "test_get_audience_mapping" -v
```

## Makefile 指令

本專案支援以 Makefile 搭配 Docker buildx 進行本地開發、測試與建置，確保環境與 Cloud Run/CI/CD 一致。

常用指令如下：

| 指令           | 功能說明                                  |
|----------------|------------------------------------------|
| make build     | 使用 buildx 建立 linux/amd64 image        |
| make test      | 執行單元測試（不含 performance 測試）     |
| make test-all  | 執行所有 pytest 測試（含 performance）     |
| make shell     | 進入 container shell (debug 用)           |
| make clean     | 刪除本地 image                            |
| make logs      | 顯示 pytest 測試 log（logs/pytest.log）    |
| make lint      | 程式碼靜態檢查 (flake8)                   |
| make format    | 自動格式化 (black)                        |
| make push      | 推送 image 到 registry（預設自動用 branchname-abcdefgh tag，或用 IMAGE_TAG 覆蓋）|
| make help      | 顯示所有指令簡介                          |

#### Tag 說明
- 所有 build/test/shell/push 相關指令，預設都會自動以「branch 名 + git commit sha 前8碼」為 image tag
- 例：`main-1a2b3c4d`、`feature-foo-9f8e7d6c`
- 你也可以用 `IMAGE_TAG=xxx` 覆蓋（如 `make build IMAGE_TAG=release-20250527`）

#### 範例
```sh
make build          # 產生的 image tag 會自動是 branch-commitsha8
make test           # 用同一 tag 跑測試
make shell          # 進 container shell
make push           # 推送同一 tag 的 image 到 registry
make build IMAGE_TAG=release-20250527  # 手動指定 tag
```

---

## 使用說明

### 新增電商流程

當需要為新的電商加入用戶統計功能時，需要執行一些步驟：
詳細的使用說明請參考 [腳本說明文件](scripts/README.md)

### 上傳受眾規則設定

將受眾規則設定上傳到 Cloud Storage：

```bash
# 確保已安裝 gsutil 並設定好認證
gsutil cp scripts/audience_rules.json gs://tagtoo-ml-workflow/LTA/user_stats_configs/audience_rules.json

# 確認上傳結果
gsutil ls -l gs://tagtoo-ml-workflow/LTA/user_stats_configs/audience_rules.json
```

或者也可以使用

```bash
python scripts/upload_rules.py
```

### 發送請求

使用 `POST` 請求來調用 Cloud Function，請求的主體應該是 JSON 格式。

#### 請求參數說明

- `ec_ids`: 要處理的 EC ID 列表（必填）
- `start_time`: 開始時間（選填，預設為前一天 UTC+8 的凌晨1點）
- `end_time`: 結束時間（選填，預設為 start_time 加 24 小時）
- `log_level`: 日誌級別（選填，預設為 "INFO"）
  - 可選值為 "DEBUG"、"INFO"、"WARNING"、"ERROR"
  - 設為 "DEBUG" 時會記錄更詳細的執行資訊，便於問題診斷
  - 設為 "ERROR" 時僅記錄錯誤訊息，可減少日誌量
- `should_calculate_stats`: 是否計算統計資料（選填，預設為 true）
  - 當設為 true 時，會重新計算該時間區間的用戶統計資料
  - 當設為 false 時，會直接從 `tagtoo-tracking.event_prod.user_stats` 表中讀取該時間區間最新的統計資料，不會重新計算
- `should_save_snapshot`: 是否儲存快照（選填，預設為 true）
- `should_write_lta`: 是否寫入 LTA（選填，預設為 true）
- `should_update_user_stats_table`: 是否更新 BigQuery 的 user_stats 表格（選填，預設為 true）
  - 當設為 true 時，會將處理後的使用者統計資料更新到 BigQuery 的 user_stats 表格
  - 當設為 false 時，不會進行 user_stats 表格的更新操作
- `add_to_dxp`: 是否加入 DXP 平台（選填，預設為 true）
  - 當設為 true 時，受眾編號格式為 `tm:c_9999_[ec_id]_c_XXX`
  - 當設為 false 時，受眾編號格式為 `tm:c_[ec_id]_c_XXX`
- `auto_repair_missing_days`: 是否自動修復缺失的天數（選填，預設為 true）
  - 當設為 true 時，若提供的時間範圍超過一天，會自動檢查並修復每一天的用戶統計資料
  - 當設為 false 時，只會處理指定的時間範圍，不會進行額外的每日修復
- `max_repair_days`: 自動修復的最大天數（選填，預設為 30）
  - 限制自動修復的最大天數，避免處理過長的時間範圍導致效能問題
  - 只有當 `auto_repair_missing_days` 為 true 時才有效

#### 請求範例

```json
{
    "ec_ids": [107, 108, 109],
    "start_time": "2023-10-01T00:00:00Z",
    "end_time": "2023-10-02T00:00:00Z",
    "log_level": "DEBUG",
    "add_to_dxp": false,
    "should_calculate_stats": true,
    "should_save_snapshot": true,
    "should_write_lta": true,
    "should_update_user_stats_table": true,
    "auto_repair_missing_days": true,
    "max_repair_days": 30
}
```

#### 使用 `curl` 發送請求

```bash
curl -X POST https://YOUR_CLOUD_FUNCTION_URL \
-H "Content-Type: application/json" \
-d '{
    "ec_ids": [107, 108, 109],
    "start_time": "2023-10-01T00:00:00Z",
    "end_time": "2023-10-02T00:00:00Z",
    "log_level": "DEBUG",
    "add_to_dxp": false,
    "should_update_user_stats_table": true
}'
```

#### 時間參數說明

- 時間格式：ISO 8601 格式（例如：`YYYY-MM-DDTHH:mm:ssZ`）
- 時區處理：
  - 如果提供的時間沒有時區資訊，會被視為 UTC 時間
  - 如果提供的時間有時區資訊，會自動轉換為 UTC
- 預設時間：
  - 如果未提供時間參數，預設會處理前一天的資料
  - 預設的開始時間是前一天 UTC+8 的凌晨 1 點（UTC 17:00）
  - 預設的結束時間是開始時間加 24 小時

### ⚡ 避免 OOM 問題的 BigQuery 計算模式

當處理大量資料時可能遇到記憶體不足（OOM）問題，此時可以使用 BigQuery 計算模式來直接在 BigQuery 中進行計算。我們提供兩種不同的使用方式，適用於不同的場景：

#### 🔄 **完整資料處理（API 方式）**
適用於需要重新計算所有用戶統計、分群計算、快照生成等完整流程的場景：

```bash
curl -X POST https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/lta-user-stats/update-user-stats-table \
  -H "Content-Type: application/json" \
  -d '{
    "ec_ids": [107, 2980, 3819, 3820],
    "start_time": "2025-05-28T00:00:00Z",
    "end_time": "2025-06-02T00:00:00Z",
    "timezone_str": "UTC",
    "use_bigquery_compute": true,
    "skip_time_standardization": false,
    "only_standardize_required_columns": true
  }'
```

**功能包含：**
- ✅ 重新計算所有用戶統計資料
- ✅ 動態分群計算和規則評估
- ✅ 儲存快照到 Cloud Storage
- ✅ 寫入 LTA 特殊表格
- ✅ 成本計算和詳細統計報告

#### 🎯 **精確欄位更新（腳本方式）**
適用於只需要補充或更新特定欄位（如新增的購物車、瀏覽商品時間等）的場景：

```bash
# 僅更新特定欄位，效率更高
./scripts/update_fields.sh \
  --ec-ids=107,2980,3819,3820 \
  --fields=first_add_to_cart_time,last_add_to_cart_time,first_view_item_time,last_view_item_time \
  --start-date=2025-05-28 \
  --end-date=2025-06-02 \
  --use-bigquery-compute \
  --dry-run  # 先評估成本，實際執行時移除此參數
```

**功能包含：**
- ✅ 精確更新指定欄位
- ✅ 直接在 BigQuery 中執行 UPDATE 語句
- ✅ 可選擇性更新 GCS 快照（`--update-gcs`）
- ✅ 成本預估（`--dry-run`）

#### 📊 **方式選擇指南**

| 使用場景 | 推薦方式 | 適用情況 |
|----------|----------|----------|
| **定期完整更新** | API 方式 | 重新計算所有統計、分群更新、LTA 寫入 |
| **補充缺失欄位** | 腳本方式 | 新增欄位後的歷史資料補充 |
| **緊急修復** | 腳本方式 | 快速修復特定欄位的資料問題 |
| **開發測試** | 腳本方式 | 先用 `--dry-run` 評估成本 |

#### 💡 **共同優勢**

無論選擇哪種方式，BigQuery 計算模式都提供：

- ✅ **避免 OOM**：所有計算在 BigQuery 伺服器上進行
- ✅ **節省本地記憶體**：不需要載入大量資料到本地記憶體
- ✅ **穩定處理**：利用 BigQuery 的分散式計算能力
- ✅ **成本透明**：提供詳細的查詢成本估算

### 批次重算腳本

為了方便處理跨多日的數據重算需求，本專案提供了專門的批次重算腳本 `scripts/recalculate_lta.sh`。此腳本會自動將長時間範圍按日分割執行，確保資料完整性和穩定性。

#### 🚀 **主要特性**

- **自動日期分割**：將長時間範圍自動按日分割，避免超時和記憶體問題
- **Docker 環境支援**：支援在 Docker 容器內外運行
- **錯誤處理**：完整的錯誤處理和日誌記錄機制
- **乾跑模式**：提供 `--dry-run` 選項，可預覽執行計畫而不實際執行
- **進度追蹤**：顯示詳細的執行進度和統計資訊
- **失敗重試**：記錄失敗的日期範圍，便於後續重試

#### 📋 **環境變數配置**

| 變數名稱 | 說明 | 預設值 |
|---------|------|--------|
| `EC_IDS` | 店家 IDs（逗號分隔） | `107` |
| `START_TIME` | 開始時間（ISO 8601 格式） | `2025-05-31T16:00:00Z` |
| `END_TIME` | 結束時間（ISO 8601 格式） | `2025-06-23T16:00:00Z` |
| `USE_BIGQUERY_COMPUTE` | 是否使用 BigQuery 計算 | `true` |
| `VERBOSE` | 詳細模式 | `true` |
| `DRY_RUN` | 乾跑模式 | `false` |
| `IN_DOCKER` | 是否在 Docker 環境內 | `false` |
| `DOCKER_TAG` | Docker image tag | `latest` |
| `GCP_CREDENTIALS_FILE` | GCP 認證檔案路徑 | `tagtoo-ml-workflow-kubeflow.json` |

#### 💻 **使用方式**

**基本用法：**
```bash
# 使用預設設定
./scripts/recalculate_lta.sh

# 自訂日期範圍
START_TIME=2025-06-01T16:00:00Z END_TIME=2025-06-05T16:00:00Z ./scripts/recalculate_lta.sh

# 乾跑模式（只顯示要執行的指令）
DRY_RUN=true ./scripts/recalculate_lta.sh

# 多個店家 ID
EC_IDS="107,108,109" ./scripts/recalculate_lta.sh
```

**在 Docker 環境內執行：**
```bash
# 設定環境變數表示已在 Docker 內
IN_DOCKER=true ./scripts/recalculate_lta.sh
```

**命令列選項：**
```bash
# 顯示使用說明
./scripts/recalculate_lta.sh --help

# 使用命令列選項
./scripts/recalculate_lta.sh --dry-run --verbose
```

#### 📊 **執行範例**

處理 2025-05-31 至 2025-06-23 共 23 天的資料：

```bash
EC_IDS=107 \
START_TIME=2025-05-31T16:00:00Z \
END_TIME=2025-06-23T16:00:00Z \
USE_BIGQUERY_COMPUTE=true \
./scripts/recalculate_lta.sh
```

**預期輸出：**
```
=== LTA User Stats 批次重算開始 ===
EC IDs: 107
日期範圍: 2025-05-31T16:00:00Z 到 2025-06-23T16:00:00Z
BigQuery 計算: true
共產生 23 個日期範圍

[1/23] 處理日期範圍: 2025-05-31T16:00:00Z 到 2025-06-01T16:00:00Z
[2/23] 處理日期範圍: 2025-06-01T16:00:00Z 到 2025-06-02T16:00:00Z
...
✓ [23/23] 完成: 2025-06-22T16:00:00Z 到 2025-06-23T16:00:00Z

=== 批次處理完成 ===
總計處理: 23 個日期範圍
成功: 23 個
失敗: 0 個
🎉 所有日期範圍都處理成功！
```

#### 🔧 **進階功能**

**日誌記錄：**
- 所有執行日誌會儲存在 `logs/batch_recalculate/` 目錄
- 日誌檔案命名格式：`recalculate_YYYYMMDD_HHMMSS.log`

**錯誤處理：**
- 失敗的日期範圍會被記錄，便於後續重試
- 每個範圍之間有 3 秒延遲，避免過度負載

**相容性：**
- 支援 macOS（使用 `gdate`）和 Linux 環境
- 自動檢測並使用適當的日期工具

#### ⚠️ **注意事項**

1. **時間格式**：必須使用 ISO 8601 格式（如 `2025-06-01T16:00:00Z`）
2. **認證檔案**：確保 GCP 認證檔案存在於指定路徑
3. **Docker 權限**：在 Docker 環境外執行時，確保 Docker 可正常運行
4. **時間跨度**：建議一次處理不超過 30 天的資料
5. **網路穩定性**：長時間執行時請確保網路連線穩定

#### 📈 **效能建議**

- **小範圍測試**：建議先用 1-2 天的範圍測試，確認設定正確
- **分批執行**：對於超大範圍，可考慮分多次執行
- **監控資源**：注意監控 BigQuery 查詢配額和成本
- **最佳時間**：建議在非尖峰時段執行大量資料處理

## 必要配置

### GCP 資源使用說明

#### 專案清單
- `tagtoo-tracking`: 存放原始事件資料和使用者統計資料
- `tagtoo-ml-workflow`: 存放 LTA 相關資料和快照
- `tagtoo-dxp`: DXP 平台相關資源（如果 add_to_dxp=True）

#### BigQuery 表格
1. 來源資料表
   - 專案：`tagtoo-tracking`
   - 資料集：`event_prod`
   - 表格：`tagtoo_event`
   - 用途：原始事件資料來源

2. 使用者統計表
   - 專案：`tagtoo-tracking`
   - 資料集：`event_prod`
   - 表格：`user_stats`
   - 用途：儲存使用者的統計資訊
   - 更新時機：當 should_calculate_stats=True 時
   - **特別注意**：`last_interaction_time` 欄位被設為 REQUIRED，NULL 值會被過濾

3. 特殊 LTA 表
   - 專案：`tagtoo-ml-workflow`
   - 資料集：`tagtoo_export_results`
   - 表格：`special_lta_temp_for_update_YYYYMMDD`（每日一張表）
   - 用途：儲存使用者分群結果
   - 更新時機：當 should_write_lta=True 時
   - **特別注意**：`permanent` 和 `segment_id` 欄位被設為 REQUIRED

#### Cloud Storage
- 專案：`tagtoo-ml-workflow`
- Bucket：`tagtoo-ml-workflow`
- 路徑：`LTA/user_stats_snapshots/ec_{EC_ID}/{YYYYMMDD}.parquet`
  - 舊格式（向後兼容）：`LTA/user_stats_snapshots/user_stats_{YYYYMMDD}.parquet`
- 用途：儲存每日使用者統計資料的快照
- 更新時機：當 should_save_snapshot=True 時

### BigQuery 表格欄位模式說明

為確保數據一致性和避免資料庫模式錯誤，以下欄位被設為 REQUIRED（必須）：

#### 1. `user_stats` 表格

| 欄位名稱 | 資料型別 | 模式 | 說明 |
|---------|---------|------|------|
| `ec_id` | INT64 | REQUIRED | 電子商務 ID |
| `permanent` | STRING | REQUIRED | 用戶唯一識別碼 |
| `purchase_count` | INT64 | REQUIRED | 購買次數 |
| `first_interaction_time` | TIMESTAMP | REQUIRED | 首次互動時間 |
| `last_interaction_time` | TIMESTAMP | REQUIRED | 最後互動時間 |
| `total_purchase_amount` | FLOAT64 | REQUIRED | 總購買金額 |
| `total_sessions` | INT64 | REQUIRED | 總工作階段數 |
| `registration_time` | TIMESTAMP | NULLABLE | 註冊時間（可為 NULL） |
| `first_purchase_time` | TIMESTAMP | NULLABLE | 首次購買時間（可為 NULL） |
| `last_purchase_time` | TIMESTAMP | NULLABLE | 最後購買時間（可為 NULL） |
| `first_add_to_cart_time` | TIMESTAMP | NULLABLE | 首次加入購物車時間（可為 NULL） |
| `last_add_to_cart_time` | TIMESTAMP | NULLABLE | 最後加入購物車時間（可為 NULL） |
| `first_view_item_time` | TIMESTAMP | NULLABLE | 首次查看商品時間（可為 NULL） |
| `last_view_item_time` | TIMESTAMP | NULLABLE | 最後查看商品時間（可為 NULL） |
| `created_at` | TIMESTAMP | NULLABLE | 記錄創建時間（可為 NULL） |
| `updated_at` | TIMESTAMP | NULLABLE | 記錄更新時間（可為 NULL） |

#### 2. `special_lta_temp_for_update_{YYYYMMDD}` 表格

| 欄位名稱 | 資料型別 | 模式 | 說明 |
|---------|---------|------|------|
| `permanent` | STRING | REQUIRED | 用戶唯一識別碼 |
| `segment_id` | STRING | REQUIRED | 分群識別碼，多個用逗號分隔 |

#### 重要說明

1. **NULL 值過濾**：所有 REQUIRED 欄位不允許有 NULL 值。系統會自動過濾掉這些欄位為 NULL 的記錄，以避免資料庫錯誤。

2. **欄位處理邏輯**：
   - `last_interaction_time` 和 `first_interaction_time` 為 NULL 的記錄會被過濾掉
   - 其他 REQUIRED 欄位如為 NULL，會被填充預設值：
     - 數值型欄位填充為 0
     - 字串型欄位維持不變（不應為 NULL）

3. **NaT 與 NULL 轉換**：Pandas DataFrame 中的 NaT 值在序列化過程中會被轉換為 None，然後在過濾階段處理

4. **注意事項**：此模式設定在以下三個函數的實現中均已一致：
   - `create_user_stats_table`：創建表格時的模式定義
   - `update_user_stats_table`：更新表格時的模式定義
   - `restore_from_backup`：從備份還原時的模式定義

## 規則設定檔結構
規則設定檔儲存於 Cloud Storage，採用 JSON 格式。每個 EC ID 都有自己的規則集合，包含多個受眾規則：

```json
{
    "107": {
        "metadata": {
            "created_at": "2024-03-15T08:00:00Z",
            "updated_at": "2024-06-18T14:30:00Z"
        },
        "rules": {
            "tm:c_9999_107_c_001": {
                "description": "前2個月~1年有互動的用戶",
                "metadata": {
                    "created_at": "2024-03-15T08:00:00Z",
                    "updated_at": "2024-06-18T14:30:00Z"
                },
                "data": {
                    "min_days": 30,
                    "max_days": 365
                },
                "rule": {
                    "and": [
                        {"!=": [{"var": "last_interaction"}, null]},
                        {">": [{"var": "last_interaction_days"}, {"var": "min_days"}]},
                        {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                    ]
                }
            },
            "tm:c_9999_107_c_013": {
                "description": "近4個月內購買過第2~第5次",
                "metadata": {
                    "created_at": "2024-06-01",
                    "updated_at": "2024-06-20"
                },
                "data": {
                    "min_purchases": 2,
                    "max_purchases": 5,
                    "max_days": 120
                },
                "rule": {
                    "and": [
                        {">=": [{"var": "purchase_count"}, {"var": "min_purchases"}]},
                        {"<=": [{"var": "purchase_count"}, {"var": "max_purchases"}]},
                        {"!=": [{"var": "last_interaction"}, null]},
                        {"<=": [{"var": "last_interaction_days"}, {"var": "max_days"}]}
                    ]
                }
            }
        }
    }
}
```

### 規則結構說明

1. **EC ID 層級**
   - `metadata`: EC 層級的元數據
     - `created_at`: 規則集建立時間
     - `updated_at`: 規則集最後更新時間
   - `rules`: 包含該 EC 的所有受眾規則

2. **規則層級**
   - `description`: 規則描述，支援變數插值（例如：`"最近 {min_days} 天內有互動的用戶"`）
   - `metadata`: 規則層級的元數據
     - `created_at`: 規則建立時間
     - `updated_at`: 規則最後更新時間
   - `data`: 規則參數配置
     - 支援數值型參數（自動轉換為整數）
     - 用於規則邏輯中的變數引用
   - `rule`: JSON Logic 格式的規則定義
     - 支援多種運算符：`and`, `or`, `>`, `>=`, `<`, `<=`, `==`, `!=`
     - 使用 `{"var": "欄位名稱"}` 引用使用者資料
     - 使用 `{"var": "參數名稱"}` 引用 `data` 中的參數

### 規則評估引擎

核心評估邏輯位於 `src/rules/dynamic_audience_rules.py`，主要功能包括：

1. **時區處理**
   ```python
   def ensure_utc(dt):
       if isinstance(dt, datetime):
           if dt.tzinfo is None:
               return dt.replace(tzinfo=timezone.utc)
           return dt.astimezone(timezone.utc)
       return dt
   ```

2. **資料預處理**
   - 自動轉換時間欄位到 UTC
   - 處理欄位名稱一致性（例如：`last_interaction` → `last_interaction_time`）
   - 數值參數自動轉換為整數

3. **規則評估**
   ```python
   def evaluate_rule(mapping: dict, stats: dict) -> bool:
       # 資料預處理
       processed = {
           k: ensure_utc(v) if isinstance(v, datetime) else v
           for k, v in stats.items()
       }

       # 參數處理
       rule_params = {
           k: int(float(v)) if isinstance(v, (int, float, str)) and not isinstance(v, bool) else v
           for k, v in mapping.get('data', {}).items()
       }

       # 合併資料
       data = {**processed, **rule_params}

       # 評估規則
       try:
           result = jsonLogic(mapping.get('rule', mapping), data)
           return bool(result) if result is not None else False
       except Exception as e:
           logger.error(f"規則評估失敗: {str(e)}")
           return False
   ```

### 效能優化

1. **資料型別優化**
```python
dtype_optimized = {
    'ec_id': 'category',
    'permanent': 'category',
    'purchase_count': 'uint16',
    'total_purchase_amount': 'float32'
}
```

2. **向量化處理**
- 使用 `vectorized_evaluate` 進行批量規則評估
- 自動計算 `last_interaction_days`
- 優化記憶體使用

3. **錯誤處理**
- 完整的例外處理機制
- 詳細的錯誤日誌
- 優雅的錯誤回傳

## 資料處理特性

本專案在處理資料時具有以下特性：

1. **大量資料處理**：設計時考慮了處理海量數據的效能問題，採用分批處理機制。
2. **自動修復**：系統會檢測缺失的日期資料並嘗試自動修復。
3. **冪等性設計**：同一請求多次處理會產生相同結果，避免資料重複或不一致。
4. **中斷恢復**：長時間運行的任務支援中斷後恢復執行。
5. **資料驗證**：所有輸入和輸出數據都經過嚴格驗證，確保符合預期格式。
6. **安全處理 pandas Series 真值操作**：針對 pandas Series 的布爾運算，程式碼採用安全模式，避免如 `if series:` 這類直接使用 Series 作為條件的操作，而是使用 `.any()`、`.all()` 等明確方法，或使用布爾運算符 `&` 和 `|` 而非 `and` 和 `or` 進行條件組合，避免 "truth value of a Series is ambiguous" 錯誤。同時通過使用索引列表而非布爾掩碼來避免 "unhashable type: Series" 錯誤。

## 監控與維護

- 規則檔案更新後，Cloud Function 會自動重新載入最新設定
- 可在 Cloud Storage 控制台查看檔案版本歷史記錄
- 可通過 Cloud Logging 查看 Cloud Function 的執行日誌
- 可通過 BigQuery 的查詢歷史查看數據處理記錄
- 可通過 Cloud Monitoring 設置告警，監控 Cloud Function 的執行情況

## 執行追蹤系統

本專案實現了統一的執行追蹤系統，為所有資料處理操作提供一致的識別標準。

### 主要特性

- **時間相關 execution_id**: 所有執行 ID 都包含時間資訊，格式為 `業務前綴_YYYYMMDD_HHMMSS`
- **執行一致性**: 單次執行中的所有相關操作使用相同的 execution_id
- **業務語義化**: 包含業務邏輯標識，便於識別執行任務類型
- **工作流分析**: 支援跨系統的執行追蹤和效能分析

### BigQuery Scheduled Query 實現

**EC 107 產品分群查詢**（`sql/scheduled_queries/ec_107_product_segments.sql`）使用統一設計：

```sql
WITH execution_info AS (
  SELECT
    CONCAT('ec_107_segments_', FORMAT_TIMESTAMP('%Y%m%d_%H%M%S', CURRENT_TIMESTAMP())) AS execution_id,
    CURRENT_TIMESTAMP() AS created_at
)
-- 所有分群查詢都使用相同的 execution_id
```

**execution_id 格式範例**: `ec_107_segments_20250619_143052`

### 詳細文檔

完整的執行追蹤設計說明請參考 [執行追蹤文檔](docs/execution_tracking.md)，包含：

- execution_id 設計原則
- 時間格式標準
- 工作流事件分析方法
- 監控與告警設定
- 最佳實踐建議

### 測試與測試覆蓋率

本專案測試覆蓋率目前為 77%，相較於之前的 66% 有明顯提升。主要的改進來自新增加的 JSON 序列化和 NaT 值處理相關測試。

#### 測試覆蓋率詳情

| 檔案                                  | 覆蓋率  | 狀態      |
|--------------------------------------|--------|-----------|
| src/utils/json_helpers.py            | 100%   | ✅ 完整    |
| src/optimization/memory_optimizer.py | 100%   | ✅ 完整    |
| src/__init__.py                      | 100%   | ✅ 完整    |
| src/utils/__init__.py                | 99%    | ✅ 優良    |
| src/core/data_processing.py          | 86%    | ✅ 良好    |
| src/main.py                          | 86%    | ✅ 良好    |
| src/optimization/parallel_processing.py | 85%  | ✅ 良好    |
| src/config/__init__.py               | 80%    | ✅ 良好    |
| src/rules/dynamic_audience_rules.py  | 80%    | ✅ 良好    |
| src/utils/logging_setup.py           | 80%    | ✅ 良好    |
| src/core/cloud_integration.py        | 70%    | ⚠️ 中等    |
| src/utils/error_handling.py          | 54%    | ⚠️ 需改進   |
| src/utils/time_utils.py              | 22%    | ❌ 偏低     |
| src/utils/format_utils.py            | 17%    | ❌ 偏低     |
| src/core/cost_calculation.py         | 0%     | ❌ 無覆蓋   |

#### 生成覆蓋率報告：
```bash
# 生成命列覆蓋率報告
python -m pytest --cov=src tests/

# 生成 HTML 格式的詳細報告
python -m pytest --cov=src --cov-report=html tests/
```

## 維護者

Tagtoo Data Team

## 如何貢獻

歡迎提交 Issue 與 Pull Request。

## 開發工具

### Parquet 時間範圍檢查工具

本專案包含一個強大的 parquet 檔案時間範圍檢查工具，用於分析 Google Cloud Storage 中的 parquet 檔案時間覆蓋範圍和資料完整性。

#### 主要功能

- 🔍 **檔案發現**: 自動掃描 GCS bucket 中的 parquet 檔案
- ⏰ **時間分析**: 提取和分析檔案中的時間戳資料
- 📊 **覆蓋範圍分析**: 計算整體時間覆蓋範圍和統計資訊
- 🕳️ **間隙檢測**: 識別時間序列中的缺失時段
- 📈 **詳細報告**: 生成包含統計資訊、時間範圍和間隙的綜合報告
- 🏷️ **日期分組**: 按日期分組檔案，便於理解資料分布

#### 快速開始

```bash
# 檢查特定 bucket 中的 parquet 檔案時間範圍
python scripts/parquet_time_range_checker.py \
  --bucket-name your-bucket-name \
  --prefix data/

# 使用 Docker 執行（推薦）
make build-test
docker run --rm -v "$(pwd)":/app lta-user-stats:test \
  python scripts/parquet_time_range_checker.py \
  --bucket-name your-bucket-name
```

#### 詳細文件

- [快速開始指南](QUICKSTART.md) - 立即上手使用
- [詳細使用說明](docs/parquet_time_range_checker.md) - 完整功能介紹
- [使用範例](examples/parquet_checker_examples.py) - 實際應用案例

#### 適用場景

- **資料品質檢查**: 驗證 parquet 檔案的時間完整性
- **時間序列監控**: 識別資料管道中的時間間隙
- **資料遷移驗證**: 確保資料遷移後的完整性
- **定期健康檢查**: 監控生產環境中的資料狀態

## 最近變更

- **2025-07-29: 安全遷移至 Polars 高效能實現**：
  - 實現雙實現架構：保留 pandas 實現並新增 polars 實現
  - 修復 pandas 實現中的 sessions 累加邏輯錯誤（只賦值而非累加）
  - 修復 polars 實現中的 join key 處理問題（full outer join 時 key 變 null）
  - 固定 polars-lts-cpu 版本為 1.31.0，確保開發與生產環境一致性
  - 新增 `test_pandas_vs_polars_implementation_consistency` 測試確保兩種實現產生相同結果
  - 修復 `test_daily_query_and_processing_field_name_consistency` 語法錯誤
  - 保留所有原始驗證邏輯，確保資料完整性和向後兼容性
  - **預設啟用 Polars 高效能模式**（`use_polars=True`），可選擇 pandas 實現
  - 預期效能提升：更高效的記憶體管理、向量化操作和懶惰評估

- **2025-07-28: 修復 BigQuery row size 超過 100MB 限制問題**：
  - 解決了 `JSON parsing error in row starting at position 0: Row size is larger than: 104857600` 錯誤
  - 實作自動 row size 檢查和切分機制，防止單一記錄超過 100MB 限制
  - 新增 `check_row_size`、`split_large_record`、`process_records_with_size_check` 函數
  - 支援字串和列表欄位的智能切分，保留資料關聯性
  - 詳細記錄切分和丟棄的記錄資訊，便於問題追蹤
  - 新增完整的單元測試：`tests/utils/test_json_helpers.py`
  - 確保所有現有功能不受影響，保持冪等性設計

- **2025-07-01: 修復 EC 107 空字符串導致的 BigQuery 數據類型轉換錯誤**：
  - 解決了 `Invalid value '' for dtype Int64` 錯誤，該錯誤導致 EC ID 107 的 user_stats 表格更新失敗
  - 實現增強版數據清理邏輯：`safe_int_convert_enhanced` 和 `safe_float_convert_enhanced` 函數
  - 新增 ec_id 字段驗證機制，檢測並過濾無效的 ec_id 值（空字符串、NULL 等）
  - 改進錯誤日誌記錄，提供詳細的轉換統計和錯誤信息
  - 新增專門測試用例：`test_ec_107_empty_string_issue` 和 `test_ec_id_empty_string_handling`
  - 確保所有數值字段（purchase_count、total_sessions、total_purchase_amount）能正確處理空字符串和無效值
  - 調整 terraform scheduled query location 從 "US" 改為 "asia-east1" 以提升性能

- **2025-06-25: BigQuery 數據類型轉換修復**：
  - 修復了 `Invalid value '' for dtype Int64` 錯誤，解決 BigQuery user_stats 表格更新失敗問題
  - 實現智能 fillna 邏輯，根據欄位數據類型使用適當的填充值（整數欄位填充 0，浮點數欄位填充 0.0，字串欄位填充空字串）
  - 改進安全類型轉換函數，增強錯誤處理和詳細日誌記錄
  - 新增專門的測試案例驗證修復效果，確保數據類型轉換的穩定性

- **2025-06-19: BigQuery Scheduled Query execution_id 統一設計**：
  - 修改 EC 107 產品分群查詢，使用統一的時間相關 execution_id
  - 執行 ID 格式：`ec_107_segments_YYYYMMDD_HHMMSS`（例如：`ec_107_segments_20250619_143052`）
  - 單次執行中所有分群使用相同的 execution_id，提升工作流事件分析能力
  - 使用 WITH 子句確保所有 UNION ALL 分段使用相同的時間戳
  - 更新相關測試以配合新的統一設計，移除對舊 GENERATE_UUID() 的檢查
  - 新增執行追蹤系統文檔 (`docs/execution_tracking.md`)，說明設計原則和使用方法

- **2025-06-02: 修正 functions-framework 路由配置**：
  - 移除了 `src/main.py` 中重複的 `main_router` 函數定義，避免函數重複定義問題
  - 修正 Dockerfile 中的 `--source` 參數，從 `main` 改為 `main.py`，確保 functions-framework 能正確找到源文件
  - 驗證了 HTTP 路由功能正常運作，包括 `/calculate-user-stats` 和 `/update-user-stats-table` 端點
  - 確保了無效路由會回傳適當的 404 錯誤訊息

- **2025-05-20: 新增 add_to_cart 和 view_item 事件的統計**：
  - 新增了 `first_add_to_cart_time`、`last_add_to_cart_time`、`first_view_item_time` 和 `last_view_item_time` 欄位
  - 比照 purchase 事件的處理方式，追蹤用戶的購物車和瀏覽商品行為
  - 更新了相關查詢和數據處理邏輯
  - 新增了 `update_fields.sh` 腳本，用於更新現有 EC 的特定欄位數據
  - 修改了 `init_user_stats_for_ec_id.py` 腳本，支援初始化這四個新欄位

- **2025-04-15: 將 LTA 分群資料寫入臨時表格**：
  - 修改了寫入目標表格，從 `special_lta_{YYYYMMDD}` 改為 `special_lta_temp_for_update_{YYYYMMDD}`
  - 修改了資料寫入格式，不再將每個 permanent 的多個 segment_id 合併為逗號分隔的字串，而是為每個 permanent 和 segment_id 組合創建一筆記錄
  - 更新了相關測試以適應新的資料寫入格式
  - 這項修改使得在臨時表格中，每個 permanent 和 segment_id 組合都有一筆獨立的記錄，便於後續處理

- **BigQuery 直接計算功能**：添加了 `--use-bigquery-compute` 選項到 `scripts/init_user_stats_for_ec_id.py`，允許直接在 BigQuery 中執行統計計算，無需下載原始數據。此功能適合處理大型資料集，能顯著提高效能並減少記憶體使用。詳見 [腳本說明](scripts/README.md#bigquery-直接計算功能)。
- **日誌級別參數**：新增了 `log_level` 參數，允許使用者動態設定日誌級別（DEBUG、INFO、WARNING、ERROR），便於開發和調試過程中獲取不同詳細程度的執行資訊
- **互動時間欄位優先順序調整**：修正了 `update_user_stats_with_daily_data` 函數中欄位搜尋的優先順序，優先使用帶有 `_daily` 後綴的欄位（如 `first_interaction_of_day_daily` 和 `last_interaction_of_day_daily`），確保新用戶使用的是最新的每日互動數據而非 snapshot 中可能存在的舊數據
- **改進 JSON 序列化處理**：新增了 `json_helpers` 模組，專門處理 DataFrame 中的 NaT 值和時間戳，避免序列化錯誤
- **欄位模式優化**：在 BigQuery 表格定義中將關鍵欄位（`last_interaction_time`, `permanent`, `segment_id`）設為 REQUIRED，提高數據質量
- **NULL 值過濾**：增強了對 `last_interaction_time` 為 NULL 值的處理，確保寫入 BigQuery 時的數據一致性
- **測試覆蓋率提升**：新增測試用例，特別是針對 NaT 值處理和 BigQuery 欄位模式設定，測試覆蓋率從 66% 提升至 77%
- **備份還原改進**：優化了 `restore_from_backup` 方法，加入對 NULL 值的篩選，確保還原的數據與當前數據模式一致
- **效能優化**：重寫 `update_user_stats_table` 方法，實現並行批次處理、增大批次大小和 PyArrow 加速，預計提升 3-5 倍性能
- **錯誤處理強化**：改進 `calculate_user_stats` 中的日誌格式化，確保對 MagicMock 物件的處理，修復了單元測試中的格式化錯誤
- **測試模擬修復**：修正測試中的 `BigQueryClient` 模擬路徑問題，移除了不必要的模擬，簡化了測試代碼
- **空資料處理**：優化了主函數中空 DataFrame 的處理，確保即使資料為空也能正常調用 `to_json_safe` 函數
- **動態分群計算**：修復了動態分群計算時的錯誤處理，確保在計算動態分群時的異常不會中斷整體流程
- **自動修復功能**：添加了 `auto_repair_missing_days` 和 `max_repair_days` 功能標誌，允許系統自動檢測和修復缺失的歷史資料，並限制最大修復天數以避免效能問題
- **2024-03-11: 修復 Pandas Series 真值錯誤**
  * 修正了在處理 pandas Series 時可能出現的 "the truth value of a Series is ambiguous" 錯誤，主要透過以下方式：
    - 在需要評估 Series 布爾條件時，使用 `.any()` 或 `.all()` 明確指定評估方式
    - 在組合多個條件時，使用 `&` 和 `|` 運算符而非 `and` 和 `or`
    - 避免將 Series 直接放入 `if` 條件中
  * 解決了 "unhashable type: Series" 錯誤，主要透過：
    - 在 `calculate_audience_segments_dynamic` 函數中，使用 `.tolist()` 將索引轉換為列表，而非直接使用布爾掩碼
    - 添加了專門的單元測試確保這些修復持續有效
  * 增強了程式碼的穩健性，即使在特殊數據情況下也能正確處理（如 `-1` 值和 `NaN` 值）

### 2025-03-14: 修復時間精度不一致問題

修復了 `first_interaction_time` 欄位時間精度不一致的問題：
- 新增對 `datetime64[us, UTC]`（微秒精度）格式的支援
- 修改 `init_user_stats_for_ec_id.py` 腳本在儲存時自動轉換為奈秒精度
- 增加 `convert_time_precision.py` 工具用於修復現有的 parquet 檔案

若遇到 `欄位 first_interaction_time 的資料型別不正確` 的錯誤，可使用以下命令修復：

```bash
python scripts/convert_time_precision.py --input-file <parquet_file_path>
```

## 使用者統計資料處理工具

這個專案包含一系列用於處理使用者統計資料的工具，可從 BigQuery 擷取資料、計算統計資訊，並將結果儲存回 BigQuery、GCS 或本地檔案。

## 最新更新

### 2025-07-30: BigQuery JSON 解析錯誤修復與生產環境驗證 ✅

**重大修復完成**: 成功修復生產環境中的 BigQuery JSON 解析錯誤，系統現已穩定運行。

#### 修復內容
- ✅ **JSON 序列化增強**: 修復 numpy 陣列和特殊數據類型的序列化問題
- ✅ **縮排錯誤修復**: 修復 `cloud_integration.py` 中導致 `load_table_from_file` 未執行的縮排錯誤
- ✅ **原子文件寫入**: 實現安全的 JSON 文件寫入和驗證機制
- ✅ **生產環境驗證**: 完成全面的數據完整性和功能驗證

#### 驗證結果
- **測試時間**: 2025-07-30T03:05:26Z
- **處理用戶**: 2,094 位 (EC ID 107)
- **寫入記錄**: 6,282 筆到 BigQuery
- **錯誤狀態**: 零 JSON 解析錯誤 ✅
- **數據一致性**: 臨時表與正式表數據完全一致 ✅

#### 性能表現
- **執行時間**: < 30 秒完成整個流程
- **向量化評估**: 0.02 秒處理 2,094 筆記錄
- **數據寫入**: 2.73 秒寫入 6,282 筆記錄

### 2025-07-29: Polars 高效能實現遷移 ✅

實現雙架構設計，提供最佳性能和穩定性：
- ✅ **Polars 實現**: 預設啟用高效能模式 (`use_polars=True`)
- ✅ **Pandas 實現**: 保留向後兼容性 (`use_polars=False`)
- ✅ **一致性保證**: 兩種實現產生完全相同的結果
- ✅ **修復邏輯錯誤**: 修復 sessions 累加和 join key 處理問題

### 2025-03-14: 修復時間精度不一致問題

修復了 `first_interaction_time` 欄位時間精度不一致的問題：
- 新增對 `datetime64[us, UTC]`（微秒精度）格式的支援
- 修改 `init_user_stats_for_ec_id.py` 腳本在儲存時自動轉換為奈秒精度
- 增加 `convert_time_precision.py` 工具用於修復現有的 parquet 檔案

若遇到 `欄位 first_interaction_time 的資料型別不正確` 的錯誤，可使用以下命令修復：

```bash
python scripts/convert_time_precision.py --input-file <parquet_file_path>
```

## 使用方法

### 初始化特定 EC ID 的使用者統計資料

```bash
python scripts/init_user_stats_for_ec_id.py --ec-id 123
```

選項說明：
- `--ec-id`: 電商 ID（必填）
- `--start-date`: 開始日期，格式為 YYYY-MM-DD，預設為 2022-01-01
- `--end-date`: 結束日期，格式為 YYYY-MM-DD，預設為查詢當下的前一天
- `--skip-bigquery`: 跳過寫入 BigQuery
- `--skip-gcs`: 跳過寫入 GCS
- `--local-parquet`: 產生本地 parquet 檔案
- `--local-path`: 本地 parquet 檔案的儲存路徑，預設為 ./local_data
- `--batch-size`: 每次處理的資料筆數，預設為 10000

### 時間精度轉換工具

```bash
python scripts/convert_time_precision.py --input-file <parquet_file_path> --output-file <output_parquet_path>
```

選項說明：
- `--input-file`: 輸入的 parquet 檔案路徑（必填）
- `--output-file`: 輸出的 parquet 檔案路徑，若不提供則覆蓋輸入檔案
- `--columns`: 要轉換的時間欄位，逗號分隔，預設為 "first_interaction_time,last_interaction_time,first_purchase_time,last_purchase_time,registration_time"
- `--backup`: 是否在覆蓋前備份原檔案，預設為 True
- `--verbose`: 顯示詳細資訊

## 已知問題與解決方案

### 時間精度不一致

**問題描述**: 不同的 pandas/pyarrow 版本在處理 parquet 檔案時可能會使用不同的時間精度，導致在讀取時出現驗證錯誤：`欄位 first_interaction_time 的資料型別不正確`。

**解決方案**:
1. 使用 `convert_time_precision.py` 工具轉換現有的 parquet 檔案
2. 或修改驗證程式碼接受微秒精度 (已在 2025-03-14 版本中更新)

## 測試指令說明

本專案提供多種測試指令，幫助開發者進行高效測試與除錯。

### 基本測試指令

| 指令 | 說明 |
|------|------|
| `make test` | 執行單元測試（不包含效能測試）|
| `make test-all` | 執行所有測試（包含效能測試）|
| `make watch-test` | 監控檔案變更自動執行測試（需安裝 `entr`）|
| `make test-debug` | 使用 pdb 進行互動式除錯 |

### 測試報告與分析

| 指令 | 說明 |
|------|------|
| `make test-report` | 生成 HTML 格式的測試報告（輸出至 `test-reports/` 目錄）|
| `make test-cov` | 顯示程式碼覆蓋率報告 |
| `make test-slow` | 顯示執行時間最長的 10 個測試案例 |

### 測試套件依賴

測試功能依賴以下 Python 套件：
- `pytest`：測試框架
- `pytest-xdist`：平行測試支援
- `pytest-html`：HTML 測試報告
- `pytest-cov`：程式碼覆蓋率分析
- `pytest-durations`：測試時間分析

## Docker 建置優化

### 多階段建置
- 使用兩階段建置（Builder Pattern）減少最終 image 大小
- 建置階段安裝建置依賴，運行階段只保留必要套件
- 最終 image 大小顯著減少

### 快取優化
- 依賴安裝與程式碼分離，充分利用 Docker 快取層
- 使用 `.dockerignore` 排除不必要的檔案，加速建置過程
- 清理不必要的暫存檔和快取

### 清理建置快取
```bash
# 清理未使用的 Docker 物件
make clean-cache
```

## 文件生成

### 測試文件
- 使用 `make test-doc` 生成 API 文件
- 文件輸出至 `docs/test-docs/` 目錄
- 包含所有模組和函數的文件字串（docstring）

### 文件預覽
```bash
# 生成測試文件
make test-doc

# 預覽文件（需安裝 Python http.server）
python3 -m http.server 8000 -d docs/test-docs
```

## 常見 build 問題排查

### 問題：make build 卡在 booting buildkit 很久
- 通常是 buildx/buildkit daemon 啟動異常，與 Dockerfile 無關
- 常見原因：
  - Docker Desktop 沒開啟或資源不足
  - buildx builder 狀態殘留/zombie
  - Docker 設定或權限有問題
  - 本機資源爆掉（RAM/CPU/磁碟）
  - Docker Desktop/Engine 版本太舊
- 解法：
  1. 重啟 Docker Desktop
  2. 執行 `docker buildx ls`，移除異常 builder：`docker buildx rm <builder>`
  3. 手動新建 builder：`docker buildx create --name mybuilder --use`
  4. 升級 Docker Desktop/Engine
  5. 檢查本機資源、proxy、VPN

如還有異常 log，請貼給工程團隊協助診斷。