"""
向量化規則評估器 - 實現真正的向量化處理來提升分群性能

這個模組專門處理受眾分群規則的向量化評估，避免逐行處理的性能瓶頸。
"""

import pandas as pd
import numpy as np
import time
from typing import Dict, List, Any, Optional, Tuple
from collections import defaultdict
import logging

try:
    from src.utils import logging_setup
except ImportError:
    from utils import logging_setup

logger = logging_setup.configure_logging()


class VectorizedRuleEvaluator:
    """向量化規則評估器

    將JSON Logic規則轉換為pandas向量化操作，大幅提升處理速度
    """

    def __init__(self):
        self.performance_stats = {
            'total_evaluation_time': 0,
            'rules_processed': 0,
            'records_processed': 0,
            'vectorized_operations': 0
        }

    def evaluate_rules(self, data: pd.DataFrame, rules: Dict[str, Any]) -> Dict[str, List[str]]:
        """評估所有規則並返回符合的用戶列表

        Args:
            data: 包含用戶數據的DataFrame
            rules: 規則定義字典

        Returns:
            Dict[str, List[str]]: 每個規則對應的符合用戶列表
        """
        start_time = time.time()
        logger.info(f"開始向量化規則評估: {len(data)} 筆記錄, {len(rules)} 個規則")

        # 檢查核心必要欄位
        core_required_fields = ['permanent']
        missing_core_fields = [field for field in core_required_fields if field not in data.columns]
        if missing_core_fields:
            logger.error(f"缺少核心必要欄位: {missing_core_fields}")
            return {}

        # 檢查常用欄位，如果缺失則警告但不中斷
        common_fields = ['purchase_count', 'last_interaction_days']
        missing_common_fields = [field for field in common_fields if field not in data.columns]
        if missing_common_fields:
            logger.warning(f"缺少常用欄位（將使用預設值）: {missing_common_fields}")
            # 為缺失的欄位添加預設值
            data = data.copy()
            if 'purchase_count' not in data.columns:
                data['purchase_count'] = 0
            if 'last_interaction_days' not in data.columns:
                data['last_interaction_days'] = -1

        # 預處理數據
        processed_data = self._preprocess_data(data)

        # 分析規則模式
        rule_patterns = self._analyze_rule_patterns(rules)
        logger.info(f"識別出 {len(rule_patterns)} 種規則模式")

        # 向量化評估
        results = {}
        for rule_id, rule_data in rules.items():
            try:
                matched_users = self._evaluate_single_rule_vectorized(
                    processed_data, rule_id, rule_data
                )
                results[rule_id] = matched_users
                self.performance_stats['rules_processed'] += 1

            except Exception as e:
                logger.error(f"評估規則 {rule_id} 時發生錯誤: {str(e)}")
                results[rule_id] = []

        # 記錄性能統計
        total_time = time.time() - start_time
        self.performance_stats['total_evaluation_time'] += total_time
        self.performance_stats['records_processed'] += len(data)

        logger.info(f"向量化評估完成，耗時: {total_time:.2f}秒")
        logger.info(f"平均每筆記錄處理時間: {total_time/len(data)*1000:.2f}ms")

        return results

    def _preprocess_data(self, data: pd.DataFrame) -> pd.DataFrame:
        """預處理數據，優化數據類型和處理缺失值

        Args:
            data: 原始數據DataFrame

        Returns:
            pd.DataFrame: 預處理後的數據
        """
        logger.info("開始數據預處理")
        processed = data.copy()

        # 優化數據類型
        if 'purchase_count' in processed.columns:
            processed['purchase_count'] = processed['purchase_count'].fillna(0).astype('uint16')

        if 'last_interaction_days' in processed.columns:
            processed['last_interaction_days'] = processed['last_interaction_days'].fillna(-1).astype('int32')

        # 處理其他時間相關欄位
        for col in ['last_view_item_days', 'last_add_to_cart_days', 'last_purchase_days']:
            if col in processed.columns:
                processed[col] = processed[col].fillna(-1).astype('int32')

        # 確保permanent欄位是字符串類型
        if 'permanent' in processed.columns:
            processed['permanent'] = processed['permanent'].astype('string')

        logger.info("數據預處理完成")
        return processed

    def _analyze_rule_patterns(self, rules: Dict[str, Any]) -> Dict[str, List[str]]:
        """分析規則模式，將相似的規則分組以優化處理

        Args:
            rules: 規則定義字典

        Returns:
            Dict[str, List[str]]: 規則模式分組
        """
        patterns = defaultdict(list)

        for rule_id, rule_data in rules.items():
            pattern_key = self._get_rule_pattern_key(rule_data)
            patterns[pattern_key].append(rule_id)

        return dict(patterns)

    def _get_rule_pattern_key(self, rule_data: Dict[str, Any]) -> str:
        """生成規則模式的唯一鍵

        Args:
            rule_data: 單個規則的定義

        Returns:
            str: 規則模式鍵
        """
        # 提取規則中的主要條件類型
        conditions = []

        rule = rule_data.get('rule', {})
        if isinstance(rule, dict) and 'and' in rule:
            for condition in rule['and']:
                if isinstance(condition, dict):
                    for op, operands in condition.items():
                        if op in ['==', '!=', '>', '>=', '<', '<='] and isinstance(operands, list):
                            if len(operands) == 2 and isinstance(operands[0], dict):
                                var_name = operands[0].get('var', '')
                                conditions.append(f"{var_name}_{op}")

        return '|'.join(sorted(conditions)) if conditions else 'unknown'

    def _evaluate_single_rule_vectorized(self, data: pd.DataFrame, rule_id: str, rule_data: Dict[str, Any]) -> List[str]:
        """使用向量化操作評估單個規則

        Args:
            data: 預處理後的數據
            rule_id: 規則ID
            rule_data: 規則定義

        Returns:
            List[str]: 符合規則的用戶permanent列表
        """
        try:
            # 解析規則中的條件
            conditions = self._parse_rule_conditions(rule_data)

            if not conditions:
                logger.warning(f"規則 {rule_id} 沒有可識別的條件")
                return []

            # 創建基礎mask（所有行為True）
            mask = pd.Series(True, index=data.index)

            # 應用各種條件
            for condition_type, condition_value in conditions.items():
                condition_mask = self._apply_vectorized_condition(
                    data, condition_type, condition_value
                )
                mask &= condition_mask
                self.performance_stats['vectorized_operations'] += 1

            # 返回符合條件的用戶permanent列表
            if mask.any():
                return data.loc[mask, 'permanent'].tolist()
            else:
                return []

        except Exception as e:
            logger.error(f"向量化評估規則 {rule_id} 時發生錯誤: {str(e)}")
            return []

    def _parse_rule_conditions(self, rule_data: Dict[str, Any]) -> Dict[str, Any]:
        """解析規則條件為可向量化的格式

        Args:
            rule_data: 規則定義

        Returns:
            Dict[str, Any]: 解析後的條件字典
        """
        conditions = {}
        rule = rule_data.get('rule', {})
        data_params = rule_data.get('data', {})

        if isinstance(rule, dict) and 'and' in rule:
            for condition in rule['and']:
                if isinstance(condition, dict):
                    parsed = self._parse_single_condition(condition, data_params)
                    if parsed:
                        condition_key, condition_value = parsed
                        conditions[condition_key] = condition_value

        return conditions

    def _parse_single_condition(self, condition: Dict[str, Any], data_params: Dict[str, Any]) -> Optional[Tuple[str, Any]]:
        """解析單個條件

        Args:
            condition: 單個條件定義
            data_params: 規則參數

        Returns:
            Optional[Tuple[str, Any]]: 解析後的條件，如果無法解析則返回None
        """
        for op, operands in condition.items():
            if op == '==' and isinstance(operands, list) and len(operands) == 2:
                # 等於條件
                var_ref, value = operands
                if isinstance(var_ref, dict) and var_ref.get('var'):
                    var_name = var_ref['var']
                    actual_value = self._resolve_value(value, data_params)
                    return f"{var_name}_eq", actual_value

            elif op == '!=' and isinstance(operands, list) and len(operands) == 2:
                # 不等於條件
                var_ref, value = operands
                if isinstance(var_ref, dict) and var_ref.get('var'):
                    var_name = var_ref['var']
                    actual_value = self._resolve_value(value, data_params)
                    return f"{var_name}_ne", actual_value

            elif op in ['>', '>=', '<', '<='] and isinstance(operands, list) and len(operands) == 2:
                # 比較條件
                var_ref, value = operands
                if isinstance(var_ref, dict) and var_ref.get('var'):
                    var_name = var_ref['var']
                    actual_value = self._resolve_value(value, data_params)
                    return f"{var_name}_{op}", actual_value

        return None

    def _resolve_value(self, value: Any, data_params: Dict[str, Any]) -> Any:
        """解析值，處理變數引用

        Args:
            value: 值或變數引用
            data_params: 規則參數

        Returns:
            Any: 實際值
        """
        if isinstance(value, dict) and value.get('var'):
            # 變數引用，從data_params中獲取實際值
            param_name = value['var']
            return data_params.get(param_name, value)
        else:
            return value

    def _apply_vectorized_condition(self, data: pd.DataFrame, condition_type: str, condition_value: Any) -> pd.Series:
        """應用向量化條件

        Args:
            data: 數據DataFrame
            condition_type: 條件類型（如'purchase_count_eq'）
            condition_value: 條件值

        Returns:
            pd.Series: 布爾mask，指示哪些行符合條件
        """
        try:
            parts = condition_type.split('_')
            if len(parts) < 2:
                logger.warning(f"條件類型格式不正確: {condition_type}")
                return pd.Series(True, index=data.index)

            var_name = '_'.join(parts[:-1])  # 重組變數名稱（可能包含下劃線）
            operator = parts[-1]

            if var_name not in data.columns:
                logger.warning(f"欄位 {var_name} 不存在於數據中，可用欄位: {data.columns.tolist()}")
                return pd.Series(False, index=data.index)

            # 應用向量化操作
            if operator == 'eq':
                return data[var_name] == condition_value
            elif operator == 'ne':
                if condition_value is None:
                    # 處理 != null 的情況
                    return data[var_name].notna()
                else:
                    return data[var_name] != condition_value
            elif operator == '>':
                return data[var_name] > condition_value
            elif operator == '>=':
                return data[var_name] >= condition_value
            elif operator == '<':
                return data[var_name] < condition_value
            elif operator == '<=':
                return data[var_name] <= condition_value
            else:
                logger.warning(f"未知的操作符: {operator}")
                return pd.Series(True, index=data.index)

        except Exception as e:
            logger.error(f"應用向量化條件時發生錯誤: {condition_type}={condition_value}, 錯誤: {str(e)}")
            return pd.Series(False, index=data.index)

    def get_performance_stats(self) -> Dict[str, Any]:
        """獲取性能統計信息

        Returns:
            Dict[str, Any]: 性能統計
        """
        stats = self.performance_stats.copy()
        if stats['records_processed'] > 0:
            stats['avg_time_per_record_ms'] = (stats['total_evaluation_time'] / stats['records_processed']) * 1000
        if stats['rules_processed'] > 0:
            stats['avg_time_per_rule_sec'] = stats['total_evaluation_time'] / stats['rules_processed']

        return stats

    def reset_performance_stats(self):
        """重置性能統計"""
        self.performance_stats = {
            'total_evaluation_time': 0,
            'rules_processed': 0,
            'records_processed': 0,
            'vectorized_operations': 0
        }


def precompute_derived_fields(user_stats: pd.DataFrame, current_time: pd.Timestamp) -> pd.DataFrame:
    """一次性預計算所有衍生欄位，使用向量化操作

    Args:
        user_stats: 原始用戶統計數據
        current_time: 當前時間

    Returns:
        pd.DataFrame: 包含所有衍生欄位的數據
    """
    logger.info("開始預計算衍生欄位（向量化版本）")
    start_time = time.time()

    # 創建副本避免修改原始數據
    result = user_stats.copy()

    # 定義時間欄位對應關係
    time_field_mappings = {
        'last_interaction_time': 'last_interaction_days',
        'last_view_item_time': 'last_view_item_days',
        'last_add_to_cart_time': 'last_add_to_cart_days',
        'last_purchase_time': 'last_purchase_days'
    }

    # 向量化計算所有天數欄位
    for time_col, days_col in time_field_mappings.items():
        if time_col in result.columns:
            # 使用向量化操作計算天數差
            time_series = pd.to_datetime(result[time_col], errors='coerce', utc=True)

            # 計算天數差異（向量化操作）
            days_diff = (current_time - time_series).dt.total_seconds() / 86400

            # 處理NaT值和負值
            result[days_col] = days_diff.fillna(-1).astype('int32')

            # 將負值設為-1（表示無效或未來時間）
            result.loc[result[days_col] < 0, days_col] = -1

            logger.info(f"已計算 {days_col} 欄位，有效值數量: {(result[days_col] != -1).sum()}")

    elapsed_time = time.time() - start_time
    logger.info(f"預計算衍生欄位完成，耗時: {elapsed_time:.2f}秒")

    return result


# 測試和基準比較函數
def benchmark_evaluators(data: pd.DataFrame, rules: Dict[str, Any],
                        original_func: callable, iterations: int = 3) -> Dict[str, Any]:
    """比較向量化評估器和原始評估器的性能

    Args:
        data: 測試數據
        rules: 規則定義
        original_func: 原始評估函數
        iterations: 測試迭代次數

    Returns:
        Dict[str, Any]: 基準測試結果
    """
    logger.info(f"開始基準測試，數據量: {len(data)}, 規則數: {len(rules)}, 迭代次數: {iterations}")

    # 測試向量化評估器
    vectorized_evaluator = VectorizedRuleEvaluator()
    vectorized_times = []
    vectorized_results = None

    for i in range(iterations):
        start_time = time.time()
        vectorized_results = vectorized_evaluator.evaluate_rules(data, rules)
        elapsed = time.time() - start_time
        vectorized_times.append(elapsed)
        logger.info(f"向量化評估器第{i+1}次測試: {elapsed:.2f}秒")

    # 測試原始評估器
    original_times = []
    original_results = None

    for i in range(iterations):
        start_time = time.time()
        original_results = original_func(data, rules)
        elapsed = time.time() - start_time
        original_times.append(elapsed)
        logger.info(f"原始評估器第{i+1}次測試: {elapsed:.2f}秒")

    # 計算統計數據
    vectorized_avg = np.mean(vectorized_times)
    original_avg = np.mean(original_times)
    speedup = original_avg / vectorized_avg if vectorized_avg > 0 else 0

    # 驗證結果一致性
    consistency_check = compare_results(vectorized_results, original_results)

    benchmark_results = {
        'vectorized_avg_time': vectorized_avg,
        'original_avg_time': original_avg,
        'speedup_factor': speedup,
        'vectorized_times': vectorized_times,
        'original_times': original_times,
        'consistency_check': consistency_check,
        'data_size': len(data),
        'rules_count': len(rules),
        'vectorized_performance_stats': vectorized_evaluator.get_performance_stats()
    }

    logger.info(f"基準測試完成，速度提升: {speedup:.2f}倍")
    return benchmark_results


def compare_results(results1: Dict[str, List[str]], results2: Dict[str, List[str]]) -> Dict[str, Any]:
    """比較兩個評估結果的一致性

    Args:
        results1: 第一個結果
        results2: 第二個結果

    Returns:
        Dict[str, Any]: 一致性檢查結果
    """
    if not results1 or not results2:
        return {"consistent": False, "reason": "一個或兩個結果為空"}

    if set(results1.keys()) != set(results2.keys()):
        return {
            "consistent": False,
            "reason": f"規則鍵不一致: {set(results1.keys())} vs {set(results2.keys())}"
        }

    inconsistencies = []
    for rule_id in results1.keys():
        set1 = set(results1[rule_id])
        set2 = set(results2[rule_id])

        if set1 != set2:
            inconsistencies.append({
                "rule_id": rule_id,
                "count_diff": len(set1) - len(set2),
                "only_in_first": list(set1 - set2)[:5],  # 只顯示前5個差異
                "only_in_second": list(set2 - set1)[:5]
            })

    return {
        "consistent": len(inconsistencies) == 0,
        "inconsistencies": inconsistencies,
        "total_rules_checked": len(results1)
    }