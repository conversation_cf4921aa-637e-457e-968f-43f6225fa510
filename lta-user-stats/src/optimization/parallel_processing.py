from concurrent.futures import ThreadPoolExecutor, as_completed
from src.utils import logging_setup, merge_results, split_into_chunks
import psutil
import time
import pandas as pd
import logging
import traceback
import numpy as np
from functools import partial

logger = logging_setup.configure_logging()

def parallel_process(data, func, max_workers=None, chunk_size=10000, additional_args=None):
    """搬移自 main.py 第203-213行多進程邏輯"""
    # 產生分塊索引
    chunks = split_into_chunks(len(data), chunk_size)

    # 記錄處理前的記憶體使用情況
    mem_before = psutil.virtual_memory()
    logger.info(f"記憶體使用情況 (處理前): {mem_before.percent}% 使用中, 可用: {mem_before.available/(1024*1024):.2f} MB")
    logger.info(f"開始平行處理資料: 總筆數={len(data)}, 分塊數={len(chunks)}, 每塊大小={chunk_size}, 工作進程數={max_workers}")

    start_time = time.time()

    # 增加參數傳遞支援
    additional_args = additional_args or ()

    with ThreadPoolExecutor(max_workers=max_workers) as executor:
        # 提交所有任務
        futures = []
        for i, chunk_range in enumerate(chunks):
            # 從原始資料提取子集
            if isinstance(data, pd.DataFrame):
                # 直接使用整個範圍作為索引
                chunk_data = data.iloc[list(chunk_range)]
                logger.info(f"處理 chunk {i+1}: 索引範圍 {chunk_range.start}-{chunk_range.stop-1}, 大小 {len(chunk_data)}")
            else:
                # 對於列表和其他類型使用切片
                chunk_data = data[chunk_range.start:chunk_range.stop]
                logger.info(f"處理 chunk {i+1}: 索引範圍 {chunk_range.start}-{chunk_range.stop-1}, 大小 {len(chunk_data)}")

            # 如果有額外參數，傳入函數
            if additional_args:
                futures.append(executor.submit(func, chunk_data, *additional_args))
            else:
                futures.append(executor.submit(func, chunk_data))

        results = []
        completed = 0
        total_chunks = len(futures)

        # 使用日誌顯示進度
        logger.info(f"開始處理 {total_chunks} 個資料分塊")

        for future in as_completed(futures):
            chunk_idx = futures.index(future)
            try:
                result = future.result()
                results.append(result)
                completed += 1
                # 每完成 10% 的任務或在完成最後一個任務時顯示進度
                if completed % max(1, total_chunks // 10) == 0 or completed == total_chunks:
                    logger.info(f"已處理 {completed}/{total_chunks} 個資料分塊 ({completed/total_chunks*100:.1f}%)")
            except Exception as e:
                logger.error(f"處理分塊 {chunk_idx+1} 時發生錯誤: {str(e)}")
                logger.exception("詳細錯誤堆疊")

    # 記錄處理後的記憶體使用情況
    mem_after = psutil.virtual_memory()
    elapsed_time = time.time() - start_time
    logger.info(f"平行處理完成，耗時: {elapsed_time:.2f} 秒")
    logger.info(f"記憶體使用情況 (處理後): {mem_after.percent}% 使用中, 可用: {mem_after.available/(1024*1024):.2f} MB")
    logger.info(f"記憶體使用變化: {mem_after.percent - mem_before.percent:.2f}%")

    # 合併結果
    if not results:
        logger.warning("沒有成功處理的任務結果，返回空結果")
        logger.info(f"結果合併完成，共有 0 個項目")
        return [] if not isinstance(data, pd.DataFrame) else pd.DataFrame()

    if isinstance(results[0], pd.DataFrame):
        logger.info(f"合併 {len(results)} 個 DataFrame 結果")

        # 過濾掉空的 DataFrame，避免 FutureWarning
        non_empty_results = [df for df in results if not df.empty]

        if not non_empty_results:
            logger.warning("所有結果 DataFrame 都是空的，返回空 DataFrame")
            merged_result = pd.DataFrame(columns=results[0].columns)
        else:
            merged_result = pd.concat(non_empty_results, ignore_index=True)

        logger.info(f"合併後的 DataFrame 大小: {len(merged_result)} 行，{merged_result.shape[1]} 列")
    else:
        merged_result = merge_results(results)
    logger.info(f"結果合併完成，共有 {len(merged_result)} 個項目")

    return merged_result