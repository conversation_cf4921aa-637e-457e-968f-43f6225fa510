import pyarrow as pa
import pandas as pd
from src.rules.dynamic_audience_rules import evaluate_rule

class ArrowDataProcessor:
    """使用 Arrow 格式優化記憶體使用"""

    def __init__(self, df):
        self.table = pa.Table.from_pandas(df)

    def process_chunk(self, indices):
        """處理資料分塊"""
        batch = self.table.take(indices).to_pandas()
        return self.evaluate_rules_on_batch(batch)

    def evaluate_rules_on_batch(self, batch):
        """評估規則"""
        results = {}
        for rule_id, rule_data in self.rules.items():
            matches = []
            for _, row in batch.iterrows():
                if evaluate_rule(rule_data, row.to_dict()):
                    matches.append(row['permanent'])
            if matches:
                results[rule_id] = matches
        return results