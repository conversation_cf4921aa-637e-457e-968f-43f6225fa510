import json
import logging
import os
import sys
import tempfile
import base64
from datetime import datetime, timedelta, timezone
from collections import defaultdict
from typing import Dict, Any, List, Union

import google.auth
import pandas as pd
from dateutil.parser import parse as date_parse
from google.api_core.exceptions import GoogleAPIError
from google.cloud import bigquery, storage
from google.cloud.exceptions import GoogleCloudError
from joblib import Memory
from json_logic import jsonLogic
from pytz import timezone as pytz_timezone
import pprint
from flask import jsonify, request
import functions_framework
import traceback

from src.utils import logging_setup, error_handling, time_utils, json_helpers
from src.core import data_processing, cloud_integration
from src.core.cloud_integration import BigQueryClient, StorageManager, write_to_special_lta
from src.rules.dynamic_audience_rules import (
    evaluate_rule, fetch_audience_mapping_with_rules,
    get_final_description, RuleEvaluationError,
    calculate_audience_segments_dynamic
)
from src.optimization.memory_optimizer import ArrowDataProcessor

logger = logging_setup.configure_logging()

# 注意：main_router 函數已移至根目錄的 main.py 作為 functions-framework 的入口點
# 此檔案專注於業務邏輯實現

class LTAError(Exception):
    """自定義錯誤類別，用於處理 LTA 相關錯誤"""
    pass

class BigQueryError(LTAError):
    """BigQuery 相關錯誤"""
    pass

class StorageError(LTAError):
    """Cloud Storage 相關錯誤"""
    pass

def parse_bool(val):
    """解析布林值參數

    Args:
        val: 輸入值，可能是布林值、字串、數字等

    Returns:
        bool: 解析後的布林值
    """
    if isinstance(val, bool):
        return val
    if isinstance(val, str):
        return val.lower() in ('true', '1', 'yes', 'y', 'on')
    if isinstance(val, (int, float)):
        return bool(val)
    return False

def calculate_user_stats(
    ec_ids: List[int],
    date_range: List[datetime],
    should_calculate_stats: bool = True,
    should_save_snapshot: bool = True,
    should_write_lta: bool = True,
    add_to_dxp: bool = True,
    auto_repair_missing_days: bool = True,
    max_repair_days: int = 30,
    should_update_user_stats_table: bool = True,
    skip_time_standardization: bool = False,
    only_standardize_required_columns: bool = True,
    use_bigquery_compute: bool = False
) -> Dict[str, Any]:
    """
    計算使用者統計資料

    Args:
        ec_id: 電商 ID 列表
        date_range: 日期範圍
        should_calculate_stats: 是否計算統計資料
        should_save_snapshot: 是否儲存快照
        should_write_lta: 是否寫入 LTA 資料
        add_to_dxp: 是否加入 DXP
        auto_repair_missing_days: 是否自動修復缺失的天數
        max_repair_days: 最多修復多少天（防止處理太長時間）
        should_update_user_stats_table: 是否更新 BigQuery 的 user_stats 表格
        skip_time_standardization: 是否跳過時間標準化
        only_standardize_required_columns: 是否僅處理必要欄位
        use_bigquery_compute: 是否使用 BigQuery 直接計算統計資料（避免 OOM）

    Returns:
        Dict[str, Any]: 處理結果
    """
    try:
        # 如果使用 BigQuery 計算，則調用特別的函數
        if use_bigquery_compute:
            logger.info("使用 BigQuery 直接計算統計資料以避免 OOM 問題")
            return calculate_user_stats_via_bigquery_wrapper(
                ec_ids=ec_ids,
                date_range=date_range,
                should_calculate_stats=should_calculate_stats,
                should_save_snapshot=should_save_snapshot,
                should_write_lta=should_write_lta,
                add_to_dxp=add_to_dxp,
                auto_repair_missing_days=auto_repair_missing_days,
                max_repair_days=max_repair_days,
                should_update_user_stats_table=should_update_user_stats_table,
                skip_time_standardization=skip_time_standardization,
                only_standardize_required_columns=only_standardize_required_columns
            )

        # 否則使用原來的方法
        return data_processing.calculate_user_stats(
            ec_ids=ec_ids,
            date_range=date_range,
            should_calculate_stats=should_calculate_stats,
            should_save_snapshot=should_save_snapshot,
            should_write_lta=should_write_lta,
            add_to_dxp=add_to_dxp,
            auto_repair_missing_days=auto_repair_missing_days,
            max_repair_days=max_repair_days,
            should_update_user_stats_table=should_update_user_stats_table,
            skip_time_standardization=skip_time_standardization,
            only_standardize_required_columns=only_standardize_required_columns
        )
    except GoogleAPIError as e:
        # 當發生 Google API 錯誤時，返回包含錯誤信息的字典
        logging.error(f"Google API 錯誤: {str(e)}")
        return {"error": f"Google API 錯誤: {str(e)}"}
    except Exception as e:
        # 當發生其他錯誤時，返回包含錯誤信息的字典
        logging.error(f"計算使用者統計資料時發生錯誤: {str(e)}")
        return {"error": f"處理失敗: {str(e)}"}

def calculate_user_stats_via_bigquery_wrapper(
    ec_ids: List[int],
    date_range: List[datetime],
    should_calculate_stats: bool = True,
    should_save_snapshot: bool = True,
    should_write_lta: bool = True,
    add_to_dxp: bool = True,
    auto_repair_missing_days: bool = True,
    max_repair_days: int = 30,
    should_update_user_stats_table: bool = True,
    skip_time_standardization: bool = False,
    only_standardize_required_columns: bool = True
) -> Dict[str, Any]:
    """
    使用 BigQuery 直接計算用戶統計資料的包裝函數

    Args:
        ec_ids: 電商 ID 列表
        date_range: 時間範圍 [start_time, end_time]
        should_calculate_stats: 是否計算統計資料
        should_save_snapshot: 是否儲存快照
        should_write_lta: 是否寫入 LTA 資料
        add_to_dxp: 是否加入 DXP
        auto_repair_missing_days: 是否自動修復缺失天數
        max_repair_days: 最大修復天數限制
        should_update_user_stats_table: 是否更新 BigQuery 的 user_stats 表格
        skip_time_standardization: 是否跳過時間標準化
        only_standardize_required_columns: 是否僅處理必要欄位

    Returns:
        Dict[str, Any]: 處理結果
    """
    from src.core.data_processing import find_latest_snapshot, update_user_stats_with_daily_data
    from src.core import queries
    from src.utils.time_utils import get_taiwan_date_str
    from scripts.init_user_stats_for_ec_id import calculate_user_stats_via_bigquery
    from google.cloud import bigquery
    from datetime import timedelta
    from src.core.cloud_integration import StorageManager, BigQueryClient
    import pytz
    import pandas as pd

    logger.info(f"使用 BigQuery 計算模式處理 EC IDs: {ec_ids}")
    logger.info(f"自動修復缺失天數: {auto_repair_missing_days}, 最大修復天數: {max_repair_days}")

    # 初始化統計變數
    total_cost_usd = 0
    total_cost_twd = 0
    total_bytes_processed = 0
    total_users_processed = 0
    all_results = {}
    all_repaired_dates = []
    total_days_repaired = 0

    try:
        # 逐一處理每個 EC ID
        for ec_id in ec_ids:
            logger.info(f"開始處理 EC ID: {ec_id}")

            # 如果啟用自動修復缺失天數功能
            if auto_repair_missing_days:
                start_time, end_time = date_range[0], date_range[1]

                # 確保時間物件有時區資訊
                if start_time.tzinfo is None:
                    start_time = start_time.replace(tzinfo=timezone.utc)
                if end_time.tzinfo is None:
                    end_time = end_time.replace(tzinfo=timezone.utc)

                # 尋找最新的快照作為基礎資料
                snapshot_date, snapshot_path, snapshot_type = find_latest_snapshot(start_time, ec_id)

                if snapshot_date is not None:
                    logger.info(f"找到 {(end_time - snapshot_date).days} 天前的快照: {snapshot_path} (來源: {snapshot_type})")

                    # 嘗試載入快照資料
                    base_stats_df = None
                    try:
                        if snapshot_type == "local":
                            # 從本地檔案載入
                            base_stats_df = pd.read_parquet(snapshot_path)
                        else:  # "gcs"
                            # 從 Google Cloud Storage 下載並載入
                            storage_manager = StorageManager()
                            taiwan_date_str = get_taiwan_date_str(snapshot_date)
                            local_temp = f"/tmp/user_stats_{ec_id}_{taiwan_date_str}.parquet"
                            storage_manager.bucket.blob(snapshot_path).download_to_filename(local_temp)
                            base_stats_df = pd.read_parquet(local_temp)

                        if not base_stats_df.empty:
                            # 過濾出該 EC ID 的資料
                            base_stats_df = base_stats_df[base_stats_df['ec_id'] == ec_id]
                            logger.info(f"成功載入快照，包含 {len(base_stats_df)} 筆 EC ID {ec_id} 的資料")
                    except Exception as e:
                        logger.error(f"載入快照失敗: {str(e)}")
                        base_stats_df = None

                    # 如果成功載入快照，進行增量修復
                    if base_stats_df is not None and not base_stats_df.empty:
                        # 計算需要修復的天數
                        days_to_repair = (end_time - snapshot_date).days

                        # 特殊處理：如果 end_time 剛好是 16:00 UTC（可能是排程觸發），調整計算
                        if end_time.hour <= 16 and end_time.minute == 0 and end_time.second == 0:
                            logger.info(f"end_time 是 {end_time.date()} 的 16:00 UTC，調整 days_to_repair")
                            days_to_repair -= 1

                        days_to_repair = max(0, days_to_repair)

                        # 檢查是否超過最大修復天數限制
                        if days_to_repair > max_repair_days:
                            logger.warning(f"需要修復天數 ({days_to_repair}) 超過限制 ({max_repair_days})，將只修復最近 {max_repair_days} 天")
                            days_to_repair = max_repair_days
                            snapshot_date = end_time - timedelta(days=max_repair_days)

                        # 開始逐日增量修復
                        if days_to_repair > 0:
                            logger.info(f"開始逐日修復，總共需要修復 {days_to_repair} 天")
                            current_stats_df = base_stats_df.copy()
                            current_date = snapshot_date
                            client = bigquery.Client()

                            # 逐日處理缺失的資料
                            for day_offset in range(1, days_to_repair + 1):
                                day_start = current_date + timedelta(days=1)
                                day_end = day_start + timedelta(days=1)
                                logger.info(f"處理第 {day_offset} 天 ({day_start.date()} -> {day_end.date()}) 的資料")

                                # 轉換為台灣時間日期字串（用於記錄）
                                taiwan_dt = day_start.astimezone(pytz.timezone('Asia/Taipei'))
                                taiwan_date_str = taiwan_dt.strftime('%Y-%m-%d')
                                all_repaired_dates.append(taiwan_date_str)

                                try:
                                    # 查詢該日的用戶活動資料
                                    daily_query = queries.daily_user_stats_query(ec_id, day_start, day_end)
                                    daily_job = client.query(daily_query)
                                    daily_results = daily_job.result()
                                    daily_stats_df = daily_results.to_dataframe()
                                    total_bytes_processed += daily_job.total_bytes_processed

                                    if daily_stats_df.empty:
                                        logger.warning(f"EC ID {ec_id} 在 {day_start.date()} 沒有新資料")
                                    else:
                                        logger.info(f"查詢到 {len(daily_stats_df)} 筆 EC ID {ec_id} 在 {day_start.date()} 的資料")
                                        # 將該日資料更新到累計統計中
                                        current_stats_df = update_user_stats_with_daily_data(current_stats_df, daily_stats_df)

                                    # 儲存該日的快照
                                    if should_save_snapshot:
                                        try:
                                            snapshot_result = save_user_stats_snapshot(
                                                current_stats_df,
                                                ec_id,
                                                day_end,
                                                skip_time_standardization=skip_time_standardization,
                                                only_required_columns=only_standardize_required_columns
                                            )
                                            logger.info(f"✓ 儲存 {day_start.date()} 快照成功")
                                        except Exception as e:
                                            logger.error(f"✗ 儲存 {day_start.date()} 快照失敗: {str(e)}")

                                    # 寫入該日的 LTA 分群資料
                                    if should_write_lta:
                                        try:
                                            lta_result = write_to_special_lta(
                                                current_stats_df,
                                                ec_id,
                                                day_end,
                                                add_to_dxp=add_to_dxp,
                                                skip_time_standardization=skip_time_standardization,
                                                only_required_columns=only_standardize_required_columns
                                            )
                                            logger.info(f"✓ 寫入 {day_start.date()} LTA 資料成功")

                                            # 收集分群統計資料
                                            if isinstance(lta_result, dict) and 'segment_stats' in lta_result:
                                                if str(ec_id) not in all_results:
                                                    all_results[str(ec_id)] = {}
                                                all_results[str(ec_id)][taiwan_date_str] = lta_result['segment_stats']
                                        except Exception as e:
                                            logger.error(f"✗ 寫入 {day_start.date()} LTA 資料失敗: {str(e)}")

                                    # 更新當前日期
                                    current_date = day_start

                                except Exception as e:
                                    logger.error(f"處理 {day_start.date()} 資料時發生錯誤: {str(e)}")
                                    continue

                            # 統計本 EC ID 的處理結果
                            total_users_processed += len(current_stats_df)
                            total_days_repaired += days_to_repair
                            logger.info(f"EC ID {ec_id} 完成增量修復，共修復 {days_to_repair} 天")

                            # 更新 BigQuery user_stats 表格
                            if should_update_user_stats_table:
                                try:
                                    logger.info(f"更新 BigQuery user_stats 表格，EC ID: {ec_id}")
                                    bq_client = BigQueryClient()
                                    bq_client.update_user_stats_table(
                                        user_stats_df=current_stats_df,
                                        ec_id=ec_id,
                                        current_date=end_time,
                                        skip_time_standardization=skip_time_standardization,
                                        only_required_columns=only_standardize_required_columns
                                    )
                                    logger.info(f"✓ 成功更新 BigQuery user_stats 表格，EC ID: {ec_id}")
                                except Exception as e:
                                    logger.error(f"✗ 更新 BigQuery user_stats 表格失敗，EC ID: {ec_id}，錯誤: {str(e)}")

                            # 增量修復完成，跳過原有的 BigQuery 計算邏輯
                            continue
                        else:
                            logger.info(f"EC ID {ec_id} 沒有需要修復的天數")
                    else:
                        logger.warning(f"EC ID {ec_id} 沒有可用的快照，將使用原有的 BigQuery 計算邏輯")
                else:
                    logger.warning(f"EC ID {ec_id} 找不到快照，將使用原有的 BigQuery 計算邏輯")

            # 使用原有的 BigQuery 計算邏輯（當沒有快照或未啟用增量修復時）
            logger.info(f"使用原有 BigQuery 計算邏輯處理 EC ID: {ec_id}")

            # 設定 BigQuery 查詢配置
            query_job_config = bigquery.QueryJobConfig()
            query_job_config.use_query_cache = True
            query_job_config.priority = "INTERACTIVE"
            query_job_config.use_legacy_sql = False
            query_job_config.maximum_bytes_billed = 100_000_000_000  # 100GB

            # 執行 BigQuery 計算
            user_stats_df = calculate_user_stats_via_bigquery(
                ec_id=ec_id,
                time_range=date_range,
                query_job_config=query_job_config,
                force_query=True  # 在 API 模式下強制執行
            )

            if user_stats_df.empty:
                logger.warning(f"EC ID {ec_id} 沒有數據")
                continue

            logger.info(f"EC ID {ec_id} 獲得 {len(user_stats_df)} 筆用戶統計資料")
            total_users_processed += len(user_stats_df)

            # 更新 BigQuery user_stats 表格
            if should_update_user_stats_table:
                try:
                    logger.info(f"更新 BigQuery user_stats 表格，EC ID: {ec_id}")
                    bq_client = BigQueryClient()
                    bq_client.update_user_stats_table(
                        user_stats_df=user_stats_df,
                        ec_id=ec_id,
                        current_date=date_range[1] if len(date_range) > 1 else datetime.now(timezone.utc),
                        skip_time_standardization=skip_time_standardization,
                        only_required_columns=only_standardize_required_columns
                    )
                    logger.info(f"✓ 成功更新 BigQuery user_stats 表格，EC ID: {ec_id}")
                except Exception as e:
                    logger.error(f"✗ 更新 BigQuery user_stats 表格失敗，EC ID: {ec_id}，錯誤: {str(e)}")

            # 儲存快照
            if should_save_snapshot:
                try:
                    logger.info(f"儲存快照，EC ID: {ec_id}")
                    snapshot_result = save_user_stats_snapshot(
                        user_stats_df,
                        ec_id,
                        date_range[1] if len(date_range) > 1 else datetime.now(timezone.utc),
                        skip_time_standardization=skip_time_standardization,
                        only_required_columns=only_standardize_required_columns
                    )
                    logger.info(f"✓ 成功儲存快照，EC ID: {ec_id}")
                except Exception as e:
                    logger.error(f"✗ 儲存快照失敗，EC ID: {ec_id}，錯誤: {str(e)}")

            # 寫入 LTA 分群資料
            if should_write_lta:
                try:
                    logger.info(f"寫入 LTA，EC ID: {ec_id}")
                    lta_result = write_to_special_lta(
                        user_stats_df,
                        ec_id,
                        date_range[1] if len(date_range) > 1 else datetime.now(timezone.utc),
                        add_to_dxp=add_to_dxp,
                        skip_time_standardization=skip_time_standardization,
                        only_required_columns=only_standardize_required_columns
                    )
                    logger.info(f"✓ 成功寫入 LTA，EC ID: {ec_id}")

                    # 收集分群統計資料
                    if isinstance(lta_result, dict) and 'segment_stats' in lta_result:
                        all_results[str(ec_id)] = lta_result['segment_stats']

                except Exception as e:
                    logger.error(f"✗ 寫入 LTA 失敗，EC ID: {ec_id}，錯誤: {str(e)}")

            logger.info(f"完成處理 EC ID: {ec_id}")

        # 準備回應資料
        response = {
            'calculate_stats': should_calculate_stats,
            'total_cost_usd': total_cost_usd,
            'total_cost_twd': total_cost_twd,
            'total_bytes_processed': total_bytes_processed,
            'users': total_users_processed,
            'use_bigquery_compute': True,
            'auto_repair_enabled': auto_repair_missing_days,
            'repaired_dates': all_repaired_dates,
            'days_repaired': total_days_repaired,
            'message': f'成功使用 BigQuery 計算模式處理 {len(ec_ids)} 個 EC ID，總計 {total_users_processed} 位用戶'
        }

        # 如果有修復天數，在訊息中說明
        if total_days_repaired > 0:
            response['message'] += f'，修復了 {total_days_repaired} 天的缺失資料'

        # 如果有分群統計結果，加入回應
        if all_results:
            response['segment_stats'] = all_results

        logger.info(f"BigQuery 計算模式完成: {response}")
        return response

    except Exception as e:
        logger.error(f"BigQuery 計算模式發生錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return {
            'error': f'BigQuery 計算模式失敗: {str(e)}',
            'use_bigquery_compute': True,
            'auto_repair_enabled': auto_repair_missing_days
        }

def save_user_stats_snapshot(user_stats_df: pd.DataFrame, ec_id: int, current_date: datetime, skip_time_standardization: bool = False, only_required_columns: bool = True):
    """儲存使用者統計資料快照到 GCS

    Args:
        user_stats_df: 使用者統計資料
        ec_id: 電商 ID
        current_date: 資料日期
        skip_time_standardization: 是否跳過時間標準化以提高性能
        only_required_columns: 是否只標準化必要的時間欄位

    Returns:
        dict: 操作結果
    """
    logger.info(f"save_user_stats_snapshot: 儲存快照，UTC日期: {current_date} UTC")

    if skip_time_standardization:
        logger.info("跳過時間標準化以提高性能")

    try:
        result = cloud_integration.save_user_stats_snapshot(
            user_stats_df,
            ec_id,
            current_date,
            skip_time_standardization,
            only_required_columns
        )

        in_test_env = os.environ.get('PYTEST_CURRENT_TEST') is not None

        if isinstance(result, dict):
            if result.get('success', False):
                return result.get('path', "")
            else:
                return ""
        return result
    except Exception as e:
        logger.error(f"儲存快照失敗: {str(e)}")
        return ""

def write_to_special_lta(user_stats: pd.DataFrame, ec_id: int, current_date: datetime, add_to_dxp: bool = True, skip_time_standardization: bool = False, only_required_columns: bool = True):
    """將使用者統計資料寫入特殊 LTA 資料表

    Args:
        user_stats: 使用者統計資料
        ec_id: 電子商務 ID
        current_date: 當前日期
        add_to_dxp: 是否加入 DXP 受眾
        skip_time_standardization: 是否跳過時間標準化
        only_required_columns: 是否只處理必要的時間欄位

    Returns:
        Dict: 包含成功與否的資訊和其他相關數據
    """
    try:
        return cloud_integration.write_to_special_lta(
            user_stats,
            ec_id,
            current_date,
            add_to_dxp,
            skip_time_standardization=skip_time_standardization,
            only_required_columns=only_required_columns
        )
    except Exception as e:
        logger.error(f"寫入 LTA 時發生錯誤: {str(e)}")
        return {"success": False, "error": str(e)}

@error_handling.handle_error
def main(request):
    """精簡後的 Cloud Functions 入口"""
    try:
        request_json = request.get_json(silent=True)
        if request_json is None:
            logger.error("Invalid request: No JSON body provided.")
            return jsonify({
                'error': 'Invalid request: No JSON body provided.'
            }), 400

        # 處理日誌級別參數
        log_level_str = request_json.get('log_level', 'INFO')
        if log_level_str in ['DEBUG', 'INFO', 'WARNING', 'ERROR', 'CRITICAL']:
            log_level = getattr(logging, log_level_str)
            # 動態調整日誌級別（不重新配置，避免重複handler）
            logger.setLevel(log_level)
            logging.getLogger().setLevel(log_level)
            logger.info(f"日誌級別已調整為: {log_level_str}")

        # 處理並記錄請求參數
        logger.info(f"請求參數: {request_json}")
        ec_ids = data_processing.validate_ec_ids(request_json.get('ec_ids'))
        if not ec_ids:
            return jsonify({
                'error': 'Invalid request: No valid EC IDs provided.'
            }), 400

        # 解析時間範圍
        start_time, end_time = time_utils.parse_time_range(request_json)
        if start_time is None or end_time is None:
            return jsonify({
                'error': 'Invalid time range provided.'
            }), 400

        # 讀取其他參數
        should_calculate_stats = parse_bool(request_json.get('should_calculate_stats', True))
        should_save_snapshot = parse_bool(request_json.get('should_save_snapshot', True))
        should_write_lta = parse_bool(request_json.get('should_write_lta', True))
        add_to_dxp = parse_bool(request_json.get('add_to_dxp', True))
        auto_repair_missing_days = parse_bool(request_json.get('auto_repair_missing_days', True))
        max_repair_days = int(request_json.get('max_repair_days', 30))
        should_update_user_stats_table = parse_bool(request_json.get('should_update_user_stats_table', True))

        # 新增時間標準化相關參數
        skip_time_standardization = parse_bool(request_json.get('skip_time_standardization', False))
        only_standardize_required_columns = parse_bool(request_json.get('only_standardize_required_columns', True))

        # 新增 BigQuery 計算模式參數
        use_bigquery_compute = parse_bool(request_json.get('use_bigquery_compute', False))

        if skip_time_standardization:
            logger.info("將跳過時間標準化以提高性能")
        else:
            logger.info(f"將進行時間標準化，{'僅處理必要欄位' if only_standardize_required_columns else '處理所有可能欄位'}")

        if use_bigquery_compute:
            logger.info("將使用 BigQuery 直接計算統計資料以避免 OOM 問題")

        # 計算用戶統計資料
        try:
            logger.info(f"開始計算用戶統計資料，參數: ec_ids={ec_ids}, start_time={start_time}, end_time={end_time}, "
                      f"auto_repair_missing_days={auto_repair_missing_days}, max_repair_days={max_repair_days}, "
                      f"skip_time_standardization={skip_time_standardization}, use_bigquery_compute={use_bigquery_compute}")
            results = calculate_user_stats(
                ec_ids=ec_ids,
                date_range=[start_time, end_time],
                should_calculate_stats=should_calculate_stats,
                should_save_snapshot=should_save_snapshot,
                should_write_lta=should_write_lta,
                add_to_dxp=add_to_dxp,
                auto_repair_missing_days=auto_repair_missing_days,
                max_repair_days=max_repair_days,
                should_update_user_stats_table=should_update_user_stats_table,
                skip_time_standardization=skip_time_standardization,
                only_standardize_required_columns=only_standardize_required_columns,
                use_bigquery_compute=use_bigquery_compute
            )
        except Exception as e:
            logger.error(f"計算用戶統計資料時發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")
            return jsonify({
                'error': f'Failed to calculate user stats: {str(e)}',
                'parameters': {
                    'ec_ids': ec_ids,
                    'start_time': start_time.isoformat(),
                    'end_time': end_time.isoformat(),
                    'auto_repair_missing_days': auto_repair_missing_days,
                    'max_repair_days': max_repair_days
                }
            }), 500

        # 從 results 取出處理結果
        segment_stats = results.get('segment_stats')

        dynamic_segments = results.get('dynamic_segments', {})
        segment_counts = {segment_id: len(users) for segment_id, users in dynamic_segments.items()} if dynamic_segments else {}

        # 如果沒有任何處理資料，直接返回結果
        if not results.get('calculate_stats', False) and (segment_stats is None):
            return jsonify({
                'message': 'No user data found for the given parameters.',
                'total_cost_usd': results.get('total_cost_usd', 0),
                'total_cost_twd': results.get('total_cost_twd', 0),
                'total_bytes_processed_mb': results.get('total_bytes_processed', 0) / (1024 * 1024)
            }), 200

        logger.info("準備返回結果")

        # 在回應中加入分群數量資訊
        response = {
            'message': 'User stats calculated and written to special_lta successfully.',
            'total_cost_usd': results.get('total_cost_usd', 0),
            'total_cost_twd': results.get('total_cost_twd', 0),
            'total_bytes_processed_mb': results.get('total_bytes_processed', 0) / (1024 * 1024),
            'snapshot_used': results.get('snapshot_used', False),
            'snapshot_type': results.get('snapshot_type'),
            'auto_repair_info': {
                'enabled': auto_repair_missing_days,
                'max_days': max_repair_days,
                'days_repaired': results.get('days_repaired', 0),
                'repaired_dates': results.get('repaired_dates', [])
            }
        }

        # 如果有 segment_stats，將其加入回應
        if segment_stats:
            # 使用 segment_stats 中的數據
            try:
                response['segment_stats'] = {
                    'segment_counts': segment_stats.get('segment_counts', {}),
                    'segments_per_user': {
                        f"{k}_個分群": v  # 為數字添加說明文字 (例如: "1_個分群": 784354)
                        for k, v in segment_stats.get('segments_per_user', {}).items()
                    },
                    'total_users': segment_stats.get('total_users', 0)
                }
            except Exception as e:
                logger.error(f"處理 segment_stats 時發生錯誤: {str(e)}")
                logger.exception("詳細錯誤堆疊")
                # 使用本地計算的 segment_counts 作為備用
                response['segment_counts'] = segment_counts
        else:
            # 當沒有 segment_stats 時，使用本地計算的 segment_counts
            response['segment_counts'] = segment_counts

        # 如果有每日分群統計資料，將其加入回應
        daily_segment_stats = results.get('daily_segment_stats', {})
        if daily_segment_stats:
            try:
                # 轉換每日分群統計資料的格式
                formatted_daily_stats = {}
                for date_str, stats in daily_segment_stats.items():
                    formatted_daily_stats[date_str] = {
                        'segment_counts': stats.get('segment_counts', {}),
                        'segments_per_user': {
                            f"{k}_個分群": v  # 為數字添加說明文字
                            for k, v in stats.get('segments_per_user', {}).items()
                        },
                        'total_users': stats.get('total_users', 0)
                    }

                # 將每日統計資料合併到 segment_stats 中
                if 'segment_stats' not in response:
                    response['segment_stats'] = {}

                # 將每天的數據添加到 segment_stats 中
                for date_str, stats in formatted_daily_stats.items():
                    response['segment_stats'][date_str] = stats

                logger.info(f"已將每日分群統計資料加入回應，包含 {len(formatted_daily_stats)} 天的資料")
            except Exception as e:
                logger.error(f"處理每日分群統計資料時發生錯誤: {str(e)}")
                logger.exception("詳細錯誤堆疊")

        logger.info(f"回應: {response}")

        return jsonify(response), 200
    except Exception as e:
        logger.error(f"處理請求時發生錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return jsonify({
            'error': f'An error occurred while processing the request: {str(e)}'
        }), 500

def update_user_stats_table(
    ec_ids: List[int],
    start_time: str,
    end_time: str,
    timezone_str: str = "UTC",
    use_bigquery_compute: bool = False
) -> dict:
    """更新使用者統計資料表

    Args:
        ec_ids: 電商 ID 列表
        start_time: 開始時間字串
        end_time: 結束時間字串
        timezone_str: 時區字串
        use_bigquery_compute: 是否使用 BigQuery 直接計算（避免 OOM）
    """

    try:
        parsed_start_time = parse_time(start_time, timezone_str)
        parsed_end_time = parse_time(end_time, timezone_str)

        logger.info(f"使用者統計更新: ec_ids={ec_ids}, start_time={parsed_start_time}, end_time={parsed_end_time}")
        logger.info(f"時區: {timezone_str}, use_bigquery_compute: {use_bigquery_compute}")

        all_segment_stats = {}
        total_users = 0
        total_cost_usd = 0
        total_cost_twd = 0
        total_bytes_processed = 0

        for ec_id in ec_ids:
            logger.info(f"處理 ec_id={ec_id} 的使用者統計資料")

            result = calculate_user_stats(
                ec_ids=[ec_id],
                date_range=[parsed_start_time, parsed_end_time],
                use_bigquery_compute=use_bigquery_compute
            )

            total_cost_usd += result.get("total_cost_usd", 0)
            total_cost_twd += result.get("total_cost_twd", 0)
            total_bytes_processed += result.get("total_bytes_processed", 0)

            if "segment_stats" in result:
                all_segment_stats[str(ec_id)] = result["segment_stats"]

            total_users += result.get("users", 0)

            logger.info(f"處理完成 ec_id={ec_id}, 用戶數={result.get('users', 0)}")

        response = {
            "status": "success",
            "ec_ids": ec_ids,
            "date_range": [parsed_start_time.strftime('%Y-%m-%d'), parsed_end_time.strftime('%Y-%m-%d')],
            "total_users": total_users,
            "total_cost_usd": total_cost_usd,
            "total_cost_twd": total_cost_twd,
            "total_bytes_processed": total_bytes_processed
        }

        if all_segment_stats:
            response["segment_stats"] = all_segment_stats

            segment_counts = {}
            for ec_id, stats in all_segment_stats.items():
                if "segments" in stats:
                    for segment_info in stats["segments"]:
                        segment_name = f"{segment_info['segment_count']}_個分群"
                        segment_counts[segment_name] = segment_counts.get(segment_name, 0) + segment_info['user_count']

            if segment_counts:
                response["segments_per_user"] = {k: segment_counts[k] for k in sorted(segment_counts.keys())}

        logger.info(f"使用者統計更新完成: total_users={total_users}, cost=${total_cost_usd:.4f} USD")
        return response
    except Exception as e:
        logger.error(f"更新使用者統計資料表時發生錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return {
            "status": "error",
            "message": str(e)
        }

@functions_framework.cloud_event
def update_user_stats_pubsub(cloud_event):
    """GCP Pub/Sub 事件處理函數，用於定期更新使用者統計資料

    此函數是與 GCP Pub/Sub 整合的預留功能，用於未來實現自動化排程任務。
    當設定 Pub/Sub 訂閱關係後，此函數會在接收到事件時自動執行，從事件內容中
    提取參數並調用 calculate_user_stats 函數進行處理。

    參數格式範例：
    {
        "ec_ids": [107],
        "start_time": "2023-12-01T00:00:00Z",
        "end_time": "2023-12-01T23:59:59Z",
        "calculate_stats": true,
        "save_snapshot": true,
        "write_lta": true,
        "add_to_dxp": true
    }

    Args:
        cloud_event (CloudEvent): GCP Pub/Sub 事件物件，包含 base64 編碼的訊息資料

    Returns:
        Flask.Response: JSON 回應，包含處理結果或錯誤訊息

    錯誤處理:
        - 若事件格式無效，返回 400 錯誤
        - 若處理過程發生錯誤，返回 500 錯誤並記錄詳細堆疊

    使用說明:
        此功能需要與 GCP Pub/Sub 主題建立訂閱關係，並設定適當的觸發排程。
        適合用於每日、每週或每月定期更新使用者統計資料的場景。
    """
    try:
        if not cloud_event or not hasattr(cloud_event, 'data') or 'message' not in cloud_event.data:
            logger.error("無效的 Cloud Event 格式")
            return jsonify({"error": "無效的 Cloud Event 格式"}), 400

        pubsub_message = base64.b64decode(cloud_event.data["message"]["data"]).decode("utf-8")
        data = json.loads(pubsub_message)

        ec_id = data.get("ec_id", [])
        if not isinstance(ec_id, list):
            ec_id = [ec_id] if ec_id else []

        ec_id = [int(x) if x is not None else x for x in ec_id]

        if not ec_id:
            logger.warning("未提供 ec_id，將使用預設值")
            ec_id = [107]

        start_time_str = data.get("start_time")
        end_time_str = data.get("end_time")

        try:
            start_time = parse_datetime(start_time_str) if start_time_str else None
            end_time = parse_datetime(end_time_str) if end_time_str else None
        except ValueError as e:
            logger.error(f"時間解析錯誤: {str(e)}")
            start_time = end_time = None

        if not start_time or not end_time:
            now = datetime.now(timezone.utc)
            start_time = (now - timedelta(days=1)).replace(hour=0, minute=0, second=0, microsecond=0)
            end_time = (now - timedelta(days=1)).replace(hour=23, minute=59, second=59, microsecond=999999)
            logger.info(f"未提供時間範圍，使用預設值 (昨天的資料)：{start_time} 至 {end_time}")

        should_calculate_stats = parse_bool(data.get("calculate_stats", True))
        should_save_snapshot = parse_bool(data.get("save_snapshot", True))
        should_write_lta = parse_bool(data.get("write_lta", True))
        add_to_dxp = parse_bool(data.get("add_to_dxp", True))
        should_update_user_stats_table = parse_bool(data.get("should_update_user_stats_table", True))

        logger.info(f"開始處理 Pubsub 事件: ec_id={ec_id}, start_time={start_time}, end_time={end_time}")
        logger.info(f"處理參數：ec_id={ec_id}, add_to_dxp={add_to_dxp}, should_update_user_stats_table={should_update_user_stats_table}")

        result = calculate_user_stats(
            ec_ids=ec_id,
            date_range=[start_time, end_time],
            should_calculate_stats=should_calculate_stats,
            should_save_snapshot=should_save_snapshot,
            should_write_lta=should_write_lta,
            add_to_dxp=add_to_dxp,
            should_update_user_stats_table=should_update_user_stats_table
        )

        logger.info(f"處理 Pubsub 事件完成，返回結果: {result}")
        return jsonify(result)
    except Exception as e:
        logging.error(f"處理 pubsub 事件時發生錯誤: {str(e)}")
        traceback.print_exc()
        return jsonify({"error": str(e), "traceback": traceback.format_exc()}), 500

def parse_time(time_str: str, timezone_str: str = "UTC") -> datetime:
    """解析時間字串為datetime物件

    Args:
        time_str: 時間字串
        timezone_str: 時區字串，預設為UTC

    Returns:
        datetime: 解析後的datetime物件
    """
    try:
        if isinstance(time_str, datetime):
            dt = time_str
        else:
            if time_str is None or time_str == "":
                raise ValueError(f"時間字串不能為空")
            dt = date_parse(str(time_str))

        if dt.tzinfo is None:
            tz = pytz_timezone(timezone_str)
            dt = tz.localize(dt)
        return dt
    except Exception as e:
        logger.error(f"解析時間字串 '{time_str}' 時出錯: {str(e)}")
        raise ValueError(f"無法解析時間字串: {time_str}") from e

def parse_datetime(time_str: str) -> datetime:
    """解析時間字串為datetime物件

    Args:
        time_str: 時間字串或datetime物件

    Returns:
        datetime: 解析後的datetime物件
    """
    try:
        if isinstance(time_str, datetime):
            return time_str

        if time_str is None or time_str == "":
            raise ValueError("時間字串不能為空")

        if not isinstance(time_str, str):
            time_str = str(time_str)

        return parse_time(time_str)
    except Exception as e:
        logger.error(f"解析時間字串 '{time_str}' 時出錯: {str(e)}")
        raise ValueError(f"無法解析時間字串: {time_str}") from e
