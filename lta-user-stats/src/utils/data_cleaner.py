import pandas as pd
import numpy as np
import json
import os
from typing import List, Optional, Dict, Any
import logging

logger = logging.getLogger(__name__)

def load_bigquery_schema(schema_path: str = None) -> Dict[str, Dict[str, Any]]:
    """
    載入 BigQuery schema 並建立欄位類型映射

    Args:
        schema_path: schema 檔案路徑，預設為 sql/user_stats_schema.json

    Returns:
        Dict: 欄位名稱對應類型和模式的字典
    """
    if schema_path is None:
        # 從專案根目錄找到 schema 檔案
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.join(current_dir, '..', '..')
        schema_path = os.path.join(project_root, 'sql', 'user_stats_schema.json')

    try:
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema_list = json.load(f)

        # 轉換為更方便使用的字典格式
        schema_dict = {}
        for field in schema_list:
            schema_dict[field['name']] = {
                'type': field['type'],
                'mode': field.get('mode', 'NULLABLE')
            }

        logger.info(f"成功載入 BigQuery schema，共 {len(schema_dict)} 個欄位")
        return schema_dict

    except Exception as e:
        logger.error(f"載入 BigQuery schema 失敗: {str(e)}")
        logger.warning("將使用預設的欄位類型映射")

        # 回傳預設映射
        return {
            'ec_id': {'type': 'INTEGER', 'mode': 'REQUIRED'},
            'permanent': {'type': 'STRING', 'mode': 'REQUIRED'},
            'purchase_count': {'type': 'INTEGER', 'mode': 'REQUIRED'},
            'total_sessions': {'type': 'INTEGER', 'mode': 'REQUIRED'},
            'total_purchase_amount': {'type': 'FLOAT', 'mode': 'REQUIRED'},
            'registration_time': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'},
            'first_interaction_time': {'type': 'TIMESTAMP', 'mode': 'REQUIRED'},
            'last_interaction_time': {'type': 'TIMESTAMP', 'mode': 'REQUIRED'},
            'first_purchase_time': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'},
            'last_purchase_time': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'},
            'created_at': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'},
            'updated_at': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'},
            'first_add_to_cart_time': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'},
            'last_add_to_cart_time': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'},
            'first_view_item_time': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'},
            'last_view_item_time': {'type': 'TIMESTAMP', 'mode': 'NULLABLE'}
        }

def clean_parquet_dataframe_dynamic(df: pd.DataFrame, ec_id: Optional[int] = None,
                                   schema_path: str = None) -> pd.DataFrame:
    """
    基於 BigQuery schema 動態清理從 parquet 檔案讀取的 DataFrame

    Args:
        df: 原始 DataFrame
        ec_id: 電商 ID（用於日誌記錄）
        schema_path: BigQuery schema 檔案路徑

    Returns:
        清理後的 DataFrame
    """
    if df.empty:
        logger.warning(f"DataFrame 為空，EC ID: {ec_id}")
        return df

    logger.info(f"開始基於 schema 動態清理 DataFrame，EC ID: {ec_id}，原始形狀: {df.shape}")
    cleaned_df = df.copy()

    # 載入 BigQuery schema
    schema_dict = load_bigquery_schema(schema_path)

    # 統計處理的欄位
    processed_fields = {'INTEGER': [], 'FLOAT': [], 'TIMESTAMP': [], 'STRING': []}

    # 動態處理每個欄位
    for column in cleaned_df.columns:
        if column not in schema_dict:
            logger.warning(f"欄位 '{column}' 不在 BigQuery schema 中，跳過處理")
            continue

        field_info = schema_dict[column]
        field_type = field_info['type']
        field_mode = field_info['mode']

        logger.debug(f"處理欄位 '{column}': 類型={field_type}, 模式={field_mode}")

        # 根據類型進行相應處理
        if field_type == 'INTEGER':
            cleaned_df[column] = clean_numeric_column_dynamic(
                cleaned_df[column],
                dtype='int',
                column_name=column,
                is_required=(field_mode == 'REQUIRED')
            )
            processed_fields['INTEGER'].append(column)

        elif field_type == 'FLOAT':
            cleaned_df[column] = clean_numeric_column_dynamic(
                cleaned_df[column],
                dtype='float',
                column_name=column,
                is_required=(field_mode == 'REQUIRED')
            )
            processed_fields['FLOAT'].append(column)

        elif field_type == 'TIMESTAMP':
            cleaned_df[column] = clean_timestamp_column_dynamic(
                cleaned_df[column],
                column_name=column,
                is_required=(field_mode == 'REQUIRED')
            )
            processed_fields['TIMESTAMP'].append(column)

        elif field_type == 'STRING':
            cleaned_df[column] = clean_string_column_dynamic(
                cleaned_df[column],
                column_name=column,
                is_required=(field_mode == 'REQUIRED')
            )
            processed_fields['STRING'].append(column)

    # 記錄處理結果
    for dtype, fields in processed_fields.items():
        if fields:
            logger.info(f"✓ {dtype} 欄位處理完成 ({len(fields)} 個): {', '.join(fields)}")

    logger.info(f"動態清理完成，EC ID: {ec_id}，最終形狀: {cleaned_df.shape}")
    return cleaned_df

def clean_numeric_column_dynamic(series: pd.Series, dtype: str = 'int',
                               column_name: str = '', is_required: bool = False) -> pd.Series:
    """
    動態清理數值欄位，處理空字符串和無效值

    Args:
        series: 原始 Series
        dtype: 目標數據類型 ('int' 或 'float')
        column_name: 欄位名稱（用於日誌）
        is_required: 是否為必填欄位

    Returns:
        清理後的 Series
    """
    logger.debug(f"清理數值欄位 {column_name}，原始類型: {series.dtype}，必填: {is_required}")

    def safe_convert(x):
        try:
            # 處理各種無效值
            if pd.isna(x) or x == '' or x is None or str(x).strip() == '':
                if is_required:
                    return 0.0 if dtype == 'float' else 0
                else:
                    return None  # 非必填欄位保持 None

            # 處理字符串類型的數值
            if isinstance(x, str):
                x = x.strip()
                if x == '':
                    if is_required:
                        return 0.0 if dtype == 'float' else 0
                    else:
                        return None

            # 轉換為目標類型
            if dtype == 'float':
                return float(x)
            else:
                return int(float(x))  # 先轉 float 再轉 int，處理 '1.0' 這種情況

        except (ValueError, TypeError, OverflowError):
            logger.warning(f"無法轉換值 '{x}' (類型: {type(x)}) 為 {dtype}，使用預設值")
            if is_required:
                return 0.0 if dtype == 'float' else 0
            else:
                return None

    # 應用轉換
    result = series.apply(safe_convert)

    # 確保最終類型正確
    if dtype == 'float':
        result = result.astype('float64')
    else:
        # 對於非必填的整數欄位，使用 Int64 (nullable integer)
        if is_required:
            result = result.astype('int64')
        else:
            result = result.astype('Int64')  # pandas nullable integer

    logger.debug(f"欄位 {column_name} 清理完成，最終類型: {result.dtype}")
    return result

def clean_timestamp_column_dynamic(series: pd.Series, column_name: str = '',
                                  is_required: bool = False) -> pd.Series:
    """
    動態清理時間戳欄位，處理 NaT 值

    Args:
        series: 原始 Series
        column_name: 欄位名稱（用於日誌）
        is_required: 是否為必填欄位

    Returns:
        清理後的 Series
    """
    logger.debug(f"清理時間欄位 {column_name}，原始類型: {series.dtype}，必填: {is_required}")

    series = pd.to_datetime(series, errors='coerce')
    # 統計 NaT 值數量
    nat_count = series.isna().sum()
    if nat_count > 0:
        logger.debug(f"欄位 {column_name} 包含 {nat_count} 個 NaT 值")

        # 對於必填欄位中的 NaT 值發出警告
        if is_required:
            logger.warning(f"必填時間欄位 {column_name} 包含 {nat_count} 個 NaT 值，這可能導致 BigQuery 載入失敗")

        # 為了能成功序列化為 JSON，我們需要將 NaT 轉換為 None。
        # pandas 的 datetime64 類型會自動將 None 轉為 NaT，因此直接賦值無效。
        # 解決方法是先將 Series 轉為 object 類型，這樣它就可以同時容納 Timestamp 和 None 物件。
        series = series.astype('object')
        series.loc[series.isna()] = None


    # 返回一個混合類型的 Series (Timestamp 和 None)
    return series

def clean_string_column_dynamic(series: pd.Series, column_name: str = '',
                               is_required: bool = False) -> pd.Series:
    """
    動態清理字符串欄位

    Args:
        series: 原始 Series
        column_name: 欄位名稱（用於日誌）
        is_required: 是否為必填欄位

    Returns:
        清理後的 Series
    """
    logger.debug(f"清理字符串欄位 {column_name}，原始類型: {series.dtype}，必填: {is_required}")

    # For string, we primarily care about converting None/NaN to empty strings if required
    if is_required:
        # Fill NaN, None, etc., with an empty string
        series = series.fillna('').astype(str)
    else:
        # If not required, we can leave them as None (which pandas might show as NaN for object dtype)
        # Convert to string type to handle mixed types, but allow for None
        series = series.astype('object').where(pd.notna(series), None)

    return series

def validate_dataframe_for_bigquery_dynamic(df: pd.DataFrame, ec_id: Optional[int] = None,
                                           schema_path: str = None) -> bool:
    """
    在載入 BigQuery 之前，根據 schema 驗證 DataFrame 的完整性

    Args:
        df: 待驗證的 DataFrame
        ec_id: 電商 ID (用於日誌)
        schema_path: BigQuery schema 檔案路徑

    Returns:
        如果驗證通過返回 True，否則返回 False
    """
    is_valid = True
    logger.info(f"開始驗證 DataFrame，EC ID: {ec_id}")
    schema_dict = load_bigquery_schema(schema_path)

    for field_name, field_info in schema_dict.items():
        # 檢查必填欄位是否存在
        if field_name not in df.columns:
            if field_info['mode'] == 'REQUIRED':
                logger.error(f"驗證失敗: 必填欄位 '{field_name}' 在 DataFrame 中不存在")
                is_valid = False
            else:
                logger.debug(f"可選欄位 '{field_name}' 不存在，將在後續步驟中添加")
            continue

        # 檢查必填欄位的 NULL 值
        if field_info['mode'] == 'REQUIRED':
            if df[field_name].isnull().any():
                null_count = df[field_name].isnull().sum()
                logger.error(f"驗證失敗: 必填欄位 '{field_name}' 包含 {null_count} 個 NULL 值")
                is_valid = False

        # 檢查資料類型
        expected_type = field_info['type']
        actual_type = df[field_name].dtype

        type_mapping = {
            'INTEGER': ['int64', 'Int64'],
            'FLOAT': ['float64'],
            'TIMESTAMP': ['datetime64[ns]'],
            'STRING': ['object', 'string']
        }

        if expected_type in type_mapping:
            if str(actual_type) not in type_mapping[expected_type]:
                logger.warning(f"類型不匹配: 欄位 '{field_name}' 的預期類型是 {expected_type}，但實際是 {actual_type}")
                # This might not be a failure, but a warning is good
        else:
            logger.warning(f"未知的 BigQuery 類型 '{expected_type}'，無法驗證欄位 '{field_name}'")

    if is_valid:
        logger.info(f"DataFrame 驗證成功，EC ID: {ec_id}")
    else:
        logger.error(f"DataFrame 驗證失敗，EC ID: {ec_id}")

    return is_valid


# --- Deprecated functions for backward compatibility ---

def clean_parquet_dataframe(df: pd.DataFrame, ec_id: Optional[int] = None) -> pd.DataFrame:
    """舊版的清理函數，為了向後相容而保留"""
    logger.warning("正在使用舊版的 clean_parquet_dataframe，建議切換到 dynamic 版本")
    return clean_parquet_dataframe_dynamic(df, ec_id)

def clean_numeric_column(series: pd.Series, dtype: str = 'int', column_name: str = '') -> pd.Series:
    """舊版的數值清理函數"""
    logger.warning(f"正在使用舊版的 clean_numeric_column for {column_name}，建議切換到 dynamic 版本")
    return clean_numeric_column_dynamic(series, dtype, column_name, is_required=True)


def create_bigquery_schema_fields(schema_path: str = None) -> List:
    """
    從 JSON 檔案讀取並建立 BigQuery 的 SchemaField 物件列表。

    Args:
        schema_path: schema 檔案的路徑。

    Returns:
        一個包含 `google.cloud.bigquery.SchemaField` 物件的列表。
    """
    try:
        from google.cloud import bigquery
    except ImportError:
        logger.error("此函數需要 google-cloud-bigquery 套件")
        return []

    if schema_path is None:
        current_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.join(current_dir, '..', '..')
        schema_path = os.path.join(project_root, 'sql', 'user_stats_schema.json')

    schema_fields = []
    try:
        with open(schema_path, 'r', encoding='utf-8') as f:
            schema_data = json.load(f)

        for field_data in schema_data:
            schema_fields.append(
                bigquery.SchemaField(
                    name=field_data['name'],
                    field_type=field_data['type'],
                    mode=field_data.get('mode', 'NULLABLE'),
                    description=field_data.get('description', None)
                )
            )
        logger.info(f"成功從 {schema_path} 建立 BigQuery schema")
        return schema_fields

    except FileNotFoundError:
        logger.error(f"Schema 檔案未找到: {schema_path}")
        return []
    except json.JSONDecodeError:
        logger.error(f"無法解析 Schema 檔案: {schema_path}")
        return []
    except Exception as e:
        logger.error(f"建立 BigQuery schema 時發生錯誤: {e}")
        return []
