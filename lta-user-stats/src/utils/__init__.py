from collections import defaultdict
import pandas as pd
import numpy as np
import logging
import time
from typing import Dict, List, Any, Union, Optional, Tuple, Callable
from concurrent.futures import ThreadPoolExecutor, as_completed

def _safe_scalar_conversion(value):
    """安全地將值轉換為標量，避免 JSON Logic 中的類型問題"""
    if pd.isna(value):
        return None
    elif isinstance(value, (np.integer, np.floating)):
        return value.item()
    elif isinstance(value, np.bool_):
        return bool(value)
    elif isinstance(value, pd.Series):
        # 如果意外傳入 Series，取第一個值
        if len(value) > 0:
            first_val = value.iloc[0]
            return _safe_scalar_conversion(first_val)
        else:
            return None
    elif isinstance(value, np.ndarray):
        # 處理 numpy 陣列
        if value.size == 0:
            return None
        elif value.size == 1:
            return _safe_scalar_conversion(value.item())
        else:
            # 多個值，取第一個
            return _safe_scalar_conversion(value.flat[0])
    elif hasattr(value, '__len__') and not isinstance(value, str):
        # 如果是其他陣列類型，取第一個元素
        try:
            if len(value) > 0:
                first_item = value[0]
                return _safe_scalar_conversion(first_item)
            else:
                return None
        except (TypeError, IndexError):
            # 如果取索引失敗，嘗試直接返回值
            try:
                # 檢查是否是數值類型
                if isinstance(value, (int, float, bool)):
                    return value
                elif hasattr(value, 'item'):
                    return value.item()
                else:
                    return value
            except:
                return None
    else:
        return value

def analyze_data_time_range(chunk):
    """分析資料的時間範圍

    Args:
        chunk: pandas DataFrame，包含用戶統計資料

    Returns:
        dict: 包含各個時間欄位的統計資訊
    """
    time_columns = ['last_interaction_days', 'last_view_item_days', 'last_add_to_cart_days', 'last_purchase_days']
    analysis = {}

    for col in time_columns:
        if col in chunk.columns:
            valid_data = chunk[col][chunk[col] != -1]
            if len(valid_data) > 0:
                analysis[col] = {
                    'min_days': int(valid_data.min()),
                    'max_days': int(valid_data.max()),
                    'valid_count': len(valid_data),
                    'total_count': len(chunk)
                }
            else:
                analysis[col] = {
                    'min_days': None,
                    'max_days': None,
                    'valid_count': 0,
                    'total_count': len(chunk)
                }
        else:
            analysis[col] = 'column_not_exists'

    return analysis

def check_rule_data_requirement(rule_data, data_time_range):
    """檢查規則是否需要超過資料時間範圍的資料

    Args:
        rule_data: 規則資料
        data_time_range: 資料時間範圍分析結果

    Returns:
        dict: 包含檢查結果
    """
    result = {
        'has_sufficient_data': True,
        'missing_requirements': [],
        'reason': None
    }

    rule_vars = rule_data.get('data', {})
    json_logic_rule = rule_data.get('rule', {})


    # 檢查規則中的時間要求
    time_requirements = {}

    # 從 rule_vars 中檢查
    for var_name, var_value in rule_vars.items():
        if 'days' in var_name and isinstance(var_value, (int, float)):
            time_requirements[var_name] = var_value

    # 從 JSON Logic 規則中提取時間要求
    def extract_time_requirements_from_json_logic(rule, requirements, in_or_condition=False):
        if isinstance(rule, dict):
            for key, value in rule.items():
                if key == 'or':
                    # 對於 OR 條件，檢查是否包含 null 檢查
                    has_null_check = False
                    if isinstance(value, list):
                        for condition in value:
                            if isinstance(condition, dict):
                                for cond_key, cond_value in condition.items():
                                    if cond_key == '==' and isinstance(cond_value, list) and len(cond_value) == 2:
                                        if cond_value[1] is None or cond_value[1] == 'null':
                                            has_null_check = True
                                            break


                    # 如果 OR 條件包含 null 檢查，那麼我們認為這個條件可以通過 null 值滿足
                    # 不需要提取其中的時間要求作為強制要求
                    if has_null_check:
                        continue
                    else:
                        # 如果沒有 null 檢查，則正常處理 OR 條件
                        extract_time_requirements_from_json_logic(value, requirements, True)
                elif key in ['>', '>=', '<', '<='] and isinstance(value, list) and len(value) == 2:
                    # 檢查比較運算
                    left, right = value
                    if isinstance(left, dict) and left.get('var') and 'days' in str(left.get('var')):
                        var_name = left.get('var')
                        # 解析 right 值 - 可能是直接數值或變數引用
                        right_value = right
                        if isinstance(right, dict) and right.get('var'):
                            # 如果是變數引用，從 rule_vars 中獲取實際值
                            right_value = rule_vars.get(right.get('var'))


                        if isinstance(right_value, (int, float)):
                            # 在 OR 條件中的 > 或 >= 不應該作為強制的最小值要求
                            # 除非所有 OR 分支都要求長時間資料
                            if not in_or_condition:
                                if key in ['>', '>=']:
                                    requirements[f'{var_name}_min'] = right_value
                                else:
                                    requirements[f'{var_name}_max'] = right_value
                elif isinstance(value, (dict, list)):
                    extract_time_requirements_from_json_logic(value, requirements, in_or_condition)
        elif isinstance(rule, list):
            for item in rule:
                extract_time_requirements_from_json_logic(item, requirements, in_or_condition)


    extract_time_requirements_from_json_logic(json_logic_rule, time_requirements)

    # 檢查是否有超過資料範圍的要求
    for requirement_name, requirement_value in time_requirements.items():
        if 'days' in requirement_name and isinstance(requirement_value, (int, float)):
            # 找到對應的資料欄位
            for time_col, time_info in data_time_range.items():
                if time_info == 'column_not_exists':
                    continue

                if time_col.replace('_days', '') in requirement_name or requirement_name in time_col:
                    if time_info['max_days'] is not None:
                        if 'min' in requirement_name and requirement_value > time_info['max_days']:
                            result['has_sufficient_data'] = False
                            result['missing_requirements'].append({
                                'requirement': requirement_name,
                                'required_value': requirement_value,
                                'available_max': time_info['max_days'],
                                'column': time_col
                            })


    if not result['has_sufficient_data']:
        max_required = max(req['required_value'] for req in result['missing_requirements'])
        max_available = max(time_info['max_days'] for time_info in data_time_range.values() if isinstance(time_info, dict) and time_info['max_days'] is not None)
        result['reason'] = f"規則需要超過 {max_required} 天的資料，但實際資料最大只有 {max_available} 天"

    return result

def split_into_chunks(total_size, chunk_size=100000):
    """產生分塊索引

    Args:
        total_size: 總數據量
        chunk_size: 每個分塊的大小，預設 100000

    Returns:
        list: 包含多個 range 物件的列表，每個 range 代表一個分塊的索引範圍
    """
    return [range(i, min(i+chunk_size, total_size)) for i in range(0, total_size, chunk_size)]

def merge_results(results):
    """合併分散式處理結果

    Args:
        results: 包含多個結果字典的列表，每個字典的格式為 {rule_id: [user_ids]}
                 或者包含 list 的結果

    Returns:
        dict: 合併後的結果字典，格式為 {rule_id: [all_user_ids]}
    """
    merged = defaultdict(list)
    for result in results:
        if result is None:
            continue

        # 檢查 result 的類型，處理不同的輸入格式
        if isinstance(result, dict):
            # 正常的字典格式
            for rule_id, users in result.items():
                if isinstance(users, list):
                    merged[rule_id].extend(users)
                else:
                    # 如果 users 不是 list，轉換為 list
                    merged[rule_id].append(users)
        elif isinstance(result, list):
            # 如果 result 是 list，可能是錯誤的格式
            # 嘗試將其作為單一規則的用戶列表處理
            logging.warning(f"收到 list 格式的結果，可能是錯誤的輸入格式: {type(result)}")
            # 跳過這個結果，避免錯誤處理
            continue
        else:
            logging.warning(f"未知的結果格式: {type(result)}, 值: {result}")
            continue

    return dict(merged)

def vectorized_evaluate(chunk, rules):
    """向量化規則評估 - 使用 JSON Logic 進行完整規則評估

    Args:
        chunk: pandas DataFrame，包含要評估的資料
        rules: dict，包含規則定義

    Returns:
        dict: 符合各規則的用戶列表
    """
    try:
        import json_logic
    except ImportError:
        # 如果 json_logic 導入失敗，回退到舊版本
        logging.warning("無法導入 json_logic，回退到舊版本評估方式")
        return _legacy_vectorized_evaluate(chunk, rules)


    from src.utils import logging_setup
    logger = logging_setup.configure_logging()

    logger.info(f"開始執行 vectorized_evaluate (JSON Logic版本): 資料筆數={len(chunk)}, 規則數量={len(rules)}")

    # 檢查規則是否為空
    if not rules:
        logger.warning("規則為空，無法進行評估")
        return {}

    # 檢查資料是否為空
    if chunk.empty:
        logger.warning("資料為空，無法進行評估")
        return {}

    # 確保 'permanent' 欄位存在
    if 'permanent' not in chunk.columns:
        logger.error("欄位 'permanent' 不存在於資料中，無法進行評估")
        return {}

    # 分析資料時間範圍
    data_time_range = analyze_data_time_range(chunk)
    logger.info(f"資料時間範圍分析: {data_time_range}")

    # 顯示資料統計資訊
    logger.info(f"資料欄位: {chunk.columns.tolist()}")

    # 顯示 purchase_count 的統計資訊
    if 'purchase_count' in chunk.columns:
        purchase_stats = {
            'count': len(chunk),
            'null_count': chunk['purchase_count'].isna().sum(),
            'zero_count': (chunk['purchase_count'] == 0).sum(),
            'min': chunk['purchase_count'].min(),
            'max': chunk['purchase_count'].max(),
            'mean': chunk['purchase_count'].mean()
        }
        logger.info(f"Purchase count 統計: 總筆數={purchase_stats['count']}, 空值={purchase_stats['null_count']}, 0值={purchase_stats['zero_count']}, 範圍=[{purchase_stats['min']}, {purchase_stats['max']}], 平均={purchase_stats['mean']:.2f}")

    matches = defaultdict(list)

    # 顯示樣本資料
    sample_size = min(3, len(chunk))
    if sample_size > 0:
        logger.debug(f"資料樣本({sample_size}筆):")
        for i, (idx, row) in enumerate(chunk.iloc[:sample_size].iterrows()):
            sample_data = {
                'permanent': row.get('permanent', 'N/A'),
                'last_interaction_days': row.get('last_interaction_days', 'N/A'),
                'last_view_item_days': row.get('last_view_item_days', 'N/A'),
                'last_add_to_cart_days': row.get('last_add_to_cart_days', 'N/A'),
                'purchase_count': row.get('purchase_count', 'N/A')
            }
            logger.debug(f"  樣本 {i+1}: {sample_data}")

    # 遍歷每個規則
    for rule_id, rule_data in rules.items():
        try:
            logger.debug(f"開始評估規則 {rule_id}")

            # 檢查規則的資料需求
            data_requirement_check = check_rule_data_requirement(rule_data, data_time_range)

            if not data_requirement_check['has_sufficient_data']:
                logger.warning(f"規則 {rule_id} 需要的資料超過現有時間範圍: {data_requirement_check['reason']}")
                logger.info(f"規則 {rule_id} 的缺失需求: {data_requirement_check['missing_requirements']}")
                # 對於資料不足的規則，直接設定為 0 個用戶符合
                matches[rule_id] = []
                continue

            # 獲取 JSON Logic 規則
            json_logic_rule = rule_data.get('rule')
            if not json_logic_rule:
                logger.warning(f"規則 {rule_id} 沒有 JSON Logic 規則定義，跳過")
                continue

            logger.debug(f"規則 {rule_id} 的 JSON Logic: {json_logic_rule}")

            matched_users = []
            evaluation_errors = 0

            # 對每一行資料進行 JSON Logic 評估
            for idx, row in chunk.iterrows():
                try:
                    # 將 pandas Series 轉換為字典，並處理 NaN 值和特殊類型
                    data_dict = {}
                    for col in chunk.columns:
                        value = row[col]

                        # 首先進行基本的安全轉換
                        safe_value = _safe_scalar_conversion(value)
                        data_dict[col] = safe_value

                        # 額外檢查，確保沒有複雜類型漏網
                        if isinstance(safe_value, (pd.Series, np.ndarray)):
                            logger.warning(f"欄位 {col} 在安全轉換後仍然是複雜類型 {type(safe_value)}，進行強制轉換")
                            try:
                                if hasattr(safe_value, '__len__') and len(safe_value) > 0:
                                    if hasattr(safe_value, 'iloc'):
                                        data_dict[col] = safe_value.iloc[0]
                                    else:
                                        data_dict[col] = safe_value[0]

                                    # 再次確保是標量
                                    if isinstance(data_dict[col], (np.integer, np.floating)):
                                        data_dict[col] = data_dict[col].item()
                                    elif isinstance(data_dict[col], np.bool_):
                                        data_dict[col] = bool(data_dict[col])
                                else:
                                    data_dict[col] = None
                            except Exception as e:
                                logger.error(f"強制轉換欄位 {col} 時發生錯誤: {str(e)}, 設為 None")
                                data_dict[col] = None

                    # 檢查 data_dict 中是否包含規則需要的資料
                    rule_vars = rule_data.get('data', {})
                    for var_name, var_value in rule_vars.items():
                        data_dict[var_name] = _safe_scalar_conversion(var_value)

                    # 確保動態計算的欄位也被包含進去，並進行額外驗證
                    dynamic_columns = ['last_interaction_days', 'last_view_item_days', 'last_add_to_cart_days', 'last_purchase_days']
                    for col in dynamic_columns:
                        if col in chunk.columns and col not in data_dict:
                            value = row[col]
                            data_dict[col] = _safe_scalar_conversion(value)

                        # 對動態欄位進行額外檢查，確保是數值類型
                        if col in data_dict:
                            val = data_dict[col]
                            if isinstance(val, (pd.Series, np.ndarray, list)):
                                logger.warning(f"動態欄位 {col} 仍然是複雜類型，進行修正")
                                try:
                                    if hasattr(val, 'iloc'):
                                        data_dict[col] = int(val.iloc[0]) if pd.notna(val.iloc[0]) else -1
                                    elif hasattr(val, '__len__') and len(val) > 0:
                                        data_dict[col] = int(val[0]) if pd.notna(val[0]) else -1
                                    else:
                                        data_dict[col] = -1
                                except (ValueError, TypeError, IndexError):
                                    data_dict[col] = -1
                            elif val is not None and not isinstance(val, (int, float)):
                                try:
                                    data_dict[col] = int(val) if pd.notna(val) else -1
                                except (ValueError, TypeError):
                                    data_dict[col] = -1

                    # 更詳細的調試信息
                    if idx < 3:  # 只記錄前3筆資料的詳細評估過程
                        logger.debug(f"評估規則 {rule_id} 對用戶 {row['permanent']}:")
                        logger.debug(f"  輸入資料: {data_dict}")
                        logger.debug(f"  規則定義: {json_logic_rule}")

                    # 使用 JSON Logic 評估規則
                    result = json_logic.jsonLogic(json_logic_rule, data_dict)

                    # 記錄評估結果
                    if idx < 3:
                        logger.debug(f"  評估結果: {result}")

                    # 處理結果，確保為標量值，避免 truth value ambiguous 錯誤
                    if isinstance(result, pd.Series):
                        # 如果結果是 Series，根據內容決定如何處理
                        if len(result) == 0:
                            result = False
                        elif len(result) == 1:
                            scalar_result = result.iloc[0]
                            # 確保標量結果也不會引起問題
                            if isinstance(scalar_result, (np.bool_, bool)):
                                result = bool(scalar_result)
                            elif isinstance(scalar_result, (np.integer, np.floating)):
                                result = bool(scalar_result.item())
                            else:
                                result = bool(scalar_result)
                        else:
                            # 多個值的情況，使用 any() 來避免歧義
                            try:
                                # 首先嘗試檢查是否所有值都是布林類型
                                if all(isinstance(x, (bool, np.bool_)) for x in result if pd.notna(x)):
                                    result = bool(result.any())
                                else:
                                    # 對於非布林值，檢查是否有任何非零/非空值
                                    result = bool(result.notna().any() and (result != 0).any())
                            except ValueError:
                                # 如果上述方法失敗，取第一個非空值
                                non_null_values = result.dropna()
                                if len(non_null_values) > 0:
                                    first_value = non_null_values.iloc[0]
                                    result = bool(first_value) if not isinstance(first_value, (np.integer, np.floating)) else bool(first_value.item() if hasattr(first_value, 'item') else first_value)
                                else:
                                    result = False
                    elif isinstance(result, np.ndarray):
                        # 處理 numpy 陣列
                        if result.size == 0:
                            result = False
                        elif result.size == 1:
                            scalar_result = result.item()
                            result = bool(scalar_result)
                        else:
                            try:
                                result = bool(np.any(result))
                            except ValueError:
                                # 如果 np.any 失敗，取第一個元素
                                result = bool(result.flat[0])
                    elif hasattr(result, '__len__') and not isinstance(result, str):
                        # 處理其他陣列類型的結果
                        if len(result) == 0:
                            result = False
                        elif len(result) == 1:
                            result = bool(result[0])
                        else:
                            try:
                                result = bool(any(result))
                            except (ValueError, TypeError):
                                # 如果 any() 失敗，取第一個元素
                                result = bool(result[0]) if result else False

                    # 最終確保 result 是標量布林值
                    if not isinstance(result, (bool, np.bool_)):
                        if isinstance(result, (np.integer, np.floating)):
                            result = bool(result.item())
                        elif hasattr(result, '__len__') and not isinstance(result, str):
                            # 最後的安全措施：如果仍然是陣列類型，強制轉換
                            try:
                                if isinstance(result, np.ndarray):
                                    result = bool(np.any(result))
                                elif isinstance(result, pd.Series):
                                    result = bool(result.any())
                                else:
                                    result = bool(any(result)) if result else False
                            except (ValueError, TypeError):
                                result = False
                        else:
                            result = bool(result)

                    # 如果結果為 True，添加到符合列表
                    if result:
                        matched_users.append(row['permanent'])

                except Exception as e:
                    evaluation_errors += 1
                    if evaluation_errors <= 3:  # 只記錄前3個錯誤
                        logger.error(f"規則 {rule_id} 評估第 {idx} 行時發生錯誤: {str(e)}")
                        logger.error(f"  輸入資料: {data_dict}")
                        logger.error(f"  規則定義: {json_logic_rule}")

            if evaluation_errors > 3:
                logger.warning(f"規則 {rule_id} 共有 {evaluation_errors} 行評估錯誤")

            # 添加符合規則的用戶
            if matched_users:
                matches[rule_id].extend(matched_users)
                logger.info(f"規則 {rule_id} 符合的用戶數量: {len(matched_users)}")
            else:
                # 確保所有規則都有記錄，即使是空的
                if rule_id not in matches:
                    matches[rule_id] = []
                logger.debug(f"規則 {rule_id} 沒有符合的用戶")

        except Exception as e:
            logger.error(f"評估規則 {rule_id} 時發生錯誤: {str(e)}")
            # 即使發生錯誤，也要確保規則有記錄
            if rule_id not in matches:
                matches[rule_id] = []
            continue

    # 確保所有輸入的規則都在結果中有記錄
    for rule_id in rules.keys():
        if rule_id not in matches:
            matches[rule_id] = []
            logger.info(f"規則 {rule_id} 沒有任何結果，設定為空列表")

    # 計算每個規則符合的用戶數量
    match_counts = {rule_id: len(users) for rule_id, users in matches.items()}

    # 檢查是否有多個規則產生相同數量的用戶
    count_to_rules = defaultdict(list)
    for rule_id, count in match_counts.items():
        count_to_rules[count].append(rule_id)

    # 找出前三大的規則
    top_matches = sorted(
        [(count, rules) for count, rules in count_to_rules.items()],
        key=lambda x: x[0],
        reverse=True
    )[:3]

    # 格式化輸出
    top_matches_formatted = [
        f"{count} 用戶: {', '.join(rules)}"
        for count, rules in top_matches
    ]

    logger.info(f"vectorized_evaluate (JSON Logic版本) 完成，共有 {len(matches)} 個規則有符合的用戶")

    # 詳細的結果分析
    result_analysis = {}
    data_insufficient_rules = []

    for rule_id, users in matches.items():
        user_count = len(users)
        result_analysis[rule_id] = user_count

        # 檢查是否是因為資料不足而沒有結果
        if user_count == 0:
            rule_data = rules.get(rule_id, {})
            data_requirement_check = check_rule_data_requirement(rule_data, data_time_range)
            if not data_requirement_check['has_sufficient_data']:
                data_insufficient_rules.append({
                    'rule_id': rule_id,
                    'reason': data_requirement_check['reason'],
                    'requirements': data_requirement_check['missing_requirements']
                })

    # 記錄詳細的結果
    logger.info(f"所有規則結果統計: {result_analysis}")

    if data_insufficient_rules:
        logger.warning(f"因資料時間範圍不足而無結果的規則 ({len(data_insufficient_rules)} 個):")
        for rule_info in data_insufficient_rules:
            logger.warning(f"  {rule_info['rule_id']}: {rule_info['reason']}")

    # 計算每個規則符合的用戶數量
    match_counts = {rule_id: len(users) for rule_id, users in matches.items()}

    # 檢查是否有多個規則產生相同數量的用戶
    count_to_rules = defaultdict(list)
    for rule_id, count in match_counts.items():
        count_to_rules[count].append(rule_id)

    # 找出前三大的規則
    top_matches = sorted(
        [(count, rules) for count, rules in count_to_rules.items()],
        key=lambda x: x[0],
        reverse=True
    )[:3]

    # 格式化輸出
    top_matches_formatted = [
        f"{count} 用戶: {', '.join(rules_list)}"
        for count, rules_list in top_matches
    ]

    logger.info(f"前3大規則: {top_matches_formatted if top_matches_formatted else '無'}")

    # 檢查是否有重複問題
    duplicates = [(count, rules) for count, rules in count_to_rules.items() if len(rules) > 1]
    if duplicates:
        logger.warning(f"發現 {len(duplicates)} 組規則產生相同的用戶數量: {duplicates}")
        # 檢查是否所有重複的規則都是因為資料不足
        data_insufficient_duplicates = []
        for count, duplicate_rules in duplicates:
            if count == 0:  # 只檢查 0 用戶的重複規則
                all_data_insufficient = True
                for rule_id in duplicate_rules:
                    rule_data = rules.get(rule_id, {})
                    data_requirement_check = check_rule_data_requirement(rule_data, data_time_range)
                    if data_requirement_check['has_sufficient_data']:
                        all_data_insufficient = False
                        break
                if all_data_insufficient:
                    data_insufficient_duplicates.append((count, duplicate_rules))

        if data_insufficient_duplicates:
            logger.info(f"其中 {len(data_insufficient_duplicates)} 組重複是因為資料時間範圍不足造成的，這是正常現象")

        # 只對非資料不足的重複發出警告
        problematic_duplicates = [dup for dup in duplicates if dup not in data_insufficient_duplicates]
        if problematic_duplicates:
            logger.warning(f"需要關注的重複問題: {problematic_duplicates}")
        else:
            logger.info("所有重複都是因為正當原因（資料不足）造成的")
    else:
        logger.info("所有規則產生了不同的用戶數量，修復成功！")

    return matches

def _legacy_vectorized_evaluate(chunk, rules):
    """舊版本的向量化規則評估 - fallback函數

    Args:
        chunk: pandas DataFrame，包含要評估的資料
        rules: dict，包含規則定義

    Returns:
        dict: 符合各規則的用戶列表
    """
    from src.utils import logging_setup
    logger = logging_setup.configure_logging()

    logger.warning("使用舊版本的 vectorized_evaluate (無 JSON Logic 支援)")
    logger.info(f"開始執行 _legacy_vectorized_evaluate: 資料筆數={len(chunk)}, 規則數量={len(rules)}")

    # 檢查規則是否為空
    if not rules:
        logger.warning("規則為空，無法進行評估")
        return {}

    # 檢查資料是否為空
    if chunk.empty:
        logger.warning("資料為空，無法進行評估")
        return {}

    matches = defaultdict(list)

    # 遍歷每個規則
    for rule_id, rule_data in rules.items():
        logger.debug(f"開始評估規則 {rule_id}")
        mask = pd.Series(True, index=chunk.index)

        # 處理最小天數條件
        if 'min_days' in rule_data.get('data', {}):
            min_days = rule_data['data']['min_days']
            if 'last_interaction_days' in chunk.columns:
                special_mask = chunk['last_interaction_days'] == -1
                condition_mask = ~special_mask & (chunk['last_interaction_days'] > min_days)
                mask &= condition_mask

        if 'max_days' in rule_data.get('data', {}):
            max_days = rule_data['data']['max_days']
            if 'last_interaction_days' in chunk.columns:
                special_mask = chunk['last_interaction_days'] == -1
                condition_mask = ~special_mask & (chunk['last_interaction_days'] <= max_days)
                mask &= condition_mask

        # 處理購買次數的條件
        if 'min_purchases' in rule_data.get('data', {}):
            min_purchases = rule_data['data']['min_purchases']
            if 'purchase_count' in chunk.columns:
                condition_mask = chunk['purchase_count'] >= min_purchases
                mask &= condition_mask

        if 'max_purchases' in rule_data.get('data', {}):
            max_purchases = rule_data['data']['max_purchases']
            if 'purchase_count' in chunk.columns:
                condition_mask = chunk['purchase_count'] <= max_purchases
                mask &= condition_mask

        # 如果有符合條件的用戶，則添加到結果中
        if mask.any() and 'permanent' in chunk.columns:
            matched_users = chunk.loc[mask, 'permanent'].tolist()
            matches[rule_id].extend(matched_users)

    logger.info(f"_legacy_vectorized_evaluate 完成，共有 {len(matches)} 個規則有符合的用戶")
    return matches