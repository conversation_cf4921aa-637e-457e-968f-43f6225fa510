import logging
import os
import google.cloud.logging as cloud_logging
from datetime import datetime
import pytz

# 全域變數追蹤是否已經配置過
_configured = False

def _setup_local_logging(root_logger, log_level):
    """設置本地日誌配置"""
    # 只在沒有 handlers 時添加新的 handler
    if not root_logger.handlers:
        formatter = logging.Formatter(
            '%(asctime)s - %(levelname)s - %(name)s - %(funcName)s:%(lineno)d - %(message)s'
        )
        handler = logging.StreamHandler()
        handler.setFormatter(formatter)
        handler.setLevel(log_level)

        root_logger.addHandler(handler)

def configure_logging(log_level=logging.INFO):
    """整合 main.py 第30-48行日誌設定

    Args:
        log_level: 日誌級別，默認為 logging.INFO
    """
    global _configured

    # 避免重複配置
    if _configured:
        logger = logging.getLogger('lta_user_stats')
        # 確保返回的 logger 有正確的級別
        logger.setLevel(log_level)
        return logger

    logger = logging.getLogger('lta_user_stats')
    logger.setLevel(log_level)

    # 為了更詳細的調試，也設置根日誌記錄器
    root_logger = logging.getLogger()
    root_logger.setLevel(log_level)

    # 為 src.core 設置更詳細的日誌
    core_logger = logging.getLogger('src.core')
    core_logger.setLevel(log_level)

    if os.environ.get('K_SERVICE'):
        # Cloud Functions 環境
        try:
            client = cloud_logging.Client()
            client.setup_logging(log_level=log_level)
        except Exception as e:
            # 在測試環境或無憑證環境中，回退到本地日誌設定
            print(f"警告：無法設置 Cloud Logging，回退到本地日誌設定。錯誤：{e}")
            _setup_local_logging(root_logger, log_level)
    else:
        # 本地開發環境或測試環境
        _setup_local_logging(root_logger, log_level)

    # 標記為已配置
    _configured = True
    return logger