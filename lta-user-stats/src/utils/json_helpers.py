import json
import os
import pandas as pd
from datetime import datetime
from typing import Any, List, Dict, Tuple
import logging

logger = logging.getLogger(__name__)

# BigQuery row size 限制 (100MB)
MAX_ROW_SIZE_BYTES = 104857600

def json_serializer(obj: Any) -> Any:
    """自定義 JSON 序列化處理，可處理 pandas 和 Python 標準庫中的特殊類型

    Args:
        obj: 需要序列化的對象

    Returns:
        適合 JSON 序列化的格式 (str, int, float, bool, list, dict, None)

    Raises:
        TypeError: 無法處理的數據類型
    """
    # 先處理 numpy 陣列（避免在 pd.isna 時出現 ambiguous truth value 錯誤）
    if hasattr(obj, 'tolist'):  # numpy 陣列
        return obj.tolist()

    # 處理 numpy 數值類型
    if hasattr(obj, 'item'):  # numpy 標量
        return obj.item()

    # 處理 pandas 的 NaT 和 Timestamp
    if pd.isna(obj):
        return None
    if isinstance(obj, (pd.Timestamp, datetime)):
        return obj.isoformat()

    # 處理 Python 的 Decimal 類型
    if hasattr(obj, '__float__'):
        try:
            return float(obj)
        except (ValueError, OverflowError):
            return str(obj)

    # 如果無法處理，拋出類型錯誤
    raise TypeError(f"Type {type(obj)} not serializable")

def dumps(obj: Any, **kwargs) -> str:
    """加強版的 json.dumps，可以處理 pandas 和 datetime 類型

    Args:
        obj: 需要序列化的對象
        **kwargs: 傳遞給 json.dumps 的其他參數

    Returns:
        str: JSON 格式的字串
    """
    # 設置預設參數以確保 JSON 序列化的穩定性和完整性
    default_kwargs = {
        'default': json_serializer,
        'ensure_ascii': False,  # 允許非 ASCII 字符，避免轉義問題
        'separators': (',', ':'),  # 緊湊格式，減少檔案大小
    }

    # 合併用戶提供的參數，用戶參數優先
    final_kwargs = {**default_kwargs, **kwargs}

    try:
        json_str = json.dumps(obj, **final_kwargs)

        # 驗證生成的 JSON 字串是否有效
        json.loads(json_str)

        return json_str
    except (TypeError, ValueError, json.JSONDecodeError) as e:
        logger.error(f"JSON 序列化失敗: {e}")
        logger.error(f"問題對象類型: {type(obj)}")
        logger.error(f"問題對象內容: {str(obj)[:500]}...")  # 只顯示前 500 個字符
        raise

def to_json_safe(df: pd.DataFrame) -> pd.DataFrame:
    """將 DataFrame 中的 datetime 型別欄位轉換為 JSON 安全的格式

    Args:
        df: 需要處理的 DataFrame

    Returns:
        pd.DataFrame: 處理後的 DataFrame，所有 datetime 欄位會轉為 ISO 格式字串，NaT 會轉為 None
    """
    df = df.copy()
    for col in df.columns:
        if pd.api.types.is_datetime64_any_dtype(df[col]):
            df[col] = df[col].apply(lambda x: x.isoformat() if not pd.isna(x) else None)
    return df

def check_row_size(record: Dict[str, Any]) -> Tuple[int, str]:
    """檢查單一 row 的序列化大小

    Args:
        record: 要檢查的記錄

    Returns:
        Tuple[int, str]: (大小(bytes), JSON字串)
    """
    json_str = dumps(record)
    size_bytes = len(json_str.encode('utf-8'))
    return size_bytes, json_str

def split_large_record(record: Dict[str, Any], max_size: int = MAX_ROW_SIZE_BYTES) -> List[Dict[str, Any]]:
    """將超過大小限制的記錄切分為多個較小的記錄

    Args:
        record: 原始記錄
        max_size: 最大允許的大小 (bytes)

    Returns:
        List[Dict[str, Any]]: 切分後的記錄列表
    """
    size_bytes, json_str = check_row_size(record)

    if size_bytes <= max_size:
        return [record]

    logger.warning(f"發現超大記錄，大小: {size_bytes} bytes ({size_bytes/1024/1024:.2f} MB)")

    # 分析哪些欄位可能導致記錄過大
    field_sizes = {}
    for key, value in record.items():
        field_json = dumps({key: value})
        field_size = len(field_json.encode('utf-8'))
        field_sizes[key] = field_size
        logger.info(f"欄位 '{key}' 大小: {field_size} bytes ({field_size/1024/1024:.2f} MB)")

    # 找出最大的欄位
    largest_field = max(field_sizes.items(), key=lambda x: x[1])
    logger.warning(f"最大欄位: '{largest_field[0]}' ({largest_field[1]} bytes)")

    # 嘗試切分策略
    split_records = []

    # 策略 1: 如果是字串欄位且過大，嘗試分段
    if isinstance(record.get(largest_field[0]), str) and largest_field[1] > max_size // 2:
        large_string = record[largest_field[0]]

                # 計算安全的chunk size
        estimated_base_size = size_bytes - largest_field[1]  # 基礎記錄大小

        # 為小 max_size 調整安全空間
        if max_size < 10000:  # 小於10KB的限制
            safety_margin = max_size // 20  # 使用5%的安全空間
        else:
            safety_margin = min(10000, max_size // 10)  # 大記錄使用10%或最多10KB

        safe_chunk_size = max_size - estimated_base_size - safety_margin

        # 確保chunk size合理且能實際進行切分
        if safe_chunk_size <= 100:  # 如果計算出的chunk太小
            # 使用更簡單的策略：直接按長度切分
            target_parts = max(2, (size_bytes + max_size - 1) // max_size)  # 預估需要多少份
            safe_chunk_size = len(large_string) // target_parts

        safe_chunk_size = max(50, min(safe_chunk_size, len(large_string)))  # 至少50字符

        logger.info(f"字串長度: {len(large_string)}, 使用chunk_size: {safe_chunk_size}")

        # 創建基礎記錄（不包含大欄位）
        base_record = {k: v for k, v in record.items() if k != largest_field[0]}

        # 分段處理大字串
        for i in range(0, len(large_string), safe_chunk_size):
            chunk = large_string[i:i + safe_chunk_size]
            split_record = base_record.copy()
            split_record[f"{largest_field[0]}_part_{i//safe_chunk_size + 1}"] = chunk
            split_record["_split_info"] = {
                "original_field": largest_field[0],
                "part_number": i//safe_chunk_size + 1,
                "total_parts": (len(large_string) + safe_chunk_size - 1) // safe_chunk_size
            }

            # 驗證切分後的記錄大小（允許合理的彈性）
            test_size, _ = check_row_size(split_record)
            # 對於小 max_size，允許更大的彈性
            if max_size < 10000:
                tolerance = 1.2  # 允許超過20%
            else:
                tolerance = 1.1  # 允許超過10%

            if test_size > max_size * tolerance:
                logger.warning(f"切分後記錄仍過大: {test_size} bytes (limit: {max_size * tolerance:.0f})，跳過此部分")
                continue

            split_records.append(split_record)

        logger.info(f"將字串欄位 '{largest_field[0]}' 切分為 {len(split_records)} 個部分")
        return split_records

    # 策略 2: 如果是列表欄位，嘗試拆分
    elif isinstance(record.get(largest_field[0]), list) and largest_field[1] > max_size // 2:
        large_list = record[largest_field[0]]
        base_record = {k: v for k, v in record.items() if k != largest_field[0]}

        # 列表切分策略
        estimated_base_size = size_bytes - largest_field[1]
        single_item_size = largest_field[1] / len(large_list) if large_list else 0

        # 為小 max_size 調整安全空間
        if max_size < 10000:  # 小於10KB的限制
            safety_margin = max_size // 20  # 使用5%的安全空間
        else:
            safety_margin = min(20000, max_size // 10)  # 大記錄使用10%或最多20KB

        safe_space = max_size - estimated_base_size - safety_margin

        # 計算每chunk的項目數量
        if single_item_size > 0 and safe_space > 0:
            items_per_chunk = max(1, int(safe_space / single_item_size))
        else:
            # 使用更簡單的策略：直接按預估份數切分
            target_parts = max(2, (size_bytes + max_size - 1) // max_size)
            items_per_chunk = max(1, len(large_list) // target_parts)

        items_per_chunk = min(items_per_chunk, len(large_list))  # 不超過總項目數

        logger.info(f"列表長度: {len(large_list)}, 每chunk項目數: {items_per_chunk}")

        for i in range(0, len(large_list), items_per_chunk):
            chunk = large_list[i:i + items_per_chunk]
            split_record = base_record.copy()
            split_record[f"{largest_field[0]}_part_{i//items_per_chunk + 1}"] = chunk
            split_record["_split_info"] = {
                "original_field": largest_field[0],
                "part_number": i//items_per_chunk + 1,
                "total_parts": (len(large_list) + items_per_chunk - 1) // items_per_chunk
            }

            # 驗證切分後的記錄大小（允許合理的彈性）
            test_size, _ = check_row_size(split_record)
            # 對於小 max_size，允許更大的彈性
            if max_size < 10000:
                tolerance = 1.2  # 允許超過20%
            else:
                tolerance = 1.1  # 允許超過10%

            if test_size > max_size * tolerance:
                logger.warning(f"列表切分後記錄仍過大: {test_size} bytes (limit: {max_size * tolerance:.0f})，跳過此部分")
                continue

            split_records.append(split_record)

        logger.info(f"將列表欄位 '{largest_field[0]}' 切分為 {len(split_records)} 個部分")
        return split_records

    # 策略 3: 如果無法切分，記錄錯誤並返回空列表
    else:
        logger.error(f"無法切分記錄，欄位 '{largest_field[0]}' 類型: {type(record.get(largest_field[0]))}")
        logger.error(f"記錄大小: {size_bytes} bytes，超過限制: {max_size} bytes")
        return []

def process_records_with_size_check(records: List[Dict[str, Any]], max_size: int = MAX_ROW_SIZE_BYTES) -> List[Dict[str, Any]]:
    """處理記錄列表，檢查並切分超過大小限制的記錄

    Args:
        records: 原始記錄列表
        max_size: 最大允許的大小 (bytes)

    Returns:
        List[Dict[str, Any]]: 處理後的記錄列表
    """
    processed_records = []
    split_count = 0
    dropped_count = 0

    for i, record in enumerate(records):
        size_bytes, _ = check_row_size(record)

        if size_bytes <= max_size:
            processed_records.append(record)
        else:
            logger.warning(f"記錄 {i} 超過大小限制: {size_bytes} bytes ({size_bytes/1024/1024:.2f} MB)")

            # 嘗試切分記錄
            split_records = split_large_record(record, max_size)

            if split_records:
                processed_records.extend(split_records)
                split_count += 1
                logger.info(f"記錄 {i} 成功切分為 {len(split_records)} 個部分")
            else:
                dropped_count += 1
                logger.error(f"記錄 {i} 無法切分，已丟棄")

    if split_count > 0:
        logger.warning(f"總共切分了 {split_count} 個記錄")
    if dropped_count > 0:
        logger.error(f"總共丟棄了 {dropped_count} 個無法切分的記錄")

    return processed_records

def safe_write_json_lines(records: List[Dict[str, Any]], file_path: str, max_retries: int = 3) -> bool:
    """安全地將記錄列表寫入 JSONL 檔案，確保檔案完整性

    Args:
        records: 要寫入的記錄列表
        file_path: 目標檔案路徑
        max_retries: 最大重試次數

    Returns:
        bool: 寫入是否成功
    """
    for attempt in range(max_retries):
        try:
            # 使用臨時檔案確保原子性寫入
            temp_path = file_path + f'.tmp.{attempt}'

            if attempt > 0:
                logger.warning(f"JSON 寫入重試第 {attempt} 次，檔案: {file_path}")
                import time
                time.sleep(1)  # 短暫等待，避免資源競爭

            with open(temp_path, 'w', encoding='utf-8') as f:
                total_bytes_written = 0
                for i, record in enumerate(records):
                    try:
                        json_line = dumps(record)

                        # 檢查單行大小
                        line_bytes = len(json_line.encode('utf-8'))

                        # 診斷異常大的記錄
                        if line_bytes > 1024:  # 1KB 以上記錄進行診斷
                            if i < 5 or line_bytes > 10 * 1024:  # 前5筆或超過10KB的記錄
                                logger.info(f"記錄 {i} 大小: {line_bytes:,} bytes")
                                if line_bytes > 5 * 1024:  # 5KB 以上顯示詳細資訊
                                    logger.info(f"大記錄的前200字符: {json_line[:200]}...")
                                    # 分析記錄中的大欄位
                                    if isinstance(record, dict):
                                        for key, value in record.items():
                                            if isinstance(value, str) and len(value) > 1000:
                                                logger.warning(f"記錄 {i} 的欄位 '{key}' 異常大: {len(value)} 字符")

                        if line_bytes > 50 * 1024 * 1024:  # 50MB 警告
                            logger.warning(f"記錄 {i} 非常大: {line_bytes:,} bytes ({line_bytes/(1024*1024):.2f} MB)")
                            logger.warning(f"大記錄的前100字符: {json_line[:100]}...")

                        f.write(json_line + '\n')
                        total_bytes_written += line_bytes + 1  # +1 for newline

                        # 檢查累計檔案大小
                        if total_bytes_written > 1024 * 1024 * 1024:  # 1GB 警告
                            logger.warning(f"JSON 檔案已達 {total_bytes_written/(1024*1024):.2f} MB，可能會導致 BigQuery 載入問題")

                    except Exception as e:
                        logger.error(f"序列化記錄 {i} 失敗: {e}")
                        logger.error(f"問題記錄: {str(record)[:200]}...")
                        # 清理臨時檔案
                        if os.path.exists(temp_path):
                            os.remove(temp_path)
                        raise e  # 重新拋出異常，讓外層重試

                # 確保資料寫入磁碟
                f.flush()
                os.fsync(f.fileno())

            # 驗證寫入的檔案
            # 檢查檔案大小
            file_size = os.path.getsize(temp_path)
            logger.info(f"JSON 檔案大小: {file_size:,} bytes ({file_size/(1024*1024):.2f} MB)")

            with open(temp_path, 'r', encoding='utf-8') as f:
                line_count = 0
                content = f.read()

                # 檢查檔案是否以換行符結尾
                if content and not content.endswith('\n'):
                    logger.error("JSON 檔案沒有正確以換行符結尾，可能發生截斷")
                    logger.error(f"檔案最後 100 字符: ...{content[-100:]}")
                    os.remove(temp_path)
                    raise Exception("JSON 檔案截斷")

                # 檢查檔案是否為空或過小（動態計算最小期望大小）
                # 每筆記錄至少應該有 2 bytes（最小的 JSON 如 "{}"），加上換行符
                min_expected_size = len(records) * 3  # 每筆記錄至少 3 bytes（包含換行符）
                if file_size < min_expected_size and file_size < 5:  # 絕對最小值 5 bytes
                    logger.error(f"JSON 檔案過小 ({file_size} bytes)，期望至少 {min_expected_size} bytes，可能寫入失敗")
                    os.remove(temp_path)
                    raise Exception("JSON 檔案過小")

                # 逐行驗證 JSON
                f.seek(0)  # 重新回到檔案開頭
                for line_num, line in enumerate(f, 1):
                    if line.strip():  # 跳過空行
                        try:
                            json.loads(line)  # 驗證每行都是有效的 JSON
                            line_count += 1
                        except json.JSONDecodeError as json_err:
                            logger.error(f"第 {line_num} 行 JSON 解析失敗: {json_err}")
                            logger.error(f"問題行的前200字符: {line[:200]}...")
                            if len(line) > 200:
                                logger.error(f"問題行的後200字符: ...{line[-200:]}")
                            # 檢查是否是檔案截斷問題
                            if line_num == len(content.splitlines()) and not line.endswith('\n'):
                                logger.error("最後一行沒有換行符，可能是檔案截斷")
                            os.remove(temp_path)
                            raise Exception(f"JSON 解析失敗: {json_err}")

                if line_count != len(records):
                    logger.error(f"檔案驗證失敗: 期望 {len(records)} 行，實際 {line_count} 行")
                    logger.error(f"可能的檔案截斷問題，檔案大小: {file_size} bytes")
                    os.remove(temp_path)
                    raise Exception("記錄數量不匹配")

            # 原子性移動檔案
            import shutil
            shutil.move(temp_path, file_path)
            logger.info(f"成功寫入 {len(records)} 筆記錄到 {file_path}")
            return True

        except Exception as e:
            logger.error(f"第 {attempt + 1} 次嘗試失敗: {e}")
            # 清理臨時檔案
            if os.path.exists(temp_path):
                os.remove(temp_path)

            if attempt == max_retries - 1:
                logger.error(f"所有 {max_retries} 次重試都失敗，放棄寫入")
                return False
            # 繼續下一次重試

    return False