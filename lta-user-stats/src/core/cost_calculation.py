from google.cloud import bigquery
from utils import logging_setup

logger = logging_setup.configure_logging()

class CostCalculator:
    """搬移自 main.py 第657-664行成本計算邏輯"""

    USD_PER_TB = 5.0  # BigQuery 每 TB 處理成本
    USD_TO_TWD = 30.0  # 匯率

    @classmethod
    def calculate_cost(cls, bytes_processed: int) -> dict:
        tb_processed = bytes_processed / (1024 ** 4)
        usd_cost = tb_processed * cls.USD_PER_TB
        return {
            'usd': round(usd_cost, 4),
            'twd': round(usd_cost * cls.USD_TO_TWD, 2)
        }