from datetime import datetime, timezone

def check_ec_data_query(ec_id: int, start_time: datetime, end_time: datetime) -> str:
    """
    建立檢查電商資料存在與否的查詢

    Args:
        ec_id: 電商 ID
        start_time: 查詢的開始時間
        end_time: 查詢的結束時間

    Returns:
        查詢字串
    """
    query = f"""
    SELECT COUNT(*) as count
    FROM `tagtoo-tracking.event_prod.tagtoo_event`
    WHERE ec_id = {ec_id}
    AND event_time BETWEEN TIMESTAMP('{start_time.strftime("%Y-%m-%d %H:%M:%S")}')
                        AND TIMESTAMP('{end_time.strftime("%Y-%m-%d %H:%M:%S")}')
    LIMIT 1
    """
    return query

def base_user_stats_query(ec_id: int, start_time: datetime, end_time: datetime) -> str:
    """
    建立獲取使用者基礎統計資料的查詢

    Args:
        ec_id: 電商 ID
        start_time: 查詢的開始時間
        end_time: 查詢的結束時間

    Returns:
        查詢字串
    """
    query = f"""
    SELECT
        {ec_id} as ec_id,
        permanent,
        COUNT(DISTINCT session.id) as total_sessions,
        SUM(CASE WHEN event.name = 'purchase' THEN 1 ELSE 0 END) as purchase_count,
        SUM(CASE WHEN event.name = 'purchase' THEN event.value ELSE 0 END) as total_purchase_amount,
        MIN(event_time) as first_interaction_time,
        MAX(event_time) as last_interaction_time,
        MIN(CASE WHEN event.name = 'purchase' THEN event_time ELSE NULL END) as first_purchase_time,
        MAX(CASE WHEN event.name = 'purchase' THEN event_time ELSE NULL END) as last_purchase_time,
        MIN(CASE WHEN event.name = 'add_to_cart' THEN event_time ELSE NULL END) as first_add_to_cart_time,
        MAX(CASE WHEN event.name = 'add_to_cart' THEN event_time ELSE NULL END) as last_add_to_cart_time,
        MIN(CASE WHEN event.name = 'view_item' THEN event_time ELSE NULL END) as first_view_item_time,
        MAX(CASE WHEN event.name = 'view_item' THEN event_time ELSE NULL END) as last_view_item_time,
        MIN(CASE WHEN event.name = 'registration' THEN event_time ELSE NULL END) as registration_time
    FROM `tagtoo-tracking.event_prod.tagtoo_event`
    WHERE ec_id = {ec_id}
    AND permanent IS NOT NULL
    AND event_time BETWEEN TIMESTAMP('{start_time.strftime("%Y-%m-%d %H:%M:%S")}')
                        AND TIMESTAMP('{end_time.strftime("%Y-%m-%d %H:%M:%S")}')
    GROUP BY permanent
    """
    return query

def daily_user_stats_query(ec_id: int, start_time: datetime, end_time: datetime) -> str:
    """
    建立獲取使用者每日統計資料的查詢

    Args:
        ec_id: 電商 ID
        start_time: 查詢的開始時間
        end_time: 查詢的結束時間

    Returns:
        查詢字串
    """
    query = f"""
    SELECT
        {ec_id} as ec_id,
        permanent,
        COUNT(DISTINCT session.id) as daily_sessions,
        COUNT(DISTINCT DATE(event_time)) as active_days,
        SUM(CASE WHEN event.name = 'purchase' THEN 1 ELSE 0 END) as daily_purchase_count,
        SUM(CASE WHEN event.name = 'purchase' THEN event.value ELSE 0 END) as daily_purchase_amount,
        MIN(CASE WHEN event.name = 'purchase' THEN event_time ELSE NULL END) as first_purchase_of_day,
        MAX(CASE WHEN event.name = 'purchase' THEN event_time ELSE NULL END) as last_purchase_of_day,
        MIN(CASE WHEN event.name = 'add_to_cart' THEN event_time ELSE NULL END) as first_add_to_cart_of_day,
        MAX(CASE WHEN event.name = 'add_to_cart' THEN event_time ELSE NULL END) as last_add_to_cart_of_day,
        MIN(CASE WHEN event.name = 'view_item' THEN event_time ELSE NULL END) as first_view_item_of_day,
        MAX(CASE WHEN event.name = 'view_item' THEN event_time ELSE NULL END) as last_view_item_of_day,
        MIN(event_time) as first_interaction_of_day,
        MAX(event_time) as last_interaction_of_day
    FROM `tagtoo-tracking.event_prod.tagtoo_event`
    WHERE ec_id = {ec_id}
    AND permanent IS NOT NULL
    AND event_time BETWEEN TIMESTAMP('{start_time.strftime("%Y-%m-%d %H:%M:%S")}')
                        AND TIMESTAMP('{end_time.strftime("%Y-%m-%d %H:%M:%S")}')
    GROUP BY permanent
    """
    return query
