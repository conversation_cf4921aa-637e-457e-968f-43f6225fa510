import json
import logging
import numpy as np
import pandas as pd
from collections import defaultdict
from datetime import datetime, timedelta, timezone
from typing import Any, Dict, List, Optional, Tuple, Union

from google.api_core.exceptions import GoogleAPICallError
from google.cloud import storage
from google.cloud.exceptions import GoogleCloudError
from json_logic import jsonLogic

from src.core.storage import StorageManager
from src.utils import merge_results, split_into_chunks, vectorized_evaluate
from src.utils.utils import get_project_id, get_taiwan_date_str
from src.optimization.vectorized_rule_evaluator import VectorizedRuleEvaluator, precompute_derived_fields as vectorized_precompute_derived_fields
from src.utils import logging_setup
from src.utils import json_helpers

logger = logging_setup.configure_logging()

class RuleEvaluationError(Exception):
    """自訂規則評估例外"""
    pass

def fetch_audience_mapping_with_rules(ec_id: int, add_to_dxp: bool = True) -> dict:
    """從 GCS 獲取受眾規則"""
    logger.info(f"開始執行 fetch_audience_mapping_with_rules: ec_id={ec_id}, add_to_dxp={add_to_dxp}")
    try:
        logger.info("初始化 Storage Client")
        storage_client = storage.Client()
        bucket_name = 'tagtoo-ml-workflow'
        logger.info(f"獲取 bucket: {bucket_name}")
        bucket = storage_client.bucket(bucket_name)
        rules_path = 'LTA/user_stats_configs/audience_rules.json'
        logger.info(f"獲取 blob: {rules_path}")
        blob = bucket.blob(rules_path)

        # 檢查 blob 是否存在
        if not blob.exists():
            logger.error(f"規則檔案不存在: {rules_path}")
            return {}

        logger.info("開始下載規則檔案")
        content = blob.download_as_text()
        logger.info(f"規則檔案下載完成，大小: {len(content)} 字元")
        all_rules = json.loads(content)
        logger.info(f"JSON 解析完成，包含 {len(all_rules)} 個 EC")

        # 檢查 EC ID 是否存在於規則中
        if str(ec_id) not in all_rules:
            logger.warning(f"EC ID {ec_id} 不存在於規則檔案中")
            logger.info(f"可用的 EC ID: {list(all_rules.keys())}")
            return {}

        ec_rules = all_rules.get(str(ec_id), {}).get('rules', {})
        logger.info(f"獲取到 EC {ec_id} 的規則，共 {len(ec_rules)} 條")

        # 修正前綴邏輯
        if add_to_dxp:
            logger.info(f"使用 DXP 前綴: tm:c_9999_{ec_id}_c_")
            prefix = f"tm:c_9999_{ec_id}_c_"

            # 檢查是否有符合前綴的規則
            matching_rules = [rule_id for rule_id in ec_rules.keys() if rule_id.startswith(prefix)]
            if not matching_rules:
                logger.warning(f"沒有找到符合前綴 {prefix} 的規則")
                logger.info(f"規則 ID 範例: {list(ec_rules.keys())[:5]}{'...' if len(ec_rules) > 5 else ''}")

            result = {
                rule_id: rule_data
                for rule_id, rule_data in ec_rules.items()
                if rule_id.startswith(prefix)
            }
        else:
            # 移除 9999 前綴
            logger.info("不使用 DXP 前綴，移除 9999")

            # 檢查是否有符合條件的規則
            matching_rules = [rule_id for rule_id in ec_rules.keys() if rule_id.startswith(f"tm:c_9999_{ec_id}_c_")]
            if not matching_rules:
                logger.warning(f"沒有找到符合條件的規則 (tm:c_9999_{ec_id}_c_)")
                logger.info(f"規則 ID 範例: {list(ec_rules.keys())[:5]}{'...' if len(ec_rules) > 5 else ''}")

            result = {
                rule_id.replace(f'9999_', ''): rule_data
                for rule_id, rule_data in ec_rules.items()
                if rule_id.startswith(f"tm:c_9999_{ec_id}_c_")
            }

        logger.info(f"處理後的規則數量: {len(result)}")

        # 檢查規則格式
        if result:
            sample_rule_id = next(iter(result))
            sample_rule = result[sample_rule_id]
            logger.info(f"規則範例 {sample_rule_id}: {sample_rule.keys()}")

            # 檢查規則是否包含必要的欄位
            if 'rule' not in sample_rule:
                logger.warning(f"規則 {sample_rule_id} 缺少 'rule' 欄位")
            if 'data' not in sample_rule:
                logger.warning(f"規則 {sample_rule_id} 缺少 'data' 欄位")

        return result
    except Exception as e:
        logger.error(f"取得規則失敗: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return {}

def evaluate_rule(mapping: dict, stats: dict) -> bool:
    """強化版規則評估函式（統一用 jsonLogic）"""
    from datetime import datetime, timezone
    from json_logic import jsonLogic
    import pandas as pd
    # 時區轉換處理
    def ensure_utc(dt):
        if isinstance(dt, datetime):
            if dt.tzinfo is None:
                return dt.replace(tzinfo=timezone.utc)
            return dt.astimezone(timezone.utc)
        return dt

    # 類型安全處理
    processed = {k: ensure_utc(v) if isinstance(v, datetime) else v for k, v in stats.items()}
    rule_params = {k: int(float(v)) if isinstance(v, (int, float, str)) and not isinstance(v, bool) else v for k, v in mapping.get('data', {}).items()}
    data = {**processed, **rule_params}

    # 處理 None 值和數據類型轉換
    for k, v in data.items():
        if v is None:
            # 根據欄位名稱設定適當的預設值
            if k.endswith('_days') or k.endswith('_count'):
                data[k] = -1  # 天數和計數欄位預設為 -1
            elif k.endswith('_amount'):
                data[k] = 0.0  # 金額欄位預設為 0
            else:
                data[k] = None  # 其他欄位保持 None
        elif isinstance(v, float) and not pd.isna(v) and v.is_integer():
            data[k] = int(v)

    rule = mapping.get('rule', mapping)

    def resolve_rule_vars(rule, data):
        if isinstance(rule, dict) and len(rule) == 1:
            op, vals = next(iter(rule.items()))
            # 遇到邏輯運算，直接計算
            if op in {'>', '>=', '<', '<=', '==', '!='}:
                a, b = [resolve_rule_vars(v, data) for v in vals]
                # // MODIFIED: numpy int/float 轉 Python int/float，避免型態不一致
                import numpy as np
                if isinstance(a, (np.integer, np.floating)):
                    a = a.item()
                if isinstance(b, (np.integer, np.floating)):
                    b = b.item()

                # 處理 None 值比較
                if a is None or b is None:
                    if op in ['==', '!=']:
                        return (a == b) if op == '==' else (a != b)
                    else:
                        return False  # None 值無法進行大小比較，返回 False

                # 確保可比較的類型
                try:
                    if op == '>': return a > b
                    if op == '>=': return a >= b
                    if op == '<': return a < b
                    if op == '<=': return a <= b
                    if op == '==': return a == b
                    if op == '!=': return a != b
                except TypeError as e:
                    logger.warning(f"無法比較 {a} ({type(a)}) 和 {b} ({type(b)}): {str(e)}")
                    return False

            elif op == 'and':
                results = [resolve_rule_vars(v, data) for v in vals]
                logger.debug(f"[AND DEBUG] permanent={data.get('permanent')} results={results} rule={rule}")
                return all(results)
            elif op == 'or':
                return any(resolve_rule_vars(v, data) for v in vals)
            elif op == 'var':
                return data.get(vals)
            else:
                # fallback: 保留原本結構
                return {op: [resolve_rule_vars(v, data) for v in vals]}
        elif isinstance(rule, list):
            return [resolve_rule_vars(x, data) for x in rule]
        else:
            return rule

    try:
        # 處理特殊情況：non_existent 變數
        if isinstance(rule, dict) and len(rule) == 1 and 'var' in rule:
            var_name = rule['var']
            if isinstance(var_name, list):
                # 處理帶有預設值的變數
                return data.get(var_name[0], var_name[1])
            elif var_name not in data:
                return None

        # 處理比較運算（所有 rule 先 resolve，直接用 Python 運算符）
        result = resolve_rule_vars(rule, data)

        # 確保布林值的正確性
        if isinstance(result, bool):
            return result
        elif result is None:
            return False
        else:
            return bool(result)

    except Exception as e:
        logger.error(f"規則評估失敗: {str(e)}, 規則: {rule}, 資料: {data}")
        return False

def get_final_description(mapping: dict) -> str:
    """根據 mapping 中的 description 與 params 來動態生成最終描述文字"""
    params = mapping.get('data', {})
    template = mapping.get('description', '')
    try:
        return template.format(**params)
    except KeyError as e:
        logger.error(f"描述模板缺少參數: {e}")
        return template



def optimized_vectorized_evaluate(user_stats: pd.DataFrame, rules: dict) -> dict:
    """
    優化版的向量化評估函數，使用新的 VectorizedRuleEvaluator

    Args:
        user_stats: 用戶統計數據
        rules: 規則定義字典

    Returns:
        dict: 規則ID到用戶列表的映射
    """
    logger.info(f"使用優化版向量化評估器處理 {len(user_stats)} 筆記錄, {len(rules)} 個規則")

    try:
        # 使用向量化評估器
        evaluator = VectorizedRuleEvaluator()

        # 預處理數據
        current_time = pd.Timestamp.now(tz=timezone.utc)
        processed_data = vectorized_precompute_derived_fields(user_stats, current_time)

        # 執行評估
        import time
        start_time = time.time()
        results = evaluator.evaluate_rules(processed_data, rules)
        elapsed_time = time.time() - start_time

        # 記錄性能統計
        stats = evaluator.get_performance_stats()
        logger.info(f"✅ 優化版向量化評估成功完成，耗時: {elapsed_time:.3f}秒")
        logger.info(f"📊 平均每筆記錄處理時間: {stats.get('avg_time_per_record_ms', 0):.4f}ms")
        logger.info(f"⚡ 向量化操作次數: {stats.get('vectorized_operations', 0)}")

        # 記錄結果統計
        total_matched_users = sum(len(users) for users in results.values())
        logger.info(f"🎯 總匹配用戶數: {total_matched_users}, 規則覆蓋率: {len([r for r in results.values() if r])}/{len(results)}")

        return results

    except Exception as e:
        logger.error(f"優化版向量化評估失敗: {str(e)}")
        logger.error(f"異常類型: {type(e).__name__}")
        import traceback
        logger.error(f"詳細堆疊: {traceback.format_exc()}")

        # 記錄數據狀態以便調試
        logger.info(f"數據狀態 - 形狀: {user_stats.shape}, 欄位: {user_stats.columns.tolist()}")
        logger.info(f"規則數量: {len(rules)}")

        logger.warning("⚠️ 回退到原始 vectorized_evaluate 函數，性能將大幅下降")
        return vectorized_evaluate(user_stats, rules)

def calculate_audience_segments_dynamic(user_stats: pd.DataFrame, ec_id: int, add_to_dxp: bool = True) -> dict:
    from datetime import datetime, timezone
    current_time = datetime.now(timezone.utc)  # // MODIFIED: 補上 current_time，修正 NameError
    """優化版本的動態受眾分群計算"""
    logger.info(f"開始執行 calculate_audience_segments_dynamic: ec_id={ec_id}, add_to_dxp={add_to_dxp}, user_stats 資料筆數={len(user_stats) if user_stats is not None else 0}")

    # 檢查是否為特別關注的 EC ID
    special_ec_ids = [2980, 3819, 3820]
    if ec_id in special_ec_ids:
        logger.info(f"特別關注的 EC ID {ec_id}: 開始處理")

    try:
        # 檢查輸入資料
        if user_stats is None or len(user_stats) == 0:
            logger.warning("輸入的 user_stats 為空 DataFrame")
            return {}

        # 確保 ec_id 是整數
        if isinstance(ec_id, float):
            logger.info(f"傳入的 ec_id 是浮點型 ({ec_id})，轉換為整數: {int(ec_id)}")
            ec_id = int(ec_id)

        # 確保 DataFrame 中存在 ec_id 欄位
        if 'ec_id' not in user_stats.columns:
            logger.info(f"DataFrame 中不存在 ec_id 欄位，添加固定值 {ec_id}")
            user_stats['ec_id'] = ec_id
        elif user_stats['ec_id'].dtype != np.int64:
            # 確保 ec_id 欄位是整數型別
            logger.info(f"將 DataFrame 中的 ec_id 從 {user_stats['ec_id'].dtype} 轉換為 int64")
            try:
                user_stats['ec_id'] = user_stats['ec_id'].astype(np.int64)
            except Exception as e:
                logger.warning(f"直接轉換 ec_id 欄位類型時出錯: {str(e)}，使用替代方法")
                # 如果直接轉換失敗，使用另一種方法：先轉為浮點數再轉為整數
                user_stats['ec_id'] = user_stats['ec_id'].astype(float).astype(np.int64)

        # 處理 DataFrame 中的日期時間欄位，使其可以安全地轉換為 JSON
        user_stats = json_helpers.to_json_safe(user_stats)

        # 確保 ec_id 欄位在 json_safe 轉換後仍然存在
        if 'ec_id' not in user_stats.columns:
            logger.warning(f"經過 json_safe 轉換後，ec_id 欄位遺失，重新添加")
            user_stats['ec_id'] = ec_id

        # 檢查必要的欄位
        required_columns = ['permanent', 'last_interaction_time', 'purchase_count']
        missing_columns = [col for col in required_columns if col not in user_stats.columns]

        # 如果 last_interaction_time 不存在但 last_interaction 存在，則不視為缺失
        if 'last_interaction_time' in missing_columns and 'last_interaction' in user_stats.columns:
            missing_columns.remove('last_interaction_time')
            logger.info("使用 last_interaction 欄位代替 last_interaction_time")

        if missing_columns:
            logger.error(f"user_stats 缺少必要的欄位: {missing_columns}")
            logger.info(f"現有欄位: {user_stats.columns.tolist()}")

            # 嘗試自動修復缺失欄位
            for col in missing_columns:
                if col == 'permanent':
                    logger.error("permanent 欄位缺失，無法自動修復")
                    return {}
                elif col == 'purchase_count':
                    logger.error("purchase_count 欄位缺失，無法進行分群")
                    return {}  # 如果沒有購買次數資料，直接返回空結果
                elif col == 'last_interaction_time':
                    # 嘗試從相關欄位創建 last_interaction_time
                    if 'last_interaction_of_day' in user_stats.columns:
                        logger.warning("last_interaction_time 欄位缺失，從 last_interaction_of_day 創建")
                        user_stats['last_interaction_time'] = user_stats['last_interaction_of_day']
                    else:
                        logger.error("last_interaction_time 欄位缺失，無法自動修復")
                        return {}

            # 重新檢查是否還有缺失欄位
            missing_columns = [col for col in required_columns if col not in user_stats.columns]
            if missing_columns:
                logger.error(f"自動修復後仍然缺少必要的欄位: {missing_columns}")
                return {}

        # 獲取規則資訊
        try:
            rules = fetch_audience_mapping_with_rules(ec_id, add_to_dxp=add_to_dxp)
            logger.info(f"獲取到 {len(rules)} 條規則定義")
            rule_ids = list(rules.keys())
            logger.info(f"規則 ID 範例: {rule_ids[:3]}{'...' if len(rule_ids) > 3 else ''}")

            # 檢查規則是否為空
            if not rules:
                logger.warning(f"沒有找到任何規則，ec_id={ec_id}, add_to_dxp={add_to_dxp}")
                return {}

            # 檢查規則格式
            for rule_id, rule_data in list(rules.items())[:3]:  # 只檢查前3個規則
                logger.info(f"規則 {rule_id} 的格式: {rule_data.keys()}")
                if 'rule' not in rule_data:
                    logger.warning(f"規則 {rule_id} 缺少 'rule' 欄位")
                if 'data' not in rule_data:
                    logger.warning(f"規則 {rule_id} 缺少 'data' 欄位")

            # 計算 last_view_item_days
            logger.info("開始計算 last_view_item_days 和 last_add_to_cart_days 欄位")

            # 使用通用函數計算 last_view_item_days
            if 'last_view_item_time' in user_stats.columns:
                try:
                    user_stats = calculate_days_since_event(
                        user_stats,
                        event_time_column='last_view_item_time',
                        event_days_column='last_view_item_days',
                        current_time=current_time
                    )
                except Exception as e:
                    logger.error(f"計算 last_view_item_days 時發生錯誤: {str(e)}")
                    logger.exception("詳細錯誤堆疊")
                    # 如果計算失敗，設定預設值
                    user_stats['last_view_item_days'] = -1
            else:
                logger.warning("找不到 last_view_item_time 欄位，無法計算 last_view_item_days")
                user_stats['last_view_item_days'] = -1

            # 使用通用函數計算 last_add_to_cart_days
            if 'last_add_to_cart_time' in user_stats.columns:
                try:
                    user_stats = calculate_days_since_event(
                        user_stats,
                        event_time_column='last_add_to_cart_time',
                        event_days_column='last_add_to_cart_days',
                        current_time=current_time
                    )
                except Exception as e:
                    logger.error(f"計算 last_add_to_cart_days 時發生錯誤: {str(e)}")
                    logger.exception("詳細錯誤堆疊")
                    # 如果計算失敗，設定預設值
                    user_stats['last_add_to_cart_days'] = -1
            else:
                logger.warning("找不到 last_add_to_cart_time 欄位，無法計算 last_add_to_cart_days")
                user_stats['last_add_to_cart_days'] = -1

            # 計算 last_interaction_days 如果尚未存在
            if 'last_interaction_time' in user_stats.columns and 'last_interaction_days' not in user_stats.columns:
                try:
                    user_stats = calculate_days_since_event(
                        user_stats,
                        event_time_column='last_interaction_time',
                        event_days_column='last_interaction_days',
                        current_time=current_time
                    )
                except Exception as e:
                    logger.error(f"計算 last_interaction_days 時發生錯誤: {str(e)}")
                    logger.exception("詳細錯誤堆疊")
                    # 如果計算失敗，設定預設值
                    user_stats['last_interaction_days'] = -1
            else:
                if 'last_interaction_time' not in user_stats.columns:
                    logger.warning("找不到 last_interaction_time 欄位，無法計算 last_interaction_days")
                    user_stats['last_interaction_days'] = -1

            # 計算 last_purchase_days 如果尚未存在
            if 'last_purchase_time' in user_stats.columns and 'last_purchase_days' not in user_stats.columns:
                try:
                    user_stats = calculate_days_since_event(
                        user_stats,
                        event_time_column='last_purchase_time',
                        event_days_column='last_purchase_days',
                        current_time=current_time
                    )
                except Exception as e:
                    logger.error(f"計算 last_purchase_days 時發生錯誤: {str(e)}")
                    logger.exception("詳細錯誤堆疊")
                    # 如果計算失敗，設定預設值
                    user_stats['last_purchase_days'] = -1
        except Exception as e:
            logger.error(f"計算 last_view_item_days 或 last_add_to_cart_days 時發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")

        # 優化數據類型
        logger.info("開始優化數據類型")
        dtype_optimized = {
            'permanent': 'category',
            'purchase_count': 'uint16',  # 使用標準 uint16 類型
            'total_purchase_amount': 'float32',  # 使用 float32 減少記憶體使用
            'ec_id': 'category'
        }

        for col, dtype in dtype_optimized.items():
            if col in user_stats.columns:
                try:
                    # 先處理 NULL 值再轉換類型
                    if col == 'purchase_count':
                        # 確保 None/NaN 值被轉換為 0
                        user_stats[col] = user_stats[col].fillna(0).astype('int64')  # 使用 int64 避免轉換錯誤
                        logger.info(f"已將 purchase_count 的 NaN 值填充為 0，類型轉換為 int64")
                    elif col == 'total_purchase_amount':
                        user_stats[col] = user_stats[col].fillna(0.0).astype(dtype)
                    else:
                        user_stats[col] = user_stats[col].astype(dtype)
                except Exception as e:
                    logger.warning(f"將 {col} 轉換為 {dtype} 時發生錯誤: {str(e)}")
                    # 對於 purchase_count，即使類型轉換失敗，也要確保 NaN 值被處理
                    if col == 'purchase_count':
                        user_stats[col] = user_stats[col].fillna(0)
                        logger.info(f"類型轉換失敗，但已確保 purchase_count 的 NaN 值被填充為 0")
        logger.info("已完成數據類型優化")

        # // MODIFIED: 若規則有 purchase_count==0，先排除不合格 row
        def rule_has_purchase_count_eq_zero(rule):
            if isinstance(rule, dict):
                for k, v in rule.items():
                    if k == '==' and isinstance(v, list) and len(v) == 2:
                        if (isinstance(v[0], dict) and v[0].get('var') == 'purchase_count' and v[1] == 0):
                            return True
                    elif isinstance(v, list):
                        for item in v:
                            if rule_has_purchase_count_eq_zero(item):
                                return True
            return False
        # 直接使用向量化評估或平行處理
        result = {}

        # 分析資料大小來決定處理方式
        data_size = len(user_stats)
        logger.info(f"總資料筆數: {data_size}")

        try:
            if data_size == 0:
                logger.warning("無任何用戶資料，跳過所有分群")
                return {}
            elif data_size <= 50000:
                logger.info("使用向量化評估處理所有規則")
                result = optimized_vectorized_evaluate(user_stats, rules)
            else:
                from src.optimization.parallel_processing import parallel_process
                logger.info("使用平行處理評估大量資料")

                def process_chunk(chunk):
                    return optimized_vectorized_evaluate(chunk, rules)

                if data_size <= 100000:
                    max_workers = 2
                    chunk_size = 25000
                elif data_size <= 500000:
                    max_workers = 4
                    chunk_size = 50000
                else:
                    max_workers = 8
                    chunk_size = 100000

                logger.info(f"平行處理參數: max_workers={max_workers}, chunk_size={chunk_size}")
                chunk_results = parallel_process(user_stats, process_chunk, max_workers=max_workers, chunk_size=chunk_size)

                # parallel_process 已經通過 merge_results 合併了所有 chunk 的結果
                # chunk_results 應該直接是合併後的字典格式
                if isinstance(chunk_results, dict):
                    result = chunk_results
                    logger.info(f"平行處理結果格式正確，共有 {len(result)} 個規則")
                else:
                    logger.error(f"parallel_process 返回了意外的格式: {type(chunk_results)}")
                    logger.info(f"chunk_results 內容: {chunk_results}")
                    # 嘗試處理其他可能的格式
                    if isinstance(chunk_results, list):
                        # 如果是列表，嘗試合併
                        from src.utils import merge_results
                        cleaned_chunk_results = []
                        for i, chunk_result in enumerate(chunk_results):
                            if isinstance(chunk_result, dict):
                                cleaned_chunk_results.append(chunk_result)
                            elif chunk_result is None:
                                continue
                            else:
                                logger.warning(f"chunk_results[{i}] 格式錯誤: {type(chunk_result)}, 跳過此結果")
                        result = merge_results(cleaned_chunk_results)
                    else:
                        result = {}

        except Exception as e:
            logger.error(f"分群評估時發生錯誤: {str(e)}")
            logger.exception("詳細錯誤堆疊")
            # 如果向量化/平行處理失敗，回退到逐一處理
            logger.info("回退到逐一處理模式")
            result = {}
            for rule_id, rule_data in rules.items():
                logger.info(f"逐一處理規則 {rule_id}")
                try:
                    matched_users = []
                    for _, row in user_stats.iterrows():
                        if evaluate_rule(rule_data, row):
                            matched_users.append(row['permanent'])
                    result[rule_id] = matched_users
                    logger.info(f"規則 {rule_id} 符合用戶數: {len(matched_users)}")
                except Exception as rule_error:
                    logger.error(f"處理規則 {rule_id} 時發生錯誤: {str(rule_error)}")
                    result[rule_id] = []

        # 檢查結果是否為空
        if not result:
            logger.warning("評估結果為空，沒有用戶符合任何規則")
        else:
            # 記錄每個規則符合的用戶數量
            for rule_id, users in result.items():
                logger.info(f"規則 {rule_id} 符合的用戶數量: {len(users)}")

            # 對於特別關注的 EC ID，輸出更詳細的日誌
            if ec_id in special_ec_ids:
                # 記錄所有欄位的統計資訊，特別是關鍵的天數欄位
                key_columns = ['last_interaction_days', 'last_view_item_days', 'last_add_to_cart_days', 'last_purchase_days']
                logger.info(f"EC ID {ec_id} 關鍵欄位統計:")
                for col in key_columns:
                    if col in user_stats.columns:
                        valid_data = user_stats[col][user_stats[col] != -1]
                        if len(valid_data) > 0:
                            logger.info(f"  {col}: 平均={valid_data.mean():.2f}, 最小={valid_data.min()}, 最大={valid_data.max()}, 非-1值數量={len(valid_data)}")
                        else:
                            logger.warning(f"  {col}: 所有值都是 -1 或欄位為空")
                    else:
                        logger.warning(f"  {col}: 欄位不存在")

                # 記錄分群結果詳細情況
                segments_info = {}
                for rule_id, users in result.items():
                    segment_name = rule_id.split('_')[-1] if '_' in rule_id else rule_id
                    segments_info[segment_name] = len(users)

                logger.info(f"EC ID {ec_id} 分群結果: {segments_info}")

        return result

    except Exception as e:
        logger.error(f"calculate_audience_segments_dynamic 發生未處理的錯誤: {str(e)}")
        logger.exception("詳細錯誤堆疊")
        return {}

def calculate_days_since_event(user_stats, event_time_column, event_days_column, current_time):
    """計算事件發生至今的天數

    Args:
        user_stats (pd.DataFrame): 用戶統計資料
        event_time_column (str): 事件時間欄位名稱
        event_days_column (str): 要產生的天數欄位名稱
        current_time (pd.Timestamp): 當前時間

    Returns:
        pd.DataFrame: 更新後的 user_stats DataFrame
    """
    logger.info(f"開始計算 {event_days_column} 欄位 (來源欄位: {event_time_column}, 當前時間: {current_time})")

    # 檢查事件時間欄位是否存在
    if event_time_column not in user_stats.columns:
        logger.warning(f"欄位 '{event_time_column}' 不存在，無法計算 {event_days_column}")
        user_stats[event_days_column] = -1
        return user_stats

    # 設定預設值為 -1
    user_stats[event_days_column] = -1

    # 檢查值是否為 datetime 或字串
    if isinstance(user_stats[event_time_column].iloc[0] if not user_stats.empty else None, str):
        logger.info(f"{event_time_column} 欄位是字串格式，先轉換為 Timestamp")
        # 創建一個臨時的 Series 用於轉換
        temp_series = pd.Series([None if pd.isna(x) else pd.Timestamp(x) for x in user_stats[event_time_column]])

        # 檢查轉換後是否有 NaT 值
        nat_count = temp_series.isna().sum()
        logger.info(f"轉換後 {event_time_column} 欄位中的 NaT 值數量: {nat_count}")

        # 只處理有效的時間資料
        valid_mask = temp_series.notna()
        if valid_mask.any():
            logger.info(f"有效的 {event_time_column} 數量: {valid_mask.sum()}")
            try:
                # 使用索引而不是布爾掩碼
                valid_indices = user_stats.index[valid_mask].tolist()

                # 檢查是否有可能的索引超出範圍問題
                if valid_indices:
                    max_index = max(valid_indices)
                    if max_index >= len(user_stats):
                        valid_indices = [idx for idx in valid_indices if idx < len(user_stats)]

                if valid_indices:
                    # 使用批次處理避免索引問題
                    chunk_size = 1000
                    for i in range(0, len(valid_indices), chunk_size):
                        chunk_indices = valid_indices[i:i+chunk_size]
                        try:
                            # 獲取有效的時間
                            chunk_times = pd.to_datetime(user_stats.loc[chunk_indices, event_time_column], errors='coerce', utc=True)

                            # 計算天數差異
                            days = ((current_time - chunk_times).dt.total_seconds() / 86400).astype(int)

                            # 安全地逐一更新值
                            for j, idx in enumerate(chunk_indices):
                                if j < len(days) and not pd.isna(days.iloc[j]):
                                    user_stats.at[idx, event_days_column] = days.iloc[j]
                        except Exception as e:
                            logger.error(f"處理 {event_time_column} 索引批次 {i}-{i+chunk_size} 時發生錯誤: {str(e)}")
                            continue
            except Exception as e:
                logger.error(f"計算 {event_days_column} 時發生錯誤: {str(e)}")
                logger.exception("詳細錯誤堆疊")
    else:
        # 直接處理 datetime 格式
        valid_mask = user_stats[event_time_column].notna()
        if valid_mask.any():
            try:
                valid_indices = user_stats.index[valid_mask].tolist()

                if valid_indices:
                    chunk_size = 1000
                    for i in range(0, len(valid_indices), chunk_size):
                        chunk_indices = valid_indices[i:i+chunk_size]
                        try:
                            # 確保是 datetime 類型
                            chunk_times = user_stats.loc[chunk_indices, event_time_column]
                            if not pd.api.types.is_datetime64_dtype(chunk_times):
                                chunk_times = pd.to_datetime(chunk_times, errors='coerce', utc=True)

                            # 計算天數差異
                            days = ((current_time - chunk_times).dt.total_seconds() / 86400).astype(int)

                            # 安全地逐一更新值
                            for j, idx in enumerate(chunk_indices):
                                if j < len(days) and not pd.isna(days.iloc[j]):
                                    user_stats.at[idx, event_days_column] = days.iloc[j]
                        except Exception as e:
                            logger.error(f"處理 {event_time_column} 索引批次 {i}-{i+chunk_size} 時發生錯誤: {str(e)}")
                            continue
            except Exception as e:
                logger.error(f"計算 {event_days_column} 時發生錯誤: {str(e)}")
                logger.exception("詳細錯誤堆疊")

    # 檢查負值
    invalid_days = ((user_stats[event_days_column] < 0) & (user_stats[event_days_column] != -1)).sum()
    if invalid_days > 0:
        logger.warning(f"發現 {invalid_days} 筆不合理的 {event_days_column} 值（負數但不是 -1）")
        # 將不合理的值修正為 -1，使用安全的方法
        invalid_indices = user_stats.index[(user_stats[event_days_column] < 0) & (user_stats[event_days_column] != -1)]
        for idx in invalid_indices:
            user_stats.at[idx, event_days_column] = -1

    # 檢查計算結果
    valid_mask = user_stats[event_days_column] != -1
    if valid_mask.any():
        valid_days = user_stats.loc[valid_mask, event_days_column]
        if not valid_days.empty:
            logger.info(f"{event_days_column} 統計:\n平均: {valid_days.mean():.2f}\n最小: {valid_days.min()}\n最大: {valid_days.max()}\n中位數: {valid_days.median()}\n非-1值數量: {len(valid_days)}／總數量: {len(user_stats)}")

            # 檢查分布，每30天為一個區間
            days_distribution = {}
            for i in range(0, max(int(valid_days.max()) + 30, 360), 30):
                upper = i + 29
                count = ((valid_days >= i) & (valid_days <= upper)).sum()
                if count > 0:
                    days_distribution[f"{i}-{upper}天"] = count
            logger.info(f"{event_days_column} 區間分布: {days_distribution}")
    else:
        logger.warning(f"{event_days_column} 欄位沒有有效值，所有值都是 -1")

    logger.info(f"已完成計算 {event_days_column} 欄位")
    return user_stats
