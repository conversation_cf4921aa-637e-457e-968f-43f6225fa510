# Parquet Time Range Checker - 快速開始指南

這個簡短的指南將幫助您快速開始使用 Parquet Time Range Checker。

## 🚀 快速開始

### 1. 設定環境

確保您已經有 GCS 存取權限。選擇以下其中一種方法：

**方法 A: 使用服務帳戶金鑰**
```bash
export GOOGLE_APPLICATION_CREDENTIALS="path/to/your/service-account-key.json"
```

**方法 B: 使用 gcloud CLI**
```bash
gcloud auth application-default login
```

### 2. 在 Docker 環境中執行 (推薦)

```bash
# 建構環境
cd /Users/<USER>/tagtoo/ml-workflow-cloud-functions/lta-user-stats
make build-test

# 執行檢查 (記得替換 bucket 名稱)
docker run --rm \
  -v ~/.config/gcloud:/root/.config/gcloud \
  -e GOOGLE_APPLICATION_CREDENTIALS=/root/.config/gcloud/application_default_credentials.json \
  asia-east1-docker.pkg.dev/tagtoo-ml-workflow/gcf-artifacts/tagtoo--ml--workflow__asia--east1__lta--user--stats:main-3f7f0e9d-test \
  python scripts/parquet_time_range_checker.py \
  --bucket-name your-bucket-name \
  --max-files 10
```

### 3. 本地執行 (需要 Python 環境)

```bash
# 安裝依賴
pip install -r requirements.txt

# 執行檢查
python scripts/parquet_time_range_checker.py \
  --bucket-name your-bucket-name \
  --prefix data/parquet/ \
  --max-files 5
```

## 📋 常用命令範例

### 檢查特定路徑
```bash
python scripts/parquet_time_range_checker.py \
  --bucket-name my-data-bucket \
  --prefix "data/events/2024/"
```

### 指定時間戳欄位
```bash
python scripts/parquet_time_range_checker.py \
  --bucket-name my-data-bucket \
  --timestamp-columns event_time created_at processing_time
```

### 儲存報告到檔案
```bash
python scripts/parquet_time_range_checker.py \
  --bucket-name my-data-bucket \
  --output analysis_report.txt
```

### 除錯模式
```bash
python scripts/parquet_time_range_checker.py \
  --bucket-name my-data-bucket \
  --log-level DEBUG
```

## 🔧 故障排除

### 認證問題
```
DefaultCredentialsError: Your default credentials were not found
```
**解決方案**: 確保已設定 `GOOGLE_APPLICATION_CREDENTIALS` 或執行 `gcloud auth application-default login`

### 權限不足
```
google.api_core.exceptions.Forbidden: 403
```
**解決方案**: 確保服務帳戶有 `Storage Object Viewer` 權限

### 找不到檔案
```
在 bucket 中未找到 parquet 檔案
```
**解決方案**: 檢查 bucket 名稱和路徑前綴是否正確

## 📊 輸出報告範例

執行成功後，您會看到類似這樣的報告：

```
================================================================================
PARQUET 檔案時間範圍分析報告
================================================================================
Bucket: my-data-bucket
Prefix: data/events/
分析時間: 2024-01-15 14:30:25

整體統計:
  檔案數量: 48
  總資料行數: 567,890
  總檔案大小: 1,234.56 MB

時間範圍:
  開始時間: 2024-01-01 00:00:00 UTC+0 (2024-01-01 08:00:00 CST+8)
  結束時間: 2024-01-14 23:59:59 UTC+0 (2024-01-15 07:59:59 CST+8)
  總天數: 14 天

發現 1 個時間空隙:
  空隙 1:
    開始: 2024-01-05 18:00:00 UTC+0 (2024-01-06 02:00:00 CST+8)
    結束: 2024-01-06 06:00:00 UTC+0 (2024-01-06 14:00:00 CST+8)
    持續時間: 0.50 天 (12.00 小時)

按日期分組的檔案:
  20240101: 4 個檔案
  20240102: 3 個檔案
  20240103: 5 個檔案
  ...
================================================================================
```

## 🎯 下一步

- 查看詳細文檔: [docs/parquet_time_range_checker.md](docs/parquet_time_range_checker.md)
- 運行測試: `make test`
- 查看更多範例: [examples/parquet_checker_examples.py](examples/parquet_checker_examples.py)

## 💡 提示

- 首次執行時建議使用 `--max-files 10` 來測試
- 對於大型 bucket，可以使用 `--prefix` 縮小範圍
- 使用 `--log-level DEBUG` 來查看詳細的處理過程
