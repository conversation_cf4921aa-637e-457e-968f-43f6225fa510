steps:
  # 1. 建置用於部署的生產 Docker image (不含測試依賴)
  - name: 'gcr.io/cloud-builders/docker'
    id: 'build-prod-image'
    args:
      - 'build'
      - '--platform=linux/amd64'
      - '--build-arg'
      - 'INSTALL_TEST_DEPS=false' # 確保不安裝測試依賴
      - '-t'
      - 'asia-east1-docker.pkg.dev/tagtoo-ml-workflow/gcf-artifacts/tagtoo--ml--workflow__asia--east1__lta--user--stats:${COMMIT_SHA}'
      - '.'

  # 2. 推送生產 image 到 Artifact Registry
  - name: 'gcr.io/cloud-builders/docker'
    id: 'push-prod-image'
    args:
      - 'push'
      - 'asia-east1-docker.pkg.dev/tagtoo-ml-workflow/gcf-artifacts/tagtoo--ml--workflow__asia--east1__lta--user--stats:${COMMIT_SHA}'

  # 3. 使用 gcloud run deploy 部署 Cloud Function (基於容器)
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk'
    id: 'deploy-function'
    args:
      - 'gcloud'
      - 'run'
      - 'deploy'
      - 'lta-user-stats'
      - '--image=asia-east1-docker.pkg.dev/tagtoo-ml-workflow/gcf-artifacts/tagtoo--ml--workflow__asia--east1__lta--user--stats:${COMMIT_SHA}'
      - '--region=asia-east1'
      - '--memory=16Gi'
      - '--timeout=3600'
      - '--set-env-vars=GOOGLE_APPLICATION_CREDENTIALS=tagtoo-ml-workflow-kubeflow.json'
      - '--allow-unauthenticated'
      - '--cpu=4'
      - '--min-instances=0'
      - '--max-instances=10'
      - '--port=8080'

options:
  substitutionOption: 'ALLOW_LOOSE'
