# ml-workflow-cloud-functions

Source code & CI for [tagtoo-ml-workflow Cloud Functions](https://console.cloud.google.com/functions/list?project=tagtoo-ml-workflow)

新增、修改、刪除這邊的 cloud function source code 都會透過 GitHub Actions 部署到 GCP 上。

## 🚀 快速開始 (推薦使用模板)

### 使用標準模板建立新的 Cloud Function

```bash
# 使用互動式模板管理工具
./.templates/manage_templates.sh

# 或直接使用命令列
./.templates/create_function.sh my-new-function

# 包含 Terraform 配置
./.templates/create_function.sh my-new-function --terraform
```

### 模板功能特色

- ✅ **標準化結構**: 統一的專案布局和最佳實踐
- ✅ **完整測試**: 包含單元測試和代碼品質檢查
- ✅ **自動化構建**: 預配置的 Makefile 和 Cloud Build
- ✅ **基礎設施管理**: 可選的 Terraform 配置
- ✅ **開發工具**: 預設的 Git hooks 和代碼格式化
- ✅ **詳細文檔**: 完整的使用指南和範例

### 模板系統包含

| 組件 | 說明 | 檔案 |
|------|------|------|
| **Cloud Function** | Python 3.11 基礎模板 | `main.py`, `requirements.txt`, `test_main.py` |
| **構建配置** | Cloud Build 和 Makefile | `cloudbuild.yaml`, `Makefile` |
| **基礎設施** | Terraform 配置 (可選) | `terraform/main.tf` |
| **開發工具** | Git 配置和代碼格式化 | `.gitignore`, 代碼檢查 |
| **文檔** | 完整的使用指南 | `README.md`, 使用說明 |

更多詳細資訊請參考 [模板使用指南](.templates/docs/usage-guide.md)。

## 🔧 開發工具

### Git Hooks 管理

本專案提供完整的 Git hooks 管理工具：

```bash
# 智能檢測和安裝
./manage_git_hooks.sh

# 快速安裝
./manage_git_hooks.sh --quick

# 互動式菜單
./manage_git_hooks.sh --menu

# 批量更新所有子專案（管理員使用）
./.git-template/update_git_hooks.sh
```

### Git Hooks 功能

| Hook | 功能 | 說明 |
|------|------|------|
| **pre-commit** | 語法檢查、格式檢查 | 防止提交有問題的代碼 |
| **pre-push** | 全面品質檢查 | 推送前的最後檢查 |
| **commit-msg** | 提交信息格式 | 強制使用規範的提交格式 |

詳細說明請參考 [Git Hooks 指南](.docs/git-hooks-guide.md)。

## 使用說明

### CI/CD 測試機制
GitHub Actions 會在部署前自動執行測試，但**只有同時滿足以下兩個條件的專案才會執行測試**：

1. **存在 `tests/` 目錄**: 專案根目錄下必須有 `tests/` 資料夾
2. **Makefile 包含 test rule**: `Makefile` 中必須包含 `test` 目標

不滿足條件的專案會跳過測試並顯示：
```
Skipping tests for your-function - requires both Makefile with test rule and tests/ directory
```

### 開發新 Cloud Function
- 一個 cloud function 就開一個資料夾放著。（命名規則請使用英文小寫字母和`-`作為分隔符，注意事項裡面有說明原因。）
- 基本的程式碼設定好後就把 `cloudbuild.template.yaml` 複製到新的資料夾底下
  - 將其更名為 `cloudbuild.yaml`
  - 修改裡面的 `{YOUR-CLOUD-FUNCTION-NAME}` （要和資料夾的名稱相同）
  - 修改裡面的相關機器規格設定
  - 新增的資料夾沒有 `cloudbuild.yaml` 的話，CI 會出錯，也就不會部署到 GCP
  - `git add` 新的資料夾並 push 到 GitHub，GitHub Actions 就會開始自動部署該 function 到 GCP 上
  - 記得檢查 GitHub Actions 部署的 Job 有沒有成功執行完畢: <https://github.com/Tagtoo/ml-workflow-cloud-functions/actions>
- 本地開發 Cloud Function 請參考 GCP 的官方文件：
  - <https://cloud.google.com/functions/docs/running/overview>
  - <https://cloud.google.com/functions/docs/running/function-frameworks>


## 注意事項

- 命名最好是都採用英文小寫跟 `-` 來命名（之前已經命名的就先照搬過來。新的 function 按照這樣的規則命名在 Google Cloud Platform 比較不會發生奇怪的問題。）
  - 以下是一些詳細原因：
    - cloud function gen2 如果 name 裡頭有 `_` 的話，其 cloud run service 的名稱中的 `_` 會被 GCP 全換成 `-`。
      - 例如：不能同時有 `hello_world` 和 `hello-world` 這兩個 gen2 function，因為 cloud run service 的名稱會衝突。
      - 所以建議資料夾名稱和 function 名稱都用 `-` 來分隔就不會有 `_` 被轉成 `-` 的問題。
    - cloud function gen2 使用產生的 container image 被儲存在 Artifact Registry 時，因為 docker registry 對命名規則有限制不能使用英文大寫，所以 GCP 會把名字做轉換。
      - 目前已知的規則如下：
        - 開頭第一個英文字母：如果是大寫 `A` 的話，會被轉成 `a-a`。其他依此類推。
        - 非第一個英文字母：如果是大寫 `A` 的話，會被轉成 `_a`。其他依此類推。
        - `-` 單引號：會被轉成兩個單引號 `--`。
        - `_` 底線：會被轉成兩個底線 `__`。
      - 例如：`Hello_world` 這個 function 的 container image 被儲存到 artifact registry 的時候，會被存到 `h-hello__world` 這個 repository。
      - 目前有遇到的奇怪問題是 `EC_DCIU_model_predict_715` 這個 function 的 container image 被存到 `e-e_c___d_c_i_u__model__predict__715` 這個 repository，直接複製 cloud console 的 docker pull 指令會無法成功 pull image。
        - Docker 回傳 `invalid reference format` 這個錯誤，不論使用什麼樣的 image tag 都無效，就只有這個 function 遇到這個問題。
        - 猜測可能是因為 `c` 和 `d` 中間有 3 個 `_`，導致 docker 會將其判斷為 invalid reference format。
- 資料夾名稱一定要跟 `cloudbuild.yaml` 裡頭的 function name 相同，否則刪除資料夾的時候 CI 會無法對應到該刪除的 cloud function。
- 因為 CI 成功執行完畢會記錄 `last_deployed_commit` 到 Google Cloud Storage：
  - 記錄在[tagtoo-ml-workflow-cloud-functions 這個 bucket](https://console.cloud.google.com/storage/browser/tagtoo-ml-workflow-cloud-functions?project=tagtoo-ml-workflow)
  - main branch 禁止 force push，否則 CI 那邊可能會找不到對應的 `last_deployed_commit`
  - CI 沒有成功部署的 commit 會在下次新的 commit 被 push 的時候再次被嘗試部署。
- `requirements.txt` 裡頭的 version 最好寫清楚固定的版本，確保重複部署的一致性。
  - 沒寫清楚的話會遇到很久沒有重新部署的 function 因為使用到了新版的套件而無法成功執行的問題。
  - 不然要看裡面的套件版本會有點麻煩，需要把 container image pull 下來並使用 `dive` [這個工具](https://github.com/wagoodman/dive)來查看。

# 文檔索引

本專案包含以下技術文檔，詳細說明各功能模組和開發流程：

## 📋 開發工具文檔

| 文檔 | 說明 |
|------|------|
| [Git Hooks 指南](.docs/git-hooks-guide.md) | 詳細的 Git hooks 配置、使用和故障排除指南 |
| [CI/CD 文檔](.docs/ci-cd/) | GitHub Actions 測試機制、認證設置和最佳實踐 |

## 📊 LTA 功能文檔

| 文檔 | 說明 |
|------|------|
| [LTA 功能關係說明](.docs/lta_functions_relationship.md) | LTA 相關 Cloud Functions 基礎關係和資料流向 |
| [LTA 功能詳細關係](.docs/lta_functions_detailed_relationship.md) | LTA Cloud Functions 詳細資料流程和依賴關係圖 |
| [LTA 模型改進計畫](.docs/lta_model_improvement_plan.md) | LTA 模型現況分析、問題點和改進計畫 |

## 🔗 相關資源

- [模板使用指南](.templates/docs/usage-guide.md) - 完整的標準模板使用說明
- [GitHub Actions 部署紀錄](https://github.com/Tagtoo/ml-workflow-cloud-functions/actions) - CI/CD 部署狀態檢查
- [GCP Cloud Functions 控制台](https://console.cloud.google.com/functions/list?project=tagtoo-ml-workflow) - 線上函數管理
- [Cloud Storage 部署紀錄](https://console.cloud.google.com/storage/browser/tagtoo-ml-workflow-cloud-functions?project=tagtoo-ml-workflow) - 部署版本追蹤
