steps:
  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    args:
      - "gcloud"
      - "functions"
      - "deploy"
      - "picture-to-description-aroom"  # Replace it. Must be same with the directory name.
      - "--source=."

      - "--entry-point=main"
      - "--region=asia-east1"
      - "--trigger-http"
      - "--runtime=python310"
      - "--memory=8GiB"
      - "--cpu=2"
      - "--timeout=3600s"  # default 60s
      - "--min-instances=0"   # default 0
      - "--max-instances=100" # default 100. 0 means no limit.

      - "--gen2"  # Comment out this line and --concurrency for deploying gen1
      - "--concurrency=10"  # Only applicable when the --gen2 flag is provided.

      - "--allow-unauthenticated"  # Will only apply to the first deployment. Comment out this line for non-public access.

        # `gcloud functions deploy --help`
        # or <https://cloud.google.com/sdk/gcloud/reference/builds/submit>
        # for more info
options:
    substitutionOption: 'ALLOW_LOOSE'
