import functions_framework
from datetime import datetime
from pytz import timezone
from google.oauth2 import service_account
from utils import *
from google.cloud import bigquery
import google_storage

# Constants
TIMEZONE = timezone('Asia/Taipei')
DATE_FORMAT = "%Y-%m-%d"
current_date = datetime.now(TIMEZONE).astimezone(TIMEZONE).strftime(DATE_FORMAT)
key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
credentials = service_account.Credentials.from_service_account_file(key_path)
project_id = credentials.project_id
output_path = f'gs://tagtoo-ml-workflow/tagtoo-91app/id-mapping/{datetime.now(TIMEZONE).strftime(DATE_FORMAT)}'
query_path = f'{output_path}/query/*.parquet'
bucket_name = 'tagtoo-ml-workflow'
blob_name = '/'.join(query_path.split('/')[3:-1]) + '/************.parquet'
GSIO = google_storage.GoogleStorageIO(credentials=credentials)

@functions_framework.http
def main(request):
    if object_exists(bucket_name, blob_name, credentials):
        print(f"{query_path} exist.")
    else:
        sql = make_id_mapping_query(query_path, current_date)
        client = bigquery.Client(credentials=credentials)
        client.query(sql).result()
        print("Query results loaded to {}".format(query_path))

    df = load_data(query_path)
    df = process_data(df)
    result = df.drop('fpid', axis=1).to_dict('records')
    print(f"當天共有 {len(result)} 個使用者要發送")

    # * 切分檔案
    batch_result = process_and_save_data(result)
    # * 驗證切分的資料有符合規定的大小
    validate_data(batch_result)
    # * 上傳到 GCS 並將權限設定成公開
    gcs_filepaths = upload_batches(batch_result, current_date, GSIO)
    # * 把資料網址打給 91App
    notify_partner(gcs_filepaths)

    return 'Success', 200
