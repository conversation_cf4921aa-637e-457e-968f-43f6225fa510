import pandas as pd
import dask.dataframe as dd
from google.oauth2 import service_account
from google.cloud import storage
from datetime import datetime
from pytz import timezone
import random
import time
import json
import gzip
import sys
import os
import requests

def load_data(path: str, file_format: str = 'PARQUET', **kwargs) -> pd.DataFrame:
    if file_format == 'PARQUET':
        return dd.read_parquet(path, assume_missing=True, **kwargs).compute()
    elif file_format == 'CSV':
        return dd.read_csv(path, assume_missing=True, **kwargs).compute()
    else:
        raise ValueError("Invalid file format")

def object_exists(bucket_name: str, blob_name: str, credentials: service_account.Credentials) -> bool:
    storage_client = storage.Client(credentials=credentials)
    bucket = storage_client.bucket(bucket_name)
    return bucket.blob(blob_name).exists()

def make_id_mapping_query(query_path: str, current_date: str):
    return f"""
    EXPORT DATA
    OPTIONS ( uri = '{query_path}', format = 'PARQUET', OVERWRITE = TRUE) AS (
        WITH AnnsUsers AS (
            SELECT DISTINCT permanent
            FROM `tagtoo-tracking.event_prod.tagtoo_event`
            WHERE DATE(event_time, 'Asia/Taipei') >= DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 30 DAY)
            AND DATE(event_time, 'Asia/Taipei') < CURRENT_DATE('Asia/Taipei')
            AND ec_id = 285
        ), IdMapping AS (
            SELECT DISTINCT partner_user_id, tagtoo_user_id
            FROM `gothic-province-823.tagtooad.partner_uid_mapping`
            WHERE partner_id = '1010'
        )
        SELECT DISTINCT permanent, partner_user_id, user.em, user.ph, segment_id
        FROM `tagtoo-tracking.event_prod.tagtoo_event` TE
        JOIN `tagtoo-ml-workflow.tagtoo_export_results.special_lta_{current_date.replace('-', '')}`
        USING (permanent)
        JOIN AnnsUsers USING (permanent)
        JOIN IdMapping ON IdMapping.tagtoo_user_id = TE.permanent
        WHERE DATE(event_time, 'Asia/Taipei') >= DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 30 DAY)
        AND DATE(event_time, 'Asia/Taipei') < CURRENT_DATE('Asia/Taipei')
    )
    """

def random_sample_from_list(input_list):
    if len(input_list) > 50:
        num_to_keep = random.randint(50, min(len(input_list), 60))
        return random.sample(input_list, num_to_keep)
    return input_list

def make_current_timestamp():
    dt = datetime.now(timezone('Asia/Taipei')).replace(microsecond=0)
    return int(time.mktime(dt.timetuple()))

def process_and_save_data(data):
    max_lines = 1000000
    max_size = 150 * 1024 * 1024  # 150MB in bytes
    current_size = 0
    batch_data = []
    batch_result = []

    for item in data:
        json_string = json.dumps(item)
        line_size = len(json_string.encode('utf-8')) + 1  # +1 for newline
        if len(batch_data) >= max_lines or current_size + line_size > max_size:
            batch_result.append(batch_data)
            batch_data = []
            current_size = 0
        batch_data.append(item)
        current_size += line_size

    if batch_data:
        batch_result.append(batch_data)

    return batch_result

def save_batch_to_gz(batch_data, file_index, file_name):
    with gzip.open(file_name, 'wt', encoding='utf-8') as gz_file:
        for item in batch_data:
            gz_file.write(json.dumps(item) + '\n')

def get_total_size(obj, seen=None):
    """Calculate the total memory size of an object and its internal objects"""
    size = sys.getsizeof(obj)
    if seen is None:
        seen = set()
    obj_id = id(obj)
    if obj_id in seen:
        return 0
    seen.add(obj_id)
    if isinstance(obj, dict):
        size += sum(get_total_size(v, seen) for v in obj.values())
        size += sum(get_total_size(k, seen) for k in obj.keys())
    elif hasattr(obj, '__dict__'):
        size += get_total_size(obj.__dict__, seen)
    elif hasattr(obj, '__iter__') and not isinstance(obj, (str, bytes, bytearray)):
        size += sum(get_total_size(i, seen) for i in obj)
    return size / (1024 * 1024)  # Convert to MB

def process_data(df):
    df['segment_id'] = df['segment_id'].str.split(',').apply(random_sample_from_list).apply(lambda x: ','.join(x))
    df['timestamp'] = make_current_timestamp()
    df.rename(columns={'permanent': 'fpid', 'partner_user_id': 'ny_fid', 'segment_id': 'tags', 'em': 'hash_em', 'ph': 'hash_ph'}, inplace=True)
    df['tags'] = df['tags'].str.split(',').apply(lambda x: list(filter(lambda y: 'tm:c' not in y, x)))
    return df[['timestamp', 'hash_em', 'hash_ph', 'fpid', 'ny_fid', 'tags']]

def validate_data(batch_result):
    for data in batch_result:
        if get_total_size(data) > 150:
            raise ValueError("Batch data is exceed max size.")

def upload_batches(batch_result, current_date, GSIO):
    local_prefix = '/tmp/'
    gcs_filepaths = []

    for idx, batch_data in enumerate(batch_result):
        filename = f'lta_data_test_{idx}.jsonl.gz'
        filepath = os.path.join(local_prefix, filename)
        gcs_filepath = f'gs://tagtoo-ml-workflow/tagtoo-91app/result/{current_date}/{filename}'
        https_gcs_filepath = 'https://storage.googleapis.com/' + '/'.join(gcs_filepath.split('/')[2:])
        
        save_batch_to_gz(batch_data, idx, filepath)
        print(f'Batch {idx} saved.')

        GSIO.upload_file_and_make_public(localpath=filepath, gsuri=gcs_filepath)
        gcs_filepaths.append(https_gcs_filepath)
        print(f'Batch {idx} upload to GCS.')

    return gcs_filepaths

def notify_partner(gcs_filepaths):
    total_file_count = len(gcs_filepaths)
    token = os.environ['TOKEN']
    data_partner_id = os.environ['DATA_PARTNER_ID']
    tag_type = os.environ['TAG_TYPE']

    for idx, gcs_filepath in enumerate(gcs_filepaths):
        url = "https://data-ingestion-webhook.91app.com/data-ingestion/v1"
        params = {
            'pid': data_partner_id,
            'data_type': tag_type,
            'file_link': gcs_filepath,
            'data_source_time': int(time.time()),
            'current_file': idx+1,
            'total_file': total_file_count
        }
        headers = {'Authorization': f'Bearer {token}'}
        response = requests.get(url, params=params, headers=headers)

        if response.status_code != 200:
            print(f"{file_link} error.")
            raise f"{reponse.text}"
        else:
            print(response.text)