import functions_framework
import pandas as pd
import dask.dataframe as dd
from datetime import datetime
from datetime import timedelta
from pytz import timezone
from typing import List, Dict, Tuple, Set
from ctypes import ArgumentError
from google.oauth2 import service_account
import google_storage
from google.cloud import bigquery
from google.cloud import storage

def load_data(path: str, file_format: str = 'PARQUET', **kwargs) -> pd.core.frame.DataFrame:
  if file_format == 'PARQUET':
    df = dd.read_parquet(path, assume_missing=True, **kwargs).compute()
  elif file_format == 'CSV':
    df = dd.read_csv(path, assume_missing=True, **kwargs).compute()
  else:
    raise ArgumentError("Invalid file format")
  return df

def get_vip(df: pd.DataFrame) -> pd.core.frame.DataFrame:
  VIP_THRESHOLD = df.value.quantile(0.8)
  return df.query("value>=@VIP_THRESHOLD").group_id.drop_duplicates().tolist()

def get_frequency_purchaser(df: pd.DataFrame) -> List[str]:
  df_tmp = df.group_id.value_counts().to_frame()
  THRESHOLD = df_tmp.quantile(0.8).values[0]
  return df_tmp.query("group_id>@THRESHOLD").index.tolist()

def tag_segment_id_on_users(
    industry_users: Dict[int, Dict[str, str]],
    industry_segment_ids: Dict[int, Dict[str, str]],
    idx: int,
    segment_name: str # 'VIP', 'FP'
) -> pd.core.frame.DataFrame:
  df = pd.DataFrame(industry_users[idx][segment_name], columns=['group_id'])
  df['segment_id'] = industry_segment_ids[idx][segment_name]
  return df

def object_exists(bucket_name: str, blob_name: str, credentials: service_account.Credentials) -> bool:
  """Check if an object exists in the GCS bucket.

  Args:
  - bucket_name: Name of the GCS bucket.
  - blob_name: Name (path) of the object within the bucket.

  Returns:
  - True if the object exists, otherwise False.
  """
  storage_client = storage.Client(credentials=credentials)
  bucket = storage_client.bucket(bucket_name)
  blob = bucket.blob(blob_name)
  return blob.exists()

@functions_framework.http
def main(request):
  
  # * external argument
  current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
  # uid = str(uuid.uuid4())
  output_path = f'gs://tagtoo-ml-workflow/topic10/industry_segment/{current_date}' # output gcs path
  query_path = output_path + '/query/*.parquet'
  
  # Credentials
  key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
  credentials = service_account.Credentials.from_service_account_file(key_path)
  project_id = credentials.project_id
  GSIO = google_storage.GoogleStorageIO(credentials=credentials)

  # * Query if not exist
  bucket_name = 'tagtoo-ml-workflow'
  blob_name = '/'.join(query_path.split('/')[3:-1]) + '/************.parquet'
  if object_exists(bucket_name, blob_name, credentials):
    print(f"{query_path} exist.")
  else:
    QUERY = f"""
    EXPORT DATA
    OPTIONS (
        uri = '{query_path}',
        format = 'PARQUET',
        overwrite = true)
    AS (
      SELECT
        group_id,
        permanent,
        industry_id,
        DATE(event_time) date,
        event.name,
        SUM(event.value) value,
      FROM
        `tagtoo-tracking.event_prod.tagtoo_event`,
        UNNEST(event.items) items
      JOIN
        `gothic-province-823.tagtoo_from_cloudsql.ECID_to_IndustryID`
      USING
        (ec_id)
      JOIN
        `tagtoo-tracking.event_prod.user_unify_group_permanent`
      USING
        (permanent)
      WHERE
        DATE(event_time) >= DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 90 DAY)
        AND DATE(event_time) < CURRENT_DATE("Asia/Taipei")
        AND event.currency IN ('TWD',
          'NTD')
        AND location.country_code = 'TW'
        AND event.name IN ('view_item',
          'purchase')
        AND items.price > 1
      GROUP BY
        1,
        2,
        3,
        4,
        5
    );
    """
    client = bigquery.Client(credentials=credentials)
    query_job = client.query(QUERY)  # API request
    rows = query_job.result()  # Waits for query to finish
    print("Query job complete.")

  # * Load data
  df = load_data(query_path, file_format='PARQUET')
  df.reset_index(drop=True, inplace=True)

  group_id_permanent_mapping = df[['group_id', 'permanent']].drop_duplicates()
  df = df.drop(['permanent'], axis=1).drop_duplicates(subset=['group_id', 'industry_id', 'date', 'name', 'value'])

  industry_ids = [
    4, # 服飾配件
    8, # 養生美容
    23, # 綜合電商
    26, # 書報雜誌
  ]

  industry_users = {}
  for idx in industry_ids:
    df_i = df.query("industry_id==@idx")
    df_i = df_i.query("value<@df_i.value.quantile(0.99)")
    df_ip = df_i.query("name=='purchase'")
    industry_users[idx] = {
        'VIP': get_vip(df_ip),
        'FP': get_frequency_purchaser(df_ip)
    }

  industry_segment_ids = {
      4: {
          'VIP': 'tm:d1786',
          'FP': 'tm:d1790'
      },
      8: {
          'VIP': 'tm:d1787',
          'FP': 'tm:d1791'
      },
      23: {
          'VIP': 'tm:d1788',
          'FP': 'tm:d1792'
      },
      26: {
          'VIP': 'tm:d1789',
          'FP': 'tm:d1793'
      }
  }

  segment_names = ['VIP', 'FP']

  results = [
      tag_segment_id_on_users(industry_users, industry_segment_ids, idx, name)
      for idx in industry_ids
      for name in segment_names
  ]

  result = pd.concat(results, axis=0, ignore_index=True)
  result = result.merge(group_id_permanent_mapping, on='group_id', how='inner')
  result = result.drop(['group_id'], axis=1).drop_duplicates(subset=['permanent', 'segment_id'])
  result = result[['permanent', 'segment_id']]

  print("Upload to GCS.")
  path = "/tmp/result.csv"
  gcs_path = output_path + '/result/result.csv'
  result.to_csv(path, index=False)
  GSIO.upload_file(gsuri=gcs_path, localpath=path)
  print("Result GCS path:", gcs_path)

  print("Upload dataframe to BigQuery.")
  DATE = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y%m%d")
  project_id = "tagtoo-ml-workflow"
  table_id = f'tagtoo_export_results.special_lta_temp_for_update_{DATE}'
  schema_data = [
    {"name": "permanent", "type": "STRING"},
    {"name": "segment_id", "type": "STRING"}
  ]
  result.to_gbq(table_id, project_id=project_id, credentials=credentials, table_schema=schema_data, if_exists='append')

  return 'Success', 200
