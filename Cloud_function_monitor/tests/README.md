# Cloud Function Monitor 測試

本目錄包含 Cloud Function Monitor 的單元測試。

## 測試內容

目前測試包含：

- `test_utils.py`: 測試 `utils.py` 中的 `make_mail_body` 函式，確保其能正確地為失敗的 Cloud Functions 產生特定的 Log 連結。

## 執行測試

有兩種方法可以執行測試：

### 方法一：使用腳本

```bash
./tests/run_tests.sh
```

### 方法二：直接使用 Python 的 unittest

```bash
cd Cloud_function_monitor
python -m unittest discover tests
```

## 新增測試

當新增或修改功能時，請同時更新或新增相應的測試案例，確保所有重要功能都有測試覆蓋。