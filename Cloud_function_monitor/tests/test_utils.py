import unittest
import sys
import os
from unittest.mock import patch, MagicMock

# 將 Cloud_function_monitor, tests 資料夾加入 Python 模組搜尋路徑
module_root = os.path.dirname(os.path.dirname(os.path.abspath(__file__)))
sys.path.append(module_root)

from utils import make_mail_body, extract_main_error, parse_error_string, format_slack_error_message
# 注意：以下 import 實際運行時可能會失敗，取決於 main.py 的實際實現方式
# 這裡僅作為示例，實際可能需要調整

class TestUtils(unittest.TestCase):
    def setUp(self):
        """設定測試環境"""
        self.project_id = "test-project-id"
        self.region = "asia-east1"  # 與 make_mail_body 中硬編碼的地區相同

    # 移除 test_make_mail_body_no_errors 測試案例，因為根據實際邏輯，
    # 無錯誤時根本不會呼叫 make_mail_body 函數

    def test_make_mail_body_one_error(self):
        """測試一個錯誤的信件內容"""
        monitor_results = [
            {
                'function_name': 'test-function',
                'trigger_count': 5,
                'success_count': 3,
                'errors': ['Error message 1', 'Error message 2']
            }
        ]

        body = make_mail_body(monitor_results, self.project_id)

        # 檢查信件標頭和結尾是否正確
        self.assertIn("Hi, Team", body)
        self.assertIn("今日未成功執行的 Cloud Function 如下：", body)
        self.assertIn("Best,\nData Team", body)

        # 檢查錯誤訊息
        self.assertIn("Function Name: test-function", body)
        self.assertIn("Trigger Count: 5", body)
        self.assertIn("Success Count: 3", body)
        self.assertIn("Error message 1", body)
        self.assertIn("Error message 2", body)

        # 檢查 Log 連結
        expected_link = f"https://console.cloud.google.com/run/detail/{self.region}/test-function/logs?project={self.project_id}"
        self.assertIn(expected_link, body)

    def test_make_mail_body_multiple_errors(self):
        """測試多個錯誤的信件內容"""
        monitor_results = [
            {
                'function_name': 'test-function-1',
                'trigger_count': 5,
                'success_count': 3,
                'errors': ['Error message 1']
            },
            {
                'function_name': 'test-function-2',
                'trigger_count': 10,
                'success_count': 8,
                'errors': ['Error message 2']
            }
        ]

        body = make_mail_body(monitor_results, self.project_id)

        # 檢查每個 Function 的錯誤訊息
        self.assertIn("Function Name: test-function-1", body)
        self.assertIn("Trigger Count: 5", body)
        self.assertIn("Success Count: 3", body)
        self.assertIn("Error message 1", body)

        self.assertIn("Function Name: test-function-2", body)
        self.assertIn("Trigger Count: 10", body)
        self.assertIn("Success Count: 8", body)
        self.assertIn("Error message 2", body)

        # 檢查每個 Function 的 Log 連結
        expected_link_1 = f"https://console.cloud.google.com/run/detail/{self.region}/test-function-1/logs?project={self.project_id}"
        expected_link_2 = f"https://console.cloud.google.com/run/detail/{self.region}/test-function-2/logs?project={self.project_id}"
        self.assertIn(expected_link_1, body)
        self.assertIn(expected_link_2, body)

    def test_make_mail_body_error_truncation(self):
        """測試長錯誤訊息是否被截斷"""
        # 產生一個超過 500 字元的錯誤訊息
        long_error = "x" * 600

        monitor_results = [
            {
                'function_name': 'test-function',
                'trigger_count': 5,
                'success_count': 3,
                'errors': [long_error]
            }
        ]

        body = make_mail_body(monitor_results, self.project_id)

        # 檢查是否有截斷的部分
        self.assertIn("...", body)

    def test_make_mail_body_missing_error_messages(self):
        """測試缺少錯誤訊息的情況"""
        monitor_results = [
            {
                'function_name': 'test-function',
                'trigger_count': 5,
                'success_count': 3,
                'errors': []  # 空的錯誤列表
            }
        ]

        body = make_mail_body(monitor_results, self.project_id)

        # 檢查是否顯示「未捕獲到具體錯誤訊息」的提示
        self.assertIn("未捕獲到具體錯誤訊息，但執行失敗", body)

    def test_make_mail_body_missing_fields(self):
        """測試缺少某些欄位的情況"""
        # 缺少 trigger_count 和 success_count
        monitor_results = [
            {
                'function_name': 'test-function',
                'errors': ['Error message']
            }
        ]

        body = make_mail_body(monitor_results, self.project_id)

        # 檢查是否使用預設值
        self.assertIn("Function Name: test-function", body)
        self.assertIn("Trigger Count: N/A", body)
        self.assertIn("Success Count: N/A", body)

    def test_extract_main_error_standard_traceback(self):
        """測試從標準 Traceback 中提取主要錯誤"""
        traceback = """Traceback (most recent call last):
  File "main.py", line 42, in process_data
    result = perform_calculation(data)
  File "calculations.py", line 105, in perform_calculation
    return data['value'] / 0
ZeroDivisionError: division by zero"""

        main_error = extract_main_error(traceback)
        self.assertEqual(main_error, "ZeroDivisionError: division by zero")

    def test_extract_main_error_severity_format(self):
        """測試從帶有嚴重性標記的錯誤中提取主要錯誤"""
        error_with_severity = "[ERROR] Failed to connect to database: Connection refused"

        main_error = extract_main_error(error_with_severity)
        self.assertEqual(main_error, "[ERROR] Failed to connect to database: Connection refused")

    def test_extract_main_error_system_error(self):
        """測試從系統錯誤消息中提取主要錯誤"""
        system_error = """Starting app
Initializing...
Loading configuration
memory limit exceeded while loading large file
Terminating..."""

        main_error = extract_main_error(system_error)
        self.assertEqual(main_error, "memory limit exceeded while loading large file")

    def test_parse_error_string(self):
        """測試解析錯誤字符串的功能"""
        error_string = """function_name: test-function
trigger_count: 5
success_count: 3
error:

Traceback (most recent call last):
  File "main.py", line 42, in process_data
    result = perform_calculation(data)
ZeroDivisionError: division by zero"""

        parsed = parse_error_string(error_string)
        self.assertEqual(parsed[0], "function_name: test-function")
        self.assertEqual(parsed[1], "trigger_count: 5")
        self.assertEqual(parsed[2], "success_count: 3")
        self.assertEqual(parsed[3], "ZeroDivisionError: division by zero")

    def test_format_slack_error_message(self):
        """測試 Slack 消息格式化"""
        error_list = [
            [
                "function_name: test-function",
                "trigger_count: 5",
                "success_count: 3",
                "ZeroDivisionError: division by zero"
            ]
        ]

        slack_message = format_slack_error_message(error_list)
        self.assertIn("*函式名稱*: `test-function`", slack_message)
        self.assertIn("• 觸發次數: 5", slack_message)
        self.assertIn("• 成功次數: 3", slack_message)
        self.assertIn("ZeroDivisionError: division by zero", slack_message)

    @patch('utils.make_mail_body')
    @patch('utils.send_mail')
    def test_no_errors_no_mail_sent(self, mock_send_mail, mock_make_mail_body):
        """測試無錯誤時不會發送信件"""
        # 直接測試 main.py 中的關鍵邏輯
        empty_monitor_results = []

        # 模擬 main.py 中的邏輯
        if empty_monitor_results:
            # 如果有錯誤，就呼叫 make_mail_body 和 send_mail
            body = mock_make_mail_body(empty_monitor_results, "test-project-id")
            mock_send_mail("<EMAIL>", ["<EMAIL>"], "password", "msg")

        # 斷言 make_mail_body 和 send_mail 沒有被呼叫
        mock_make_mail_body.assert_not_called()
        mock_send_mail.assert_not_called()

if __name__ == '__main__':
    unittest.main()