# Cloud Function Monitor Email Link Enhancement Plan

## Requirements

- Modify the Cloud Function Monitor (`Cloud_function_monitor`) to change the email notification content.
- Instead of a single generic link to the Cloud Run services page, the email should include specific links to the logs page for each Cloud Function that failed to execute successfully.
- The link format should be: `https://console.cloud.google.com/run/detail/<region>/<function_name>/logs?project=<project_id>`

## Tech Stack

- Python 3
- Google Cloud Functions
- Google Cloud Logging API
- Google Cloud Storage
- smtplib (for sending emails)
- requests (for sending Slack messages)
- unittest (for testing)

## Milestones

1.  **Analyze Current Implementation:** Review `main.py` and `utils.py` to understand the existing workflow for fetching errors and generating email content. (Completed)
2.  **Refactor Data Flow:** Modify `main.py` to pass necessary data (`project_id`, `monitor_results`) to the email generation function. (Completed)
3.  **Update Email Generation Logic:** Modify `utils.make_mail_body` to iterate through failed functions and generate specific log links using the provided `project_id`, the function name from `monitor_results`, and the hardcoded region (`asia-east1`). (Completed)
4.  **Add Unit Tests:** Create tests for the `make_mail_body` function to ensure it generates the correct email content with specific log links for each failed Cloud Function. (Completed)
5.  **Testing (Manual/Local):** Manually trigger the function (if possible in a dev environment or locally) or carefully review the code changes to ensure the correct links are generated in the email body.
6.  **Deploy:** Deploy the updated Cloud Function.

## Changes Implemented

- Modified `main.py` to pass `project_id` and `monitor_results` to `make_mail_body`.
- Updated `make_mail_body` in `utils.py` to generate specific log links for each failed Cloud Function.
- Added unit tests for `make_mail_body` in `tests/test_utils.py`.
- Created test execution script and documentation in the `tests` directory.