from google.cloud import logging
from datetime import datetime, timedelta
import pytz
from google.oauth2 import service_account
from typing import List, Dict
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
import logging as L
import requests

def monitor_function_status(credentials: service_account.Credentials, function_names: List[str], monitor_days: int = 1) -> List[Dict[str, str]]:

    # 初始化 Cloud Logging 客戶端
    client = logging.Client(credentials=credentials)

    # 計算查詢的時間範圍：過去 24 小時
    end_time = datetime.utcnow().replace(tzinfo=pytz.UTC)
    start_time = end_time - timedelta(days=monitor_days)

    result = list()

    for function_name in function_names:
        L.info(f"Checking logs for {function_name} from {start_time.strftime('%Y-%m-%d')} to {end_time.strftime('%Y-%m-%d')}")

        # 構建查詢字串
        # 2024-12-17 加入 httpRequest.requestUrl:"/{function_name}" 以排除 google 內部對 cloud function 的請求（服務健康檢查, 預熱請求, 系統層級的監控請求...等等）
        query = f"""
            resource.type = "cloud_run_revision"
            resource.labels.service_name = "{function_name}"
            resource.labels.location = "asia-east1"
            severity>="Default"
            httpRequest.requestUrl:"/{function_name}"
        """

        entries = [entry for entry in client.list_entries(filter_=query)]
        triggers = [log.http_request['status'] for log in entries if log.http_request is not None and (log.http_request['requestMethod']=='POST')]
        results = [trigger for trigger in triggers if trigger == 200]

        # print(f"總共被觸發 {len(triggers)} 次")
        # print(f"總共完成 {len(results)} 次")
        # 改進錯誤捕獲邏輯：
        # 1. 包含 Traceback 的標準 Python 錯誤
        # 2. 嚴重性級別為 ERROR 或 CRITICAL 的日誌
        # 3. 包含常見錯誤關鍵字的日誌
        traceback_errors = [log.payload for log in entries if (log.payload is not None) and ("Traceback" in log.payload)]

        # 捕獲嚴重性為錯誤或關鍵的日誌
        severity_errors = [
            f"[{log.severity}] {log.payload}"
            for log in entries
            if (log.severity in ["ERROR", "CRITICAL", "ALERT", "EMERGENCY"]) and log.payload is not None
        ]

        # 捕獲包含常見錯誤關鍵字的日誌
        error_keywords = ["error", "exception", "fail", "timeout", "exceeded", "crash", "killed", "terminated", "rejected"]
        keyword_errors = [
            log.payload
            for log in entries
            if (log.payload is not None) and
               any(keyword.lower() in log.payload.lower() for keyword in error_keywords) and
               log.payload not in traceback_errors and
               log.payload not in [err.split("] ", 1)[1] if "] " in err else err for err in severity_errors]
        ]

        # 合併所有錯誤
        all_errors = traceback_errors + severity_errors + keyword_errors

        # 去重
        unique_errors = list(dict.fromkeys(all_errors))

        if len(triggers) - len(results) > 0 or unique_errors:
            # print(f"發生錯誤 {len(triggers) - len(results)} 次")

            result.append(
                {
                    'function_name': function_name,
                    'trigger_count': len(triggers),
                    'success_count': len(results),
                    'errors': unique_errors
                }
            )

    return result

def make_error_report(erros: List[str]) -> str:

    error_string_list = list()
    for error in erros:
        error_string = ""
        i = 0
        for k, v in error.items():
            if type(v) == list:
                for v_ in v:
                    error_string += v_ + "\n\n"
                    i += 1
            elif k == "success_count":
                error_string += f"{k}: {v}" + "\n" + "error:\n\n"
            else:
                error_string += f"{k}: {v}" + "\n"
        error_string_list.append(error_string)
    return error_string_list

def make_mail_body(monitor_results: List[Dict[str, any]], project_id: str):
    """Generates the email body content with specific log links for each failed function.

    Args:
        monitor_results: A list of dictionaries, each containing details of a failed function.
                         Expected keys: 'function_name', 'trigger_count', 'success_count', 'errors'.
        project_id: The Google Cloud project ID.

    Returns:
        The formatted email body string.
    """
    region = "asia-east1"  # Hardcoded region as identified in monitor_function_status

    header_ = """Hi, Team

今日未成功執行的 Cloud Function 如下：

"""

    body_content = ""
    for error_dict in monitor_results:
        function_name = error_dict.get('function_name', 'N/A')
        trigger_count = error_dict.get('trigger_count', 'N/A')
        success_count = error_dict.get('success_count', 'N/A')
        errors = error_dict.get('errors', [])

        body_content += f"Function Name: {function_name}\n"
        body_content += f"Trigger Count: {trigger_count}\n"
        body_content += f"Success Count: {success_count}\n"
        body_content += "Errors:\n"

        if errors:
            # 處理每個錯誤訊息，限制長度並提取關鍵信息
            for i, err in enumerate(errors, 1):
                # 檢查錯誤訊息格式
                if isinstance(err, str):
                    # 提取主要錯誤信息
                    main_error = extract_main_error(err)

                    # 添加格式化的錯誤訊息，限制長度以保持郵件可讀性
                    body_content += f"  {i}. {main_error}\n"

                    # 如果原始錯誤很長，只顯示前500個字符
                    if len(err) > 500:
                        short_err = err[:500] + "..."
                        body_content += f"     詳細錯誤: {short_err}\n"
                else:
                    # 處理非字符串類型的錯誤
                    body_content += f"  {i}. 非文本錯誤: {str(err)[:200]}\n"

            body_content += "\n"
        else:
            body_content += "  (未捕獲到具體錯誤訊息，但執行失敗)\n\n"

        # Generate specific log link
        log_link = f"https://console.cloud.google.com/run/detail/{region}/{function_name}/logs?project={project_id}"
        body_content += f"詳細 Log 請查看: {log_link}\n\n"
        body_content += "---\n\n" # Separator

    bottem = """\nBest,
Data Team
    """
    body = header_ + body_content + bottem
    return body

def prepare_mail(from_email: str, to_email: List[str], subject: str, body: str) -> MIMEMultipart:
    # Prepare the email
    msg = MIMEMultipart()
    msg['From'] = from_email
    msg['To'] = ", ".join(to_email)
    msg['Subject'] = subject
    msg.attach(MIMEText(body, 'plain'))
    return msg

def send_mail(from_email: str, to_email: str, password: str, msg: MIMEMultipart):
    # Send the email
    server = smtplib.SMTP('smtp.gmail.com', 587)
    server.starttls()
    server.login(from_email, password)
    text = msg.as_string()
    server.sendmail(from_email, to_email, text)
    server.quit()
    L.info("Send mail successfully")
    return

def extract_main_error(error_string: str) -> str:
    """從完整的錯誤訊息中提取主要錯誤

    Args:
        error_string: 完整的錯誤訊息字串

    Returns:
        str: 主要的錯誤訊息
    """
    # 檢查是否是格式化的嚴重性錯誤 (例如 "[ERROR] 錯誤訊息")
    if error_string.startswith('[') and '] ' in error_string:
        severity, message = error_string.split('] ', 1)
        return f"{severity}] {message[:200]}" if len(message) > 200 else f"{severity}] {message}"

    # 常見的錯誤類型
    error_types = [
        'FileNotFoundError:',
        'RuntimeError:',
        'ValueError:',
        'TypeError:',
        'AttributeError:',
        'ImportError:',
        'IndexError:',
        'KeyError:',
        'NameError:',
        'SyntaxError:',
        'PermissionError:',
        'ConnectionError:',
        'TimeoutError:',
        'OSError:',
        'Error:',
        'Exception:',
        'Failed:',
        'Timeout:',
        'Terminated:',
        'Killed:'
    ]

    # 將錯誤訊息按行分割
    lines = error_string.split('\n')

    # 檢查是否有系統級別的錯誤關鍵字
    system_error_keywords = [
        'memory limit exceeded',
        'timeout exceeded',
        'insufficient memory',
        'connection reset',
        'service unavailable',
        'quota exceeded',
        'resource exhausted',
        'permission denied',
        'service timeout',
        'service error',
        'execution timed out',
        'process killed',
        'failed to initialize',
        'failed to execute'
    ]

    # 尋找包含系統錯誤關鍵字的行
    for line in lines:
        line = line.strip()
        if any(keyword in line.lower() for keyword in system_error_keywords):
            return line

    # 尋找最後一個實際的錯誤訊息
    for line in reversed(lines):
        line = line.strip()
        # 檢查是否包含任何已知的錯誤類型
        for error_type in error_types:
            if error_type in line:
                return line

    # 如果找不到已知的錯誤類型，返回最後一個非空行
    for line in reversed(lines):
        if line.strip():
            # 限制錯誤訊息長度
            return line.strip()[:200] + "..." if len(line.strip()) > 200 else line.strip()

    # 如果沒有找到任何有用的訊息，顯示原始錯誤的一部分
    if error_string:
        return error_string[:200] + "..." if len(error_string) > 200 else error_string

    return "Unknown error"

def parse_error_string(error_string: str) -> list:
    """解析錯誤訊息字串，提取關鍵資訊

    Args:
        error_string: 完整的錯誤訊息字串

    Returns:
        list: 包含關鍵資訊的列表
    """
    # 將字串依換行分割
    lines = error_string.split('\n')

    # 提取基本資訊
    basic_info = [
        lines[0],  # function_name
        lines[1],  # trigger_count
        lines[2],  # success_count
    ]

    # 提取主要錯誤訊息
    main_error = extract_main_error(error_string)
    basic_info.append(main_error)

    return basic_info

def format_slack_error_message(error_list: list) -> str:
    """將錯誤資訊列表轉換成格式化的訊息

    Args:
        error_list: 包含多個函式錯誤資訊的列表

    Returns:
        str: 格式化後的訊息
    """
    # 訊息開頭
    message_parts = [
        "*:warning: Cloud Function 執行異常通知*",
        "今日有以下 Cloud Function 未成功執行：\n"
    ]

    # 處理每個函式的錯誤資訊
    for error_info in error_list:
        try:
            # 從 function_name 中提取函式名稱
            function_parts = error_info[0].split(': ', 1)
            function_name = function_parts[1] if len(function_parts) > 1 else function_parts[0]

            # 提取其他資訊
            trigger_count = error_info[1].split(': ')[1] if len(error_info) > 1 and ': ' in error_info[1] else 'N/A'
            success_count = error_info[2].split(': ')[1] if len(error_info) > 2 and ': ' in error_info[2] else 'N/A'

            # 提取錯誤訊息，確保其存在並格式化
            error_message = error_info[3] if len(error_info) > 3 else "未捕獲到詳細錯誤訊息"

            # 限制錯誤訊息長度，避免 Slack 消息太長
            if len(error_message) > 500:
                error_message = error_message[:500] + "..."

            # 組合每個函式的資訊區塊
            function_block = [
                f"*函式名稱*: `{function_name}`",
                f"• 觸發次數: {trigger_count}",
                f"• 成功次數: {success_count}",
                f"*錯誤訊息*:\n```{error_message}```",
                f"*詳細資訊*: https://console.cloud.google.com/run/detail/asia-east1/{function_name}/logs?project=tagtoo-ml-workflow\n"
            ]

            message_parts.append("\n".join(function_block))
        except Exception as e:
            # 確保處理錯誤訊息時發生的異常不會中斷程序
            message_parts.append(f"*處理錯誤資訊時發生問題*: {str(e)}\n原始錯誤資訊: {str(error_info)[:200]}\n")

    # 組合完整訊息
    return "\n".join(message_parts)

def send_slack_message(webhook_url: str, message: str) -> None:
    """發送訊息至 Slack

    Args:
        webhook_url: Slack Webhook URL
        message: 要發送的訊息
    """
    payload = {"text": message}
    try:
        response = requests.post(
            webhook_url,
            json=payload,
            headers={'Content-Type': 'application/json'}
        )
        if response.status_code != 200:
            L.error(f"發送 Slack 訊息失敗: {response.status_code} - {response.text}")
    except Exception as e:
        L.error(f"發送 Slack 訊息時發生錯誤: {str(e)}")