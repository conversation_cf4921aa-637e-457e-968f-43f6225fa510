import functions_framework
from datetime import datetime, timedelta
import pytz
from google.oauth2 import service_account
import google_storage
import json
import logging as L
import os
from utils import *


@functions_framework.http
def main(request):
    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    
    L.basicConfig(level=L.INFO)

    # * external argument
    request_json = request.get_json(silent=True)
    request_args = request.args

    if request_json and 'monitor_days' in request_json: # POST
        monitor_days = int(request_json['monitor_days'])
    elif request_args and 'monitor_days' in request_args: # GET
        monitor_days = int(request_args['monitor_days'])
    else:
        raise AttributeError("No monitor_days found in URL.")
    L.info("monitor_days: %s", monitor_days)

    if request_json and 'dev' in request_json: # POST
        dev = int(request_json['dev'])
    elif request_args and 'dev' in request_args: # GET
        dev = int(request_args['dev'])
    else:
        dev = 0
    
    if dev == 0:
        L.info("This is a production version.")
    elif dev == 1:
        L.info("This is a beta version.")

    # verify account
    slack_webhook_url = os.environ.get('SLACK_WEBHOOK_URL')
    key_path = 'tagtoo-ml-workflow-sa.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    project_id = credentials.project_id

    # initial GSIO
    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    # download function names - use local file for monitor list
    function_names_localpath = 'cloud_function_monitor_list.txt'

    # download sending list - restore GCS download for email recipients
    sending_list_localpath = '/tmp/sending_list.txt'
    sending_list_gcs_path = 'gs://tagtoo-ml-workflow/topic10/Fredrick_Workbench/Cloud_function_monitor/sending_list.txt'
    GSIO.download_to_path(localpath=sending_list_localpath, gsuri=sending_list_gcs_path)

    # download gmail password
    gmail_password_localpath = '/tmp/gmail_password.json'
    gmail_password_gcs_path = 'gs://tagtoo-ml-workflow/topic10/Fredrick_Workbench/Cloud_function_monitor/gmail_password.json'
    GSIO.download_to_path(localpath=gmail_password_localpath, gsuri=gmail_password_gcs_path)
    
    with open(function_names_localpath, 'r') as f:
        function_names = f.read().split(', ')
    with open(sending_list_localpath, 'r') as f:
        to_email = f.read().split(', ')
    with open(gmail_password_localpath, 'r') as f:
        PASSWORD = json.load(f)['tagtooshop']

    monitor_results = monitor_function_status(credentials, function_names, monitor_days)

    if monitor_results:
        
        # 發送 gmail
        body = make_mail_body(monitor_results, project_id)
        from_email = "<EMAIL>"
        if dev == 1:
            to_email = ["<EMAIL>"]
        else:
            to_email = to_email
        subject = f"Data Team - Cloud Function 執行錯誤報告 - {datetime.now(pytz.timezone('Asia/Taipei')).strftime('%Y%m%d')}"
        msg = prepare_mail(from_email, to_email, subject, body)
        send_mail(from_email, to_email, PASSWORD, msg)
        
        # 傳訊息到 slack
        errors_string_list = make_error_report(monitor_results)
        slack_errors_list = [parse_error_string(error_string) for error_string in errors_string_list]
        slack_message = format_slack_error_message(slack_errors_list)
        send_slack_message(slack_webhook_url, slack_message)
    else:
        L.info("No error")

    L.info("Job complete")

    return 'Success', 200
