# Cloud_function_monitor

> 一個用於監控其他 Cloud Functions 執行狀態的自動化監控系統

## 目錄

- [背景說明](#背景說明)
- [功能簡介](#功能簡介)
- [安裝方式](#安裝方式)
- [使用說明](#使用說明)
- [流程說明](#流程說明)
- [監控與維護](#監控與維護)

## 背景說明

此專案為 GCP tagtoo-ml-workflow 專案底下，Cloud Function 的監控系統，用於自動化監控各個 Cloud Functions 的執行狀態。當發現執行異常時，會透過 Email 和 Slack 通知相關人員，確保系統穩定運作。

## 功能簡介

這是一個監控功能，用於檢查指定的 Cloud Functions 在過去指定時間內的執行狀態。

主要功能包括:
1. 監控多個 Cloud Functions 的執行狀態
2. 查詢執行成功、失敗次數與錯誤原因
3. 透過 Email 發送詳細的錯誤報告
4. 透過 Slack 發送即時通知

## 安裝方式

此專案透過 GitHub Actions 自動部署至 Google Cloud Functions，無需手動安裝。

### 部署流程
1. 當程式碼推送到 `main` 分支時，會自動觸發部署
2. GitHub Actions 會執行 `.github/workflows/deploy.yml` 中定義的部署步驟
3. 部署完成後，Cloud Function 會自動更新
4. 環境變數需要人工部署（`SLACK_WEBHOOK_URL`）

### 排程執行
- 透過 Cloud Scheduler 每日自動觸發
- 排程時間：每日 UTC+8 9:30
- 觸發方式：HTTP POST 請求
- 確保在設定 Cloud Scheduler 時，有傳入以下的 API 參數

### API 參數

- `monitor_days`: 要監控的天數（整數）
- `dev`: 開發模式開關（0: 正式環境, 1: 測試環境）

### 必要配置
1. 環境變數設定
   - SLACK_WEBHOOK_URL: Slack 通知 Webhook URL

2. Cloud Storage 檔案配置
   - 檔案都放在此資料夾中：gs://tagtoo-ml-workflow/topic10/Fredrick_Workbench/Cloud_function_monitor
   - 監控清單：cloud_function_monitor_list.txt
   - 通知名單：sending_list.txt
   - Gmail 密碼：gmail_password.json

## 流程說明

1. 接收監控請求並驗證參數
2. 讀取監控清單和通知設定
3. 檢查各個 Cloud Function 的執行記錄
4. 分析執行狀態和錯誤原因
5. 產生錯誤報告
6. 發送 Email 和 Slack 通知

### 監控與維護
- 可在 Cloud Functions 控制台查看執行記錄

## 維護者

[@Fredrick](https://github.com/fredrick84823) - Tagtoo Data Team

## 如何貢獻

歡迎提交 Issue 與 Pull Request。