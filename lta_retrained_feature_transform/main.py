import functions_framework
import logging
import numpy as np
import pandas as pd
import random
import block_timer
import dask_utils
from dask_utils import load_data, parquet_load_data
import joblib
import google_storage
import dask.dataframe as dd
from google.cloud import bigquery
import datetime
from pytz import timezone
from typing import List, Tu<PERSON>, Dict, Set
import json
from datetime import datetime, timedelta
from google.oauth2 import service_account

GSIO = google_storage.GoogleStorageIO()

def concate_ecid_industry(df):
    """
    times industry 10000 than plus ecid
    ex:industry:4 ecid:256 become 40256
    """
    df['inad'] = df['industry_id']*10000 + df['ec_id']
    df = df.drop(columns=['industry_id','ec_id'])
    return df

def get_ec_list(df):
    """
    use frequence and cookies_num to ranking ec
    than get ec_list
    """
    ec_cookies_num = df.groupby('inad').size()
    #ec_frequences = df.groupby('inad').date.unique()
    ec_list = []
    for i in ec_cookies_num.index:
        ec_list.append([i,ec_cookies_num[i]])
    ec_list = pd.DataFrame(ec_list)
    ec_list.columns = ['ec','cookies_num']
    ec_list = ec_list.set_index('ec')
    ec_list = ec_list.sort_values(['cookies_num'],ascending=False)
    print('total ec numbers: ',len(ec_list))
    return ec_list

def ec_onehot(df):
    ec_onehot = pd.get_dummies(df['inad'])
    ec_onehot.columns = ec_onehot.columns.astype('str')
    df = df.drop(columns = ['inad'])
    df = pd.concat([df,ec_onehot],axis=1)
    print('df done!')
    return df

def sum_other_ec(df,ec_list,ec_num,length):
    """
    use ec_list and ec_num to get top10(ec_num) ec
    than remove than from df to get other_ec list
    select that data in other_ec,sum into one columns
    final we got "other_track" and "other_transaction"
    """
    #select other ec_track(ec not in ec_num[:ec_num])
    track_idx = ec_list.index[:ec_num].astype(str)
    #transaction_idx = ec_list.index[:ec_num].astype(str)+'transaction'
    other_idx = df.columns[~df.columns.isin(track_idx)][length:]
    temp = other_idx[:int(len(other_idx))]
    other_track = pd.DataFrame(df[temp].sum(axis=1))
    other_track.columns = ['other_ec']
    #temp = other_idx[int(len(other_idx)/2):]
    #other_transaction = pd.DataFrame(df[temp].sum(axis=1))
    #other_transaction.columns = ['other_transaction']

    df = pd.concat([df[df.columns[:length]],
            df[ec_list.index[:ec_num].astype(str)],
            other_track],
            axis=1)
    return df

def trans(alist: pd.Series, dict1: Dict[str, int]) -> List[int]:
    """
    Extract values from a dictionary based on indices of a list if the list value > 0.

    Args:
    - alist (List[int]): Input list with string indices and integer values.
    - dict1 (Dict[str, int]): Dictionary with string keys and integer values -> {label_name: semgent_id}.

    Returns:
    - List: A list containing unique **segment_id** extracted from the dictionary.
    """
    temp = [dict1[col] for col in alist.index if col in dict1 and alist[col] > 0]         
    temp = [*set(temp)] # Remove duplicates from the temporary list
    temp = [item for item in temp if item is not None]
        
    return temp

def str_isin(x, input_str):
  return (input_str == x)

def make_date_arguments(current_datetime: datetime) -> Dict[str, str]:

  DATE_FORMAT = "%Y-%m-%d"
  tmp_dict = {}
  
  tmp_dict['start_date'] = (current_datetime - timedelta(days=31)).strftime(DATE_FORMAT)
  tmp_dict['end_date'] = (current_datetime - timedelta(days=1)).strftime(DATE_FORMAT)

  return tmp_dict

@functions_framework.http
def main(request):
    # 設置 logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s'
    )
    
    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    output_path = "gs://tagtoo-ml-workflow/topic10/LTA_validation"

    GSIO = google_storage.GoogleStorageIO(credentials=credentials)
    today = datetime.now(timezone('Asia/Taipei'))
    date_dict = make_date_arguments(today)

    # load data
    export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature_v2/{date_dict['start_date']}_{date_dict['end_date']}/*.parquet"
    print(export_path)
    df_train_f = parquet_load_data(export_path)
    logging.info("\n=== Initial DataFrame ===")
    logging.info(f"\nShape: {df_train_f.shape}")
    logging.info(f"\nColumns: {df_train_f.columns.tolist()}")
    logging.info(f"\nSample data:\n{df_train_f.head()}")

    length = len(df_train_f.columns)-1

    df_train_f = concate_ecid_industry(df_train_f)
    logging.info("\n=== After ECID-Industry Concatenation ===")
    logging.info(f"\nShape: {df_train_f.shape}")
    logging.info(f"\nColumns: {df_train_f.columns.tolist()}")
    logging.info(f"\nSample data:\n{df_train_f.head()}")

    ec_list = get_ec_list(df_train_f)
    df_train_f = ec_onehot(df_train_f)
    logging.info("\n=== After One-hot Encoding ===")
    logging.info(f"\nShape: {df_train_f.shape}")
    logging.info(f"\nSample columns: {df_train_f.columns[:10].tolist()}")
    logging.info(f"\nSample data:\n{df_train_f.head()}")

    df_train_f = sum_other_ec(df_train_f, ec_list, 10, length)
    logging.info("\n=== After Summing Other ECs ===")
    logging.info(f"\nShape: {df_train_f.shape}")
    logging.info(f"\nColumns: {df_train_f.columns.tolist()}")
    logging.info(f"\nSample data:\n{df_train_f.head()}")

    df_mode = df_train_f[list(df_train_f.columns[:6]) + ['avg_value_view_item']]
    list_sum = list(df_train_f.columns[:1]) + list(df_train_f.columns[6:])
    list_sum.remove('avg_value_view_item')
    df_sum = df_train_f[list_sum]
    df_mode = df_mode.groupby(['permanent']).mean().reset_index()
    df_sum = df_sum.groupby(['permanent']).sum().reset_index()
    df = pd.merge(df_mode, df_sum, on = ['permanent'])
    
    logging.info("\n=== After Mode and Sum Operations ===")
    logging.info(f"\nShape: {df.shape}")
    logging.info(f"\nColumns: {df.columns.tolist()}")
    logging.info(f"\nSample data:\n{df.head()}")

    df = df[[
    'permanent',
    'day_diff',
    'hour',
    'week_of_day',
    'lat',
    'lon',
    'avg_value_view_item',
    'session_direct',
    'session_organic',
    'session_referral',
    'session_social',
    'session_media',
    'currency_TWD',
    'currency_other',
    'is_mobile',
    'is_tablet',
    'is_pc',
    'is_touch_capable',
    'payment_method_credit_card',
    'value_add_to_cart',
    'item_count_add_to_cart',
    'value_view_cart',
    'item_count_view_cart',
    'value_purchase',
    'item_count_purchase',
    'value_checkout',
    'item_count_checkout',
    'count_add_to_cart',
    'count_add_shipping_info',
    'count_lead',
    'count_view_item',
    'count_add_payment_info',
    'count_view_item_list',
    'count_purchase',
    'count_search',
    'count_focus',
    'count_copy',
    'count_checkout',
    'count_view_cart',
    'count_view_health_beauty',
    'count_purchase_health_beauty',
    'count_view_home_garden',
    'count_purchase_home_garden',
    'count_view_apparel',
    'count_purchase_apparel',
    'count_view_food',
    'count_purchase_food',
    'count_view_baby',
    'count_purchase_baby',
    'count_view_sport',
    'count_purchase_sport',
    'count_view_electronics',
    'count_purchase_electronics',
    'count_view_office',
    'count_purchase_office',
    'count_view_toy_game',
    'count_purchase_toy_game',
    'count_view_hardware',
    'count_purchase_hardware',
    'count_view_entertainment',
    'count_purchase_entertainment',
    'count_view_mature',
    'count_purchase_mature',
    'count_view_pet',
    'count_purchase_pet',
    'count_view_furniture',
    'count_purchase_furniture',
    'count_view_camera',
    'count_purchase_camera',
    'count_view_business',
    'count_purchase_business',
    'count_view_software',
    'count_purchase_software'
    ] + list(df.columns)[-11:]]

    logging.info("\n=== Final DataFrame ===")
    logging.info(f"\nShape: {df.shape}")
    logging.info(f"\nColumns count: {len(df.columns)}")
    logging.info(f"\nSample data:\n{df.head()}")

    print("feature transform complete")

    df.to_feather('/tmp/*.ftr')
    GSIO.upload_file('/tmp/*.ftr', f"gs://tagtoo-ml-workflow/topic10/LTA_feature_v2/{date_dict['start_date']}_{date_dict['end_date']}/transformed/*.ftr")
    print("feature uploaded")

    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    schema = [
    {"name": "permanent", "type": "STRING"}
    ]

    project_id = "tagtoo-ml-workflow"
    table_id = f'tagtoo_export_results.lta_retrained_model_fb_permanent'
    df[['permanent']].to_gbq(table_id, project_id=project_id, table_schema = schema, if_exists = 'replace') 
            
    print('Job complete.')

    return 'Success', 200
