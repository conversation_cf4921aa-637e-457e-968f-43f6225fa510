# 系統模式

## 系統架構

本系統採用模塊化設計，主要由以下幾個關鍵組件構成：

1. **數據獲取層**：從 BigQuery 獲取待處理的商品或文章數據
2. **標籤生成層**：處理數據並生成結構化標籤
3. **GMC 分類層**：為商品生成 Google Merchant Center 分類
4. **數據存儲層**：將處理結果存儲到 BigQuery 和 Google Cloud Storage
5. **日誌和監控層**：記錄處理過程和成本

系統流程設計為異步處理，可以高效處理大量數據，同時對 API 調用進行優化，以控制成本。

```
數據獲取 --> 價值評估過濾 --> 並行處理 --> 標籤生成 --> GMC分類 --> 數據存儲
   |                                                                  |
   |                                                                  V
   └────────────────────────────── 日誌和監控 ─────────────────────────┘
```

## 關鍵技術決策

1. **使用 OpenAI API**：選擇 OpenAI 的強大語言模型能力，以獲取高質量的標籤生成結果
2. **異步處理機制**：使用 Python 的 asyncio 和 aiohttp 實現高效的並行請求處理
3. **批量處理策略**：採用批量處理策略，優化 API 調用次數和成本
4. **分層處理邏輯**：將處理邏輯分為多個層次，包括價值評估、標籤生成和 GMC 分類，以實現更精細的控制
5. **雲服務整合**：充分利用 Google Cloud 服務，包括 BigQuery、Cloud Storage 和 Cloud Functions
6. **重試機制**：實現智能重試機制，處理 API 調用中的臨時錯誤

## 設計模式

1. **處理器模式**：建立不同類型的處理器（MediaGPTHandler、EcGPTHandler、GmcCategoryPredictor）來處理不同類型的數據
2. **管道模式**：實現數據處理管道，使數據可以順序經過多個處理階段
3. **工廠模式**：根據輸入參數動態選擇適當的處理器和處理流程
4. **裝飾器模式**：利用裝飾器實現日誌記錄和錯誤處理等橫切關注點

## 組件關係

主要組件的關係如下：

- **main.py**：系統入口點，負責解析請求參數，初始化各組件，並調用相應的處理流程
- **process_pipeline.py**：定義處理流程和成本計算，協調不同處理器的工作
- **label_handler.py**：基於 base.py 實現標籤生成處理邏輯
- **gmc_label_processor.py**：實現 GMC 商品分類處理邏輯
- **base.py**：提供 OpenAI API 請求的基礎處理器和重試機制
- **google_storage.py**：處理與 Google Cloud Storage 相關的操作
- **bigquery_handler.py**：處理與 BigQuery 相關的操作
- **last_event_handler.py**：處理最後事件時間更新邏輯
- **utils.py**：提供各種輔助函數和工具

## 擴展策略

1. **新標籤類型**：可以通過擴展現有處理器或添加新處理器來支持新的標籤類型
2. **新數據源**：可以通過添加新的數據獲取模塊來支持新的數據源
3. **新處理邏輯**：可以通過添加新的處理流程來支持新的處理邏輯
4. **模型更新**：系統設計允許輕鬆地更新或切換底層的 AI 模型 