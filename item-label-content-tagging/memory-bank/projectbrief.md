# 項目簡介：商品與文章標籤生成系統

## 核心需求

本項目旨在透過 OpenAI API 為不同的媒體內容和電子商務商品生成結構化的標籤。系統能夠分析商品和文章的內容，生成多維度的標籤，這些標籤將用於後續的分析報告和受眾包建立。

## 主要目標

1. **自動標籤生成**：為商品和文章自動生成結構化標籤，減少人工標記的需求
2. **深度內容分析**：分析商品或文章的核心特點，生成相關關鍵字和描述
3. **標籤結構化**：生成多維度的標籤，包括內容標籤、受眾輪廓、核心描述和關鍵字
4. **GMC 商品分類**：為商品生成 Google Merchant Center 的分類標籤
5. **高效批量處理**：能夠高效處理大量的商品和文章數據
6. **成本效益最佳化**：優化 API 調用，降低處理成本

## 成功標準

1. 標籤生成準確性：生成的標籤能準確反映商品或文章的特性和目標受眾
2. 處理效率：能夠在合理的時間內處理大量數據
3. 成本控制：將 API 調用成本維持在可接受的範圍內
4. 整合性：能夠順利與現有系統整合，包括數據輸入和輸出
5. 可擴展性：系統能夠適應新增的標籤類型和處理需求

## 應用場景

- **分析報告**：通過標籤系統，將用戶喜愛瀏覽或購買的商品標籤以視覺化方式呈現
- **受眾包建立**：通過媒合自定義關鍵字與標籤，找出特定類型用戶喜愛的商品
- **特徵工程**：將商品標籤作為受眾包特徵，用於受眾包預測 