# 技術上下文

## 技術棧

本項目使用的主要技術和框架如下：

1. **核心語言**
   - Python 3.10：主要開發語言

2. **異步處理**
   - Asyncio：Python 的非同步 I/O 框架，用於並行處理
   - Aiohttp：基於 asyncio 的非同步 HTTP 客戶端/伺服器框架，用於與 OpenAI API 通信

3. **數據處理**
   - Pandas：用於數據處理和轉換
   - JSON：用於結構化數據處理和存儲

4. **雲服務**
   - Google Cloud Functions：作為無伺服器運行環境
   - Google Cloud Storage：用於存儲中間數據和配置文件
   - Google Cloud BigQuery：用於數據存儲和查詢
   - Cloud Logging：用於記錄和監控系統運行情況

5. **AI 服務**
   - OpenAI API：為商品和文章生成標籤和分類

6. **部署工具**
   - Cloud Build：用於自動化部署流程

## 開發設置

### 本地開發環境
1. Python 3.10
2. 虛擬環境（推薦使用 venv 或 conda）
3. 使用 requirements.txt 安裝依賴
4. 本地配置 Google Cloud 和 OpenAI 的憑證

### 雲端部署
1. 使用 Cloud Functions 作為運行環境
2. HTTP 觸發函數入口為 content_tagging
3. 通過 cloudbuild.yaml 配置部署流程

## 技術約束

1. **API 限制**
   - OpenAI API 有調用頻率和並發限制，需要進行適當的節流和重試
   - BigQuery 的查詢和寫入操作有配額限制

2. **成本考量**
   - OpenAI API 調用會產生費用，需要優化調用次數和數據量
   - BigQuery 和 Cloud Storage 的使用也會產生費用，需要合理設計數據存儲策略

3. **性能考量**
   - 異步處理需要考慮內存使用和並發數量
   - Cloud Functions 有執行時間限制，需要在限制內完成處理

4. **安全考量**
   - API 密鑰需要安全存儲和管理
   - 數據訪問需要適當的權限控制

## 依賴關係

主要的外部依賴包括：

```
aiohttp==3.8.5
asyncio==3.4.3
functions-framework==3.4.0
google-auth==2.22.0
google-cloud-bigquery==3.11.4
google-cloud-logging==3.6.0
google-cloud-storage==2.10.0
openai==0.27.8
pandas==2.0.3
pytz==2023.3
```

## 技術債務和注意事項

1. **異常處理**: 系統實現了基本的重試機制，但某些邊緣情況下的錯誤處理可能需要進一步完善
2. **並行優化**: 當前的並行處理參數可能需要根據實際運行情況進行調整
3. **版本控制**: OpenAI API 和其他依賴可能會更新，需要定期檢查兼容性
4. **測試覆蓋**: 系統需要更全面的單元測試和集成測試 