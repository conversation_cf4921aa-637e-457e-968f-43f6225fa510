.DS_Store
.cursorignore
.vscode/

# Vim
.*.sw*

# Python
__pycache__/

# Dirs
logs/
debug_output/

# Venv
venv/

# log
debug.log

# Added by <PERSON> Task Master
# Logs
logs
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
dev-debug.log
# Dependency directories
node_modules/
# Environment variables
.env
# Editor directories and files
.idea
.vscode
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?
# OS specific
# Task files
tasks.json
tasks/ 