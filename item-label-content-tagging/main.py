import functions_framework
import logging as logger
import google.cloud.logging
from get_credentials import get_google_credentials, get_openai_credentials
from google_storage import GoogleStorageIO
from config import GOOGLE_APPLICATION_CREDENTIALS_PATH, OPENAI_API_KEY_PATH, EC_INFO_PATH, INDUSTRY_PATH, PROJECT_ID
from base import Async<PERSON>THandler
from label_handler import MediaGPTHandler, EcGPTHandler
from last_event_handler import UpdateLastEventHandler
from make_query import *
from utils import *
from process_pipeline import *
from google.cloud import bigquery
from datetime import datetime, timedelta
from pytz import timezone
import asyncio
import time
import sys

# 新增本地端logging設定函數
def setup_local_logging():
    logger.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s',
        handlers=[
            logger.StreamHandler(sys.stdout)  # 確保輸出到 stdout
        ]
    )

@functions_framework.http
def content_tagging(request):

    # 嘗試設定 Cloud Logging
    try:
        logging_client = google.cloud.logging.Client()
        logging_client.setup_logging(log_level=logging.INFO)
        logging.info("使用 Cloud Logging 設定")
    except Exception as e:
        # 如果 Cloud Logging 設定失敗，使用本地設定
        logger = setup_local_logging()
        logger.info("使用本地端logging設定")
        logger.error(f"Cloud logging setup failed: {e}")

    # 解析請求參數
    request_json = request.get_json(silent=True)
    media_or_ec_gate = request_json.get('media_or_ec_gate') # 選項: media, ec, single_ec
    
    if media_or_ec_gate == 'single_ec':
        ECID = int(request_json.get('ecid', -1))
        logging.info(f"ECID: {ECID}")
        if ECID == -1:
            return '缺少參數: ecid', 400
    elif media_or_ec_gate is None:
        return '缺少參數: media_or_ec_gate', 400
    
    if request_json and 'topk' in request_json:
        TOPK = int(request_json['topk'])
    else:
        return '缺少參數: topk', 400
    
    # 可指定日期範圍，若沒有指定，則會針對前一天的商品進行貼標
    # 格式為 YYYY-MM-DD
    if request_json and 'start_date' in request_json:
        start_date = request_json['start_date']
    else:
        start_date = (datetime.now(timezone('Asia/Taipei')) - timedelta(days=1)).strftime('%Y-%m-%d')

    if request_json and 'end_date' in request_json:
        end_date = request_json['end_date']
    else:
        end_date = start_date

    # 初始化參數
    google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    GSIO = GoogleStorageIO(credentials=google_credentials)
    openai_client = get_openai_credentials(GSIO, OPENAI_API_KEY_PATH)
    
    bq_client = bigquery.Client(credentials=google_credentials)

    if media_or_ec_gate == 'media':
        target_query = make_media_query(start_date, end_date, topk=TOPK)
    elif media_or_ec_gate == 'ec':
        target_query = make_ec_query(start_date, end_date, topk=TOPK)
    elif media_or_ec_gate == 'single_ec':
        target_query = make_singel_ec_query(start_date, end_date, ec_id=ECID, topk=TOPK)
    elif media_or_ec_gate == 'reurl': # 新增 reurl 選項
        target_query = make_reurl_query(start_date, end_date, topk=TOPK)
    else:
        raise AttributeError("Invalid input of media_or_ec_gate")

    print("target_query", target_query)
    item_df = bq_client.query_and_wait(target_query).to_dataframe()
    logging.info({
        "event": "process_complete",
        "type": media_or_ec_gate,
        "message": "資料查詢完畢"
    })

    if item_df.empty:
        logging.info({
            "event": "process_complete",
            "type": media_or_ec_gate,
            "message": f'{ECID} 無新商品'
        })
        return f'{ECID} 無新商品', 200

    item_df['unique_id'] = [generate_unique_product_id() for i in range(len(item_df))]
    worth_dataset, n_worth_dataset = asyncio.run(process_dataset(item_df, media_or_ec_gate, openai_client, GSIO))
    logging.info({
        "event": "process_complete",
        "type": media_or_ec_gate,
        "worth_item_count": len(worth_dataset),
        "n_worth_item_count": len(n_worth_dataset),
        "message": "貼標完成"
    })

    nworth_table_id = 'data_prod.item_labels_unworthy_items'
    nworth_schema_data = [
        {"name": "id", "type": "STRING"},
        {"name": "name", "type": "STRING"},
        {"name": "ec_name", "type": "STRING"},
        {"name": "ec_id", "type": "INTEGER"},
        {"name": "industry_id", "type": "INTEGER"},
        {"name": "industry_name", "type": "STRING"},
    ]

    if n_worth_dataset:
        process_and_upload_n_worth_dataset(
            n_worth_dataset,
            item_df,
            google_credentials,
            EC_INFO_PATH,
            INDUSTRY_PATH,
            PROJECT_ID,
            nworth_table_id,
            nworth_schema_data
        )
        logging.info({
            "event": "process_complete",
            "type": media_or_ec_gate,
            "message": f"無效標題已上傳至 {project_id}.{nworth_table_id}"
        })

    worth_dataset = process_worth_dataset(worth_dataset, item_df, google_credentials, EC_INFO_PATH, INDUSTRY_PATH)
    logging.info({
        "event": "process_complete",
        "type": media_or_ec_gate,
        "message": "標籤處理完畢"
    })

    upload_to_gcs_and_load_to_bigquery(
        worth_dataset=worth_dataset,
        table_name='item_labels',
        google_credentials=google_credentials,
        project_id=PROJECT_ID,
        gsio=GSIO,
        dataset_id='data_prod',
        media_or_ec_gate=media_or_ec_gate
    )
    
    # 單一家 EC 不需要更新 last_event_at，因為是想監控 tagtoo event 整體商品的數量
    if (media_or_ec_gate != 'single_ec') and (media_or_ec_gate != 'reurl'):
        # 確保貼標資料上傳到 BigQuery 後，再更新 last_event_at
        time.sleep(20)
        last_event_handler = UpdateLastEventHandler(bq_client)
        if not last_event_handler.last_event_at_update_statistics_is_today():
            last_event_handler.execute_update()
        else:
            logging.info({
                "event": "process_complete",
                "type": media_or_ec_gate,
                "message": "last_event_at 今日已被更新過"
            })

    return '執行完畢', 200

# 如果是直接執行此檔案(本地測試)
if __name__ == "__main__":
    # 啟動 Functions Framework
    functions_framework.cloud_event(content_tagging) # 入口