# 項目特定規則和偏好

## 命名慣例
- 函數名稱使用 snake_case
- 類名稱使用 PascalCase
- 常量使用全大寫和下劃線分隔
- 變量名稱應該具有描述性，避免縮寫和簡寫

## 代碼風格
- 使用 4 個空格進行縮進
- 行長度控制在 120 字符以內
- 使用 f-strings 進行字符串格式化
- 使用類型注解提高代碼可讀性
- 使用具有描述性的變量和函數名稱

## 日誌記錄
- 使用結構化日誌記錄，包含事件類型、消息和相關數據
- 使用適當的日誌級別：INFO 用於正常流程，WARNING 用於可處理的問題，ERROR 用於需要注意的錯誤
- 包含足夠的上下文信息以便於故障排除
- 使用 Cloud Logging 作為主要日誌記錄工具，失敗時回退到本地日誌記錄

## 異步處理
- 使用 asyncio 進行異步處理
- 使用適當的並行度控制，避免過度並行導致資源耗盡
- 實現適當的錯誤處理和重試機制
- 避免在異步上下文中使用同步調用

## 處理偏好
- 使用批量處理提高效率
- 盡量減少 API 調用次數以控制成本
- 實現適當的重試策略處理臨時性錯誤
- 處理過程應該是幂等的，重複處理不會產生不同的結果

## 數據處理
- 使用 pandas 進行數據處理和轉換
- 使用標準化的數據格式和結構
- 實現適當的數據驗證和清洗
- 處理數值類型和日期類型時要特別小心

## 文檔偏好
- 每個文件頂部應該有簡短的描述性注釋
- 複雜函數和類應該有文檔字符串
- 文檔應該包含足夠的信息以便於理解代碼的用途和使用方法
- 更新代碼時同時更新相關文檔

## 安全偏好
- API 密鑰和憑證應該通過環境變量或專用的憑證管理服務獲取
- 敏感數據不應該硬編碼在代碼中
- 所有用戶輸入應該經過適當的驗證和清洗
- 遵循最小權限原則進行權限管理

## 項目結構
- 保持模塊化設計，每個文件有明確的職責
- 使用適當的抽象層次，避免過度抽象
- 相關功能應該放在一起，避免分散
- 保持代碼的整潔和可維護性

## 待改進項
- GMC 商品分類的準確性需要進一步提高
- 成本監控機制需要完善
- 標籤質量評估機制需要建立
- 自動化測試覆蓋率需要提高 