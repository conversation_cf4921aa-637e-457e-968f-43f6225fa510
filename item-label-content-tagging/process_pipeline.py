import logging
import pandas as pd
import json
from datetime import datetime, timed<PERSON><PERSON>
from pytz import timezone
from label_handler import MediaGPTHandler, EcGP<PERSON>and<PERSON>
from gmc_label_processor import process_gmc_category_predictions
from utils import *
from config import GMC_CATEGORY_DICT_PATH

async def process_media_pipeline(item_df, openai_client):
    dataset = get_media_dataset(item_df)
    logging.info({
        "event": "process_start",
        "type": "media",
        "message": f"共有 {len(dataset)} 筆資料待貼標"
    })
    media_handler = MediaGPTHandler(api_key=openai_client.api_key, max_concurrent_requests=50)

    # Process analysis gate
    content_analysis_gate_responses = await media_handler.process_and_request(
        dataset=dataset,
        item_id_to_something_map=media_handler.item_id_to_analysis_gate_map,
        system_prompt=media_handler.analysis_gate_system_prompt,
        step=1
    )

    # Update dataset with analysis gate results
    dataset = [
        {**data, 'if_worth_analysis': media_handler.item_id_to_analysis_gate_map[data['id']]['gate']}
        for data in dataset
    ]

    # Separate datasets based on worth analysis
    n_worth_dataset = [data for data in dataset if not data['if_worth_analysis']]
    worth_dataset = [data for data in dataset if data['if_worth_analysis']]

    # Process labels for worth dataset
    article_label_responses = await media_handler.process_and_request(
        dataset=worth_dataset,
        item_id_to_something_map=media_handler.item_id_to_label_map,
        generate_prompt_func=media_handler.generate_labels_prompt,
        system_prompt=media_handler.article_system_prompt,
        step=1
    )

    worth_dataset = [
        {**data, 'structured_labels': media_handler.item_id_to_label_map[data['id']]}
        for data in worth_dataset
    ]
    
    gate_cost = sum(media_handler.count_response_cost(res) for res in content_analysis_gate_responses[0] if res)
    label_cost = sum(media_handler.count_response_cost(res) for res in article_label_responses[0] if res)
    total_cost = gate_cost + label_cost
    
    logging.info({
        "event": "cost_summary",
        "message": f"總成本: NT${total_cost:.2f}"
    })
    logging.info({
        "event": "cost_summary",
        "message": f"每項平均成本: NT${total_cost / len(dataset):.2f}"
    })
    logging.info({
        "event": "process_complete",
        "type": "media",
        "message": "文章貼標完成"
    })

    # 媒體不需要貼標 gmc category
    # 處理其他需要被記錄的欄位
    dataset_df = pd.DataFrame(worth_dataset)

    # 商品價格
    # 媒體沒有商品價格
    dataset_df['price'] = None

    # 貼標時間
    current_date = datetime.now(timezone('UTC')).date()
    dataset_df['label_updated_at'] = current_date

    # 版本
    dataset_df['version'] = "1.1"

    # 因為貼標的商品，都是昨天的熱門瀏覽商品或文章，因此最後出現時間會是昨天的日期
    yesterday_date = datetime.now(timezone('UTC')).date() - timedelta(days=1)
    dataset_df['last_event_at'] = yesterday_date

    # 使用 date_format 參數來正確處理日期格式
    worth_dataset = json.loads(
        dataset_df.to_json(
            orient='records',
            date_format='iso'
        )
    )

    return worth_dataset, n_worth_dataset

async def process_ec_pipeline(item_df, openai_client, gsio):
    dataset = get_ec_dataset(item_df)
    logging.info({
        "event": "process_start",
        "type": "ec",
        "message": f"共有 {len(dataset)} 筆資料待貼標"
    })
    ec_handler = EcGPTHandler(api_key=openai_client.api_key, max_concurrent_requests=50)

    # Process labels for EC dataset
    ec_label_responses = await ec_handler.process_and_request(
        dataset=dataset,
        item_id_to_something_map=ec_handler.item_id_to_label_map,
        generate_prompt_func=ec_handler.generate_ec_labels_prompt,
        system_prompt=ec_handler.product_system_prompt,
        step=1
    )

    # Process categories for EC dataset
    ec_category_responses = await ec_handler.process_and_request(
        dataset=dataset,
        item_id_to_something_map=ec_handler.item_id_to_category_map,
        generate_prompt_func=ec_handler.generate_ec_labels_prompt,
        system_prompt=ec_handler.category_system_prompt,
        step=1
    )

    dataset = [
        {**data, 'structured_labels': ec_handler.item_id_to_label_map[data['id']]}
        for data in dataset
    ]

    dataset = [
        {**data, 'category': ec_handler.item_id_to_category_map[data['id']]}
        for data in dataset
    ]

    # 將 Category 合併到 structured_labels 的 ContentTags 底下
    dataset = [
        {
            **data,
            'structured_labels': {
                **data['structured_labels'],
                'ContentTags': {
                    **data['structured_labels'].get('ContentTags', {}),
                    **data.pop('category')
                }
            }
        } if 'category' in data else data
        for data in dataset
    ]

    # 刪除 category 的 key
    dataset = [{k: v for k, v in data.items() if k != 'category'} for data in dataset]

    label_cost = sum(ec_handler.count_response_cost(res) for res in ec_label_responses[0] if res)
    category_cost = sum(ec_handler.count_response_cost(res) for res in ec_category_responses[0] if res)
    total_cost = label_cost + category_cost

    logging.info({
        "event": "cost_summary",
        "message": f"總成本: NT${total_cost:.2f}"
    })
    logging.info({
        "event": "cost_summary",
        "message": f"每項平均成本: NT${total_cost / len(dataset):.2f}"
    })
    logging.info({
        "event": "process_complete",
        "type": "ec",
        "message": "第一階段: 商品貼標完成"
    })

    # 貼標 gmc category
    dataset = await process_gmc_category_predictions(
        openai_client=openai_client,
        item_data=dataset,
        gsio=gsio,
        gmc_category_dict_gcs_path=GMC_CATEGORY_DICT_PATH
    )
    logging.info({
        "event": "process_complete",
        "type": "ec",
        "message": "第二階段: GMC 商品種類貼標完成"
    })

    # 處理其他需要被記錄的欄位
    dataset_df = pd.DataFrame(dataset)

    # 商品價格
    item_name_to_price_map = item_df.set_index('name')['price'].to_dict()
    # 將 price 轉換成數字，並保留空值
    dataset_df['price'] = dataset_df['name'].map(item_name_to_price_map)
    dataset_df = dataset_df[~dataset_df['price'].isnull()]
    dataset_df['price'] = dataset_df['price'].astype('int64')

    # 貼標時間
    current_date = datetime.now(timezone('UTC')).date()    
    dataset_df['label_updated_at'] = current_date

    # 版本
    dataset_df['version'] = "1.1"

    # 因為貼標的商品，都是昨天的熱門瀏覽商品或文章，因此最後出現時間會是昨天的日期
    yesterday_date = datetime.now(timezone('UTC')).date() - timedelta(days=1)
    dataset_df['last_event_at'] = yesterday_date

    # gmc 類別
    # 如果找不到對應的 key，則會回傳空字串
    dataset_df['gmc_category_l1'] = dataset_df['gmc_category'].map(lambda x: x['l1'] if x is not None else None)
    dataset_df['gmc_category_l2'] = dataset_df['gmc_category'].map(lambda x: x['l2'] if x is not None else None)
    dataset_df['gmc_category_l3'] = dataset_df['gmc_category'].map(lambda x: x['l3'] if x is not None else None)
    dataset_df.drop(columns=['gmc_category'], inplace=True)

    # 使用 date_format 參數來正確處理日期格式
    dataset = json.loads(
        dataset_df.to_json(
            orient='records',
            date_format='iso'
        )
    )

    return dataset, []  # Return an empty list for n_worth_dataset

def filter_structured_labels(dataset, allowed_keys):
    """
	功能: 過濾結構化標籤中不在允許列表中的鍵。

	參數:
    - dataset: 包含結構化標籤的數據集
    - allowed_keys: 一個字典，包含兩個標籤類型作為鍵，和它們各自允許的鍵列表作為值
    """
    for data in dataset:
        for label_type, keys in allowed_keys.items():
            if label_type in data['structured_labels']:
                data['structured_labels'][label_type] = {
                    # 僅保留在 allow_keys 中的 key 和其 value
                    key: value for key, value in data['structured_labels'][label_type].items() if key in keys
                }

async def process_dataset(item_df, media_or_ec_gate, openai_client, gsio):
    allowed_keys = {
        'media': {
            'AudienceProfile': ['Interests', 'OccupationalTraits', 'PersonalOpinions'],
            'ContentTags': ['Purpose', 'PotentialIssues', 'Category', 'Tone']
        },
        'reurl': {
            'AudienceProfile': ['Interests', 'OccupationalTraits', 'PersonalOpinions'],
            'ContentTags': ['Purpose', 'PotentialIssues', 'Category', 'Tone']
        },
        'ec': {
            'AudienceProfile': ['Interests', 'PotentialPainPoints', 'BrowsingMotivations'],
            'ContentTags': ['Features', 'MarketSegmentation', 'Industry', 'Category']
        },
        'single_ec': {
            'AudienceProfile': ['Interests', 'PotentialPainPoints', 'BrowsingMotivations'],
            'ContentTags': ['Features', 'MarketSegmentation', 'Industry', 'Category']
        }
    }

    pipeline = process_media_pipeline if (media_or_ec_gate == 'media') or (media_or_ec_gate == 'reurl') else process_ec_pipeline
    dataset_type = media_or_ec_gate
    
    # 只有 ec 需要提供 gsio
    if media_or_ec_gate == 'ec' or media_or_ec_gate == 'single_ec':
        worth_dataset, n_worth_dataset = await pipeline(item_df, openai_client, gsio)
    else:
        worth_dataset, n_worth_dataset = await pipeline(item_df, openai_client)
        
    filter_structured_labels(worth_dataset, allowed_keys[dataset_type])
    
    if dataset_type == 'media' or dataset_type == 'reurl':
        worth_dataset = [{k: v for k, v in data.items() if k != 'if_worth_analysis'} for data in worth_dataset]
    
    # 產生 encoded_id ("item_name:ec_id")
    for data in worth_dataset:
        data['encoded_id'] = generate_encoded_id(data['name'], data['ec_id'])
        data['item_type'] = media_or_ec_gate

    return worth_dataset, n_worth_dataset