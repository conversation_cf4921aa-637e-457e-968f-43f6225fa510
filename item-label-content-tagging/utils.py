import json
import uuid
import base64
from datetime import datetime
from pytz import timezone
import pandas as pd
import pandas_gbq
from google.cloud import bigquery
from bigquery_handler import AdBigQueryHandler

def check_media_or_not(ecid):
    
	media_ecids = [
		2851, # -- Setn
		1347, # -- BusinessWeekly
		1604, # -- Ctee
		2625, # -- CMoney
		2913, # -- Babyhome
		3093, # -- MamiGuide
		3195, # -- Reurl
		3542 # -- VMFive
	]

	return ecid in media_ecids

def read_json(file_path):
    with open(file_path, 'r') as f:
        return json.load(f)

# encode id 相關
def generate_unique_product_id():
    # 使用 uuid4 生成一個隨機的 UUID
    return str(uuid.uuid4())

def generate_encoded_id(item_name: str, ec_id: str) -> str:
    # 避免 item_name 過長，固定只取前 50 個字元，並跟 ec_id串接成字串
    combined = f"{item_name[:50]}:{ec_id}"
    
    # 轉換為 bytes 並進行 base64 編碼
    encoded_bytes = base64.b64encode(combined.encode('utf-8'))
    
    # 將結果轉回字串
    encoded_id = encoded_bytes.decode('utf-8')
    
    return encoded_id

def decode_encoded_id(encoded_id: str) -> tuple[str, str]:
    # 解碼
    decoded = base64.b64decode(encoded_id).decode('utf-8')
    
    # 分割字串
    item_name, ec_id = decoded.split(':')
    
    return item_name, ec_id

def get_media_dataset(df):
    dataset = [
        {"id": row['unique_id'], "name": row['name'], "ec_id": row['ec_id']} for _, row in df.iterrows()
    ]
    return dataset

def get_ec_dataset(df):
    dataset = [
        {"id": row['unique_id'], "name": row['name'], "price": row['price'], "ec_id": row['ec_id']} for _, row in df.iterrows()
    ]
    return dataset

def map_ec_name(df, ec_info_path):
    ec_info_df = pd.read_csv(ec_info_path)
    ec_id_to_name_map = ec_info_df.set_index('id')['name'].to_dict()
    ec_id_to_name_map[3542] = "第五代虛擬科技"
    df['ec_name'] = df['ec_id'].map(ec_id_to_name_map)
    return df

def enrich_with_industry_info(df, google_credentials, industry_path):
    industry_df = pd.read_csv(industry_path)
    industry_id_to_name_map = industry_df.set_index('code')['name'].to_dict()

    bq_client = bigquery.Client(credentials=google_credentials)
    query = "SELECT * FROM `gothic-province-823.tagtoo_from_cloudsql.ECID_to_IndustryID`"
    ec_industry_df = bq_client.query(query).to_dataframe()

    ec_id_to_industry_id_map = ec_industry_df.set_index('ec_id')['industry_id'].to_dict()
    df['industry_id'] = df['ec_id'].map(ec_id_to_industry_id_map)
    df['industry_name'] = df['industry_id'].map(industry_id_to_name_map)
    df['industry_id'] = df['industry_id'].astype(int)
    return df

def enrich_dataset_with_ec_and_industry_info(df, item_df, google_credentials, ec_info_path, industry_path):
    # 映射 ec_id 到 ec_name
    item_id_to_ecid_map = item_df.set_index('unique_id')['ec_id'].to_dict()
    df['ec_id'] = df['id'].map(item_id_to_ecid_map)
    df = map_ec_name(df, ec_info_path)

    # 增強行業信息
    df = enrich_with_industry_info(df, google_credentials, industry_path)

    return df

def process_and_upload_n_worth_dataset(n_worth_dataset, item_df, google_credentials, ec_info_path, industry_path, project_id, table_id, schema_data):
    n_worth_df = pd.DataFrame.from_dict(n_worth_dataset)
    n_worth_df = enrich_dataset_with_ec_and_industry_info(n_worth_df, item_df, google_credentials, ec_info_path, industry_path)
    n_worth_df.drop('if_worth_analysis', axis=1, inplace=True)
    # 上傳到 BigQuery
    pandas_gbq.to_gbq(n_worth_df, table_id, project_id=project_id, credentials=google_credentials, table_schema=schema_data, if_exists='append')

def process_worth_dataset(worth_dataset, item_df, google_credentials, ec_info_path, industry_path):
    label_df = pd.DataFrame.from_dict(worth_dataset)
    label_df = enrich_dataset_with_ec_and_industry_info(label_df, item_df, google_credentials, ec_info_path, industry_path)
    return json.loads(label_df.to_json(orient="records"))

# 上傳到 BigQuery
def upload_to_gcs_and_load_to_bigquery(worth_dataset, google_credentials, project_id, gsio, dataset_id, table_name, media_or_ec_gate):
	# 有 JSON 欄位的資料用 pandas_gbq 有錯誤，直接用 jsonl 上傳
    # 初始化 BigQuery 客戶端和處理器
    bq_client = bigquery.Client(credentials=google_credentials, project=project_id)
    bq_handler = AdBigQueryHandler(client=bq_client)

    # 設定路徑
    current_time = datetime.now(timezone('Asia/Taipei')).strftime("%Y%m%d_%H%M")
    local_jsonl_path = f"/tmp/{media_or_ec_gate}_{current_time}.jsonl"
    gcs_jsonl_path = f"gs://tagtoo-ml-workflow/item_label_content_tagging/results/{media_or_ec_gate}/{current_time}.jsonl"

    # 將數據轉換為 JSONL 並上傳到 GCS
    bq_handler.data_to_jsonl(worth_dataset, local_jsonl_path)
    gsio.upload_file(gsuri=gcs_jsonl_path, localpath=local_jsonl_path)

    # 確認文件格式為 NDJSON
    assert bq_handler.is_ndjson(local_jsonl_path)
    
	# 定義 BigQuery 表結構
    label_schema = [
		# 基本資訊
		bigquery.SchemaField("id", "STRING", mode="REQUIRED"),
        bigquery.SchemaField("encoded_id", "STRING", mode="NULLABLE"),
		bigquery.SchemaField("name", "STRING", mode="REQUIRED"),
		bigquery.SchemaField("ec_name", "STRING", mode="NULLABLE"),
		bigquery.SchemaField("ec_id", "INTEGER", mode="NULLABLE"),
		bigquery.SchemaField("industry_id", "INTEGER", mode="NULLABLE"),
		bigquery.SchemaField("industry_name", "STRING", mode="NULLABLE"),
		
		# JSON 欄位
		bigquery.SchemaField("structured_labels", "JSON", mode="REQUIRED"),
		
		# 分類欄位
		bigquery.SchemaField("gmc_category_l1", "STRING", mode="NULLABLE"),
		bigquery.SchemaField("gmc_category_l2", "STRING", mode="NULLABLE"),
		bigquery.SchemaField("gmc_category_l3", "STRING", mode="NULLABLE"),
		
		# 時間欄位
		bigquery.SchemaField("label_updated_at", "DATETIME", mode="NULLABLE"),
		bigquery.SchemaField("last_event_at", "DATETIME", mode="NULLABLE"),
		
		# 數值欄位
		bigquery.SchemaField("price", "INTEGER", mode="NULLABLE"),
		
		# 版本欄位
		bigquery.SchemaField("version", "STRING", mode="NULLABLE"),

        # 商品類別
        bigquery.SchemaField("item_type", "STRING", mode="NULLABLE"),
	]

    # 將 JSONL 文件加載到 BigQuery
    bq_handler.load_jsonl_to_bigquery(
        gcs_jsonl_path, 
        dataset_id, 
        table_name, 
        label_schema, 
        location="US"
    )