def make_ec_query(start_date: str, end_date: str, topk: int = 1000) -> str:

    sql = f"""
        SELECT
            items.id,
            items.name,
            items.price,
            te.ec_id,
            COUNT(*) view_count
        FROM
            `tagtoo-tracking.event_prod.tagtoo_event` te, UNNEST(event.items) AS items
        LEFT JOIN
            `tagtoo-ml-workflow.data_prod.item_labels` tag
        ON
            items.name = tag.name
        LEFT JOIN
            `tagtoo-ml-workflow.data_prod.item_labels_unworthy_items` unworth
        ON
            items.name = unworth.name
        WHERE
            DATE(event_time, 'Asia/Taipei') >= "{start_date}"
            AND DATE(event_time, 'Asia/Taipei') <= "{end_date}"
            AND event.name = "view_item"
            AND items.name != ""
            AND items.name != "product"
            AND tag.name IS NULL
            AND unworth.name IS NULL
            AND te.ec_id NOT IN (
                2851, -- Setn
                1347, -- BusinessWeekly
                1604, -- Ctee
                2625, -- <PERSON><PERSON><PERSON>
                2913, -- Babyhome
                3093, -- MamiGuide
                3195, -- Reurl
                3542, -- VMFive
                3037 -- LineTV
            )
        GROUP BY
            items.id,
            items.name,
            items.price,
            te.ec_id
        ORDER BY
            view_count DESC
        LIMIT
            {topk}
    """

    return sql

def make_media_query(start_date: str, end_date: str, topk: int = 1000) -> str:

    sql = f"""
        SELECT
            items.id,
            items.name,
            te.ec_id,
            COUNT(*) view_count
        FROM
            `tagtoo-tracking.event_prod.tagtoo_event` te, UNNEST(event.items) AS items
        LEFT JOIN
            `tagtoo-ml-workflow.data_prod.item_labels` tag
        ON
            items.name = tag.name
        LEFT JOIN
            `tagtoo-ml-workflow.data_prod.item_labels_unworthy_items` unworth
        ON
            items.name = unworth.name
        WHERE
            DATE(event_time, 'Asia/Taipei') >= "{start_date}"
            AND DATE(event_time, 'Asia/Taipei') <= "{end_date}"
            AND event.name = "view_item"
            AND items.name != ""
            AND items.name != "product"
            AND tag.name IS NULL
            AND unworth.name IS NULL
            AND te.ec_id IN (
                2851, -- Setn
                1347, -- BusinessWeekly
                1604, -- Ctee
                2625, -- CMoney
                2913, -- Babyhome
                3093, -- MamiGuide
                3195, -- Reurl
                3542, -- VMFive
                3037 -- LineTV
            )
        GROUP BY
            items.id,
            items.name,
            te.ec_id
        ORDER BY
            view_count DESC
        LIMIT
            {topk}
    """

    return sql

def make_last_event_at_update_statistics_query() -> str:
    # 取得 last_event_at 更新統計資料表中，最後一次更新日期
    # execution_time 已經是台灣時間
    return """
        SELECT DISTINCT DATE(execution_time) AS update_date
        FROM `tagtoo-ml-workflow.monitoring.item_labels_last_event_at_update_statistics`
    """

def make_singel_ec_query(start_date: str, end_date: str, ec_id: int, topk: int) -> str:

    sql = f"""
        SELECT
            items.id,
            items.name,
            items.price,
            te.ec_id,
            COUNT(*) view_count
        FROM
            `tagtoo-tracking.event_prod.tagtoo_event` te, UNNEST(event.items) AS items
        LEFT JOIN
            `tagtoo-ml-workflow.data_prod.item_labels` tag
        ON
            items.name = tag.name
        LEFT JOIN
            `tagtoo-ml-workflow.data_prod.item_labels_unworthy_items` unworth
        ON
            items.name = unworth.name
        WHERE
            DATE(event_time, 'Asia/Taipei') >= "{start_date}"
            AND DATE(event_time, 'Asia/Taipei') <= "{end_date}"
            AND event.name = "view_item"
            AND items.name != ""
            AND items.name != "product"
            AND tag.name IS NULL
            AND unworth.name IS NULL
            AND te.ec_id = {ec_id}
        GROUP BY
            items.id,
            items.name,
            items.price,
            te.ec_id
        ORDER BY
            view_count DESC
        LIMIT
            {topk}
    """

    return sql

# 在 reurl 還沒整合到 integrated_event 大表之前，暫時用這個 query
def make_reurl_query(start_date: str, end_date: str, topk: int) -> str:

    sql = f"""
        WITH
        agg_link AS (
        SELECT
            JSON_EXTRACT_SCALAR(arg, '$.link') AS link,
            COUNT(*) link_count
        FROM
            `gothic-province-823.tagtooad.partner_uid_mapping`
        WHERE
            partner_id = '1009'
            AND DATE(created, 'Asia/Taipei') >= '{start_date}'
            AND DATE(created, 'Asia/Taipei') <= '{end_date}'
        GROUP BY
            link
        )

        SELECT
            CONCAT(title, '::', description) as name,
            1009 as ec_id
        FROM
            `tagtoo-tracking.tagtooad.reurl_link_metadata` reurl
        JOIN
            agg_link
        USING
            (link)
        LEFT JOIN
            `tagtoo-ml-workflow.data_prod.item_labels_unworthy_items` unworth
        ON
            reurl.link = unworth.name
        WHERE
            unworth.name IS NULL
            AND reurl.title IS NOT NULL
            AND reurl.description IS NOT NULL
        ORDER BY
            link_count DESC
        LIMIT
            {topk}
    """
    return sql