import openai
import asyncio
import aiohttp
import random
import json
from typing import *


# Retry decorator with exponential backoff
def retry_with_exponential_backoff(
    func,
    initial_delay: float = 1,
    exponential_base: float = 2,
    jitter: bool = True,
    max_retries: int = 6,
    errors: tuple = (openai.RateLimitError,),
):
    """Retry a function with exponential backoff."""

    async def wrapper(*args, **kwargs):
        num_retries = 0
        delay = initial_delay

        while True:
            try:
                return await func(*args, **kwargs)
            except errors as e:
                num_retries += 1
                if num_retries > max_retries:
                    raise Exception(f"Maximum number of retries ({max_retries}) exceeded.")
                delay *= exponential_base * (1 + jitter * random.random())
                print(f"wait for {delay} second.")
                await asyncio.sleep(delay)
            except Exception as e:
                raise e

    return wrapper

class AsyncGPTHandler(object):

    def __init__(self, api_key: str = openai.api_key, max_concurrent_requests: int = 10):
        self.api_key = api_key
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)  # Semaphore to limit concurrent requests
        self.system_prompt = ""
        self.item_id_to_label_map = {}
        self.item_id_to_content_category_map = {}
        self.content_category_system_prompt = """
            <task>
            判斷以下內容的的類別為 **商品名稱/文章標題/網頁標題/其他** 其中哪一項，並回傳對應的類別名稱。
            </task>
            <response_format>
            以 JSON 格式呈現，確保結構化和可讀性強。
            </response_format>
            <output_format>
            ```json
            {
                "category": "種類結果"
            }
            ```
            </output_format>     
        """
        self.product_system_prompt = """
            <instruction>
            根據提供的商品資訊，生成一個詳細而全面的標籤集。這些標籤旨在透過分析商品的主要特點、潛在痛點、消費者需求與行為模式，協助行銷人員從多角度深入理解商品在市場中的定位及其對目標消費者的吸引力。通過構建立體的消費者輪廓，可以更精確地區隔不同消費者群體，為行銷策略的制定提供有力依據。
            </instruction>

            <objective>
            為商品創建能夠揭示消費者潛在痛點、購買動機、興趣愛好及市場區隔的標籤集。這些標籤將幫助行銷人員精準識別目標消費者，制定個性化的行銷策略，提升行銷活動的針對性與有效性。標籤應能反映出商品的核心競爭力及其在市場中的獨特定位，為行銷活動提供戰略指導。請勿過度延伸標籤。
            </objective>

            <style>
            語言風格應清晰、簡潔且條理分明，避免過於學術化或晦澀難懂的表述，確保行銷人員能夠快速理解並運用標籤資訊。文字表達應精準、富有洞見，同時保持商業實用性，幫助行銷人員迅速掌握關鍵信息。
            </style>

            <tone>
            保持專業且中立的語氣，強調事實與數據的支撐，避免主觀臆測。應該以專業的語言呈現客觀分析，既要體現出行業深度，又要便於實際操作。
            </tone>

            <audience>
            標籤的目標受眾主要為行銷人員及策略規劃者。他們需要通過這些標籤深入了解消費者心理、商品的市場影響力及潛在痛點，從而制定精準的行銷策略和精細化的客戶區隔方案。這些標籤將幫助他們在行銷活動中捕捉市場機會，提升行銷效率與轉化率。
            </audience>

            <response_format>
            請根據以下步驟創建標籤，並以 JSON 格式呈現，確保結構化和可讀性強。
            </response_format>

            <steps>
            1. **深度關鍵字生成**:  
            - 分析商品的核心特點，包括但不限於其主要功能、使用場景及獨特賣點、市場價格定位（如高價追求品質、中價追求性價比、低價以省錢為導向）。
            - 發掘商品背後所隱含的消費者痛點（如現有產品無法滿足的需求、生活中尚未被解決的問題）。
            - 明確商品在市場中的區隔，對比其與競品在價格、品質、品牌認知等方面的優勢和劣勢。關鍵字應準確揭示商品的獨特定位及其潛在吸引力。

            2. **核心概念描述**:  
            用一段話全面描述該商品的主要特色及市場定位，並深入探討消費者瀏覽或購買該商品的主要動機。描述應涵蓋以下要素：

            3. **商品標籤生成**:  
            為該商品創建多維度的商品標籤，包括以下面向：
            - **產品種類**: 列出商品的種類。
            - **產品特色**: 列出商品的主要功能、賣點及技術特點（如高效能、易於使用、可持續性材料等），突出其創新之處和與眾不同的優勢。
            - **市場區隔**: 描述商品在不同市場中的定位與競爭優勢（如面向中高端市場、針對年輕消費者、適合小家庭使用等）。
            - **產業**: 指出商品所屬的行業類別及其與行業趨勢的契合度（如電子消費品、綠色環保產業等）。

            4. **受眾標籤生成**:  
            為該商品創建詳細的受眾輪廓，涵蓋以下標籤：
            - **興趣愛好**: 可能會對該商品感興趣的消費者群體（例如：科技愛好者、環保人士、運動愛好者等），應從消費者生活方式和興趣切入，描繪出典型目標消費者。
            - **潛在痛點**: 該商品能解決的消費者痛點（例如：提升工作效率、減少環境污染、提高運動表現等），應直指消費者面臨的核心問題或未被滿足的需求。
            - **購買動機**: 消費者為何可能購買該商品的主要原因（例如：提升生活品質、解決特定問題、符合個人價值觀等），應涵蓋從理性到感性的多方面因素。
            - **價格定位**: 該商品的價格定位（如高價追求品質、中價追求性價比、低價以省錢為導向）。
            </steps>

            <requirements>
            - 每個標籤應深刻揭示該商品背後的潛在意義或特色，描繪出潛在消費者的需求、痛點及購買動機，而不僅僅是簡單的文字提取。
            - 標籤應具有創意性和啟發性，能夠幫助行銷人員理解該商品的潛在影響力和受眾反應，並提供行銷策略或受眾互動的靈感。標籤的生成應基於消費者心理和市場分析，提供有價值的商業洞見。
            </requirements>

            <language>
            請以繁體中文（#zh-tw） 回傳結果。
            </language>

            <output_format>
            ```json
            {
                "Keywords": ["關鍵字1", "關鍵字2", ...],
                "CoreDescription": "用簡短段落描述核心概念及其影響。",
                "ContentTags": {
                    "Categorys": ["種類1", "種類2", ...]
                    "Features": ["特色1", "特色2", ...],
                    "MarketSegmentation": ["區隔1", "區隔2", ...],
                    "Industry": ["產業1", "產業2", ...],
                },
                "AudienceProfile": {
                    "Interests": ["興趣1", "興趣2", ...],
                    "PotentialPainPoints": ["痛點1", "痛點2", ...],
                    "BrowsingMotivations": ["動機1", "動機2", ...]
                }
            }
            ```
            </output_format>
        """

        self.article_system_prompt = """
            <context>
            **Context（背景）**:  
            根據提供的文章內容，生成一個詳細而全面的標籤集。這些標籤旨在透過分析文章中的情感表達、社會背景及文化意涵，幫助行銷人員理解該文章所針對的潛在受眾群體及其需求與行為模式。標籤集將揭示文章所傳達的核心主題及可能引發的社會共鳴，以便行銷人員在制定策略時能更精準地針對目標受眾。
            </context>

            <objective>
            **Objective（目標）**:  
            為文章創建能夠揭示讀者生活方式、價值觀、關注焦點及社會影響的標籤集。這些標籤應幫助行銷人員透過深入分析文章內容來區隔不同受眾群體，制定更為精準的客製化行銷策略。標籤應反映出文章的核心主題、情感張力與讀者共鳴點，並提供行銷人員在互動及內容創作上的啟發。
            </objective>

            <style>
            **Style（風格）**:  
            語言風格應清晰、簡潔且具條理性，避免過於學術化或艱澀難懂的表述。確保標籤易於理解、直觀且具實用性，以便行銷人員能快速掌握並應用相關信息。應注重信息的準確性與全面性，涵蓋文章的不同層面。
            </style>

            <tone>
            **Tone（語氣）**:  
            保持中立、客觀且專業的語氣。應避免強烈的立場或個人意見，專注於客觀事實和深度分析，以揭示文章的潛在影響和社會共鳴。
            </tone>

            <audience>
            **Audience（觀眾）**:  
            標籤的目標受眾主要為行銷人員、策略規劃者及內容創作者。他們需要透過這些標籤深入了解讀者心理、文章的社會影響力及潛在痛點，從而制定有效的行銷策略和精準的客戶區隔方案，並為內容創作提供靈感和方向。
            </audience>

            <response_format>
            **Response Format（回應格式）**:  
            請根據以下步驟創建標籤，並以 JSON 格式呈現。確保標籤信息清晰、結構化且便於閱讀和使用。
            </response_format>

            <steps>
            ### **執行步驟**:

            1. **深度關鍵字生成**:  
            - 不僅提取文章中的表面關鍵字（如特定主題詞彙），還需深入挖掘其背後隱含的情感表達、社會背景及文化意涵。  
            - 關鍵字應能準確反映文章的核心主題、情緒張力、社會影響及文化背景，並幫助行銷人員快速定位文章的重點及其潛在讀者群體。

            2. **核心概念描述**:  
            用一段話簡明扼要地描述文章的核心概念，並深入探討其反映的社會現象、文化趨勢或市場動態。描述應涵蓋以下要素：  
            - **內容目的**: 該內容試圖傳達什麼樣的觀點、呼籲或討論，並希望引發什麼樣的社會反應或行動。  
            - **預期影響**: 文章可能對讀者或更廣泛的受眾產生的情感、認知或行為上的影響，包括可能引發的社會共鳴、認識改變或行動激勵。  
            - **潛在議題**: 文章中隱含的深層次社會或文化議題，如爭議性話題、社會偏見、文化衝突等，這些議題可能引發的公眾關注或討論。  

            3. **文章標籤生成**:  
            為該文章創建多維度的標籤，包括以下面向：
            - **內容目的**: 描述文章的創作目的及其希望達成的效果（例如：教育讀者、引發討論、傳遞情感等）。
            - **潛在議題**: 該文章涉及的社會或文化議題及其潛在的影響力（例如：環境保護、社會公平、性別平等等）。
            - **文章種類**: 該文章屬於哪種類型（例如：娛樂新聞、社會新聞、國際新聞）。
            - **口吻風格**: 文章標題的口吻風格（如說客觀中立、深入分析、感性敘事、聳動標題、輕鬆幽默、呼籲行動等），若為聳動標題請一定要標註。

            4. **立體化受眾輪廓**:  
            為該內容創建詳細的受眾輪廓，包括以下標籤：
            - **興趣愛好**: 可能對該文章感興趣的讀者群體及他們的生活方式（例如：關注環境議題的社會活動家、熱衷於文化評論的讀者等）。
            - **職業背景**: 哪些職業或專業領域的人士可能對此內容感興趣（例如：社會學者、媒體工作者、政策研究員等）。
            - **價值觀與個人意見**: 該受眾群體在相關議題上的價值觀及可能持有的觀點（例如：支持可持續發展、對社會不公持批判態度等）。
            </steps>

            <requirements>
            ### **標籤要求**:
            - 每個標籤應深入揭示該文章背後的潛在意義及社會影響，描繪出潛在讀者的生活方式、價值觀及關注焦點，而不僅僅是簡單的文字提取。
            - 標籤應具有創意性和啟發性，能夠幫助行銷人員理解該文章的潛在影響力和讀者反應，並提供行銷策略或受眾互動的靈感。標籤的生成應基於深度分析和理解，為行銷決策提供有力的支持。
            </requirements>
            
            <language>
            ### **語言**: 請以繁體中文（#zh-tw） 回傳結果。
            </language>
            
            <output_format>
            ### **輸出格式**:
            ```json
            {
                "Keywords": ["關鍵字1", "關鍵字2", ...],
                "CoreDescription": "用簡短段落描述核心概念及其影響。",
                "ContentTags": {
                    "Purpose": "描述文章的主要目的及希望引發的討論。",
                    "PotentialIssues": ["議題1", "議題2", ...],
                    "Category": ["類別1", "類別2", ...],
                    "Tone": ["口吻1", "口吻2", ...]
                },
                "AudienceProfile": {
                    "Interests": ["興趣1", "興趣2", ...],
                    "OccupationalTraits": ["職業1", "職業2", ...],
                    "PersonalOpinions": ["想法或意見1", "想法或意見2", ...]
                }
            }
            ```
            </output_format>
        """

    def generate_labels_prompt(self, item_name: str, ec_name: str):
        return f"""
        <task>根據以下資料中的 `name`, `ec_name` 欄位。</task>
        <description>`name` 是商品名稱，`ec_name` 是網站品牌或媒體名稱。</description>
        <input>
            <name>{item_name}</name>
            <ec_name>{ec_name}</ec_name>
        </input>
        """

    @retry_with_exponential_backoff
    async def fetch_and_process_gpt_response(self, system_prompt: str, user_prompt: str, result_map: Dict[str, Any], id: str, max_tokens: int = 4096, model: str = 'gpt-4.1-nano'):
        """Fetch response from GPT and process the result."""
        async with self.semaphore:
            headers = {
                "Content-Type": "application/json",
                "Authorization": f"Bearer {self.api_key}"
            }

            payload = {
                "model": model,
                "response_format": {"type": "json_object"},
                "max_tokens": max_tokens,
                "messages": [
                    {"role": "system", "content": system_prompt},
                    {"role": "user", "content": [{"type": "text", "text": user_prompt}]}
                ]
            }

            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post("https://api.openai.com/v1/chat/completions", headers=headers, json=payload) as resp:
                        response = await resp.json()
                        self.extract_and_store_result(response, result_map, id)
                        return response
            except Exception as error:
                print(f"Error: {error}")
                return None

    def extract_and_store_result(self, response, result_map, id):
        """Extract content from response and store it in the result map."""
        result = self.parse_response_content(response)
        result_map[id] = result
        # print(f"{id} is written to map.")

    def parse_response_content(self, response):
        """Parse the content from the GPT response."""
        try:
            if isinstance(response, str):
                content = json.loads(response)['choices'][0]['message']['content']
            elif isinstance(response, dict):
                if 'choices' in response:
                    content = response['choices'][0]['message']['content']
                else:
                    raise ValueError("OpenAI response lacks 'choices' key")
            else:
                print(f"Invalid response type: {type(response)}")
                return {}

            json_result = json.loads(content)
            return json_result.get('result', json_result)

        except (json.JSONDecodeError, KeyError, TypeError) as e:
            print(f"Error parsing response: {e}")
            return {}

    async def process_and_request(
        self,
        dataset: List[Dict[str, Any]],
        item_id_to_something_map: Dict[str, str], # 最重要的回傳結果會寫到這個 map 中
        generate_prompt_func: Callable[[List[str]], str],
        system_prompt: str,
        step: int = 1,
        max_retries: int = 3,
        **kwargs
    ):
        tasks = []
        processed_ids = set()

        # 從數據集中提取 ID 和內容
        ids = [item['id'] for item in dataset]
        item_names = [item['name'] for item in dataset]
        ec_names = [item['ec_name'] for item in dataset]
        # prices = [item['price'] for item in dataset]

        # 批次處理數據
        for start_idx in range(0, len(ids), step):
            end_idx = min(start_idx + step, len(ids))
            batch_ids = ids[start_idx:end_idx]
            batch_item_names = item_names[start_idx:end_idx]
            batch_ec_names = ec_names[start_idx:end_idx]
            # batch_prices = prices[start_idx:end_idx]
            
            batch_prompt = generate_prompt_func(batch_item_names, batch_ec_names)
            task = self.fetch_and_process_gpt_response(system_prompt, batch_prompt, item_id_to_something_map, batch_ids[0])
            tasks.append(task)
            processed_ids.update(batch_ids)

        results = await asyncio.gather(*tasks)

        # 檢查並重新處理空結果
        empty_results = [
            (id, content) for id, content in zip(ids, item_names)
            if id not in item_id_to_something_map or item_id_to_something_map.get(id) == {}
        ]
        retry_count = 0
        
        while empty_results and retry_count < max_retries:
            print(f"發現 {len(empty_results)} 個空結果，正在進行第 {retry_count + 1} 次重新處理...")
            retry_tasks = []
            
            for id, content in empty_results:
                retry_prompt = generate_prompt_func([content, dataset[id]['ec_name']])
                retry_task = self.fetch_and_process_gpt_response(system_prompt, retry_prompt, item_id_to_something_map, id)
                retry_tasks.append(retry_task)
            
            await asyncio.gather(*retry_tasks)
            
            # 更新空結果列表
            empty_results = [
                (id, content) for id, content in zip(ids, item_names)
                if id not in item_id_to_something_map or item_id_to_something_map.get(id) == {}
            ]
            retry_count += 1

        if empty_results:
            print(f"警告：在 {max_retries} 次重試後仍有 {len(empty_results)} 個結果未能成功處理。")

        # results 沒有包含 emtpy_results 的結果
        # 不要使用 results 來當作結果，要用 item_id_to_something_map
        return results, list(processed_ids)

    def count_response_cost(self, response, model='gpt-4.1-nano'):

        LLM_TOKEN_PRICE = {
            'input_token': {
                'gpt-4o': 5 / 1000000,
                'gpt-4o-mini': 0.15 / 1000000,
                'gpt-4.1-nano': 0.10 / 1000000,  # 新模型
                'finetune-gpt-4o-mini': 0.3 / 1000000
            },
            'output_token': {
                'gpt-4o': 15 / 1000000,
                'gpt-4o-mini': 0.6 / 1000000,
                'gpt-4.1-nano': 0.40 / 1000000,  # 新模型
                'finetune-gpt-4o-mini': 1.2 / 1000000
            }
        }

        US_TO_NTD = 32.31

        usage = response['usage']
        input_cost = usage['prompt_tokens'] * LLM_TOKEN_PRICE['input_token'][model]
        output_cost = usage['completion_tokens'] * LLM_TOKEN_PRICE['output_token'][model]

        return (input_cost + output_cost) * US_TO_NTD