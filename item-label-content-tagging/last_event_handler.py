import logging
from google.cloud import bigquery
from datetime import datetime
from pytz import timezone
from make_query import make_last_event_at_update_statistics_query

class UpdateLastEventHandler:
    def __init__(self, client):
        self.client = client
        self.taipei_tz = timezone('Asia/Taipei')
        self.date_format = '%Y-%m-%d'
        self.stats = {}

    def execute_update(self) -> dict:
        """執行更新流程並返回統計資訊"""
        
        try:
            # 創建臨時表並獲取昨日商品數量
            query = """
            -- 創建臨時表
            CREATE OR REPLACE TEMP TABLE yesterday_items AS
            SELECT DISTINCT items.name
            FROM `tagtoo-tracking.event_prod.tagtoo_event` te,
                UNNEST(event.items) AS items
            WHERE DATE(event_time, 'Asia/Taipei') = 
                DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY);

            -- 執行更新
            UPDATE `tagtoo-ml-workflow.data_prod.item_labels` label
            SET last_event_at = DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY)
            WHERE EXISTS (
                SELECT 1
                FROM yesterday_items yi
                WHERE yi.name = label.name
                LIMIT 1
            );

            -- 最後回傳所需的計數結果
            SELECT 
                (SELECT COUNT(*) FROM yesterday_items) as yesterday_items_count,
                (SELECT COUNT(*) 
                FROM `tagtoo-ml-workflow.data_prod.item_labels`
                WHERE DATE(last_event_at) = 
                    DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY)
                ) as updated_count;
            """
            
            result = self.client.query(query).result()
            result = list(result)[0]  # 獲取第一個（也是唯一一個）結果行
            yesterday_items_count = result.yesterday_items_count
            updated_count = result.updated_count

            # 收集統計資訊
            self.stats = {
                'execution_time': datetime.now(self.taipei_tz),
                'yesterday_items_count': yesterday_items_count,
                'updated_count': updated_count,
                'success': True
            }

            logging.info({
                "event": "process_complete",
                "type": "last_event_at",
                "message": "last_event_at 更新成功",
                "stats": self.stats
            })

            # 記錄統計資訊到 BigQuery
            self._log_statistics()

            logging.info({
                "event": "process_complete",
                "type": "last_event_at",
                "message": "統計資訊記錄成功"
            })

            # return self.stats

        except Exception as e:
            logging.error(f"Error during execution: {str(e)}", exc_info=True)
            raise e

    def last_event_at_update_statistics_is_today(self) -> bool:
        """決定是否需要更新 last_event_at"""
        last_event_df = self.client.query_and_wait(make_last_event_at_update_statistics_query()).to_dataframe()
        last_update_date = last_event_df['update_date'].max().strftime(self.date_format)
        current_date = (datetime.now(self.taipei_tz)).strftime(self.date_format)
        return last_update_date == current_date

    def _log_statistics(self):
        """將統計資訊寫入 BigQuery"""
        try:
            log_query = f"""
            INSERT INTO `tagtoo-ml-workflow.monitoring.item_labels_last_event_at_update_statistics`
            (execution_time, yesterday_items_count, updated_count)
            VALUES
            (TIMESTAMP '{self.stats['execution_time'].isoformat()}',
             {self.stats['yesterday_items_count']},
             {self.stats['updated_count']})
            """
            
            self.client.query(log_query).result()
            logging.info("Statistics logged to BigQuery successfully")
        except Exception as e:
            logging.error(f"Error logging statistics: {str(e)}", exc_info=True)
            raise