from google.oauth2 import service_account
import openai
from openai import OpenAI
import json
# from ad.library.src.config import GOOGLE_APPLICATION_CREDENTIALS_PATH, OPENAI_API_KEY_PATH

def get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH):
    # verify account
    google_credentials = service_account.Credentials.from_service_account_file(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    project_id = google_credentials.project_id
    return google_credentials, project_id

def get_openai_credentials(GSIO, OPENAI_API_KEY_PATH):
    # API 金鑰
    key_path = '/tmp/gpt_api_key.json'
    GSIO.download_to_path(gsuri=OPENAI_API_KEY_PATH, localpath=key_path)

    with open(key_path, 'r') as f:
        openai.api_key = json.load(f)['api_key']

    return OpenAI(api_key=openai.api_key)