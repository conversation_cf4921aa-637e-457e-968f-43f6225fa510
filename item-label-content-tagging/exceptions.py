from openai import OpenAIError, APIError
import logging

class OpenAIError(Exception):
    """Base exception class for OpenAI API errors"""
    pass

class BadRequestError(OpenAIError):
    """400: Invalid request parameters"""
    pass

class AuthenticationError(OpenAIError):
    """401: Authentication failed"""
    pass

class PermissionDeniedError(OpenAIError):
    """403: Permission denied"""
    pass

class NotFoundError(OpenAIError):
    """404: Resource not found"""
    pass

class UnprocessableEntityError(OpenAIError):
    """422: Unprocessable entity"""
    pass

class RateLimitError(OpenAIError):
    """429: Rate limit exceeded"""
    pass

class InternalServerError(OpenAIError):
    """500+: Internal server error"""
    pass

class APIConnectionError(OpenAIError):
    """Network connection errors"""
    pass

def classify_error(status_code: int, error_message: str) -> Exception:
    """Classify the API error based on status code and message"""
    error_mapping = {
        400: BadRequestError(f"Bad request: {error_message}"),
        401: AuthenticationError(f"Authentication failed: {error_message}"),
        403: PermissionDeniedError(f"Permission denied: {error_message}"),
        404: NotFoundError(f"Resource not found: {error_message}"),
        422: UnprocessableEntityError(f"Unprocessable entity: {error_message}"),
        429: RateLimitError(f"Rate limit exceeded: {error_message}"),
    }
    
    # Handle 500+ status codes
    if status_code >= 500:
        return InternalServerError(f"Internal server error (status {status_code}): {error_message}")
    
    log_func = error_mapping.get(
        status_code, 
        lambda msg: logging.error(f"Unknown error (status {status_code}): {msg}")
    )
    log_func(error_message)