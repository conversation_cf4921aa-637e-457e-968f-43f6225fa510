from base import AsyncGPTHandler
from typing import List, Dict, Any
import asyncio

class MediaGPTHandler(AsyncGPTHandler):
    
    def __init__(self, api_key, max_concurrent_requests: int = 10):

        super().__init__(api_key, max_concurrent_requests)

        self.analysis_gate_system_prompt = """
        <instruction>
        你是一個專業的內容審核員，請根據以下條件，判斷文章標題是否值得被貼標。
        </instruction>
        <task>
        **任務**：判斷文章標題是否值得被貼標。根據以下條件，回傳 `true` 或 `false` 並以 JSON 格式回傳結果。
        1. **回傳 `true` 的條件**：文章標題應提供足夠的訊息，使得可以合理判斷出使用者瀏覽的議題或興趣。如果標題信息清楚且有明確的方向，回傳 `true`。
        2. **回傳 `false` 的條件**：如果文章標題的信息過於模糊、不足以準確判斷使用者的興趣，則回傳 `false`。
        </task>
        <few_shot_example>
        - 標題：「「斷魂百香果」拼酒賠命！工程師自介「酒精補給隊長」　父慟：兒不酗酒 | 中天新聞網」，因為標題資訊足夠判斷出這是一篇文章或一樣商品，
        因此判斷為 `true`。
        - 標題：「日元盤中貶破145關卡 寫近3周新低 | Anue鉅亨 - 外匯」，因為標題資訊足夠判斷出這是一篇文章或一樣商品，因此判斷為 `true`。
        - 標題：「任務列表 - Hami Point」，因為標題資訊模糊，無法判斷出這是一篇文章或一樣商品，因此判斷為 `false`。
        - 標題：「妞新聞」，因為妞新聞是一個新聞網站，並非一篇文章或一樣商品，因此判斷為 `false`。
        - 標題：「中時新聞網」，因為中時新聞網是一個新聞網站，並非一篇文章或一樣商品，因此判斷為 `false`。
        </few_shot_example>
        <output_format>
        - 回傳結果為 JSON 格式，每個標題對應一個結果。
        - 格式如下：
            ```json
            {
            "gate": true/false
            }
            ```
        </output_format>
        """
        self.item_id_to_analysis_gate_map = {}

    def generate_analysis_gate_prompt(self, item_name):
        return f"""
        <article_name>
        {item_name}
        </article_name>
        """

    async def process_and_request(
        self,
        dataset: List[Dict[str, Any]],
        item_id_to_something_map: Dict[str, str],
        # generate_prompt_func: Callable[[List[str]], str],
        system_prompt: str,
        step: int = 1,
        max_retries: int = 3,
        **kwargs
    ):
        tasks = []
        processed_ids = set()

        # 從數據集中提取 ID 和內容
        ids = [item['id'] for item in dataset]
        item_names = [item['name'] for item in dataset]

        # 批次處理數據
        for start_idx in range(0, len(ids), step):
            end_idx = min(start_idx + step, len(ids))
            batch_ids = ids[start_idx:end_idx]
            batch_item_names = item_names[start_idx:end_idx]
            
            batch_prompt = self.generate_analysis_gate_prompt(batch_item_names)
            task = self.fetch_and_process_gpt_response(system_prompt, batch_prompt, item_id_to_something_map, batch_ids[0])
            tasks.append(task)
            processed_ids.update(batch_ids)

        results = await asyncio.gather(*tasks)

        # 檢查並重新處理空結果
        empty_results = [
            (id, content) for id, content in zip(ids, item_names)
            if id not in item_id_to_something_map or item_id_to_something_map.get(id) == {}
        ]
        retry_count = 0
        
        while empty_results and retry_count < max_retries:
            print(f"發現 {len(empty_results)} 個空結果，正在進行第 {retry_count + 1} 次重新處理...")
            retry_tasks = []
            
            for id, content in empty_results:
                retry_prompt = self.generate_analysis_gate_prompt([content])
                retry_task = self.fetch_and_process_gpt_response(system_prompt, retry_prompt, item_id_to_something_map, id)
                retry_tasks.append(retry_task)
            
            await asyncio.gather(*retry_tasks)
            
            # 更新空結果列表
            empty_results = [
                (id, content) for id, content in zip(ids, item_names)
                if id not in item_id_to_something_map or item_id_to_something_map.get(id) == {}
            ]
            retry_count += 1

        if empty_results:
            print(f"警告：在 {max_retries} 次重試後仍有 {len(empty_results)} 個結果未能成功處理。")

        return results, list(processed_ids)
    

class EcGPTHandler(AsyncGPTHandler):
    
    def __init__(self, api_key, max_concurrent_requests: int = 10):

        super().__init__(api_key, max_concurrent_requests)
        self.item_id_to_category_map = {}
        self.category_system_prompt = """
        <instruction>
        你是一個專業的行銷人員，擁有豐富的商品標籤知識，熟知大小商品種類
        </instruction>
        <task>
        請根據輸入的商品名稱與價格，來判斷商品的種類，結果可以是多種類別，包含大類別與小類別，請盡量列出相符的類別
        </task>
        <output_format>
        ```json
        {"Category": ["商品種類1", "商品種類2", ...]}
        ```
        </output_format>
        
        <example>

        <product_name>
        水原力舒敏潔面乳
        </product_name>
        <product_price>
        490
        </product_price>
        <thought>
        潔面乳的同義字是洗面乳，是一種日常用品，同時也是保養品，從小類別延伸出大類別，就可以列出以下結果
        </thought>
        <result>
        {"Category": ["潔面乳", "洗面乳", "保養品", "日常用品", "保養美容"]}
        </result>
        </example>
        
        <language>
        請以繁體中文（#zh-tw） 回傳結果
        </language>
        """

    def generate_ec_labels_prompt(self, item_name, price):
        return f"""
        <task>
         **說明**: name 是商品名稱，price 是商品價格。
        </task>
        <input>
        <product_name>
        {item_name}
        </product_name>
        <product_price>
        {price}
        </product_price>
        </input>
        """

    async def process_and_request(
        self,
        dataset: List[Dict[str, Any]],
        item_id_to_something_map: Dict[str, str],
        # generate_prompt_func: Callable[[List[str]], str],
        system_prompt: str,
        step: int = 1,
        max_retries: int = 3,
        **kwargs
    ):
        tasks = []
        processed_ids = set()

        # 從數據集中提取 ID 和內容
        ids = [item['id'] for item in dataset]
        item_names = [item['name'] for item in dataset]
        item_prices = [item['price'] for item in dataset]
        
        # 批次處理數據
        for start_idx in range(0, len(ids), step):
            end_idx = min(start_idx + step, len(ids))
            batch_ids = ids[start_idx:end_idx]
            batch_item_names = item_names[start_idx:end_idx]
            batch_item_prices = item_prices[start_idx:end_idx]
            batch_prompt = self.generate_ec_labels_prompt(batch_item_names, batch_item_prices)
            
            task = self.fetch_and_process_gpt_response(system_prompt, batch_prompt, item_id_to_something_map, batch_ids[0])
            tasks.append(task)
            processed_ids.update(batch_ids)

        results = await asyncio.gather(*tasks)

        # 檢查並重新處理空結果
        empty_results = [
            (id, content, price) for id, content, price in zip(ids, item_names, item_prices)
            if id not in item_id_to_something_map or item_id_to_something_map.get(id) == {}
        ]
        retry_count = 0
        
        while empty_results and retry_count < max_retries:
            print(f"發現 {len(empty_results)} 個空結果，正在進行第 {retry_count + 1} 次重新處理...")
            retry_tasks = []
            
            for id, content, price in empty_results:
                retry_prompt = self.generate_ec_labels_prompt([content], [price])
                retry_task = self.fetch_and_process_gpt_response(system_prompt, retry_prompt, item_id_to_something_map, id)
                retry_tasks.append(retry_task)
            
            await asyncio.gather(*retry_tasks)
            
            # 更新空結果列表
            empty_results = [
                (id, content, price) for id, content, price in zip(ids, item_names, item_prices)
                if id not in item_id_to_something_map or item_id_to_something_map.get(id) == {}
            ]
            retry_count += 1

        if empty_results:
            print(f"警告：在 {max_retries} 次重試後仍有 {len(empty_results)} 個結果未能成功處理。")

        return results, list(processed_ids)