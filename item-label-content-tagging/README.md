# item-label-content-tagging

## 簡介
透過 OpenAI API 為不同的媒體內容和電子商務商品生成結構化的標籤。<br>
這些標籤有助於更多 [後續應用](#後續應用)。

## 功能
- 深度關鍵字生成：分析商品或文章的核心特點，生成相關關鍵字。
- 核心概念描述：全面描述商品或文章的主要特色及市場定位。
- 標籤生成：為商品和文章生成多維度的標籤
- 標籤結構
```
標籤系統/
├── 商品標籤/
│   ├── 內容標籤/
│   │   ├── 產品特色
│   │   ├── 商品種類
│   │   ├── 市場區隔
│   │   └── 產業
│   ├── 受眾輪廓/
│   │   ├── 興趣愛好
│   │   ├── 潛在痛點
│   │   └── 購買動機
│   ├── 核心描述
│   └── 關鍵字
│
└── 文章標籤/
    ├── 內容標籤/
    │   ├── 內容目的
    │   ├── 潛在議題
    │   ├── 文章種類
    │   └── 口吻風格
    ├── 受眾輪廓/
    │   ├── 興趣愛好
    │   ├── 職業背景
    │   └── 價值觀與個人意見
    ├── 核心描述
    └── 關鍵字
```

## 資料表結構
資料表: tagtoo-ml-workflow.data_prod.item_labels
| 欄位名稱 | 資料型別 | 說明 |
|---------|---------|------|
| id | STRING | 透過 uuid4 產生的商品或文章的識別碼 |
| ec_name | STRING | 商品或文章的 EC 名稱 |
| ec_id | STRING | 商品或文章的 EC 識別碼 |
| industry_name | STRING | 商品或文章的產業名稱 |
| industry_id | STRING | 商品或文章的產業識別碼 |
| created_at | TIMESTAMP | 標籤生成時間 |
| structured_labels | JSON | 商品標籤 |
| encoded_id | STRING | 將 `item_name` + `ec_id` 透過 base64 產生的商品或文章的唯一識別碼，考慮到在不同家 ec 可能出現同樣的商品名稱，因此才加入此欄位 |
| gmc_category_l1 | STRING | 商品的 GMC 種類第一層 |
| gmc_category_l2 | STRING | 商品的 GMC 種類第二層 |
| gmc_category_l3 | STRING | 商品的 GMC 種類第三層 |
| version | STRING | 標籤版本 |
| price | INT64 | 商品價格 |
| label_updated_at | TIMESTAMP | 標籤更新時間 |
| last_event_at | TIMESTAMP | 最後在 Tagtoo Event 出現的時間 |

## 標籤版本

| 版本 | 發佈日期 | 主要功能 | 標籤結構 | 備註 |
|-----|------|---------|----------|------|
| 0.0 | 2024-10-12 | - 基本標籤生成 | **商品標籤**:<br>- ContentTags (Features, MarketSegmentation, Industry, Category)<br>- AudienceProfile (Interests, PotentialPainPoints, BrowsingMotivations)<br>- CoreDescription<br>- Keywords<br><br>**文章標籤**:<br>- ContentTags (Purpose, PotentialIssues, Category, Tone)<br>- AudienceProfile (Interests, OccupationalTraits, PersonalOpinions)<br>- CoreDescription<br>- Keywords | 初始版本 |
| 1.0 | 2024-12-09 | - 商品與文章標籤生成<br>- GMC 商品種類生成<br>- encoded_id 生成 | **商品標籤**:<br>- ContentTags (Features, MarketSegmentation, Industry, Category)<br>- AudienceProfile (Interests, PotentialPainPoints, BrowsingMotivations)<br>- CoreDescription<br>- Keywords<br><br>**文章標籤**:<br>- ContentTags (Purpose, PotentialIssues, Category, Tone)<br>- AudienceProfile (Interests, OccupationalTraits, PersonalOpinions)<br>- CoreDescription<br>- Keywords | 增加 GMC 種類、encoded_id、標籤版本、價格、標籤更新時間、最後事件時間等欄位|
| 1.1 | 2025-06-25 | - 模型升級：gpt-4o-mini → gpt-4.1-nano<br>- 成本計算功能升級<br>- 性能與效率提升 | **商品標籤**:<br>- ContentTags (Features, MarketSegmentation, Industry, Category)<br>- AudienceProfile (Interests, PotentialPainPoints, BrowsingMotivations)<br>- CoreDescription<br>- Keywords<br><br>**文章標籤**:<br>- ContentTags (Purpose, PotentialIssues, Category, Tone)<br>- AudienceProfile (Interests, OccupationalTraits, PersonalOpinions)<br>- CoreDescription<br>- Keywords | 模型升級版本<br>- 標籤結構保持不變<br>- API 成本降低 33%<br>- 響應速度提升<br>- 指令跟隨能力增強<br>- 上下文窗口擴大至 1M tokens |

## 技術棧
- Python 3.10
- Aiohttp
- Asyncio
- Pandas
- Google Cloud Storage
- Google Cloud BigQuery
- OpenAI

## 處理流程
1. 輸入 `media_or_ec_gate` 與 `topk` 參數。
    - `media_or_ec_gate` [必填]: "media", "ec", "single_ec"。
        - 若為 `single_ec`，則需要輸入 `ecid` [必填]。
    - `topk` [必填]: 決定要生成多少個熱門商品或文章標籤。
    - `start_date` [選填]: 決定要生成多少個熱門商品或文章標籤。
    - `end_date` [選填]: 決定要生成多少個熱門商品或文章標籤。
2. 根據您的需求選擇適當的處理流程（媒體或電子商務）。
3. 調用相應的處理函數：`process_media_pipeline` 或 `process_ec_pipeline`。
3. 獲取結構化標籤並應用於您的業務邏輯。

## 代碼結構
- base.py：包含基礎的 OpenAI API request 的異步處理器和重試機制。
- google_storage.py：處理與 Google Cloud Storage 相關的操作。
- label_handler.py：基於 base.py 加入更多細節的標籤生成操作。
- process_pipeline.py：定義處理流程和成本計算。
- utils.py：提供輔助函數。

## 部署
使用 cloudbuild.yaml 來配置和部署 Google Cloud Functions。

## 後續應用
- 分析報告
    - 過去將某些使用者喜愛瀏覽或購買的商品名稱，透過文字雲呈現，可以快速了解使用者喜好的商品大概有哪些。
    - 現在可以透過標籤系統，將喜愛瀏覽或購買的商品標籤，透過文字雲呈現，除了快速了解使用者喜好的商品，還可以了解使用者喜愛的商品類型、種類、痛點...等。
- 受眾包
    - 關鍵字受眾包
        - 過去透過媒合「自定義關鍵字」與「商品名稱」，找出某一類使用者喜愛的商品，再將瀏覽過這些商品的使用者，打包成受眾包。
        - 現在可以透過媒合「自定義關鍵字」與「標籤」，找出某一類使用者喜愛的商品，再將瀏覽過這些商品的使用者，打包成受眾包。意義上會更容易媒合到意圖類似的使用者。
    - 可以將商品標籤作為受眾包特徵，用於 LTA 受眾包預測。
