import logging
from google.cloud import bigquery
from typing import *
import json

class AdBigQueryHandler:
    def __init__(self, client=None):
        self.client = client or bigquery.Client()

    def data_to_jsonl(self, data: List[Dict[str, str]], filepath: str):
        """
        Saves the given data as a JSONL file.
        """
        with open(filepath, 'w') as file:
            for item in data:
                json_line = json.dumps(item)
                file.write(json_line + '\n')

    def read_jsonl(self, filepath):
        """
        Read a JSONL file and return a list of dictionaries.
        Args:
            file_path (str): The path to the JSONL file.
        Returns:
            list: A list of dictionaries.
        """
        data = []
        with open(filepath, 'r') as f:
            for line in f:
                data.append(json.loads(line))
        return data

    def is_ndjson(self, file_path: str) -> bool:
        """
        Check if a file is in newline-delimited JSON (NDJSON) format.
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                for line_number, line in enumerate(f, start=1):
                    line = line.strip()
                    if not line:
                        continue
                    try:
                        json.loads(line)
                    except json.JSONDecodeError as e:
                        print(f"Line {line_number} is not valid JSON: {e}")
                        return False
            return True
        except FileNotFoundError:
            print(f"File not found: {file_path}")
            return False
        except Exception as e:
            print(f"An error occurred: {e}")
            return False

    def load_jsonl_to_bigquery(self, result_gcs_path: str, dataset_id: str, table_name: str, schema: List[bigquery.SchemaField], location: str):
        """從 GCS 載入 JSONL 檔案到 BigQuery 表格"""
        table_id = f"{self.client.project}.{dataset_id}.{table_name}"
        job_config = bigquery.LoadJobConfig(
            schema=schema,
            source_format=bigquery.SourceFormat.NEWLINE_DELIMITED_JSON,
            write_disposition=bigquery.WriteDisposition.WRITE_APPEND
        )

        load_job = self.client.load_table_from_uri(
            result_gcs_path, table_id, location=location, job_config=job_config
        )

        try:
            load_job.result()
        except Exception as e:
            logging.error({
                "event": "bigquery_load_error",
                "message": "載入錯誤",
                "error": str(e),
                "table_id": table_id
            })
            for error in load_job.errors or []:
                logging.error({
                    "event": "bigquery_load_error_detail", 
                    "message": str(error),
                    "table_id": table_id
                })
            raise Exception(f"資料匯入到 BigQuery 失敗: {str(e)}")
        
        destination_table = self.client.get_table(table_id)
        logging.info({
            "event": "bigquery_load_success",
            "message": f"{table_id} 目前共有 {destination_table.num_rows} 筆資料。"
        })
