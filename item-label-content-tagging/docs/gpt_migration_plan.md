# GPT Model Migration Plan: 4o-mini → 4.1-nano

## 📊 Migration Overview

**Current Model**: `gpt-4o-mini`  
**Target Model**: `gpt-4.1-nano`  
**Project**: item-label-content-tagging  
**Migration Type**: Simple model name replacement

## ✅ Pre-Migration Verification

### 1. Current Model Confirmation
- ✅ **Location**: `/base.py` line 97
- ✅ **Current Setting**: `model: str = 'gpt-4o-mini'`
- ✅ **Usage**: Default parameter in `fetch_and_process_gpt_response()`

### 2. API Compatibility Check
- ✅ **OpenAI Python Library**: Same API interface
- ✅ **Authentication**: No changes required
- ✅ **Parameters**: Identical parameter structure
- ✅ **Response Format**: Same response structure

### 3. Model Availability & Performance
- ✅ **Availability**: GPT-4.1-nano released April 2025
- ✅ **Performance**: Superior to 4o-mini (MMLU: 80.1% vs lower)
- ✅ **Context Window**: 1M tokens (improved)
- ✅ **Latency**: Lower latency than 4o-mini
- ✅ **Cost**: Lower cost than 4o-mini

## 🔧 Implementation Steps

### Phase 1: Code Modification (5 minutes)

#### Step 1.1: Update Default Model Parameter
**File**: `base.py`  
**Line**: 97  
**Change**:
```python
# Before
async def fetch_and_process_gpt_response(self, ..., model: str = 'gpt-4o-mini'):

# After  
async def fetch_and_process_gpt_response(self, ..., model: str = 'gpt-4.1-nano'):
```

#### Step 1.2: Update Cost Calculation Function
**File**: `base.py`  
**Method**: `count_response_cost()`  
**Action**: Update pricing data to support both gpt-4o-mini and gpt-4.1-nano

**Current Pricing (gpt-4o-mini)**:
- Input: $0.15 per million tokens
- Output: $0.60 per million tokens

**New Pricing (gpt-4.1-nano)**:
- Input: $0.10 per million tokens (33% cheaper)
- Output: $0.40 per million tokens (33% cheaper)

**Required Changes**:
```python
def count_response_cost(self, response, model='gpt-4.1-nano'):
    LLM_TOKEN_PRICE = {
        'input_token': {
            'gpt-4o': 5 / 1000000,
            'gpt-4o-mini': 0.15 / 1000000,
            'gpt-4.1-nano': 0.10 / 1000000,  # NEW
            'finetune-gpt-4o-mini': 0.3 / 1000000
        },
        'output_token': {
            'gpt-4o': 15 / 1000000,
            'gpt-4o-mini': 0.6 / 1000000,
            'gpt-4.1-nano': 0.40 / 1000000,  # NEW
            'finetune-gpt-4o-mini': 1.2 / 1000000
        }
    }

    US_TO_NTD = 32.31
    usage = response['usage']
    
    # Use model parameter to determine pricing
    input_cost = usage['prompt_tokens'] * LLM_TOKEN_PRICE['input_token'][model]
    output_cost = usage['completion_tokens'] * LLM_TOKEN_PRICE['output_token'][model]

    return (input_cost + output_cost) * US_TO_NTD
```

### Phase 2: Testing (15 minutes)

#### Step 2.1: Local Testing
```bash
# Set environment variables
export OPENAI_API_KEY="your-api-key"

# Test single request
python -c "
from base import AsyncGPTHandler
import asyncio

async def test():
    handler = AsyncGPTHandler()
    response = await handler.fetch_and_process_gpt_response(
        system_prompt='You are a helpful assistant',
        user_prompt='Hello, test gpt-4.1-nano',
        result_map={},
        id='test'
    )
    print('Model response received successfully')
    
asyncio.run(test())
"
```

#### Step 2.2: Cost Calculation Testing
```bash
# Test cost calculation with both models
python -c "
from base import AsyncGPTHandler

# Mock response for testing
mock_response = {
    'usage': {
        'prompt_tokens': 1000,
        'completion_tokens': 500
    }
}

handler = AsyncGPTHandler()

# Test old model cost
old_cost = handler.count_response_cost(mock_response, 'gpt-4o-mini')
print(f'gpt-4o-mini cost: NT${old_cost:.4f}')

# Test new model cost  
new_cost = handler.count_response_cost(mock_response, 'gpt-4.1-nano')
print(f'gpt-4.1-nano cost: NT${new_cost:.4f}')

savings = ((old_cost - new_cost) / old_cost) * 100
print(f'Cost savings: {savings:.1f}%')
"
```

#### Step 2.3: Integration Testing
```bash
# Test with actual pipeline
python main.py
# Use test parameters for small dataset
```

### Phase 3: Deployment (10 minutes)

#### Step 3.1: Environment-Specific Deployment
- **Development**: Deploy and test with sample data
- **Staging**: Full pipeline test with yesterday's data (small sample)
- **Production**: Deploy after staging validation

#### Step 3.2: Monitor Initial Runs
- Check latency improvements
- Verify response quality
- Monitor cost changes
- Watch for any error patterns

## 📈 Expected Benefits

### Performance Improvements
- **Lower Latency**: Faster first token response (sub-5 seconds for 128k tokens)
- **Better Accuracy**: 80.1% MMLU vs 4o-mini's lower score
- **Enhanced Context**: 1M token context window (vs 128k)
- **Significant Cost Reduction**: 33% cheaper per token

### Cost Benefits
- **Input Tokens**: $0.10/M vs $0.15/M (33% savings)
- **Output Tokens**: $0.40/M vs $0.60/M (33% savings)
- **Monthly Savings**: Estimated 33% reduction in API costs
- **ROI**: Immediate cost savings with improved performance

### Operational Benefits
- **Better Label Quality**: Improved instruction following
- **Faster Processing**: Reduced time per API call
- **Cost Efficiency**: Lower operational costs
- **Future-Proof**: Latest model generation

## ⚠️ Risk Assessment & Mitigation

### Low Risk Items
- **API Compatibility**: ✅ Identical interface
- **Code Changes**: ✅ Single parameter change
- **Rollback**: ✅ Simple revert if needed

### Monitoring Points
- **Response Quality**: Compare labeling accuracy
- **Error Rates**: Watch for new error patterns
- **Cost Impact**: Track actual vs projected costs
- **Latency**: Measure real-world performance

### Rollback Plan
```python
# Immediate rollback if issues occur
model: str = 'gpt-4o-mini'  # Revert to original
```

## 📋 Implementation Checklist

### Pre-Deployment
- [ ] Backup current `base.py`
- [ ] Update model parameter in `base.py`
- [ ] Update cost calculation function with gpt-4.1-nano pricing
- [ ] Test cost calculation with both models
- [ ] Local testing with sample data
- [ ] Verify API key has access to gpt-4.1-nano

### Deployment
- [ ] Deploy to development environment
- [ ] Run integration test with small dataset
- [ ] Verify cost calculations are working correctly
- [ ] Monitor first few API calls
- [ ] Deploy to production

### Post-Deployment
- [ ] Monitor response quality for 24 hours
- [ ] Track and compare actual cost savings (target: 33% reduction)
- [ ] Measure latency improvements
- [ ] Compare cost per label metrics
- [ ] Document any issues or improvements

## 🎯 Success Criteria

### Technical Metrics
- [ ] API calls succeed without errors
- [ ] Response latency ≤ current baseline
- [ ] Label quality maintained or improved
- [ ] Zero downtime during migration

### Business Metrics
- [ ] Cost per label ≤ current baseline
- [ ] Processing time improved
- [ ] Label accuracy maintained/improved
- [ ] System stability maintained

## 📝 Post-Migration Actions

### Optimization Opportunities
1. **Prompt Optimization**: Leverage improved instruction following
2. **Batch Size Tuning**: Test larger batch sizes with better context handling
3. **Cost Monitoring**: Establish new baseline metrics
4. **Performance Monitoring**: Set up alerting for degradation

### Documentation Updates
- Update API documentation
- Record performance benchmarks
- Update cost calculation methods
- Document lessons learned

## 📞 Emergency Contacts

- **Developer**: Fredrick (project owner)
- **Backup Plan**: Immediate rollback to `gpt-4o-mini`
- **Monitoring**: Check logs in real-time during initial deployment

---

**Estimated Total Migration Time**: 30 minutes  
**Risk Level**: Low  
**Complexity**: Simple  
**Recommended Migration Window**: Non-peak hours