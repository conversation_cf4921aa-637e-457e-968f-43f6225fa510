from base import Async<PERSON>THand<PERSON>
from exceptions import classify_error
from typing import Optional, List
import asyncio
import aiohttp
import json
import logging
from openai import APIError
from tenacity import (
    retry,
    stop_after_attempt,
    wait_random_exponential,
)

"""
GMC 商品種類分類器
- 功能：這個類別用於預測商品的 GMC 商品種類。
- 開發說明：商品貼標已經有 AsyncGPTHandler 類別，但當初把解析 openai api response 跟 request 寫在同一個 function。這裡為了方便開發，所以另外建立一個類別。
- 功能比較：
    - 這個版本的 retry 使用 tenacity 套件，可以更方便的設定重試機制，也客製化錯誤訊息。
    - 這個版本加強錯誤處理的機制，根據不同的錯誤類型，回傳不同的異常類型。
"""

def log_retry_attempt(retry_state):
    """自定義重試日誌"""
    exception = retry_state.outcome.exception()
    
    print(f"""
    重試資訊:
    - 重試次數: {retry_state.attempt_number}
    - 等待時間: {retry_state.next_action.sleep:.2f} 秒
    - 異常類型: {exception.__class__.__name__}
    - 異常信息: {str(exception)}
    """)

class GmcCategoryPredictor(AsyncGPTHandler):
    def __init__(self, openai_client, gmc_category_dict, gmc_l1_categories, max_concurrent_requests: int = 30):
        self.openai_client = openai_client
        self.semaphore = asyncio.Semaphore(max_concurrent_requests)  # Semaphore to limit concurrent requests
        self.gmc_category_dict = gmc_category_dict 
        self.gmc_l1_categories = gmc_l1_categories
        self.system_prompt = """
        <output_format>
        ```json
        {"gmc_category": "<GMC 商品種類>"}
        ```
        </output_format>
        <language>
            請以繁體中文（#zh-tw） 回傳結果。
        </language>
        """

    async def predict(self, item_data: dict) -> dict:
        """預測商品的 GMC 分類"""
        item_info = self._extract_item_info(item_data)
        
        # 依序處理三層分類
        l1_category = await self._predict_l1_category(item_info)
        l2_category = await self._predict_l2_category(item_info, l1_category)
        l3_category = await self._predict_l3_category(item_info, l1_category, l2_category)
        
        return {
            "l1": l1_category,
            "l2": l2_category, 
            "l3": l3_category
        }

    def _extract_item_info(self, item_data: dict) -> dict:
        """從商品資料中提取需要的資訊"""
        return {
            "id": item_data["id"],
            "name": item_data["name"],
            "description": item_data["structured_labels"]["CoreDescription"],
            "categories": item_data["structured_labels"]["ContentTags"]["Category"]
        }

    async def _predict_l1_category(self, item_info: dict) -> str:
        """預測第一層分類"""
        prompt = self._generate_prompt(
            item_info["name"],
            item_info["description"], 
            item_info["categories"],
            self.gmc_l1_categories
        )
        return await self._get_gpt_prediction(prompt)

    async def _predict_l2_category(self, item_info: dict, l1_category: str) -> str:
        """
        預測第二層分類
        1. 拿到候選種類
        2. 生成 prompt
        3. 取得預測結果
        """
        l2_candidates = self._get_l2_candidates(l1_category)
        prompt = self._generate_prompt(
            item_info["name"],
            item_info["description"],
            item_info["categories"], 
            l2_candidates
        )
        return await self._get_gpt_prediction(prompt)

    async def _predict_l3_category(self, item_info: dict, l1_category: str, l2_category: str) -> Optional[str]:
        """
        預測第三層分類
        1. 若有第三層
			a. 拿到候選種類
			b. 生成 prompt
			c. 取得預測結果
        2. 若沒有第三層，則回傳 None -> 則直接回傳 None，不需要預測
        """
        # 如果沒有第三層分類，則回傳 None
        if not self._has_l3_category(l1_category):
            return None
            
		# 有可能拿到空的第三層分類候選項，因為其他第二層有可能有第三層，但自己沒有
        l3_candidates = self._get_l3_candidates(l1_category, l2_category)
        # print(l3_candidates)
        if not l3_candidates:
            return None
            
        prompt = self._generate_prompt(
            item_info["name"],
            item_info["description"],
            item_info["categories"],
            l3_candidates
        )
        return await self._get_gpt_prediction(prompt)

    def _generate_prompt(self, item_name: str, description: str, categories: List[str], gmc_categories: List[str]) -> str:
        """生成 GPT prompt"""
        return f"""
        <task>
        **任務**: 請從以下提供的 GMC 商品種類，根據「目標商品名稱」、「商品描述」、「發散的商品種類」，判斷目標商品最相近的 GMC 商品種類，結果只能
回傳一個種類。
        </task>
        <input>
        <product_name>
        {item_name}
        </product_name>
        <product_description>
        {description}
        </product_description>
        <product_categories>
        {categories}
        </product_categories>
        <gmc_categories>
        {gmc_categories}
        </gmc_categories>
        </input>

        <chain_of_thought>
        - 目標商品名稱：三大香氣精油禮盒
        - 發散的商品種類: ['保養美容', '香氛', '精油', '禮盒']
        - 提供的 gmc 商品種類: ['保健與美容', '媒體', '嬰幼兒用品', '宗教和慶典', '家具', '寵物用品', '居家和庭園', '成人用品', '服飾與配件', '玩具和遊戲', '硬體', '藝術/娛樂', '車輛與零配件', '軟體', '辦公室用品', '電子器材', '食物、飲料和菸草', '體育用品']
        - 思考過程: 在發散的商品種類中，「精油」、「禮盒」都屬於「保養美容」，而「保養美容」在提供的 gmc 商品種類中，最接近的分類是「保健與
美容」
        - 輸出的 gmc category: ['保健與美容']
        - 輸出請以合法 json 以 double quote 將字串包起來: {{"gmc_category": "保健與美容"}}
        </chain_of_thought> 

        """

    async def _get_gpt_prediction(self, prompt: str) -> str:
        """從 GPT 獲取預測結果"""
        response = await self.ask_gpt(self.openai_client.api_key, self.system_prompt, prompt)
        parsed_response = self.parse_response_content(response)
        return self.extract_gmc_category(parsed_response)

    def _get_l2_candidates(self, l1_category: str) -> List[str]:
        """獲取第二層分類候選項"""
        target_categories = self.gmc_category_dict[l1_category]
        if isinstance(target_categories, dict):
            return list(target_categories.keys())
        return list(target_categories)

    def _get_l3_candidates(self, l1_category: str, l2_category: str) -> List[str]:
        """獲取第三層分類候選項"""
        # logging.info(f"第一層 - 第二層: {l1_category} - {l2_category}")
        l3_candidates = self.gmc_category_dict[l1_category][l2_category]
        if l3_candidates:
            return l3_candidates
        else:
            return None

    def _has_l3_category(self, l1_category: str) -> bool:
        """檢查是否有第三層分類"""
        return isinstance(self.gmc_category_dict[l1_category], dict)
    
    @retry(
        wait=wait_random_exponential(min=10, max=60),
        stop=stop_after_attempt(3),
        before_sleep=log_retry_attempt,
    )
    async def ask_gpt(
		self,
        api_key: str, 
        system_prompt: str, 
        user_prompt: str, 
        max_tokens: int = 4096,
        model: str = 'gpt-4.1-nano'
    ):
        """Fetch response from GPT and process the result asynchronously."""
        async with self.semaphore:
            headers = {
                "Content-Type": "application/json", 
                "Authorization": f"Bearer {api_key}"
            }
            payload = {
                "model": model,
                "response_format": {"type": "json_object"},
                "max_completion_tokens": max_tokens,
                "messages": [
                {"role": "system", "content": system_prompt},
                {"role": "user", "content": user_prompt}
                ]
            }
            try:
                async with aiohttp.ClientSession() as session:
                    async with session.post(
                        "https://api.openai.com/v1/chat/completions",
                        headers=headers,
                        json=payload,
                        timeout=aiohttp.ClientTimeout(total=300)
                    ) as response:
                        if response.status != 200:
                            error_data = await response.json()
                            error_message = error_data.get('error', {}).get('message', 'Unknown error')
                            raise classify_error(response.status, error_message)
                        
                        response_json = await response.json()
                        return response_json
                        
            except aiohttp.ClientError as e:
                logging.error(f"Network error: {str(e)}")
                raise APIError(f"Network error: {str(e)}")
            except asyncio.TimeoutError:
                logging.error("Request timed out")
                raise APIError("Request timed out") 
            except Exception as e:
                logging.error(f"Unexpected error: {str(e)}")
                raise

    def parse_response_content(self, response):
        """Response parsing with proper error handling"""
        if response is None:
            raise ValueError("No response received from API")
            
        try:
            if isinstance(response, str):
                content = json.loads(response)['choices'][0]['message']['content']
            elif isinstance(response, dict):
                if 'choices' in response:
                    content = response['choices'][0]['message']['content']
                else:
                    raise ValueError("OpenAI response lacks 'choices' key")
            else:
                raise TypeError(f"Invalid response type: {type(response)}")

            json_result = json.loads(content)
            return json_result.get('result', json_result)

        except (json.JSONDecodeError, KeyError, TypeError) as e:
            # 根據錯誤類型動態回傳對應的異常
            error_type = type(e).__name__
            error_class = globals().get(error_type, ValueError)  # 如果找不到對應的異常類型，預設使用ValueError
            raise error_class(f"Error parsing response in `parse_response_content` function: {e}")
        
    def extract_gmc_category(self, response: dict) -> str:
        if 'gmc_category' in response:
            return response['gmc_category']
        else:
            raise KeyError("No gmc_category found in the OpenAI response")