import logging
from utils import read_json
from typing import List, Dict
import asyncio
from gmc_label_handler import GmcCategoryPredictor

async def process_gmc_categories(
    openai_client, 
    item_data: dict,
    gmc_category_dict: dict,
    gmc_l1_categories: List[str],
    item_id_to_gmc_category_map: dict
) -> str:
    """處理商品的 GMC 分類"""
    predictor = GmcCategoryPredictor(openai_client, gmc_category_dict, gmc_l1_categories)
    
    # 獲取預測結果
    item_id = item_data["id"]
    prediction_result = await predictor.predict(item_data)
    
    # 儲存結果
    item_id_to_gmc_category_map[item_id] = prediction_result
    
    return f"{item_id} ok"



async def batch_predict_gmc_categories(
    openai_client,
    dataset: List[dict],
    gmc_category_dict: dict,
    gmc_l1_categories: List[str]
) -> Dict[str, str]:
    
    # 初始化結果映射
    item_id_to_gmc_category_map = {}
    
    # 建立所有項目的處理任務
    tasks = [
        process_gmc_categories(
            openai_client,
            item,
            gmc_category_dict,
            gmc_l1_categories,
            item_id_to_gmc_category_map
        )
        for item in dataset
    ]
    
    # 同時執行所有任務並等待完成
    results = await asyncio.gather(*tasks, return_exceptions=True)
    
    # 處理結果，區分成功和失敗的案例
    success_count = 0
    failed_count = 0
    failed_items = []
    success_results = []
    
    for item, result in zip(dataset, results):
        if isinstance(result, Exception):
            failed_count += 1

            # 獲取完整的錯誤追蹤信息
            import traceback
            error_traceback = ''.join(traceback.format_exception(
                type(result),
                result,
                result.__traceback__
            ))

            logging.warning({
                "message": "任務處理失敗",
                "event_type": "gmc_category_task_failed",
                # "item_id": item['id'],
                # "item_name": item.get('name', 'N/A'),
                "item_data": item,
                "error_type": type(result).__name__,
                "error_message": str(result),
                "error_traceback": error_traceback
            })
            failed_items.append(item)
        else:
            success_count += 1
        
    return item_id_to_gmc_category_map

async def process_gmc_category_predictions(openai_client, item_data: list, gsio, gmc_category_dict_gcs_path: str):

    gmc_category_dict_path = '/tmp/gmc_category_dict.json'
    gsio.download_to_path(gsuri=gmc_category_dict_gcs_path, localpath=gmc_category_dict_path)

    gmc_category_dict = read_json(gmc_category_dict_path)
    gmc_l1_categories = list(gmc_category_dict.keys())
    
    # 執行處理
    # 會將結果寫入 item_id_to_gmc_category_map
    result_map = await batch_predict_gmc_categories(
        openai_client,
        item_data,
        gmc_category_dict,
        gmc_l1_categories
    )
    
    item_data = [
        {**data, 'gmc_category': result_map.get(data['id'])}
        for data in item_data
    ]

    # # 印出最終結果
    # print("\n 處理完成的分類映射：")
    # for data in item_data:
    #     print(f"商品 名稱: {data['name']}, GMC 分類: {data['gmc_category']}")

    return item_data