# Git Hooks 使用指南

這個專案使用 Git hooks 來確保代碼品質和一致性。本文檔說明如何安裝和使用這些 hooks。

## 🚀 快速安裝

### 🎯 推薦方案：統一管理腳本 `manage_git_hooks.sh`

我們現在提供了一個完整整合的管理腳本，解決了 macOS bash 兼容性問題，並提供智能偵測功能：

```bash
# 🌟 智能偵測模式 - 無參數執行，自動判斷狀態和推薦操作
./manage_git_hooks.sh

# 🔧 新同事快速安裝（互動模式，友好提示）
./manage_git_hooks.sh --quick

# ⚙️ CI/CD 靜默安裝（無干擾模式）
./manage_git_hooks.sh --install

# 📋 完整管理選單（所有功能的統一入口）
./manage_git_hooks.sh --menu

# 🔍 狀態檢查（診斷工具）
./manage_git_hooks.sh --status

# 💾 備份現有 hooks
./manage_git_hooks.sh --backup

# 🧪 測試功能
./manage_git_hooks.sh --test

# 📖 查看所有選項
./manage_git_hooks.sh --help
```

#### ✅ 新腳本已解決的問題

- **🔧 macOS 兼容性**：完全兼容 macOS 預設 bash 和 zsh
- **🎯 智能判斷**：自動偵測安裝狀態，推薦最適合的操作
- **🔒 統一邏輯**：所有功能使用相同的核心邏輯，避免不一致
- **🛠️ 完整功能**：整合安裝、檢查、備份、測試等所有需要的功能
- **📝 友好提示**：提供清晰的錯誤信息和使用指導

### 🔄 向後兼容方案

舊的三腳本方案仍然可用，但建議遷移到新的統一腳本：

我們提供了三個不同的腳本來管理 Git hooks，根據您的需求選擇：

| 腳本名稱 | 適用場景 | 主要功能 | 推薦對象 |
|---------|---------|----------|----------|
| `setup_team_hooks.sh` | 🌟 **新同事入職** | 一鍵設定，使用者友好 | 新團隊成員 |
| `install_git_hooks.sh` | 🔧 **核心安裝** | 完整功能安裝 | 進階使用者、CI/CD |
| `update_git_hooks.sh` | 📋 **日常管理** | 互動式選單管理 | 專案維護者 |

#### 1. `setup_team_hooks.sh` - 新同事專用
```bash
bash setup_team_hooks.sh
```
**特點**：
- ✅ 簡化的一鍵安裝體驗
- ✅ 自動檢測環境並給出友好提示
- ✅ 包含安裝後的驗證和使用提示
- ✅ 錯誤處理更加人性化
- 🎯 **最適合**：剛加入團隊的新成員

#### 2. `install_git_hooks.sh` - 核心引擎
```bash
bash install_git_hooks.sh
```
**特點**：
- 🔧 完整的 430 行安裝邏輯
- 🔧 創建所有 hooks（pre-commit、pre-push、commit-msg）
- 🔧 自動備份現有 hooks
- 🔧 支援批量部署和 CI/CD 整合
- 🎯 **最適合**：自動化腳本、進階使用者

#### 3. `update_git_hooks.sh` - 管理界面
```bash
bash update_git_hooks.sh
```
**特點**：
- 📋 互動式選單（4個選項）
- 📋 檢查 hooks 狀態和 Git 配置
- 📋 備份現有 hooks
- 📋 查看使用指南
- 🎯 **最適合**：專案維護者、日常管理

### 常見使用場景

```bash
# 場景 1：新同事剛加入團隊
bash setup_team_hooks.sh

# 場景 2：CI/CD 或批量部署
bash install_git_hooks.sh

# 場景 3：檢查當前狀態或更新 hooks
bash update_git_hooks.sh  # 然後選擇對應選項

# 場景 4：快速重新安裝
bash install_git_hooks.sh  # 直接執行核心安裝
```

### 方法 1：使用安裝腳本（推薦）

```bash
# 在專案根目錄執行
bash install_git_hooks.sh
```

### 方法 2：手動安裝

```bash
# 複製 hooks 文件
cp hooks/pre-commit .git/hooks/
cp hooks/pre-push .git/hooks/
cp hooks/commit-msg .git/hooks/

# 設置執行權限
chmod +x .git/hooks/pre-commit
chmod +x .git/hooks/pre-push
chmod +x .git/hooks/commit-msg
```

## 📋 已配置的 Hooks

### 1. pre-commit（提交前自動修復）

**執行時機**：`git commit` 之前

**功能**：
- ✅ 自動移除所有文本文件的尾隨空格
- ✅ 檢查 Python 語法錯誤
- ✅ 自動將修復後的文件加入暫存區

**處理的文件類型**：
- Python: `.py`
- 文檔: `.md`, `.txt`
- 配置: `.yaml`, `.yml`, `.json`
- 腳本: `.sh`, `.sql`
- 前端: `.js`, `.ts`, `.css`, `.html`

### 2. pre-push（推送前嚴格檢查）

**執行時機**：`git push` 之前

**功能**：
- 🔍 檢查尾隨空格（不自動修復）
- 🔍 檢查 Python 語法錯誤
- 🔍 檢查 Git merge conflict markers
- 🔍 檢查大文件（>1MB，僅警告）
- ❌ 發現問題時阻止推送

### 3. commit-msg（提交訊息格式檢查）

**執行時機**：提交訊息準備完成後

**功能**：
- 📝 強制使用 conventional commits 格式
- 📝 支援中英文描述

**格式要求**：
```
type(scope): description
或
type: description
```

**允許的 types**：
- `feat`: 新功能
- `fix`: 錯誤修復
- `docs`: 文檔變更
- `style`: 代碼格式調整
- `refactor`: 代碼重構
- `test`: 測試相關
- `chore`: 其他雜項
- `perf`: 性能優化
- `ci`: CI/CD 相關
- `build`: 構建相關
- `revert`: 回退變更

**正確範例**：
```bash
git commit -m "feat(user): 新增用戶登入功能"
git commit -m "fix: 修復密碼驗證錯誤"
git commit -m "docs(readme): 更新安裝說明"
```

## 🔄 工作流程

### 正常開發流程

```bash
# 1. 修改代碼
vim src/some_file.py

# 2. 添加到暫存區
git add .

# 3. 提交（pre-commit 自動修復）
git commit -m "feat: 新增某功能"

# 4. 推送（pre-push 嚴格檢查）
git push
```

### 遇到問題時

#### 語法錯誤
```bash
# 如果提交時發現語法錯誤
❌ Python 語法檢查失敗！
  - src/example.py

# 修復語法錯誤後重新提交
git add .
git commit -m "fix: 修復語法錯誤"
```

#### 推送被阻止
```bash
# 如果推送時發現問題
❌ 代碼品質檢查失敗！

# 按照提示修復問題
git add .
git commit --amend --no-edit
git push
```

#### 跳過檢查（緊急情況）
```bash
# 跳過 pre-commit 檢查
git commit --no-verify -m "emergency fix"

# 跳過 pre-push 檢查
git push --no-verify

# 跳過 commit-msg 檢查
git commit --no-verify -m "任意格式的訊息"
```

## 🛠️ 團隊協作

### 新成員入職

1. **克隆專案後立即安裝 hooks**：
   ```bash
   git clone <repository>
   cd <project>
   bash install_git_hooks.sh
   ```

2. **驗證安裝**：
   ```bash
   # 測試提交
   echo "test" > test.py
   git add test.py
   git commit -m "test: 測試 hooks"

   # 清理測試文件
   git reset --hard HEAD~1
   rm test.py
   ```

### 維護和更新

1. **更新 hooks**：
   ```bash
   # 重新運行安裝腳本
   bash install_git_hooks.sh
   ```

2. **檢查 hooks 狀態**：
   ```bash
   # 查看已安裝的 hooks
   ls -la .git/hooks/

   # 檢查執行權限
   ls -la .git/hooks/pre-*
   ```

## 🔧 自定義配置

### 添加新的檢查項目

編輯 `.git/hooks/pre-commit` 或 `.git/hooks/pre-push`：

```bash
# 添加新的文件類型檢查
new_files=$(echo "$files" | grep '\.jsx$')

# 添加自定義檢查邏輯
if some_custom_check; then
    echo "✅ 自定義檢查通過"
else
    echo "❌ 自定義檢查失敗"
    exit 1
fi
```

### 調整 commit-msg 格式

編輯 `.git/hooks/commit-msg`：

```bash
# 修改允許的 types
valid_types="feat|fix|docs|hotfix|custom"

# 添加自定義格式檢查
if [[ "$commit_msg" =~ ^CUSTOM- ]]; then
    exit 0  # 允許 CUSTOM- 前綴
fi
```

## ❓ 常見問題

### Q: hooks 沒有執行？
A: 檢查文件權限：`chmod +x .git/hooks/pre-*`

### Q: 想要暫時禁用所有 hooks？
A: 使用 `--no-verify` 參數：
```bash
git commit --no-verify
git push --no-verify
```

### Q: 如何恢復舊的 hooks？
A: 安裝腳本會自動備份到 `.git/hooks/backup_<timestamp>/`

### Q: 可以在子目錄中執行嗎？
A: 是的，hooks 會自動處理路徑問題

### Q: 如何完全移除 hooks？
A: 刪除對應文件：
```bash
rm .git/hooks/pre-commit
rm .git/hooks/pre-push
rm .git/hooks/commit-msg
```

## 📈 最佳實踐

1. **團隊統一**：所有成員都應該安裝相同的 hooks
2. **定期更新**：當 hooks 邏輯更新時，通知團隊重新安裝
3. **測試充分**：在推送前確保本地測試通過
4. **訊息規範**：遵循 conventional commits 格式
5. **問題修復**：遇到 hooks 阻止的問題要及時修復，不要習慣性跳過

## 🎯 效果

使用這套 Git hooks 後，您的團隊將獲得：

- ✅ 統一的代碼格式
- ✅ 更少的語法錯誤
- ✅ 規範的提交訊息
- ✅ 減少 merge conflict
- ✅ 更高的代碼品質
- ✅ 更順暢的協作體驗

---

## 📞 技術支援

如果您在使用過程中遇到問題，請：

1. 查看本文檔的常見問題部分
2. 檢查 hooks 的執行權限和語法
3. 聯繫項目維護者

**記住**：這些 hooks 是為了幫助我們寫出更好的代碼，而不是阻礙開發！ 🚀
