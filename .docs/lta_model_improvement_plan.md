# LTA 模型改進計畫

## 現況分析

### 問題點
1. 模型版本混用
   - `lta_predict_new_model` 和 `lta_predict_retrained_model` 寫入同一個 BigQuery table
   - 第二層和第三層預測仍在使用舊模型的輸出路徑

2. 命名不一致
   - 檔案路徑格式不統一
   - 缺乏版本標記

3. 架構問題
   - 缺乏監控機制
   - 文件不完整
   - 缺乏錯誤處理機制

## 改進計畫

### 1. 模型統一（優先度：高）
1. 停用舊版模型
   - 標記 `lta_predict_new_model` 為棄用
   - 移除對應的 CI/CD 設定
   - 保留程式碼但加入棄用警告

2. 更新預測層級
   ```python
   # 建議的路徑格式
   BASE_PATH = "gs://tagtoo-ml-workflow/topic10/firstparty"
   MODEL_VERSION = "retrained_v1"

   # 第一層輸出
   f"{BASE_PATH}/first_party_{date}/lta_{MODEL_VERSION}_remarketing_{date}_ttd_model.csv"

   # 第二層輸出
   f"{BASE_PATH}/first_party_{date}/lta_{MODEL_VERSION}_remarketing_{date}_second_layer.parquet"

   # 第三層輸出
   f"{BASE_PATH}/first_party_{date}/lta_{MODEL_VERSION}_remarketing_{date}_third_layer.parquet"
   ```

3. 更新 BigQuery 表格
   ```python
   # 建議的表格命名
   f"tagtoo_export_results.lta_model_fb_{MODEL_VERSION}_{date}"
   ```

### 2. 架構優化（優先度：中）
1. 加入版本控制
   - 在 Cloud Storage 中維護模型版本清單
   - 在預測結果中加入模型版本標記
   - 建立模型版本切換機制

2. 改進錯誤處理
   - 加入輸入資料驗證
   - 加入結果格式驗證
   - 實作自動重試機制

3. 加入監控機制
   - 監控預測結果分布
   - 監控執行時間
   - 設定警報閾值

### 3. 文件更新（優先度：中）
1. 更新技術文件
   - 說明三層預測的目的和原理
   - 記錄模型版本歷史
   - 提供故障排除指南

2. 更新操作文件
   - 提供模型切換指南
   - 說明監控指標含義
   - 提供緊急處理流程

### 4. 測試強化（優先度：低）
1. 加入單元測試
   - 測試資料處理邏輯
   - 測試預測流程
   - 測試錯誤處理

2. 加入整合測試
   - 測試完整預測流程
   - 測試與其他服務的整合
   - 測試監控機制

## 執行時程

### 第一階段（2週）
- 停用舊版模型
- 更新預測層級路徑
- 更新 BigQuery 表格

### 第二階段（2週）
- 實作版本控制
- 改進錯誤處理
- 建立基本監控

### 第三階段（1週）
- 更新文件
- 進行測試
- 部署到生產環境

## 風險評估

1. 潛在風險
   - 預測結果可能暫時不一致
   - 可能需要停機維護
   - 可能影響下游服務

2. 緩解措施
   - 準備回滾計畫
   - 安排在低峰期執行
   - 提前通知相關團隊

## 成功指標

1. 技術指標
   - 預測結果一致性提升
   - 系統錯誤率降低
   - 執行時間優化

2. 業務指標
   - 預測準確度提升
   - 維護成本降低
   - 問題排除時間縮短