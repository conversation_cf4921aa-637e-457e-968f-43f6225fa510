# Label Target Audience (LTA) Cloud Functions 詳細關係說明

## 功能概述

此文件詳細說明 LTA 相關 Cloud Functions 之間的資料流向和依賴關係。

## 詳細資料流程圖

```mermaid
graph TD
    %% 資料來源
    TE[BigQuery: tagtoo_event] -->|原始事件資料| A
    PD[商品資料] -->|商品資訊| C

    %% 資料準備階段
    subgraph DataPreparation[資料準備]
        A[lta-user-stats] -->|寫入統計資料| US[BigQuery: user_stats]
        A -->|每日快照| SN[GCS: user_stats_snapshots/ec_{EC_ID}/{YYYYMMDD}.parquet]

        C[item-label-retrieve-csv-export] -->|商品標籤| IL[GCS: item_labels.csv]
    end

    %% 特徵轉換階段
    subgraph FeatureTransform[特徵轉換]
        US --> D[lta_feature_transform]
        US --> E[lta_retrained_feature_transform]
        IL --> D
        IL --> E

        D -->|舊版特徵| OF[GCS: LTA_feature/{start_date}_{end_date}/transformed/old_features.ftr]
        E -->|新版特徵| NF[GCS: LTA_feature/{start_date}_{end_date}/transformed/retrained_features.ftr]
    end

    %% 模型預測階段
    subgraph ModelPrediction[模型預測]
        OF --> F[lta_predict_new_model]
        NF --> G[lta_predict_retrained_model]

        F -->|舊版預測結果| OP[GCS: firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model.csv]
        G -->|新版預測結果| NP[GCS: firstparty/first_party_{date}/lta_retrained_remarketing_{date}_ttd_model.csv]
    end

    %% 進階處理階段
    subgraph AdvancedProcessing[進階處理]
        OP --> H[lta_second_layer]
        NP --> H

        H -->|第二層結果| SL[GCS: firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_second_layer.parquet]
        SL --> I[lta_third_layer]
        I -->|第三層結果| TL[GCS: firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_third_layer.parquet]
    end

    %% 部署和驗證階段
    subgraph DeploymentValidation[部署和驗證]
        NP --> J[Deploy_ttd]
        J -->|TTD平台資料| TD[GCS: ttd_segments/*.csv]

        TL --> K[LTA_validation]
        K -->|驗證結果| VR[GCS: validation_results.json]
        VR --> L[LTA_validation_to_sheet]
        L -->|報表| GS[Google Sheet: 驗證報告]
    end

    %% 相似受眾
    subgraph Lookalike[相似受眾]
        TL --> M[LTA_lookalike]
        M -->|相似受眾| LA[GCS: lookalike_audience/*.csv]
    end

    %% 監控
    subgraph Monitoring[監控]
        O[Cloud_function_monitor] -->|監控| A & C & D & E & F & G & H & I & J & K & L & M
        O -->|警報| AL[警報系統]
    end
```

## 詳細資料依賴關係

### 1. `lta-user-stats`
- 輸入：
  - BigQuery: `tagtoo-tracking.event_prod.tagtoo_event`
  - GCS: `LTA/user_stats_configs/audience_rules.json`（受眾規則）
- 輸出：
  - BigQuery: `tagtoo-tracking.event_prod.user_stats`（使用者統計）
  - GCS: `LTA/user_stats_snapshots/ec_{EC_ID}/{YYYYMMDD}.parquet`（每日快照）
  - BigQuery: `tagtoo-ml-workflow.tagtoo_export_results.special_lta_YYYYMMDD`（特殊 LTA 表格）

### 2. `item-label-retrieve-csv-export`
- 輸入：
  - 商品資料來源（未指定格式）
- 輸出：
  - GCS: `item_labels.csv`（商品標籤資料）

### 3. `lta_feature_transform`（舊版）
- 輸入：
  - BigQuery: `tagtoo-tracking.event_prod.user_stats`
  - GCS: `item_labels.csv`
- 輸出：
  - GCS: `LTA_feature/{start_date}_{end_date}/transformed/old_features.ftr`

### 4. `lta_retrained_feature_transform`（新版）
- 輸入：
  - BigQuery: `tagtoo-tracking.event_prod.user_stats`
  - GCS: `item_labels.csv`
- 輸出：
  - GCS: `LTA_feature/{start_date}_{end_date}/transformed/retrained_features.ftr`

### 5. `lta_predict_new_model`（已棄用）
- 輸入：
  - GCS: `LTA_feature/{start_date}_{end_date}/transformed/old_features.ftr`
  - 模型檔案: `lta_new2.pkl`
- 輸出：
  - GCS: `firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model.csv`
  - BigQuery: `tagtoo_export_results.lta_model_fb_{date}`

### 6. `lta_predict_retrained_model`
- 輸入：
  - GCS: `LTA_feature/{start_date}_{end_date}/transformed/retrained_features.ftr`
  - 模型檔案: `lta_retrain_v1.pkl`
- 輸出：
  - GCS: `firstparty/first_party_{date}/lta_retrained_remarketing_{date}_ttd_model.csv`
  - BigQuery: `tagtoo_export_results.lta_retrain_model_fb_{date}`

### 7. `lta_second_layer`
- 輸入：
  - GCS: `firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model.csv`
  - GCS: `firstparty/first_party_{date}/lta_retrained_remarketing_{date}_ttd_model.csv`
  - GCS: `firstparty/segment_name_to_id/{date}/label_to_segment_id.json`
- 輸出：
  - GCS: `firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_second_layer.parquet`

### 8. `lta_third_layer`
- 輸入：
  - GCS: `firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_second_layer.parquet`
- 輸出：
  - GCS: `firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_third_layer.parquet`

### 9. `Deploy_ttd`
- 輸入：
  - GCS: `firstparty/first_party_{date}/lta_retrained_remarketing_{date}_ttd_model.csv`
- 輸出：
  - GCS: `ttd_segments/*.csv`（分割後的 TTD 平台資料）

### 10. `LTA_validation`
- 輸入：
  - GCS: `firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_third_layer.parquet`
- 輸出：
  - GCS: `validation_results.json`

### 11. `LTA_validation_to_sheet`
- 輸入：
  - GCS: `validation_results.json`
- 輸出：
  - Google Sheet: 驗證報告

### 12. `LTA_lookalike`
- 輸入：
  - GCS: `firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_third_layer.parquet`
- 輸出：
  - GCS: `lookalike_audience/*.csv`

### 13. `Cloud_function_monitor`
- 監控：所有 Cloud Functions
- 輸出：
  - 監控報告
  - 警報通知

## 注意事項

1. 檔案命名規則
   - `{date}`: YYYYMMDD 格式
   - `{start_date}_{end_date}`: 資料時間範圍

2. 重要依賴關係
   - 第二層預測依賴於兩個模型的輸出
   - 驗證和相似受眾功能依賴於第三層結果

3. 資料保留政策
   - 快照資料：保留 30 天
   - 預測結果：保留 90 天
   - 驗證報告：永久保留

4. 錯誤處理
   - 上游資料缺失時的備援機制
   - 資料格式驗證
   - 自動重試機制