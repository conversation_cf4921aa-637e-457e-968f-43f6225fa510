# CI/CD 文檔總覽

本目錄包含 ml-workflow-cloud-functions 專案的 CI/CD 相關文檔。

## 📁 文檔結構

### 🔧 [測試指南](./testing-guide.md)
- **內容**：CI/CD 測試流程和最佳實踐
- **適用對象**：開發者、DevOps 工程師
- **主要內容**：
  - GitHub Actions 測試流程
  - 測試執行條件和邏輯
  - 測試模板和最佳實踐
  - 故障排除指南

### 🔐 [認證指南](./authentication-guide.md)
- **內容**：GitHub Actions 的 GCP 認證設置
- **適用對象**：DevOps 工程師、系統管理員
- **主要內容**：
  - 當前認證機制說明
  - Workload Identity Federation (WIF) 設置
  - 從服務帳戶金鑰遷移到 WIF
  - 安全最佳實踐

## 🚀 快速開始

### 對於開發者
1. 閱讀 [測試指南](./testing-guide.md) 了解如何編寫和執行測試
2. 確保你的 Cloud Function 符合測試要求
3. 使用提供的測試模板開始編寫測試

### 對於 DevOps 工程師
1. 閱讀 [認證指南](./authentication-guide.md) 了解認證機制
2. 考慮遷移到 WIF 以提升安全性
3. 監控 GitHub Actions 的執行狀況

## 🔄 CI/CD 流程概覽

```mermaid
graph TD
    A[推送到 main 分支] --> B[變更檢測]
    B --> C{有變更的函式?}
    C -->|是| D[執行測試]
    C -->|否| E[跳過部署]
    D --> F{測試通過?}
    F -->|是| G[Terraform 部署基礎設施]
    F -->|否| H[部署失敗]
    G --> I{基礎設施部署成功?}
    I -->|是| J[部署 Cloud Functions]
    I -->|否| H
    J --> K{函式部署成功?}
    K -->|是| L[部署完成]
    K -->|否| H
```

## 📋 相關資源

### GitHub Actions Workflows
- **主要工作流程**：`.github/workflows/deploy.yml`
- **觸發條件**：推送到 `main` 分支
- **執行環境**：Ubuntu latest with Python 3.11

### 模板文件
- **測試模板**：`.templates/cloud-function/test_main.py`
- **Terraform 模板**：`.templates/terraform/main.tf`
- **Makefile 模板**：`.templates/makefile/Makefile`

### 配置文件
- **GitHub Secrets**：
  - `GCP_CREDENTIALS_JSON` (當前使用)
  - `WIF_PROVIDER` (WIF 設置後使用)
- **GitHub Variables**：
  - `GCP_PROJECT_ID`: tagtoo-ml-workflow

## 🛠️ 故障排除

### 常見問題
1. **測試失敗**：檢查測試模板是否正確模擬外部依賴
2. **Terraform 錯誤**：驗證 Terraform 語法和權限
3. **認證問題**：確認服務帳戶權限或 WIF 設置

### 支援資源
- **GitHub Actions 日誌**：查看詳細的執行記錄
- **GCP Console**：監控資源部署狀況
- **Terraform State**：檢查基礎設施狀態

## 📝 貢獻指南

### 更新文檔
1. 修改相關的 Markdown 文件
2. 確保範例代碼和配置是最新的
3. 更新流程圖和圖表（如需要）

### 改進 CI/CD
1. 在測試環境驗證變更
2. 更新相關文檔
3. 通知團隊成員重要變更

---

**最後更新**：2025-07-25
**維護者**：DevOps Team
**版本**：v2.0
