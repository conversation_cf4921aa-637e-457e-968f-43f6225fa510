# CI/CD 測試流程指南

本文檔說明 ml-workflow-cloud-functions 專案中的 CI/CD 測試機制和最佳實踐。

## 🔄 CI/CD 測試流程概覽

GitHub Actions 會在每次推送到 `main` 分支時自動執行以下流程：

1. **變更檢測**: 檢查哪些 Cloud Functions 有變更
2. **測試執行**: 對有變更的函式執行測試
3. **基礎設施部署**: 使用 Terraform 部署必要的基礎設施
4. **函式部署**: 部署 Cloud Functions 到 GCP

## ✅ 測試執行條件

只對同時滿足以下兩個條件的專案執行測試：

### 必要條件
1. **存在 `tests/` 目錄**: 專案根目錄下必須有 `tests/` 資料夾
2. **Makefile 包含 test rule**: `Makefile` 中必須包含 `test` 目標

### 測試執行邏輯

```bash
# 檢查是否同時有 Makefile 中的 test rule 和 tests 目錄
if [ -f "$func/Makefile" ] && grep -q "test" "$func/Makefile" && [ -d "$func/tests" ]; then
  # 執行 make test
else
  # 跳過測試並顯示訊息
  echo "Skipping tests for $func - requires both Makefile with test rule and tests/ directory"
fi
```

## 📋 測試最佳實踐

### 1. 專案結構
```
your-cloud-function/
├── main.py                 # 主要程式碼
├── requirements.txt        # 生產依賴
├── requirements-test.txt   # 測試依賴 (可選)
├── Makefile               # 必須包含 test rule
├── tests/                 # 測試目錄
│   ├── test_main.py      # 主要測試
│   └── test_utils.py     # 工具測試
└── cloudbuild.yaml       # 部署配置
```

### 2. Makefile 設定範例

```makefile
# 基本測試 rule
test:
	python -m pytest tests/ -v

# 進階測試 rule (使用 Docker)
test: build-test
	docker run --rm --env CI=true $(IMAGE_NAME):$(TEST_TAG) \
		pytest --cache-clear -n auto -k 'not performance' -v

# 測試覆蓋率
test-cov:
	python -m pytest tests/ --cov=. --cov-report=term-missing
```

### 3. 測試依賴管理

#### requirements-test.txt 範例
```
pytest>=7.0.0
pytest-cov>=4.0.0
pytest-mock>=3.10.0
mock>=4.0.3
```

## 🚫 跳過測試的情況

以下情況下 GitHub Actions 會跳過測試：

1. **只有 `tests/` 目錄但沒有 Makefile**:
   ```
   Skipping tests for your-function - requires both Makefile with test rule and tests/ directory
   ```

2. **只有 Makefile 但沒有 `tests/` 目錄**:
   ```
   Skipping tests for your-function - requires both Makefile with test rule and tests/ directory
   ```

3. **Makefile 中沒有 `test` rule**:
   ```
   Skipping tests for your-function - requires both Makefile with test rule and tests/ directory
   ```

## 🔧 設定新專案的測試

### 步驟 1: 建立測試目錄
```bash
mkdir tests
touch tests/__init__.py
```

### 步驟 2: 撰寫基本測試
```python
# tests/test_main.py
import pytest
from main import your_function

def test_your_function():
    """測試主要功能"""
    result = your_function("test_input")
    assert result is not None
    assert isinstance(result, dict)
```

### 步驟 3: 建立或更新 Makefile
```makefile
.PHONY: test clean

test:
	@echo "Running tests..."
	python -m pytest tests/ -v --tb=short

clean:
	find . -type f -name "*.pyc" -delete
	find . -type d -name "__pycache__" -delete
```

### 步驟 4: 測試依賴 (可選)
```bash
# 建立測試依賴檔案
echo "pytest>=7.0.0" > requirements-test.txt
```

## 📊 測試失敗處理

### GitHub Actions 行為
- 任何函式測試失敗會導致整個部署流程停止
- 錯誤訊息會顯示在 GitHub Actions 日誌中
- 只有所有測試通過的函式才會被部署

### 常見測試失敗原因
1. **缺少依賴套件**: 檢查 `requirements.txt` 和 `requirements-test.txt`
2. **匯入錯誤**: 確保測試檔案能正確匯入主要模組
3. **環境變數未設定**: 測試中使用的環境變數需要適當的 mock
4. **外部服務依賴**: 使用 mock 模擬外部 API 呼叫

### 除錯技巧
```bash
# 本地執行測試
make test

# 詳細輸出
python -m pytest tests/ -v -s

# 執行特定測試
python -m pytest tests/test_main.py::test_specific_function -v
```

## 🎯 範例專案

參考 `lta-user-stats` 專案的完整測試設定：

- ✅ 有 `tests/` 目錄
- ✅ Makefile 包含完整的 `test` rule
- ✅ 使用 Docker 進行測試隔離
- ✅ 包含單元測試和整合測試
- ✅ 測試覆蓋率報告

## 🔗 相關連結

- [GitHub Actions 部署記錄](https://github.com/Tagtoo/ml-workflow-cloud-functions/actions)
- [模板使用指南](../.templates/docs/usage-guide.md)
- [Git Hooks 指南](git-hooks-guide.md)

---

**注意**: 這個測試機制的更新是為了確保所有執行測試的專案都有完整的測試環境設定，避免因為缺少依賴或配置不完整而導致的測試失敗。