# GitHub Actions GCP 認證指南

## 概述

本指南說明如何為 GitHub Actions 設置 Google Cloud Platform (GCP) 認證，包括當前使用的服務帳戶金鑰方式和推薦的 Workload Identity Federation (WIF) 方式。

## Workload Identity Federation (WIF)

Workload Identity Federation (WIF) 是 Google Cloud 推薦的認證方式，可以讓 GitHub Actions 安全地訪問 GCP 資源，無需在 GitHub Secrets 中存儲長期憑證。

## 當前狀態

✅ **Terraform 和 gcloud 部署使用相同的服務帳戶**
- 服務帳戶：`<EMAIL>`
- 認證方式：`credentials_json` (服務帳戶金鑰)
- 權限：兩種部署方式權限一致

## WIF 設置步驟

### 1. 創建 Workload Identity Pool

```bash
# 設置變數
export PROJECT_ID="tagtoo-ml-workflow"
export POOL_ID="github-actions-pool"
export PROVIDER_ID="github-actions-provider"
export SERVICE_ACCOUNT="<EMAIL>"
export REPO="Tagtoo/ml-workflow-cloud-functions"

# 創建 Workload Identity Pool
gcloud iam workload-identity-pools create $POOL_ID \
  --project=$PROJECT_ID \
  --location="global" \
  --display-name="GitHub Actions Pool"
```

### 2. 創建 Workload Identity Provider

```bash
# 創建 Provider
gcloud iam workload-identity-pools providers create-oidc $PROVIDER_ID \
  --project=$PROJECT_ID \
  --location="global" \
  --workload-identity-pool=$POOL_ID \
  --display-name="GitHub Actions Provider" \
  --attribute-mapping="google.subject=assertion.sub,attribute.actor=assertion.actor,attribute.repository=assertion.repository" \
  --issuer-uri="https://token.actions.githubusercontent.com"
```

### 3. 授權服務帳戶

```bash
# 允許 GitHub Actions 模擬服務帳戶
gcloud iam service-accounts add-iam-policy-binding $SERVICE_ACCOUNT \
  --project=$PROJECT_ID \
  --role="roles/iam.workloadIdentityUser" \
  --member="principalSet://iam.googleapis.com/projects/$(gcloud projects describe $PROJECT_ID --format='value(projectNumber)')/locations/global/workloadIdentityPools/$POOL_ID/attribute.repository/$REPO"
```

### 4. 獲取 Provider 資源名稱

```bash
# 獲取完整的 Provider 資源名稱
gcloud iam workload-identity-pools providers describe $PROVIDER_ID \
  --project=$PROJECT_ID \
  --location="global" \
  --workload-identity-pool=$POOL_ID \
  --format="value(name)"
```

### 5. 設置 GitHub Secrets

在 GitHub Repository Settings > Secrets and variables > Actions 中添加：

```
WIF_PROVIDER: projects/PROJECT_NUMBER/locations/global/workloadIdentityPools/github-actions-pool/providers/github-actions-provider
```

### 6. 更新 GitHub Actions

在 `.github/workflows/deploy.yml` 中：

```yaml
- id: auth
  uses: google-github-actions/auth@v2
  with:
    workload_identity_provider: '${{ secrets.WIF_PROVIDER }}'
    service_account: '<EMAIL>'
```

## 遷移步驟

### 階段 1：準備 WIF（不影響現有部署）
1. 執行上述 WIF 設置步驟
2. 添加 `WIF_PROVIDER` secret
3. GitHub Actions 仍使用 `credentials_json`

### 階段 2：切換到 WIF
1. 修改 `.github/workflows/deploy.yml`：
   - 註解 `credentials_json` 行
   - 取消註解 WIF 相關行
2. 測試部署確認正常運作

### 階段 3：清理（可選）
1. 刪除 `GCP_CREDENTIALS_JSON` secret
2. 停用或刪除服務帳戶金鑰

## 優勢

✅ **安全性**：無需長期憑證，自動輪換
✅ **簡潔性**：無需手動管理金鑰
✅ **審計性**：更好的存取日誌和追蹤
✅ **合規性**：符合 Google Cloud 最佳實踐

## 注意事項

⚠️ **測試建議**：先在測試環境驗證 WIF 設置
⚠️ **回滾計劃**：保留 `credentials_json` 作為備用方案
⚠️ **權限檢查**：確認 WIF 設置後權限與原本一致

## 驗證

設置完成後，可以在 GitHub Actions 日誌中看到：
```
Successfully configured Workload Identity Federation
```

而不是：
```
Successfully configured service account credentials
```
