# LTA 相關 Cloud Functions 關係說明

## 功能概述

此文件說明 LTA（Lifetime Value Analysis）相關 Cloud Functions 之間的關係和資料流向。

## 資料流程圖

```mermaid
graph TD
    %% 資料準備階段
    A[lta-user-stats] -->|產生使用者統計| B[BigQuery: 使用者行為資料]
    C[item-label-retrieve-csv-export] -->|標記商品標籤| B

    %% 特徵轉換
    B --> D[lta_feature_transform]
    B --> E[lta_retrained_feature_transform]

    %% 模型預測
    D --> F[lta_predict_new_model]
    E --> G[lta_predict_retrained_model]

    %% 預測結果處理
    F --> H[lta_second_layer]
    G --> H
    H --> I[lta_third_layer]

    %% 部署和驗證
    G --> J[Deploy_ttd]
    K[LTA_validation] --> L[LTA_validation_to_sheet]

    %% 相似受眾
    M[LTA_lookalike] --> N[相似受眾結果]

    %% 監控
    O[Cloud_function_monitor] -->|監控| P[所有 Cloud Functions]
```

## 功能說明

### 資料準備
1. `lta-user-stats`
   - 輸入：使用者行為資料
   - 輸出：BigQuery 使用者統計表格
   - 功能：計算使用者統計資料

2. `item-label-retrieve-csv-export`
   - 輸入：商品資料
   - 輸出：標記後的商品資料
   - 功能：為商品添加標籤

### 特徵處理
1. `lta_feature_transform`
   - 輸入：使用者行為資料
   - 輸出：特徵檔案（.ftr 格式）
   - 功能：舊版特徵轉換

2. `lta_retrained_feature_transform`
   - 輸入：使用者行為資料
   - 輸出：特徵檔案（.ftr 格式）
   - 功能：新版特徵轉換

### 模型預測
1. `lta_predict_new_model`（已棄用）
   - 輸入：特徵檔案
   - 輸出：預測結果（CSV）
   - 模型：lta_new2.pkl

2. `lta_predict_retrained_model`
   - 輸入：特徵檔案
   - 輸出：預測結果（CSV）
   - 模型：lta_retrain_v1.pkl

### 進階處理
1. `lta_second_layer`
   - 輸入：第一層預測結果
   - 輸出：第二層預測結果（Parquet）
   - 功能：進行第二層分群

2. `lta_third_layer`
   - 輸入：第二層預測結果
   - 輸出：第三層預測結果（Parquet）
   - 功能：進行最終分群

### 部署和驗證
1. `Deploy_ttd`
   - 輸入：retrained model 預測結果
   - 輸出：分割後的 CSV 檔案
   - 功能：準備 TTD 平台使用的資料

2. `LTA_validation`
   - 功能：驗證 LTA 結果
   - 輸出：驗證報告

3. `LTA_validation_to_sheet`
   - 輸入：驗證報告
   - 輸出：Google Sheet 報告
   - 功能：產生可視化報告

### 其他功能
1. `LTA_lookalike`
   - 功能：產生相似受眾
   - 輸出：相似受眾清單

2. `Cloud_function_monitor`
   - 功能：監控所有 Cloud Functions
   - 輸出：監控報告和警報

## 檔案路徑

### Cloud Storage
```
gs://tagtoo-ml-workflow/topic10/
├── firstparty/
│   └── first_party_{date}/
│       ├── lta_remarketing_{date}_ttd_model.csv
│       ├── lta_retrained_remarketing_{date}_ttd_model.csv
│       ├── lta_remarketing_{date}_ttd_model_second_layer.parquet
│       └── lta_remarketing_{date}_ttd_model_third_layer.parquet
└── LTA_feature/
    └── {start_date}_{end_date}/
        └── transformed/
            └── *.ftr
```

### BigQuery
```
tagtoo_export_results
├── lta_model_fb
├── lta_model_fb_{date}
└── lta_retrain_model_fb_{date}
```

## 注意事項

1. 版本管理
   - 目前同時存在新舊兩個版本的模型
   - 建議統一使用 retrained 版本

2. 資料依賴
   - 各層預測結果相互依賴
   - 需確保上游資料準確性

3. 監控重點
   - 預測結果分布
   - 執行時間
   - 錯誤率

4. 效能考量
   - 記憶體使用（8-32GB）
   - CPU 使用（2-8 cores）
   - 執行時間（30-60分鐘）