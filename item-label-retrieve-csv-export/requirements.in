#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile requirements.in
#
aiohappyeyeballs
aiohttp
aiosignal
async-timeout
attrs
blinker
cachetools
certifi
charset-normalizer
click
cloudevents
cloudpickle
dask
db-dtypes
decorator
deprecated
deprecation
flask
frozenlist
fsspec
functions-framework
gcsfs
google-api-core[grpc]
google-auth
google-auth-oauthlib
google-cloud-appengine-logging
google-cloud-audit-log
google-cloud-bigquery
google-cloud-core
google-cloud-logging
google-cloud-storage
google-crc32c
google-resumable-media
googleapis-common-protos[grpc]
grpc-google-iam-v1
grpcio
grpcio-status
gunicorn
idna
importlib-metadata
itsdangerous
jinja2
locket
markupsafe
multidict
numpy
oauthlib
opentelemetry-api
packaging
pandas
pandas-gbq
partd
propcache
proto-plus
protobuf
pyarrow
pyasn1
pyasn1-modules
pydata-google-auth
python-dateutil
pytz
pyyaml
requests
requests-oauthlib
rsa
six
toolz
typing-extensions
tzdata
urllib3
watchdog
werkzeug
wrapt
yarl
zipp
google-api-python-client
# The following packages are considered to be unsafe in a requirements file:
# setuptools
