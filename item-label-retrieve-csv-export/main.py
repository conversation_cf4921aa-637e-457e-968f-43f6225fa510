import functions_framework
import logging
import google.cloud.logging
from google_storage import GoogleStorageIO
from config import GOOGLE_APPLICATION_CREDENTIALS_PATH
from datetime import datetime
from pytz import timezone
import json
import pandas as pd
from dask import dataframe as dd
from make_query import LABEL_QUERY, make_user_query
from utils import *
from pipeline import *

# 新增本地端logging設定函數
def setup_local_logging():
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )

@functions_framework.http
def main(request):

    # 根據環境設定logging
    try:
        # Cloud Functions環境
        logging_client = google.cloud.logging.Client()
        logging_client.setup_logging()
    except Exception:
        # 本地測試環境
        setup_local_logging()
        logging.info("使用本地端logging設定")

    # 解析請求參數
    # 測試 cloud function 部署，請忽略這行註解
    # ---
    # 取得並驗證必要的請求參數
    request_json = request.get_json(silent=True)
    if not request_json:
        return '請求必須包含 JSON 資料', 400
        
    required_params = {
        'target_date': '指定日期',
        'ecid': '指定 ecid'
    }

    params = {}
    for param_key, description in required_params.items():
        if param_key not in request_json:
            return f'缺少必要參數 {param_key}：{description}', 400
        params[param_key] = request_json[param_key]
        
    # 解構參數以供後續使用
    target_date = params['target_date']
    ecid = int(params['ecid'])
    
    user_item_gcs_path = f"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/{target_date}/*.parquet"
    label_gcs_path = f"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/label_query_results/{target_date}/*.parquet"
    temp_table_id = "tagtoo-ml-workflow.temp_tables.user_query_results"

    logging.info({"message": "參數已匯入", "target_date": target_date})
    google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    GSIO = GoogleStorageIO(credentials=google_credentials)

    # 因為標籤資料有 JSON 欄位，所以需要先下載到 local 才能讀取
    save_query_results(
        QUERY=LABEL_QUERY,
        credentials=google_credentials,
        temp_table_id=temp_table_id,
        destination_uri=label_gcs_path
    )

    save_query_results(
        QUERY=make_user_query(target_date),
        credentials=google_credentials,
        temp_table_id=temp_table_id,
        destination_uri=user_item_gcs_path
    )
    logging.info({"message": "BigQuery 查詢完成", "temp_table_id": temp_table_id})

    """主要處理流程"""
    # 1. 載入資料
    label_df, user_item_df = load_data_with_dask(
        label_gcs_path,
        user_item_gcs_path,
    )
    logging.info({
        "message": "資料載入完成",
        "unique_users": len(user_item_df['permanent'].drop_duplicates()),
        "labeled_articles": len(label_df),
    })

    # 2. 處理標籤
    # * 會根據不同資料來源媒合受眾包
    label_df['structured_labels'] = label_df['structured_labels_str'].apply(lambda x: json.loads(x))
    columns_to_extract = [
        'Interests', 'OccupationalTraits', 'PersonalOpinions', 
        'BrowsingMotivations', 'PotentialPainPoints',
        'Category', 'Features', 'Industry', 'MarketSegmentation',
        'PotentialIssues', 'Purpose', 'Tone',
        'CoreDescription', 'Keywords'
    ]
    label_df = extract_label_fields(label_df, columns_to_extract)
    label_df = label_df.assign(label_str=lambda x: x.apply(concat_label_fields, axis=1)).query('label_str != ""')
    logging.info({"message": "標籤處理完成",})
    
    # 3. 合併與映射處理
    merged_df = process_merged_data(user_item_df, label_df)

    # 4. 篩選出個資料來源
    item_label_map = create_source_mappings(merged_df)
    logging.info({"message": "資料合併與映射完成",})
    
    # 5. 根據關鍵字大表中的所有 segments 與其關鍵字規則，篩選出符合條件的商品，並查找瀏覽過商品的 permanent
    audience_packages_df = read_audience_packages(google_credentials, "prod")
    
    # 若有匯入 ecid 參數，則只處理該 ecid 的關鍵字受眾包
    if ecid:
        audience_packages_df['data_source_id'] = audience_packages_df['data_source_id'].astype(int)
        audience_packages_df = audience_packages_df[audience_packages_df['data_source_id'] == ecid]
        audience_packages_df.reset_index(drop=True, inplace=True)
    
    result_df = process_segments(audience_packages_df, item_label_map)
    logging.info({"message": "使用者查找完畢", "result_rows": len(result_df)})

    # 5. 將結果匯出到 GCS
    result_path = "/tmp/result.csv"
    result_gcs_path = f"gs://tagtoo-ml-workflow-cloud-functions/item_label/retrieve_csv_export/{target_date}/*.csv"
    result_df.to_csv(result_path, index=False)
    GSIO.upload_file(localpath=result_path, gsuri=result_gcs_path)
    logging.info({
        "message": "資料匯出完成",
        "gcs_path": result_gcs_path,
        "exported_rows": len(result_df)
    })

    return '執行完畢', 200

# 如果是直接執行此檔案(本地測試)
if __name__ == "__main__":
    # 啟動 Functions Framework
    functions_framework.cloud_event(main) # 入口