from google.oauth2 import service_account
from google.cloud import bigquery
from apiclient.discovery import build
import pandas as pd
import json
import logging

def read_json(filepath: str):
    with open(filepath, mode='r') as f:
        return json.load(f)

def check_media_or_not(ecid):
    
	media_ecids = [
		2851, # -- Setn
		1347, # -- BusinessWeekly
		1604, # -- Ctee
		2625, # -- CMoney
		2913, # -- Babyhome
		3093, # -- MamiGuide
		3195, # -- Reurl
		3542 # -- VMFive
	]

	return ecid in media_ecids

def extract_all_label_fields(structured_labels):
    """
    從 structured_labels 中提取所有可能的欄位
    若欄位不存在則返回空列表或 None
    """
    # 取得主要區塊
    audience_profile = structured_labels.get('AudienceProfile', {})
    content_tags = structured_labels.get('ContentTags', {})
    
    fields = {
        # AudienceProfile 區塊
        'Interests': audience_profile.get('Interests', []),
        'OccupationalTraits': audience_profile.get('OccupationalTraits', []),
        'PersonalOpinions': audience_profile.get('PersonalOpinions', []),
        'BrowsingMotivations': audience_profile.get('BrowsingMotivations', []),
        'PotentialPainPoints': audience_profile.get('PotentialPainPoints', []),
        
        # ContentTags 區塊
        'Category': content_tags.get('Category', []),
        'Features': content_tags.get('Features', []),
        'Industry': content_tags.get('Industry', []),
        'MarketSegmentation': content_tags.get('MarketSegmentation', []),
        'PotentialIssues': content_tags.get('PotentialIssues', []),
        'Purpose': content_tags.get('Purpose', None),
        'Tone': content_tags.get('Tone', []),
        
        # 其他主要欄位
        'CoreDescription': structured_labels.get('CoreDescription', None),
        'Keywords': structured_labels.get('Keywords', [])
    }
    
    return fields

def concat_label_fields(df):
    """連接各個欄位的標籤，處理字典類型並記錄警告"""
    def process_list(lst, field_name):
        # 如果是字串（core description），則直接把字串包裝成 list
        if isinstance(lst, str):
            return [lst]
        # 如果是 list，則處理 list 中的每一個 item
        elif isinstance(lst, list):
            processed = []
            # 處理 list 中的每一個 item，如果是 dict，則取得 dict 中的值，如果是 str，則直接加入 processed，如果是其他類型，則記錄警告
            for item in lst:
                if isinstance(item, dict):
                    # 取得字典中的值
                    dict_values = [str(v) for v in item.values()]
                    processed.extend(dict_values)
                    # logging.info(f"Field '{field_name}' contains dict: {item}, extracted values: {dict_values}")
                elif isinstance(item, str):
                    processed.append(item)
                else:
                    logging.warning(f"Field '{field_name}' contains unexpected type: {type(item)}, value: {item}")
            return processed
        else:
            # 如果 lst 不是 list 或 str，則記錄警告
            logging.warning(f"Field '{field_name}' expected list or string but got {type(lst)}: {lst}")
            return []

    fields = [
        ', '.join(process_list(df['Category'], 'Category')),
        ', '.join(process_list(df['Industry'], 'Industry')),
        ', '.join(process_list(df['Interests'], 'Interests')),
        ', '.join(process_list(df['PotentialPainPoints'], 'PotentialPainPoints')),
        ', '.join(process_list(df['PotentialIssues'], 'PotentialIssues')),
        ', '.join(process_list(df['OccupationalTraits'], 'OccupationalTraits')),
        ', '.join(process_list(df['PersonalOpinions'], 'PersonalOpinions')),
        ', '.join(process_list(df['Keywords'], 'Keywords')),
        # ', '.join(process_list(df['CoreDescription'], 'CoreDescription'))
    ]
    return ', '.join(field for field in fields if field)

def get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH):
    # verify account
    google_credentials = service_account.Credentials.from_service_account_file(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    project_id = google_credentials.project_id
    return google_credentials, project_id

def concat_audience_profile_as_string(df):
	return ', '.join(df['Interests'] + df['OccupationalTraits'] + df['PersonalOpinions'])

def read_json(filepath: str):
    with open(filepath, mode='r') as f:
        return json.load(f)

def save_query_results(QUERY: str, credentials, temp_table_id: str, destination_uri: str):
    # google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    bq_client = bigquery.Client(credentials=credentials)
    
    # 建立暫存表格
    # temp_table_id = f"tagtoo-ml-workflow.temp_tables.user_query_results"
    
    # 使用 CREATE OR REPLACE TABLE 來儲存查詢結果
    create_table_query = f"""
    CREATE OR REPLACE TABLE {temp_table_id} AS
    {QUERY}
    """
    
    job = bq_client.query(create_table_query)
    job.result()  # 等待查詢完成
    
    # 將結果匯出到 GCS
    # bucket_name = "tagtoo-ml-workflow-cloud-functions"
    # destination_uri = f"gs://{bucket_name}/item_label/query_data/user_query_results_*.parquet"
    
    dataset_ref = bq_client.dataset('temp_tables', project='tagtoo-ml-workflow')
    table_ref = dataset_ref.table('user_query_results')
    
    job_config = bigquery.ExtractJobConfig()
    job_config.destination_format = bigquery.DestinationFormat.PARQUET
    
    extract_job = bq_client.extract_table(
        table_ref,
        destination_uri,
        job_config=job_config
    )
    extract_job.result()
    
    # logging.info(f"資料已匯入 {destination_uri}")

    return

def read_audience_packages(credentials, page_name: str) -> pd.DataFrame:
    """讀取 Taxonomy 資料並返回 DataFrame"""
    spreadsheet_id = '1VZxkSc6XsPSKfFPP8M9V8IyXUh4GxGpc6lq63h1Veco'
    columns = ['name', 'description', 'path', 'data_source', 'data_source_id', 'segment_id', 'or_keywords', 'and_keywords']
    cell_range = 'A2:H'
    
    service = build('sheets', 'v4', credentials=credentials)
    
    sheet = service.spreadsheets()
    result = sheet.values().get(spreadsheetId=spreadsheet_id, range=f"{page_name}!{cell_range}").execute()
    values = result.get('values', [])
    
    if not values:
        # logging.info("未找到資料。")
        return pd.DataFrame()
    
    df = pd.DataFrame(values, columns=columns)
    return df