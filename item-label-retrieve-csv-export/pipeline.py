from dask import dataframe as dd
import pandas as pd
from utils import *

def load_data_with_dask(label_path: str, user_item_path: str) -> tuple:
    """
    使用 dask 統一讀取所需的資料檔案
    
    Args:
        label_path (str): 標籤資料的路徑
        user_item_path (str): 使用者商品資料的路徑
        segment_path (str): segment 資料的路徑
        
    Returns:
        tuple: (label_df, user_item_df, segment_df)
    """
    # 使用 dask 讀取 parquet 檔案
    label_df = dd.read_parquet(label_path, assume_missing=True).compute()
    user_item_df = dd.read_parquet(user_item_path, assume_missing=True).compute()
    
    return label_df, user_item_df

# 使用範例：
# label_df, user_item_df, new_segment_df = load_data_with_dask(
#     LABEL_GCS_PATH,
#     USER_ITEM_GCS_PATH,
#     new_segment_path
# )

# 使用函數式處理標籤欄位
def extract_label_fields(df: pd.DataFrame, columns: list) -> pd.DataFrame:
    """批量處理標籤欄位"""
    for field in columns:
        df[field] = df['structured_labels'].apply(
            lambda x: extract_all_label_fields(x)[field]
        )
    return df

def process_merged_data(user_df: pd.DataFrame, label_df: pd.DataFrame) -> pd.DataFrame:
    """處理合併後的資料"""
    # 只選取需要的欄位進行合併
    merged = (user_df
             .merge(label_df[['name', 'label_str']], on='name', how='inner')
             .assign(is_media=lambda x: x['ec_id'].apply(check_media_or_not)))
    
    return merged

def create_source_mappings(merged_df: pd.DataFrame) -> dict:
    """建立資料來源映射"""
    mappings = {}
    
    # 分別處理 media 和非 media 的資料
    media_sources = merged_df.query('is_media == 1')
    non_media_sources = merged_df.query('is_media == 0')
    
    # 將 media 和非 media 的資料都按照 ec_id 分組
    mappings.update({
        ecid: group for ecid, group in media_sources.groupby('ec_id')
    })
    
    mappings.update({
        ecid: group for ecid, group in non_media_sources.groupby('ec_id')
    })
    
    # 保留原本 9999 的映射，包含所有非 media 的資料
    mappings[9999] = non_media_sources
    
    return mappings

def process_segments(audience_packages_df: pd.DataFrame, item_label_map: dict) -> pd.DataFrame:
    """處理 segment 相關邏輯"""
    
    audience_packages_df['or_keywords'] = audience_packages_df['or_keywords'].str.split(', ')
    audience_packages_df['and_keywords'] = audience_packages_df['and_keywords'].str.split(', ')
    audience_packages_df['data_source_id'] = audience_packages_df['data_source_id'].astype(int)

    def create_segment_permanent_mapping(row, item_label_map):
        """建立單一 segment 到 permanent 的映射，先篩選 and_keywords 再篩選 or_keywords"""
        source_df = item_label_map[row['data_source_id']]
        filtered_df = source_df.copy()
        
        # 如果有 and_keywords，先篩選必須包含的關鍵字
        if isinstance(row['and_keywords'], list) and len(row['and_keywords']) > 0:
            for keyword in row['and_keywords']:
                filtered_df = filtered_df[filtered_df['label_str'].str.contains(keyword, regex=True)]

        # 再篩選符合任一 or_keywords 的資料
        if isinstance(row['or_keywords'], list) and len(row['or_keywords']) > 0:
            or_pattern = '|'.join(map(str, row['or_keywords']))  # 確保所有元素都是字串
            filtered_df = filtered_df[filtered_df['label_str'].str.contains(or_pattern, regex=True)]

        permanents = filtered_df['permanent'].unique().tolist()
        logging.info(f"segment_id: {row['segment_id']}, permanents_count: {len(permanents)}")
        return permanents if permanents else []

    # 建立 segment_id 到 permanent 的映射
    segment_id_to_permanent_map = {}
    total_segments = len(audience_packages_df)
    for idx, row in audience_packages_df.iterrows():
        permanents = create_segment_permanent_mapping(row, item_label_map)
        if permanents:  # 只保留有對應到 permanent 的項目
            segment_id_to_permanent_map[row['segment_id']] = permanents
        
        # 每處理10個 segments 就印出進度
        if (idx + 1) % 10 == 0:
            progress = ((idx + 1) / total_segments) * 100
            logging.info(f"處理進度: {progress:.1f}% ({idx + 1}/{total_segments})")
    
    # 使用原本的方法處理資料
    return (pd.Series(segment_id_to_permanent_map)
            .to_frame(name='permanent')
            .reset_index()
            .rename(columns={'index': 'segment_id'})
            .explode('permanent')
            .groupby('permanent', as_index=False)
            .agg({'segment_id': lambda x: ','.join(x)})) # 用半形逗號分格，跟 gid_mapping_export (bigquery scheduler query) 的格式一致