#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile requirements.in
#
aiohappyeyeballs==2.4.4
    # via
    #   -r requirements.in
    #   aiohttp
aiohttp==3.11.11
    # via
    #   -r requirements.in
    #   gcsfs
aiosignal==1.3.2
    # via
    #   -r requirements.in
    #   aiohttp
async-timeout==5.0.1
    # via
    #   -r requirements.in
    #   aiohttp
attrs==24.3.0
    # via
    #   -r requirements.in
    #   aiohttp
blinker==1.9.0
    # via
    #   -r requirements.in
    #   flask
cachetools==5.5.0
    # via
    #   -r requirements.in
    #   google-auth
certifi==2024.12.14
    # via
    #   -r requirements.in
    #   requests
charset-normalizer==3.4.1
    # via
    #   -r requirements.in
    #   requests
click==8.1.8
    # via
    #   -r requirements.in
    #   dask
    #   flask
    #   functions-framework
cloudevents==1.11.0
    # via
    #   -r requirements.in
    #   functions-framework
cloudpickle==3.1.0
    # via
    #   -r requirements.in
    #   dask
dask==2024.12.1
    # via -r requirements.in
db-dtypes==1.3.1
    # via
    #   -r requirements.in
    #   pandas-gbq
decorator==5.1.1
    # via
    #   -r requirements.in
    #   gcsfs
deprecated==1.2.15
    # via
    #   -r requirements.in
    #   opentelemetry-api
deprecation==2.1.0
    # via
    #   -r requirements.in
    #   cloudevents
flask==3.1.0
    # via
    #   -r requirements.in
    #   functions-framework
frozenlist==1.5.0
    # via
    #   -r requirements.in
    #   aiohttp
    #   aiosignal
fsspec==2024.12.0
    # via
    #   -r requirements.in
    #   dask
    #   gcsfs
functions-framework==3.8.2
    # via -r requirements.in
gcsfs==2024.12.0
    # via -r requirements.in
google-api-core[grpc]==2.24.0
    # via
    #   -r requirements.in
    #   google-api-python-client
    #   google-cloud-appengine-logging
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-logging
    #   google-cloud-storage
    #   pandas-gbq
google-api-python-client==2.157.0
    # via -r requirements.in
google-auth==2.37.0
    # via
    #   -r requirements.in
    #   gcsfs
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-auth-oauthlib
    #   google-cloud-appengine-logging
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-logging
    #   google-cloud-storage
    #   pandas-gbq
    #   pydata-google-auth
google-auth-httplib2==0.2.0
    # via google-api-python-client
google-auth-oauthlib==1.2.1
    # via
    #   -r requirements.in
    #   gcsfs
    #   pandas-gbq
    #   pydata-google-auth
google-cloud-appengine-logging==1.5.0
    # via
    #   -r requirements.in
    #   google-cloud-logging
google-cloud-audit-log==0.3.0
    # via
    #   -r requirements.in
    #   google-cloud-logging
google-cloud-bigquery==3.27.0
    # via
    #   -r requirements.in
    #   pandas-gbq
google-cloud-core==2.4.1
    # via
    #   -r requirements.in
    #   google-cloud-bigquery
    #   google-cloud-logging
    #   google-cloud-storage
google-cloud-logging==3.11.3
    # via -r requirements.in
google-cloud-storage==2.19.0
    # via
    #   -r requirements.in
    #   gcsfs
google-crc32c==1.6.0
    # via
    #   -r requirements.in
    #   google-cloud-storage
    #   google-resumable-media
google-resumable-media==2.7.2
    # via
    #   -r requirements.in
    #   google-cloud-bigquery
    #   google-cloud-storage
googleapis-common-protos[grpc]==1.66.0
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-cloud-audit-log
    #   grpc-google-iam-v1
    #   grpcio-status
grpc-google-iam-v1==0.14.0
    # via
    #   -r requirements.in
    #   google-cloud-logging
grpcio==1.69.0
    # via
    #   -r requirements.in
    #   google-api-core
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
grpcio-status==1.69.0
    # via
    #   -r requirements.in
    #   google-api-core
gunicorn==23.0.0
    # via
    #   -r requirements.in
    #   functions-framework
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
idna==3.10
    # via
    #   -r requirements.in
    #   requests
    #   yarl
importlib-metadata==8.5.0
    # via
    #   -r requirements.in
    #   dask
    #   opentelemetry-api
itsdangerous==2.2.0
    # via
    #   -r requirements.in
    #   flask
jinja2==3.1.5
    # via
    #   -r requirements.in
    #   flask
locket==1.0.0
    # via
    #   -r requirements.in
    #   partd
markupsafe==3.0.2
    # via
    #   -r requirements.in
    #   jinja2
    #   werkzeug
multidict==6.1.0
    # via
    #   -r requirements.in
    #   aiohttp
    #   yarl
numpy==2.2.1
    # via
    #   -r requirements.in
    #   db-dtypes
    #   pandas
    #   pandas-gbq
oauthlib==3.2.2
    # via
    #   -r requirements.in
    #   requests-oauthlib
opentelemetry-api==1.29.0
    # via
    #   -r requirements.in
    #   google-cloud-logging
packaging==24.2
    # via
    #   -r requirements.in
    #   dask
    #   db-dtypes
    #   deprecation
    #   google-cloud-bigquery
    #   gunicorn
    #   pandas-gbq
pandas==2.2.3
    # via
    #   -r requirements.in
    #   db-dtypes
    #   pandas-gbq
pandas-gbq==0.26.1
    # via -r requirements.in
partd==1.4.2
    # via
    #   -r requirements.in
    #   dask
propcache==0.2.1
    # via
    #   -r requirements.in
    #   aiohttp
    #   yarl
proto-plus==1.25.0
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-cloud-appengine-logging
    #   google-cloud-logging
protobuf==5.29.2
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-cloud-appengine-logging
    #   google-cloud-audit-log
    #   google-cloud-logging
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   proto-plus
pyarrow==18.1.0
    # via
    #   -r requirements.in
    #   db-dtypes
    #   pandas-gbq
pyasn1==0.6.1
    # via
    #   -r requirements.in
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.1
    # via
    #   -r requirements.in
    #   google-auth
pydata-google-auth==1.9.0
    # via
    #   -r requirements.in
    #   pandas-gbq
pyparsing==3.2.1
    # via httplib2
python-dateutil==2.9.0.post0
    # via
    #   -r requirements.in
    #   google-cloud-bigquery
    #   pandas
pytz==2024.2
    # via
    #   -r requirements.in
    #   pandas
pyyaml==6.0.2
    # via
    #   -r requirements.in
    #   dask
requests==2.32.3
    # via
    #   -r requirements.in
    #   gcsfs
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-storage
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via
    #   -r requirements.in
    #   google-auth-oauthlib
rsa==4.9
    # via
    #   -r requirements.in
    #   google-auth
six==1.17.0
    # via
    #   -r requirements.in
    #   python-dateutil
toolz==1.0.0
    # via
    #   -r requirements.in
    #   dask
    #   partd
typing-extensions==4.12.2
    # via
    #   -r requirements.in
    #   multidict
tzdata==2024.2
    # via
    #   -r requirements.in
    #   pandas
uritemplate==4.1.1
    # via google-api-python-client
urllib3==2.3.0
    # via
    #   -r requirements.in
    #   requests
watchdog==6.0.0
    # via
    #   -r requirements.in
    #   functions-framework
werkzeug==3.1.3
    # via
    #   -r requirements.in
    #   flask
    #   functions-framework
wrapt==1.17.0
    # via
    #   -r requirements.in
    #   deprecated
yarl==1.18.3
    # via
    #   -r requirements.in
    #   aiohttp
zipp==3.21.0
    # via
    #   -r requirements.in
    #   importlib-metadata

# The following packages are considered to be unsafe in a requirements file:
# setuptools
