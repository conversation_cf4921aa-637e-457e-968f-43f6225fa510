LABEL_QUERY = """
SELECT 
  id,
  name,
  ec_name,
  TO_JSON_STRING(structured_labels) AS structured_labels_str,
  ec_id,
  industry_id,
  industry_name
FROM `tagtoo-ml-workflow.data_prod.item_labels`
"""

def make_user_query(target_date: str) -> str:
    return f"""
SELECT
  DISTINCT permanent,
  items.name,
  label.ec_id
FROM
  `tagtoo-tracking.event_prod.tagtoo_event`,
  UNNEST(event.items) items
JOIN
  `tagtoo-ml-workflow.data_prod.item_labels` label
ON
  items.name = label.name
WHERE
  DATE(event_time, 'Asia/Taipei') = "{target_date}"
  AND event.name = 'view_item'
"""