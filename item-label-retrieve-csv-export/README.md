# item-label-retrieve-permanent

> 一個用於查找並標記 EC 使用者瀏覽商品標籤的 Cloud Function

## 目錄

- [背景說明](#背景說明)
- [功能簡介](#功能簡介)
- [安裝方式](#安裝方式)
- [使用說明](#使用說明)
- [必要配置](#必要配置)
- [流程說明](#流程說明)

## 背景說明

此專案為 Tagtoo 廣告投放系統的一部分，用於根據使用者瀏覽的商品來為受眾貼上使用者標籤（LTA 標籤）。

## 功能簡介

根據 [關鍵字受眾包大表](https://docs.google.com/spreadsheets/d/1VZxkSc6XsPSKfFPP8M9V8IyXUh4GxGpc6lq63h1Veco/edit?gid=0#gid=0)，查找前一天在 Tagtoo Event 中某間 EC 或 媒體有進站的使用者，以及瀏覽了哪些商品與商品標籤，進一步找出符合受眾包各自關鍵字的商品，並替瀏覽過該商品的使用者貼上使用者標籤，透過這種貼標方式，期望更精準地進行廣告投放或分析報告的產出。

> 關鍵字可以人工發想或是透過 [關鍵字產生器](https://github.com/Tagtoo/ml-workflow-semi-automation-tools/tree/main/audience_keywords_generator) 產生出來

主要功能包括:
1. 從 BigQuery 中獲取標籤和用戶-商品數據
2. 處理新的受眾群體數據
3. 將處理後的數據上傳到 BigQuery 的 LTA 大表

## 安裝方式

此專案透過 GitHub Actions 自動部署至 Google Cloud Functions，無需手動安裝。

### 部署流程
1. 當程式碼推送到 `main` 分支時，會自動觸發部署
2. GitHub Actions 會執行 `.github/workflows/deploy.yml` 中定義的部署步驟
3. 部署完成後，Cloud Function 會自動更新

## 使用說明

此 Cloud Function 設計為自動化運作，主要透過以下方式觸發：

### 排程執行
- 透過 Cloud Scheduler 每日自動觸發
- 排程時間：每日 UTC+8 00:30
- 觸發方式：HTTP POST 請求
- 確保在設定 Cloud Scheduler 時，有傳入以下的 API 參數

### API 參數

- `upload_gate`: 上傳控制開關 (0 或 1)

## 必要配置
1. BigQuery 資料表設定
   - 確保有讀取權限與寫入的專案：`tagtoo-ml-workflow`

2. Google Sheet 關鍵字受眾包大表
   - [連結](https://docs.google.com/spreadsheets/d/1VZxkSc6XsPSKfFPP8M9V8IyXUh4GxGpc6lq63h1Veco/edit?usp=sharing)
   - 將想要新增的受眾包資料，新增在 `prod` 工作表中
   - 這個 cloud function 會從這個 google sheet 中的 `prod` 工作表中讀取資料，測試資料請新增在 `dev` 工作表中
   - 必要欄位
     - `segment_id`: 受眾包的 ID
     - `data_source_id`: 資料來源的 ID，目前有三種類別（媒體、塔圖全站 EC、單一 EC），受眾包只會篩選這個 id 底下的商品或文章
     - `or_keywords`: 受眾包的關鍵字，會以 OR 運算子連接
   - 選填欄位
     - `name`: 受眾包名稱
     - `description`: 受眾包描述
     - `path`: 受眾包路徑
     - `data_source`: 資料來源的中文或英文名稱
     - `and_keywords`: 受眾包的關鍵字，會以 AND 運算子連接

## 流程說明

1. 接收 HTTP 請求，解析請求參數
2. 獲取 Google 認證
3. 從 BigQuery 查詢資料
4. 讀取受眾包檔案
5. 過濾標籤數據
6. 媒合使用者
7. 將結果上傳到 BigQuery

### 監控與維護
- 可在 Cloud Functions 控制台查看執行記錄
- Cloud_function_monitor 這個 Cloud Function 會監控其他 functions，若未執行成功，會透過 gmail 示警通知

## 維護者

Tagtoo Data Team

## 如何貢獻

歡迎提交 Issue 與 Pull Request。