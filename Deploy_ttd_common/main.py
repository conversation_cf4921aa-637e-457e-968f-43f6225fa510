import os, json, re
import pytz
import argparse
import logging
import asyncio
import async_timeout
import aiohttp
from math import ceil
from datetime import datetime
from dask import dataframe
from asyncio.locks import Semaphore
import google_storage
from google.oauth2 import service_account

# Utils functions
def split_csv_file(gs_file_path, date, credentials_key_path, MAX_LINES_PER_CSV_FILE):
    file_name, file_ext = os.path.splitext(os.path.basename(gs_file_path))
    file_path = '/tmp/'
    output_file_name_template = f'{file_path}{file_name}_{date}_splitted_*{file_ext}'

    # verify account
    credentials = service_account.Credentials.from_service_account_file(credentials_key_path)
    project_id = credentials.project_id
    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    tmp_path = '/tmp/download_file.csv'
    GSIO.download_to_path(gsuri=gs_file_path, localpath=tmp_path)
    file = dataframe.read_csv(tmp_path)
    lines_count = len(file.index)
    partitions = ceil(lines_count / MAX_LINES_PER_CSV_FILE)

    # split orginal csv file into smaller csv files
    print(f'Got {lines_count} lines of data. Will divided into {partitions} csv files.')
    splits = file.repartition(npartitions=partitions)
    split_paths = splits.to_csv(output_file_name_template, index=False)
    return split_paths


def make_digest(message, key):
    import hashlib
    import hmac
    import base64

    key = bytes(key, 'UTF-8')
    message = bytes(message, 'UTF-8')
    digester = hmac.new(key, message, hashlib.sha1)
    signature1 = digester.digest()
    signature2 = base64.standard_b64encode(signature1)

    return str(signature2, 'UTF-8')


def remove_splitted_files(file_paths):
    for file_path in file_paths:
        if os.path.isfile(file_path):
            os.remove(file_path)


async def _post(session, url, data, signature):
    retry = 0
    max_try = 10
    headers = {
        'TtdSignature': signature,
        'Content-Length': str(len(data))
    }
    while retry <= max_try:
        response = None
        try:
            with async_timeout.timeout(5):
                async with session.post(url, data=data, headers=headers) as response:
                    assert response.status == 200
                    response = await response.read()
                break
        except (Exception, asyncio.TimeoutError) as e:
            if response:
                print(f'An error occurred. Retry: {retry}. Status Code: {response.status}. Reason: {response.reason}')
            else:
                print(f'An error occurred. Retry: {retry}. Error: {type(e).__name__}')

            if retry == max_try:
                if response:
                    response.raise_for_status()
                else:
                    raise e
            else:
                retry += 1
                await asyncio.sleep(3 * retry)

    await asyncio.sleep(1)
    return response


async def _bound_post(semaphore, session, url, data, signature):
    async with semaphore:
        result = await _post(session, url, data, signature)
        print(result)
        return result


async def post_data(semaphore, connection, url, abs_file_paths, **kwargs):
    tasks = []
    valid_date = kwargs.get('valid_date')
    expired_minutes = kwargs.get('expired_minutes')
    DATA_PROVIDER_ID = kwargs.get('data_provider_id')
    MAX_BYTES_PER_REQUEST = kwargs.get('max_bytes_per_request')
    SECRET = kwargs.get('secret')

    # limit the requests
    semaphore = asyncio.Semaphore(semaphore)
    connector = aiohttp.TCPConnector(limit=connection)

    # keep connection alive for all requests.
    async with aiohttp.ClientSession(connector=connector) as session:
        for abs_file_path in abs_file_paths:
            df = dataframe.read_csv(abs_file_path).compute()
            data_size = 0
            total_rows = len(df.index) - 1  # - 1 bcz for loop start from 0

            ttd_data = {}
            ttd_data['DataProviderId'] = DATA_PROVIDER_ID
            ttd_data['Items'] = []
            for index, row in df.iterrows():
                row_data = {}
                row_data['Data'] = []
                row_data['TDID'] = row.ttd_cookie
                for segment in row.segment_id.split(','):
                    segment = re.sub(r'\D', '', segment)
                    row_data['Data'].append(
                        {
                            "TimestampUtc": valid_date,
                            "Name": segment,
                            "TtlInMinutes": expired_minutes
                        }
                    )
                ttd_data['Items'].append(row_data)

                data_size = len(json.dumps(ttd_data))
                if (data_size > MAX_BYTES_PER_REQUEST) or (index == total_rows):
                    serialized_ttd_data = json.dumps(ttd_data)
                    signature = make_digest(serialized_ttd_data, SECRET)
                    task = asyncio.ensure_future(_bound_post(semaphore, session, url, serialized_ttd_data, signature))
                    tasks.append(task)

                    data_size = 0
                    ttd_data['Items'] = []

        await asyncio.gather(*tasks)

def main(request):

    logging.basicConfig(level=logging.INFO)

    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """

    # * external argument
    request_json = request.get_json(silent=True)
    request_args = request.args

    if request_json and 'path' in request_json: # POST
        target_path = request_json['path']
    elif request_args and 'path' in request_args: # GET
        target_path = request_args['path']
    else:
        raise AttributeError("No path found in URL.")
    print("Target path:", target_path)

    # TTD Settings
    DATA_PROVIDER_ID = 'tagtoo'
    SECRET = '53c1392cfd354e379f6ebcdeb34d3310'
    ASIA_API_HOST = 'sin-data.adsrvr.org'

    # Segment settings
    DATE_FORMAT = '%Y%m%d'
    SEGMENT_VALID_DATE = datetime.now().strftime('%Y%m%d')  # Default is valid from now on, will convert to timezone-aware isoformat()
    SEGMENT_EXPIRATION_DAYS = 21  # Default is 21 days, will convert into minutes.

    MAX_LINES_PER_CSV_FILE = 3000  # To reduce memory consumption
    MAX_BYTES_PER_REQUEST = 3000000  # The maximum size of each POST request to TTD datacenter is 4 MB, but use 3MB for safety.
    
    TARGET_DATE = datetime.strftime(datetime.now(pytz.timezone('Asia/Taipei')), "%Y-%m-%d") # for predict path

    valid_date = SEGMENT_VALID_DATE
    expired_day = SEGMENT_EXPIRATION_DAYS
    semaphore = 1
    connection = 5
    api_host = ASIA_API_HOST
    credentials_key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json' 
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    predict = target_path
    timezone = pytz.timezone('Asia/Taipei')
    dt = datetime.strptime(valid_date, DATE_FORMAT)
    timezone_aware = timezone.localize(dt)
    valid_date = timezone_aware.isoformat()
    expired_minutes = expired_day * 24 * 60

    # print("Date", valid_date)
    file_paths = split_csv_file(predict, valid_date, credentials_key_path, MAX_LINES_PER_CSV_FILE)
    url = f'https://{api_host}/data/thirdparty'
    print(f'POSTing data to "{url}"')

    loop = asyncio.new_event_loop()  # Step 1: Create a new event loop
    asyncio.set_event_loop(loop)  # Step 2: Set the new event loop as the default one for the current thread

    future = asyncio.ensure_future(post_data(
        semaphore, connection, url, file_paths, valid_date=valid_date, expired_minutes=expired_minutes, data_provider_id=DATA_PROVIDER_ID, max_bytes_per_request=MAX_BYTES_PER_REQUEST, secret=SECRET
    ))

    loop.run_until_complete(future)  # Step 3: Run your async code
    loop.close()  # Clean up the event loop after you're done

    remove_splitted_files(file_paths)
    # print('Job complete.')
    logging.info('Job complete.')

    return 'Success', 200