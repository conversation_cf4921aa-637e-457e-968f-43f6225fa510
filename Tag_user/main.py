import functions_framework
from datetime import datetime
from pytz import timezone
from google.oauth2 import service_account
import google_storage
from utils import *
import uuid
from typing import List, Dict, Set, Tuple

@functions_framework.http
def main(request):

    # * external argument
    request_json = request.get_json(silent=True)
    request_args = request.args

    if request_json and 'ecid' in request_json: # POST
        ecid = request_json['ecid']
    elif request_args and 'ecid' in request_args: # GET
        ecid = request_args['ecid']
    else:
        raise AttributeError("No ecid found in URL.")
    print("ECID:", ecid)

    # * external argument
    current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
    uid = str(uuid.uuid4())
    output_path = f'gs://tagtoo-ml-workflow/topic10/ec_product_tag/{ecid}/{current_date}' # output gcs path
    query_path = f'gs://tagtoo-ml-workflow/topic10/ec_product_tag/{ecid}/{current_date}/query/*.parquet'
    # yesterday_path = args.yesterday_user_data
    daily_product_tag_path = f'gs://tagtoo-bigquery-export/Fredrick_Test/product_tag/daily_product_tag_path/{ecid}_daily_product_tag_path.json'
    gcs_event_segment_id_dict_path = f'gs://tagtoo-bigquery-export/Fredrick_Test/product_tag/event_segment_id_dictionary/event_segment_id_{ecid}.json'
    ecid = int(ecid)

    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    project_id = credentials.project_id
    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    # * Query
    QUERY = f"""
    EXPORT DATA
    OPTIONS (
        uri = '{query_path}',
        format = 'PARQUET',
        overwrite = true)
    AS (
        SELECT
        permanent,
        ARRAY_AGG(DISTINCT items.name) AS product
        FROM
        `tagtoo-tracking.event_prod.tagtoo_event`,
        UNNEST(event.items) AS items
        WHERE
        DATE(event_time) = DATE_SUB(CURRENT_DATE(), INTERVAL 1 DAY)
        AND ec_id = {ecid}
        GROUP BY
        1
    );
    """
    client = bigquery.Client(credentials=credentials)
    query_job = client.query(QUERY)  # API request
    rows = query_job.result()  # Waits for query to finish
    print(f"{ecid} query job complete | Total {rows} rows")

    # print("Start tagging.")
    # * download daily product tag file from GCS
    product_tag_dict_path = '/tmp/daily_product_tag_path.json'
    product_tag_dict_gcs_path = daily_product_tag_path
    GSIO.download_to_path(gsuri=product_tag_dict_gcs_path, localpath=product_tag_dict_path)

    # * get latest product tag file
    product_tag_dict = read_json(product_tag_dict_path)
    product_tag_dict = flatten_data(product_tag_dict)
    tag_path = product_tag_dict['latest']
    print(f"{ecid} product tag latest GCS path:", tag_path)

    # ! important - user product tag and segment id dictionary
    # * download segment id dictionary
    # * need to update when every adding product tag and segment id

    event_segment_id_dict_path = "/tmp/event_segment_id_dict.json"
    GSIO.download_to_path(gsuri=gcs_event_segment_id_dict_path, localpath=event_segment_id_dict_path)
    with open(event_segment_id_dict_path, mode='r') as f:
        event_segment_id_dict = json.load(f)

    print("Load data.")
    df = parquet_load_data(query_path, storage_options={"token": key_path})
    # df_yesterday = parquet_load_data(yesterday_path)
    df_tag = json_load_data(tag_path, storage_options={"token": key_path})

    df_tag['name'] = df_tag['name'].apply(lambda x: x.strip())
    tag_dict = df_tag.set_index("name")['tag'].to_dict()

    if not len(tag_dict) == df_tag.drop_duplicates(subset=['name']).shape[0]:
        raise AssertionError("tag dataframe has duplicate product name")

    df.reset_index(drop=True, inplace=True) # reset index due to loading data frome dask dataframe (same index but different row)

    df['product'] = df['product'].apply(lambda x: [product.strip() for product in x]) # remove space of product name
    df['product'] = df['product'].apply(lambda x: list(filter(lambda product: product!='', x))) # remove '' in proudct name
    print("Afther strip")
    print(df.head())

    def find_tag_from_dict(product: str, missing_value: str = 'XXX') -> List[str]:
        try:
            return tag_dict[product]
        except:
            return [missing_value]
        
    def find_product_tag(x):
        duplicate_tag_list = [find_tag_from_dict(product) for product in x]
        tag_list = list(set(flatten_list(duplicate_tag_list)))
        return tag_list

    missing_value = 'XXX'
    df['tag'] = df['product'].apply(find_product_tag)
    df['tag'] = df['tag'].apply(lambda tag_list: list(filter(lambda tag: tag!=missing_value, tag_list)))

    # check_tag_in_segment_dict(df_tag, event_segment_id_dict)
    
    # df['segment_id'] = df['tag'].apply(lambda x: [event_segment_id_dict[tag] for tag in x])
    df['segment_id'] = df['tag'].apply(lambda x: [event_segment_id_dict.get(tag, "") for tag in x])
    df['segment_id'] = df['segment_id'].apply(lambda x: list(filter(lambda string: string!="", x)))
    # df['segment_id'] = df['segment_id'].apply(lambda x: ','.join(x))
    df = df[['permanent', 'segment_id']]
    # df = df.query("permanent.isin(@df_yesterday.permanent)") # only send user appeared yesterday to FB
    df = df.explode('segment_id')
    df = df[df['segment_id']!=""]
    df = df[~df['segment_id'].isnull()]
    print("Result dataframe")
    print(df.head())
    print(f"{len(df)} rows")

    path = "/tmp/result.csv"
    gcs_path = output_path + f"/result.csv"
    df.to_csv(path, index=False)
    GSIO.upload_file(gsuri=gcs_path, localpath=path)
    print(f"{ecid} result GCS path:", gcs_path)

    # print("Upload dataframe to BigQuery.")
    DATE = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y%m%d")
    project_id = "tagtoo-ml-workflow"
    table_id = f'tagtoo_export_results.special_lta_temp_for_update_{DATE}'
    schema_data = [
      {"name": "permanent", "type": "STRING"},
      {"name": "segment_id", "type": "STRING"}
    ]
    df.to_gbq(table_id, project_id=project_id, credentials=credentials, table_schema=schema_data, if_exists='append')
    
    # print("Insert data into lta update table.")
    # DATE = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y%m%d")

    # INSERT_QUERY = f"""
    # INSERT INTO `tagtoo-ml-workflow.tagtoo_export_results.special_lta_temp_for_update_{DATE}`
    # SELECT
    # DISTINCT permanent,
    # segment_id
    # FROM (
    # SELECT
    #     permanent,
    #     SPLIT(segment_id, ',') segment_id
    # FROM
    #     `{project_id}.{table_id}`),
    # UNNEST(segment_id) segment_id;

    # DROP TABLE `{project_id}.{table_id}`;
    # """

    # client = bigquery.Client(credentials=credentials)
    # query_job = client.query(QUERY)  # API request
    # rows = query_job.result()  # Waits for query to finish
    # print("Insert and drop job complete.")
    
    print(f"{ecid} all jobs complete.")

    return 'Success', 200
