import pandas as pd
from dask import dataframe
import json
from google.cloud import bigquery
import time
from typing import List, Dict

# * tag user utils

def read_json(path: str) -> List[Dict[str, str]]:
    with open(path, mode='r') as f:
        return json.load(f)
    
def write_to_json(path: str, data: List[Dict[str, str]]) -> None:
    with open(path, mode='w') as f:
        json.dump(data, f)

def parquet_load_data(path, **kwargs):
    """Loading the pieces in separate processes, then transferring all the data to be stitched into
    a single data-frame in the main process."""
    df = dataframe.read_parquet(path, assume_missing=True, **kwargs)
    return df.compute(scheduler="processes")

def json_load_data(path, **kwargs):
    """Loading the pieces in separate processes, then transferring all the data to be stitched into
    a single data-frame in the main process."""
    s = time.time()
    df = dataframe.read_json(path, **kwargs).compute(scheduler="processes")
    e = time.time()
    print("Total time {} seconds for loading {}.".format(round(e-s, 5), path.split('/')[-1]))
    return df

def flatten_list(regular_list: List[List[str]]) -> List[str]:
    return [item for sublist in regular_list for item in sublist]


def check_tag_in_segment_dict(
    df_tag: pd.DataFrame,
    segment_dict: Dict[str, str],
) -> None:
    
    all_tag = df_tag.explode('tag')['tag'].unique()
    
    count = 0
    for tag in all_tag:
        if tag in segment_dict.keys():
            count += 1
        else:
            print(tag)
            continue

    if count == all_tag.shape[0]:
        print("All product tag are in segment dictonary.")
    else:
        raise ValueError("Some tags are missing.")
        
    return

def flatten_data(json_object: List[str]) -> Dict[str, str]:
    output_dict = {}

    def flatten(x, name=''):
        if type(x) is dict:
            for a in x:
                flatten(x[a], name + a + '_')
        elif type(x) is list:
            i = 0
            for value in x:
                flatten(value)
                i += 1
        else:
            output_dict[name[:-1]] = x

    flatten(json_object)
    return output_dict