# get-product-tag

## 任務描述
將前一天進站過的使用者，根據各自事件收集的商品，貼上相對應的標籤（使用者標籤）。\
例如：某個人前一天所有事件中涵蓋的商品有 `[A, B, C]`，這三樣商品總共擁有 `[purchase, view_item, add_to_cart, hot, CVR, best]` 的標籤，因此這位使用者就會被貼上這些商品標籤。

## 商品標籤
- event: 各事件加總數量 Topk 的商品
- purchase: 購買事件加總數量 Topk 的商品
- view_item: 瀏覽事件加總數量 Topk 的商品
- add_to_wishlist: 加入許願池事件加總數量 Topk 的商品
- add_payment_info: 加入付款資訊事件加總數量 Topk 的商品
- add_to_cart: 加入購物車事件加總數量 Topk 的商品
- view_item_list: : 瀏覽清單事件加總數量 Topk 的商品
- promotion: 填單 / 促銷事件加總數量 Topk 的商品
- checkout: 結帳事件加總數量 Topk 的商品
- ADC: utm 來自廣告的瀏覽事件加總數量 Topk 的商品
- FBC: utm 來自 fb 的瀏覽事件加總數量 Topk 的商品
- AD_CVR: utm 來自廣告的轉換率 Topk 的商品
- CVR: 轉換率 Topk 的商品
- hot: 熱門商品 [(購買數 > 中位數) & (瀏覽數 > 中位數)]
- topic: 話題商品 [(購買數 <= 中位數) & (瀏覽數 > 中位數)]
- potential: 潛力商品 [(購買數 > 中位數) & (瀏覽數 <= 中位數)]
- cold: 冷門商品 [(購買數 <= 中位數) & (瀏覽數 <= 中位數)]
- best: 爆品 [(購買數 > 95百分位數) & (瀏覽數 > 95百分位數)]

## 使用說明
### Cloud Scheduler
- 一家 EC 架設一個 cloud sheduler
- 內文參數 `{"ecid": "<ecid>"}`
