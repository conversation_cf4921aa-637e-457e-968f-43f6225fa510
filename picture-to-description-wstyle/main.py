import functions_framework
from openai import OpenAI
import pandas as pd
from utils import (
    get_google_sheet_client,
    fetch_and_parse_data,
    generate_descriptions,
    update_google_sheet,
)
from google_storage import GoogleStorageIO

GSIO = GoogleStorageIO()

@functions_framework.http
def main(request):
    api_key = '***************************************************'
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    spreadsheet_url = 'https://docs.google.com/spreadsheets/d/1seb0ya3vOpFm0XqKCBTzcxuV5UN9fZmx6HjMvr6bJ_o'
    sheet_name = '圖片連結及描述'

    # Initialize Google Sheets client
    client = get_google_sheet_client(key_path)
    sheet = client.open_by_url(spreadsheet_url).worksheet(sheet_name)

    # Fetch and parse data
    url = "https://supplier-feed.tagtoo.com.tw/feed?ecid=1626&publisher=fb&trans=gpt"
    id_to_details, processed_ids = fetch_and_parse_data(url, sheet)

    # Generate descriptions using OpenAI
    descriptions = generate_descriptions(api_key, id_to_details, processed_ids)

    # Update Google Sheet
    update_google_sheet(sheet, descriptions)

    return 'Success', 200

if __name__ == "__main__":
    main()
