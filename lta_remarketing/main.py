import functions_framework
import logging
import numpy as np
import pandas as pd
import h5py
import random
import block_timer
import dask_utils
from dask_utils import load_data
import google_storage
import dask.dataframe as dd
from google.cloud import bigquery
import datetime
from pytz import timezone

GSIO = google_storage.GoogleStorageIO()


dict1 = {'item_value_1': 1088, 'item_value_2':1673, 'item_value_3':1674, 'item_value_4':1675, 'item_value_5':1676, 'item_value_6':1677, 'item_value_7':1678,
        'order_value_1': 1090, 'order_value_2': 1679, 'order_value_3': 1680, 'order_value_4': 1681, 'order_value_5':1682, 'order_value_6': 1683,
       'purchase_Animals & Pet Supplies': 760, 'purchase_Apparel & Accessories': 788,
       'purchase_Arts & Entertainment': 794, 'purchase_Baby & Toddler': 804,
       'purchase_Cameras & Optics': 805, 'purchase_Electronics': 825,
       'purchase_Food, Beverages & Tobacco': 857, 'purchase_Furniture': 883,
       'purchase_Hardware': 896, 'purchase_Health & Beauty': 917,
       'purchase_Home & Garden': 937, 'purchase_Luggage & Bags': 938, 'purchase_Mature': 947,
       'purchase_Media': 955, 'purchase_Office Supplies': 966,
       'purchase_Religious & Ceremonial': 975, 'purchase_Software': 979,
       'purchase_Sporting Goods': 1011, 'purchase_Toys & Games': 1020,
       'purchase_Vehicles & Parts': 1029,
        'purchase_0': None, 'purchase_1': None, 'purchase_2': 1060,
       'purchase_3': 1061, 'purchase_4': 1062, 'purchase_5': 1064, 'purchase_8': 1065, 'purchase_9': 1076,
       'purchase_10': 1077, 'purchase_13': 1080, 'purchase_14': 1066, 'purchase_16': 1082,
       'purchase_17': None, 'purchase_18': None, 'purchase_19': 1067, 'purchase_20': 1068,
       'purchase_21': 1069, 'purchase_22': 1070, 'purchase_23': 1071, 'purchase_26': 1073, 'purchase_28': 1063,
        'view_0': None, 'view_1': None, 'view_2': 1034, 'view_3': 1035, 'view_4': 1036,
       'view_5': 1038, 'view_8': 1039, 'view_9': 1050, 'view_10': 1051, 'view_13': 1054, 'view_14': 1040,
       'view_16': 1056, 'view_17': None, 'view_18': None, 'view_19': 1041, 'view_20':1042, 'view_21':1043,
       'view_22': 1044, 'view_23': 1045, 'view_26': 1047, 'view_28': 1037}

def trans(alist):
  temp=[]
  for col in alist.index:
      if alist[col] > 0:
          temp.append(dict1[col])
  temp = [*set(temp)]
  if None in temp:
      temp.remove(None)
  return temp

@functions_framework.http
def main(request):
   # make query
    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    client = bigquery.Client()

    query = '''
        SELECT
          *
        FROM
          `tagtoo-ml-workflow.tagtoo_export_results.lta_remarketing`
    '''
    print("start making query")
    query_job = client.query(query)
    print("query complete")
    # df = pd.read_gbq(query, dialect= "standard", progress_bar_type='tqdm')
    df = query_job.to_dataframe()
    print("query dataferame prepared")

    # labeling
    def str_isin(x,input_str):
      return (input_str == x)

    taxonomy = load_data('gs://tagtoo-ml-workflow/topic10/firstparty/stock_model/taxonomy.csv')
    GSIO.download_to_path(
        gsuri="gs://tagtoo-ml-workflow/topic10/firstparty/stock_model/TTD_predict_itemPurchase.h5",
        localpath="/tmp/TTD_predict_itemPurchase.h5"
    )
    predress = 'ROOT > Tagtoo > Tagtoo Network > Item purchased > '
    with h5py.File("/tmp/TTD_predict_itemPurchase.h5",'r') as hf:
        keys = list(hf.keys())

    keys[0]='Pet Supplies'
    seg_to_deploy = []
    for key in keys:
        key = predress+key
        seg_to_deploy.append(list(taxonomy[taxonomy['Segment Full Path'].apply(str_isin, args=(key, ))]['SEGMENT ID']))
    keys[0]='Animals & Pet Supplies'

    ind = np.sort(df.industry_id.unique())

    for k in keys:
      p = f'purchase_{k}'
      df[p] = ((df['name'] == 'purchase') & (df['category'] == k)).astype(int)

    for i in ind:
      p = f'purchase_{i}'
      df[p] = ((df['name'] == 'purchase') & (df['industry_id'] == i)).astype(int)

    for i in ind:
      v = f'view_{i}'
      df[v] = ((df['name'] == 'view_item') & (df['industry_id'] == i)).astype(int)
      # p = f'purchase_{k}'
      # dft[p] = ((dft['name'] == 'purchase') & (dft['category'] == k)).astype(int)
    
    print("category view/purachase done")

    col = list(df.columns)[:2] + list(df.columns)[5:]
    df = df[col]

    df['segment_id'] = df.apply(lambda row: trans(row[2:]), axis = 1)
    df = df[['permanent', 'segment_id', 'ttd_cookie']]
    print("first layer done")

    # second layer
    que = []
    for i in seg_to_deploy:
      que.append(int(i[0]))
    while(que):
      p_seg = que.pop(0)
      c_segs = list(taxonomy[taxonomy['Parent  ID']==str(p_seg)]['SEGMENT ID'].apply(lambda x: int(x)))
      que += c_segs
      for c_seg in c_segs:
        random_rate = random.randrange(70,95)/100
        users = df[df.segment_id.apply(lambda x:p_seg in x)].sample(frac=random_rate)['permanent']
        df[df.permanent.isin(users)].segment_id.apply(lambda x:x.append(c_seg))
    print("second layer done")

    df_ttd = df.copy()
    df = df[['permanent', 'segment_id']]
    df = df.groupby('permanent', as_index=False).sum()
    df['segment_id'] = df['segment_id'].apply(lambda x: list(set(x)))
    df['segment_id'] = df['segment_id'].apply(lambda x:['tm:d'+str(i) for i in x])
    df['segment_id'] = df['segment_id'].apply(lambda x: ','.join(x) if x!=[] else '')
    df = df[df['segment_id']!='']
    
    df_ttd['segment_id'] = df_ttd['segment_id'].apply(lambda x: [str(idx) for idx in x])
    df_ttd['segment_id_length'] = df_ttd['segment_id'].apply(lambda x: len(x))
    df_ttd = df_ttd[df_ttd['segment_id_length']!=0]
    df_ttd.drop('segment_id_length', axis=1, inplace=True)
    df_ttd = df_ttd[~df_ttd['ttd_cookie'].isna()]
    df_ttd.columns = ['track_user', 'segment_id', 'ttd_cookie']
    print("tables done")

    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    schema = [
    {"name": "permanent", "type": "STRING"},
    {"name": 'segment_id', "type": "STRING"}
    ]
    date = datetime.datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
    name = f'lta_remarketing_{date}'

    project_id = "tagtoo-ml-workflow"
    table_id = f'tagtoo_export_results.lta_remarketing_fb'
    df.to_gbq(table_id, project_id=project_id, table_schema = schema, if_exists = 'replace') 

    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    rname = f'{name}_ttd.csv'
    df_ttd.to_csv(f'/tmp/{rname}')
    GSIO.upload_file(f'/tmp/{rname}', f'gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date}/{rname}')
            
    print('Job complete.')

    return 'Success', 200