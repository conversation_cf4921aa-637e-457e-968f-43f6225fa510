steps:
  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    args:
      - "gcloud"
      - "functions"
      - "deploy"
      - "lta_remarketing"  # Replace it. Must be same with the directory name.
      - "--source=."

      - "--region=asia-east1"
      - "--trigger-http"
      - "--runtime=python310"
      - "--memory=16GiB"
      - "--cpu=4"  # Only valid when --gen2 and --memory=MEMORY are specified.  Examples: .5, 2, 2.0, 2000m.
      - "--timeout=1800s"  # default 60s
      - "--entry-point=main"
      - "--min-instances=0"   # default 0
      - "--max-instances=50" # default 100. 0 means no limit.

      - "--gen2"  # Comment out this line and --concurrency for deploying gen1
      - "--concurrency=1"  # Only applicable when the --gen2 flag is provided.

      - "--allow-unauthenticated"  # Will only apply to the first deployment. Comment out this line for non-public access.

      # `gcloud functions deploy --help`
      # or <https://cloud.google.com/sdk/gcloud/reference/builds/submit>
      # for more info
options:
    substitutionOption: 'ALLOW_LOOSE'
