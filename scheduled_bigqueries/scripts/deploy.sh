#!/bin/bash

# Scheduled BigQuery Queries 部署腳本

set -e  # 遇到錯誤時停止執行

echo "🚀 開始部署 Scheduled BigQuery Queries..."

# 進入 terraform 目錄
cd "$(dirname "$0")/terraform"

# 檢查 terraform 是否已安裝
if ! command -v terraform &> /dev/null; then
    echo "❌ Terraform 未安裝，請先安裝 Terraform"
    exit 1
fi

# 檢查 terraform.tfvars 檔案是否存在
if [ ! -f "terraform.tfvars" ]; then
    echo "📝 建立 terraform.tfvars 檔案..."
    cp terraform.tfvars.example terraform.tfvars
    echo "✅ 已建立 terraform.tfvars 檔案，請檢查配置後再執行部署"
    echo "💡 您可以編輯 terraform.tfvars 檔案來修改配置"
    exit 0
fi

# 初始化 Terraform
echo "🔧 初始化 Terraform..."
terraform init

# 檢視部署計畫
echo "📋 檢視部署計畫..."
terraform plan

# 確認部署
echo ""
echo "⚠️  即將部署以下資源："
echo "   - BigQuery Scheduled Query: tm:c_9999_3882_b_001 audience segments"
echo "   - 目標資料表: special_lta_temp_for_update_{YYYYMMDD}"
echo "   - 執行排程: 每天 17:30 UTC (台灣時間 01:30)"
echo ""
read -p "是否繼續部署？(y/N): " -n 1 -r
echo

if [[ $REPLY =~ ^[Yy]$ ]]; then
    echo "🚀 開始部署..."
    terraform apply -auto-approve
    echo "✅ 部署完成！"

    echo ""
    echo "📊 部署摘要："
    terraform output summary
else
    echo "❌ 部署已取消"
    exit 1
fi