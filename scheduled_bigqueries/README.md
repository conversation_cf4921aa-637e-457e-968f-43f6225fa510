# Scheduled BigQuery Queries

這個專案使用 Terraform 來部署 BigQuery Scheduled Queries，並透過 GitHub Actions 自動化部署流程。

## 專案結構

```
scheduled_bigqueries/
├── terraform/
│   ├── main.tf              # 主要 Terraform 配置 (使用 GCS Remote Backend)
│   ├── variables.tf         # 變數定義
│   ├── terraform.tfvars.example  # 變數範例檔案
│   └── .terraformignore     # Terraform 忽略檔案
├── sql/
│   └── ec_3882_segments.sql # EC 3882 受眾分群查詢
├── deploy.sh                # 本地部署腳本
└── README.md               # 專案說明
```

## 部署方式

### 🚀 自動部署 (推薦)

此專案透過 GitHub Actions 自動化部署：

1. **觸發條件**：
   - 推送到 `main` 分支
   - 手動觸發 (GitHub Actions UI)

2. **部署流程**：
   - 自動檢測 `scheduled_bigqueries/` 目錄的變更
   - 執行 Terraform 驗證和部署
   - 更新部署記錄到 GCS

3. **CI/CD 流程**：
   ```yaml
   # .github/workflows/deploy.yml 會自動處理：
   # 1. 檢查變更
   # 2. 執行測試 (如果有)
   # 3. Terraform 部署
   # 4. 更新部署狀態
   ```

### 🔧 本地部署

如果需要本地部署或測試：

```bash
cd scheduled_bigqueries
./deploy.sh
```

或手動執行：

```bash
cd scheduled_bigqueries/terraform

# 複製變數檔案
cp terraform.tfvars.example terraform.tfvars

# 初始化 Terraform (會自動配置 GCS Backend)
terraform init

# 檢視部署計畫
terraform plan

# 部署資源
terraform apply
```

## Terraform State 管理

### GCS Remote Backend
此專案使用 GCS Remote Backend 來管理 Terraform State：

```hcl
terraform {
  backend "gcs" {
    bucket = "tagtoo-ml-workflow-cloud-functions"
    prefix = "terraform-state/scheduled_bigqueries"
  }
}
```

**優點**：
- State 檔案集中管理在 GCS
- 支援團隊協作
- 自動備份和版本控制
- 與 GitHub Actions 整合良好

### State 路徑
- **Bucket**: `tagtoo-ml-workflow-cloud-functions`
- **Prefix**: `terraform-state/scheduled_bigqueries`
- **完整路徑**: `gs://tagtoo-ml-workflow-cloud-functions/terraform-state/scheduled_bigqueries/default.tfstate`

## 部署的 Scheduled Queries

### EC 3882 受眾分群查詢
- **查詢檔案**: `sql/ec_3882_segments.sql`
- **排程**: 每天 17:30 UTC (台灣時間 01:30)
- **目標資料表**: `tagtoo_export_results.special_lta_temp_for_update_{YYYYMMDD}`
- **寫入模式**: WRITE_APPEND (追加模式)
- **分群 ID**: `tm:c_9999_3882_b_001`

## 查詢說明

### EC 3882 受眾分群查詢
```sql
SELECT
  DISTINCT permanent,
  'tm:c_9999_3882_b_001' AS segment_id
FROM `tagtoo-tracking.event_prod.tagtoo_event`
WHERE ec_id = 3882
  AND DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
  AND DATE(event_time) <= DATE(CURRENT_DATE())
```

- 查詢最近 30 天內有活動的 EC 3882 用戶
- 使用 `DISTINCT` 避免重複記錄
- 結果會寫入 `special_lta_temp_for_update` 表格

## GitHub Actions 部署流程

### 自動化流程

1. **變更檢測**：
   - 系統會自動檢測 `scheduled_bigqueries/` 目錄的變更
   - 與上次部署的 commit 進行比較
   - 只部署有變更的資源

2. **Terraform 部署**：
   ```bash
   # GitHub Actions 會自動執行：
   cd scheduled_bigqueries/terraform
   terraform init  # 自動配置 GCS Backend
   terraform validate
   terraform plan
   terraform apply -auto-approve
   ```

3. **部署記錄**：
   - 部署成功後會更新 `deployed_commits.json`
   - 記錄每個目錄的最後部署 commit

### 部署檢查清單

確保部署成功，請確認：

- ✅ `scheduled_bigqueries/terraform/main.tf` 存在
- ✅ `scheduled_bigqueries/sql/ec_3882_segments.sql` 存在
- ✅ Terraform 配置語法正確
- ✅ GCS Backend 配置正確
- ✅ GitHub Actions 有適當的 GCP 權限
- ✅ 服務帳戶有 BigQuery Data Transfer Service 權限

## 配置說明

### Terraform 配置
- **專案 ID**: `tagtoo-ml-workflow`
- **區域**: `asia-east1`
- **服務帳戶**: `<EMAIL>`
- **目標資料集**: `tagtoo_export_results`
- **State Backend**: GCS (`tagtoo-ml-workflow-cloud-functions`)

### 重要設定
- **寫入模式**: `WRITE_APPEND` - 每次執行都會追加到目標表格
- **表格名稱模板**: `special_lta_temp_for_update_{run_time+8h|"%Y%m%d"}` - 使用台灣時間 (+8h)
- **排程**: `every day 17:30` - UTC 時間，對應台灣時間 01:30

## 監控和維護

### 部署狀態檢查
```bash
# 檢查 GitHub Actions 執行狀態
# 前往 GitHub 專案的 Actions 頁面

# 檢查 Terraform 狀態
cd scheduled_bigqueries/terraform
terraform show
terraform output

# 檢查 GCS Backend 狀態
gsutil ls gs://tagtoo-ml-workflow-cloud-functions/terraform-state/scheduled_bigqueries/
```

### BigQuery 監控
- 查詢結果會寫入 `tagtoo_export_results` 資料集
- 資料表名稱格式：`special_lta_temp_for_update_{YYYYMMDD}`
- 可以透過 BigQuery 控制台查看執行歷史和結果
- 使用 `WRITE_APPEND` 模式，每次執行都會追加新資料

## 故障排除

### 常見問題

1. **GCS Backend 錯誤**：
   ```bash
   # 檢查 GCS Bucket 是否存在
   gsutil ls gs://tagtoo-ml-workflow-cloud-functions/

   # 重新初始化 Backend
   terraform init -reconfigure
   ```

2. **GitHub Actions 部署失敗**：
   - 檢查 GCP 認證是否正確
   - 確認服務帳戶權限
   - 查看 Actions 日誌中的詳細錯誤

3. **Terraform 驗證失敗**：
   ```bash
   cd scheduled_bigqueries/terraform
   terraform validate
   ```

4. **BigQuery 權限錯誤**：
   - 確保服務帳戶有 BigQuery Data Transfer Service 權限
   - 確認 BigQuery Data Transfer API 已啟用

5. **State 鎖定問題**：
   ```bash
   # 檢查 State 鎖定狀態
   terraform force-unlock <lock-id>
   ```

### 手動修復

如果自動部署失敗，可以手動執行：

```bash
# 1. 檢查變更
git diff HEAD~1 scheduled_bigqueries/

# 2. 本地測試
cd scheduled_bigqueries/terraform
terraform plan

# 3. 手動部署
terraform apply
```

## 注意事項

- 確保服務帳戶 `<EMAIL>` 有適當的權限
- 查詢會在 UTC 時間 17:30 執行 (台灣時間 01:30)
- 使用 `WRITE_APPEND` 模式，適合累積資料的場景
- 查詢結果會與其他 LTA 相關的資料一起寫入 `special_lta_temp_for_update` 表格
- GitHub Actions 會自動處理部署記錄和狀態追蹤
- Terraform State 使用 GCS Backend 進行集中管理