# 部署檢查清單

## ✅ 已完成項目

### 1. 專案結構
- [x] `scheduled_bigqueries/terraform/main.tf` - Terraform 主配置
- [x] `scheduled_bigqueries/terraform/variables.tf` - 變數定義
- [x] `scheduled_bigqueries/terraform/terraform.tfvars.example` - 變數範例
- [x] `scheduled_bigqueries/terraform/.terraformignore` - Terraform 忽略檔案
- [x] `scheduled_bigqueries/sql/ec_3882_segments.sql` - BigQuery 查詢
- [x] `scheduled_bigqueries/deploy.sh` - 本地部署腳本
- [x] `scheduled_bigqueries/README.md` - 專案說明

### 2. Terraform 配置
- [x] 語法驗證通過 (`terraform validate`)
- [x] 部署計畫正常 (`terraform plan`)
- [x] 與 lta-user-stats 配置一致
- [x] 使用 `WRITE_APPEND` 模式
- [x] 正確的表格名稱模板

### 3. BigQuery 查詢
- [x] SQL 語法正確
- [x] 使用指定的查詢內容
- [x] 移除 `execution_info` CTE (適合 `WRITE_APPEND`)
- [x] 包含正確的追蹤欄位

### 4. GitHub Actions 整合
- [x] 專案能被 GitHub Actions 識別
- [x] 目錄結構符合自動部署要求
- [x] 部署流程文檔完整

## 🚀 部署流程

### 自動部署 (推薦)
1. 推送變更到 `main` 分支
2. GitHub Actions 自動檢測變更
3. 執行 Terraform 部署
4. 更新部署記錄

### 本地部署 (測試用)
```bash
cd scheduled_bigqueries
./deploy.sh
```

## 📋 部署後檢查

### 1. GitHub Actions
- [ ] 檢查 Actions 執行狀態
- [ ] 確認部署成功
- [ ] 查看部署日誌

### 2. BigQuery
- [ ] 確認 Scheduled Query 已建立
- [ ] 檢查執行排程設定
- [ ] 驗證目標資料表

### 3. 權限檢查
- [ ] 服務帳戶有 BigQuery Data Transfer Service 權限
- [ ] BigQuery Data Transfer API 已啟用
- [ ] 目標資料集存在且有寫入權限

## 🔧 故障排除

### 常見問題
1. **Terraform 驗證失敗**
   ```bash
   cd scheduled_bigqueries/terraform
   terraform validate
   ```

2. **權限錯誤**
   - 檢查服務帳戶權限
   - 確認 API 已啟用

3. **GitHub Actions 部署失敗**
   - 查看 Actions 日誌
   - 檢查 GCP 認證

## 📊 監控要點

### 部署後監控
- [ ] 檢查 BigQuery Scheduled Query 執行狀態
- [ ] 確認資料正確寫入目標表格
- [ ] 監控查詢執行時間和成本
- [ ] 檢查錯誤日誌

### 定期檢查
- [ ] 每週檢查執行歷史
- [ ] 確認資料品質
- [ ] 監控查詢效能

## 🎯 成功指標

- ✅ Terraform 部署成功
- ✅ BigQuery Scheduled Query 正常執行
- ✅ 資料正確寫入 `special_lta_temp_for_update` 表格
- ✅ GitHub Actions 自動化流程正常
- ✅ 部署記錄正確更新

---

**最後更新**: 2025-01-27
**狀態**: 準備部署 ✅