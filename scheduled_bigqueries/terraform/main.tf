# Scheduled BigQuery Queries Terraform 配置
# 使用 GCS Remote Backend 管理 Terraform State

terraform {
  required_version = ">= 1.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 5.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.0"
    }
  }

  # GCS Remote Backend 配置
  backend "gcs" {
    bucket = "tagtoo-ml-workflow-cloud-functions"
    prefix = "terraform-state/scheduled_bigqueries"  # 每個 cloud function 有獨立的 state 路徑
  }
}

provider "google" {
  project = var.project_id
  region  = var.region
}

# 啟用必要的 API
resource "google_project_service" "bigquery_api" {
  service = "bigquery.googleapis.com"
}

resource "google_project_service" "bigquerydatatransfer_api" {
  service = "bigquerydatatransfer.googleapis.com"
}

# 資料來源：BigQuery 資料集
data "google_bigquery_dataset" "tagtoo_export_results" {
  dataset_id = "tagtoo_export_results"
  project    = var.project_id
}

# BigQuery Scheduled Query 配置
resource "google_bigquery_data_transfer_config" "ec_3882_segments" {
  display_name   = "tm:c_9999_3882_b_001 audience segments (by Terraform)"
  location       = "asia-east1"
  data_source_id = "scheduled_query"

  schedule = "every day 17:30"  # UTC 時間，對應台灣時間 01:30 (UTC+8)

  destination_dataset_id = data.google_bigquery_dataset.tagtoo_export_results.dataset_id
  service_account_name   = "<EMAIL>"

  params = {
    destination_table_name_template = "special_lta_temp_for_update_{run_time+8h|\"%Y%m%d\"}"
    write_disposition               = "WRITE_APPEND"  # 使用 APPEND 模式，與 lta-user-stats 一致
    query                          = file("${path.module}/../sql/ec_3882_segments.sql")
  }

  depends_on = [
    google_project_service.bigquerydatatransfer_api
  ]
}

# 輸出結果
output "bigquery_scheduled_query" {
  description = "BigQuery Scheduled Query 配置"
  value = {
    name        = google_bigquery_data_transfer_config.ec_3882_segments.name
    display_name = google_bigquery_data_transfer_config.ec_3882_segments.display_name
    schedule    = google_bigquery_data_transfer_config.ec_3882_segments.schedule
    location    = google_bigquery_data_transfer_config.ec_3882_segments.location
  }
}

output "summary" {
  description = "部署摘要"
  value = {
    project_id = var.project_id
    region     = var.region
    scheduled_queries = [
      google_bigquery_data_transfer_config.ec_3882_segments.display_name
    ]
  }
}