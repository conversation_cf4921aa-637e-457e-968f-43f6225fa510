# GitHub Actions Workflows

這個目錄包含了專案的 GitHub Actions workflow 設定檔。

## Workflows

### 1. deploy.yml
- **觸發時機**: 當程式碼推送到 `main` 分支時
- **功能**:
  - 檢查哪些 Cloud Functions 有變更
  - 執行測試
  - 部署 Terraform 基礎設施
  - 部署 Cloud Functions
  - 更新部署記錄

### 2. pr-test.yml
- **觸發時機**: 當 Pull Request 被建立、更新或重新開啟時
- **功能**:
  - 檢查 PR 中有變更的 Cloud Function 目錄
  - 對有 `Makefile` 中包含 `test:` target 且有 `tests/` 目錄的函式執行測試
  - 確保所有測試通過後才允許 merge PR
  - 提供清楚的測試結果報告

## PR 測試流程說明

當你建立或更新 Pull Request 時，`pr-test.yml` workflow 會自動執行：

1. **檢查變更**: 比較 PR 的 base 和 head commit，找出有變更的檔案
2. **識別函式目錄**: 找出所有包含 `cloudbuild.yaml` 的目錄（Cloud Function 目錄）
3. **篩選需要測試的函式**: 只對同時滿足以下條件的函式執行測試：
   - 目錄中有變更的檔案
   - 存在 `Makefile` 且包含 `test:` target
   - 存在 `tests/` 目錄
4. **執行測試**: 對每個符合條件的函式：
   - 執行 `make test`（環境設置由 Makefile 處理）
5. **報告結果**:
   - ✅ 所有測試通過：PR 可以被 merge
   - ❌ 有測試失敗：PR 被阻擋，需要修復後才能 merge
   - ℹ️ 沒有需要測試的函式：PR 可以被 merge

## 設定 Branch Protection Rules

為了確保 PR 測試流程有效，建議在 GitHub 倉庫設定中啟用 Branch Protection Rules：

1. 前往 Settings > Branches
2. 對 `main` 分支設定保護規則
3. 勾選 "Require status checks to pass before merging"
4. 選擇 "PR Tests" 作為必要的狀態檢查

這樣設定後，只有通過測試的 PR 才能被 merge 到 main 分支。