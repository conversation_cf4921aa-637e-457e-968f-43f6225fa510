name: PR Tests

on:
  pull_request:
    types: [opened, synchronize, reopened]
    branches: [main]

# 同一個 PR 的新 push 會取消之前正在運行的測試
concurrency:
  group: ${{ github.workflow }}-${{ github.event.pull_request.number }}
  cancel-in-progress: true

permissions:
  contents: read
  pull-requests: write
  checks: write

jobs:
  test_changed_functions:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          # 需要 fetch 足夠的歷史記錄來進行 diff
          fetch-depth: 0

      - name: Set up Docker Buildx
        uses: docker/setup-buildx-action@v3
        with:
          platforms: linux/amd64

      - name: Find changed function directories
        id: find_changed_functions
        shell: bash
        run: |
          # 取得 PR 的 base 和 head commit
          BASE_SHA=${{ github.event.pull_request.base.sha }}
          HEAD_SHA=${{ github.event.pull_request.head.sha }}

          echo "Comparing changes between $BASE_SHA and $HEAD_SHA"

          # 找出所有有變更的檔案
          CHANGED_FILES=$(git diff --name-only $BASE_SHA $HEAD_SHA)
          echo "Changed files:"
          echo "$CHANGED_FILES"

          # 找出所有包含 cloudbuild.yaml 的資料夾（這些是 function 資料夾）
          if command -v find >/dev/null 2>&1 && find . -maxdepth 1 -name '.*' -printf '' 2>/dev/null; then
            # Linux 環境
            find . -maxdepth 2 -name 'cloudbuild.yaml' -printf '%h\n' | sed 's|^./||' | sort > all_function_dirs.txt
          else
            # macOS 環境
            find . -maxdepth 2 -name 'cloudbuild.yaml' | sed 's|/cloudbuild.yaml||' | sed 's|^./||' | sort > all_function_dirs.txt
          fi

          # 排除以 . 開頭的系統資料夾
          grep -v -E '^\.' all_function_dirs.txt > function_dirs.txt

          # 找出有變更的 function 目錄
          CHANGED_FUNCTIONS=()
          while IFS= read -r func_dir; do
            # 檢查該目錄是否有變更的檔案
            if echo "$CHANGED_FILES" | grep -q "^$func_dir/"; then
              echo "Changes detected in function directory: $func_dir"
              CHANGED_FUNCTIONS+=("$func_dir")
            fi
          done < function_dirs.txt

          # 輸出結果
          if [ ${#CHANGED_FUNCTIONS[@]} -eq 0 ]; then
            echo "NO_CHANGED_FUNCTIONS=true" >> $GITHUB_ENV
            echo "No function directories have changes."
          else
            echo "NO_CHANGED_FUNCTIONS=false" >> $GITHUB_ENV
            echo "CHANGED_FUNCTIONS=${CHANGED_FUNCTIONS[*]}" >> $GITHUB_ENV
            echo "The following function directories have changes: ${CHANGED_FUNCTIONS[*]}"
          fi

      - name: Run Tests for Changed Functions
        if: env.NO_CHANGED_FUNCTIONS == 'false'
        shell: bash
        run: |
          # 追蹤測試結果
          TESTED_FUNCTIONS=()
          PASSED_FUNCTIONS=()
          FAILED_FUNCTIONS=()
          SKIPPED_FUNCTIONS=()

          # 為每個有變更的 function 運行測試
          for func in $CHANGED_FUNCTIONS; do
            echo "=== Checking tests for $func ==="

            # 檢查是否為目錄
            if [ ! -d "$func" ]; then
              echo "Warning: Directory $func not found. Skipping..."
              SKIPPED_FUNCTIONS+=("$func")
              continue
            fi

            # 檢查是否同時有 Makefile 中的 test rule 和 tests 目錄
            HAS_MAKEFILE_TEST=false
            HAS_TESTS_DIR=false

            if [ -f "$func/Makefile" ] && grep -q "^test:" "$func/Makefile"; then
              HAS_MAKEFILE_TEST=true
              echo "✓ Found Makefile with test target in $func"
            fi

            if [ -d "$func/tests" ]; then
              HAS_TESTS_DIR=true
              echo "✓ Found tests directory in $func"
            fi

            if [ "$HAS_MAKEFILE_TEST" = true ] && [ "$HAS_TESTS_DIR" = true ]; then
              echo "Running tests for $func..."
              TESTED_FUNCTIONS+=("$func")

              pushd "$func"

              # 運行測試 - 環境設置由 Makefile 處理
              echo ">>> Debugging: Listing all files in $(pwd)"
              ls -laR
              echo ">>> End of file listing."

              echo "Running 'make test' for $func..."
              if make test; then
                echo "✅ Tests passed for $func"
                PASSED_FUNCTIONS+=("$func")
              else
                echo "❌ Tests failed for $func"
                FAILED_FUNCTIONS+=("$func")
              fi

              popd
            else
              echo "⏭️  Skipping tests for $func - missing requirements:"
              [ "$HAS_MAKEFILE_TEST" = false ] && echo "   - No 'test:' target in Makefile"
              [ "$HAS_TESTS_DIR" = false ] && echo "   - No tests/ directory"
              SKIPPED_FUNCTIONS+=("$func")
            fi

            echo ""
          done

          # 輸出測試總結
          echo "=== Test Summary ==="
          echo "Functions tested: ${#TESTED_FUNCTIONS[@]}"
          echo "Functions passed: ${#PASSED_FUNCTIONS[@]}"
          echo "Functions failed: ${#FAILED_FUNCTIONS[@]}"
          echo "Functions skipped: ${#SKIPPED_FUNCTIONS[@]}"

          if [ ${#TESTED_FUNCTIONS[@]} -gt 0 ]; then
            echo ""
            echo "Tested functions: ${TESTED_FUNCTIONS[*]}"
          fi

          if [ ${#PASSED_FUNCTIONS[@]} -gt 0 ]; then
            echo "Passed functions: ${PASSED_FUNCTIONS[*]}"
          fi

          if [ ${#FAILED_FUNCTIONS[@]} -gt 0 ]; then
            echo "Failed functions: ${FAILED_FUNCTIONS[*]}"
          fi

          if [ ${#SKIPPED_FUNCTIONS[@]} -gt 0 ]; then
            echo "Skipped functions: ${SKIPPED_FUNCTIONS[*]}"
          fi

          # 如果有測試失敗，則整個 job 失敗
          if [ ${#FAILED_FUNCTIONS[@]} -gt 0 ]; then
            echo ""
            echo "::error::❌ Some tests failed. Please fix the failing tests before merging this PR."
            echo "::error::Failed functions: ${FAILED_FUNCTIONS[*]}"
            exit 1
          fi

          # 如果沒有任何函式需要測試，給出提示
          if [ ${#TESTED_FUNCTIONS[@]} -eq 0 ]; then
            echo ""
            echo "::notice::ℹ️ No functions required testing (no Makefile with test target + tests/ directory found)"
            echo "::notice::This is not an error - the PR can be merged safely"
          else
            echo ""
            echo "::notice::✅ All tests passed! This PR is ready for review and merge."
          fi

      - name: No Changes in Function Directories
        if: env.NO_CHANGED_FUNCTIONS == 'true'
        run: |
          echo "::notice::ℹ️ No changes detected in function directories"
          echo "::notice::This PR doesn't modify any cloud functions, so no tests are required"
          echo "::notice::This PR can be merged safely"