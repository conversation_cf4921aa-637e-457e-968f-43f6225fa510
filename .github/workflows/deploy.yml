name: deploy

on:
  push:
    branches: [main]
  workflow_dispatch:
    inputs:
      reason:
        description: '手動觸發原因'
        required: false
        default: '手動部署'
        type: string

# 同一個 branch 上觸發的 job 會等之前的 job 執行完才開始執行
# 不會把前一個 job 強行取消掉，確保每次的變動都會執行 CI 的部分
concurrency:
  group: ${{ github.workflow }}-${{ github.ref }}
  cancel-in-progress: false

jobs:
  deploy_functions:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v4
        with:
          # 0 indicates all history for all branches and tags.
          # Fetch all git logs so that we can find last deployed commit
          # without the error: `fatal: bad object`
          fetch-depth: 0

      # Uncomment this section for live debugging
      #- name: Setup tmate session
      #  uses: mxschmitt/action-tmate@v3
      #  with:
      #    detached: true
      #    limit-access-to-actor: true

      - id: auth
        uses: google-github-actions/auth@v2
        with:
          # 使用 Workload Identity Federation (推薦方式)
          # 如果已設置 WIF，請取消註解以下兩行並註解 credentials_json
          # workload_identity_provider: '${{ secrets.WIF_PROVIDER }}'
          # service_account: '<EMAIL>'

          # 目前使用服務帳戶金鑰 (可在設置 WIF 後移除)
          credentials_json: '${{ secrets.GCP_CREDENTIALS_JSON }}'

      - name: Set up gcloud
        uses: google-github-actions/setup-gcloud@v2
        with:
          project_id: ${{ vars.GCP_PROJECT_ID }}

      - name: Setup Terraform
        uses: hashicorp/setup-terraform@v3
        with:
          terraform_version: "1.5.0"

      - name: Download deployed commits from GCS
        id: download_deployed_commits
        run: |
          # 下載已部署的 commits 對應表
          echo "Downloading deployed_commits.json from GCS..."
          if ! gsutil cp gs://${{ vars.GCS_BUCKET_NAME }}/deployed_commits.json .; then
            echo "::error::deployed_commits.json 不存在於 GCS 中，請先手動建立並上傳初始版本"
            echo "請執行以下指令建立並上傳初始版本："
            echo "  echo '{}' > deployed_commits.json"
            echo "  gsutil cp deployed_commits.json gs://${{ vars.GCS_BUCKET_NAME }}/"
            exit 1
          fi

          # 檢查檔案是否為空
          if [ ! -s "deployed_commits.json" ]; then
            echo "::error::deployed_commits.json 是空的"
            exit 1
          fi

          # 檢查是否為有效的 JSON
          if ! jq empty deployed_commits.json 2>/dev/null; then
            echo "::error::deployed_commits.json 不是有效的 JSON 格式"
            exit 1
          fi

          echo "Successfully loaded deployed_commits.json"

      - name: Find all function directories
        id: find_functions
        shell: bash
        run: |
          # 找出所有 Cloud Function 目錄（支援兩種部署方式）
          # 1. 優先尋找有 terraform/main.tf 的目錄（新方式）
          # 2. 尋找只有 cloudbuild.yaml 的目錄（舊方式，向後兼容）

          # 使用跨平台相容的語法（Linux 主要，macOS 相容）
          if command -v find >/dev/null 2>&1 && find . -maxdepth 1 -name '.*' -printf '' 2>/dev/null; then
            # Linux 環境（支援 -printf）
            # 尋找有 terraform/main.tf 的目錄
            find . -maxdepth 3 -path '*/terraform/main.tf' -printf '%h\n' | sed 's|/terraform$||' | sed 's|^./||' | sort > terraform_dirs.txt
            # 尋找有 cloudbuild.yaml 的目錄
            find . -maxdepth 2 -name 'cloudbuild.yaml' -printf '%h\n' | sed 's|^./||' | sort > cloudbuild_dirs.txt
          else
            # macOS 環境（不支援 -printf）
            # 尋找有 terraform/main.tf 的目錄
            find . -maxdepth 3 -path '*/terraform/main.tf' | sed 's|/terraform/main.tf||' | sed 's|^./||' | sort > terraform_dirs.txt
            # 尋找有 cloudbuild.yaml 的目錄
            find . -maxdepth 2 -name 'cloudbuild.yaml' | sed 's|/cloudbuild.yaml||' | sed 's|^./||' | sort > cloudbuild_dirs.txt
          fi

          # 合併目錄列表，Terraform 優先（避免重複）
          cat terraform_dirs.txt > function_dirs.txt
          # 只添加沒有 terraform 配置的 cloudbuild 目錄
          if [ -f cloudbuild_dirs.txt ]; then
            grep -v -F -f terraform_dirs.txt cloudbuild_dirs.txt >> function_dirs.txt 2>/dev/null || true
          fi
          sort function_dirs.txt | uniq > function_dirs_final.txt
          mv function_dirs_final.txt function_dirs.txt

          # 排除所有以 . 開頭的系統資料夾（.github, .templates, .docs 等）
          grep -v -E '^\.' function_dirs.txt > filtered_dirs.txt
          # 轉換成 JSON 陣列
          echo 'FUNCTION_DIRS=['$(cat filtered_dirs.txt | xargs -I{} echo '\"{}\"' | paste -sd ',')']' >> $GITHUB_OUTPUT
          # 輸出所有 function 資料夾
          echo "Function directories with Terraform configuration found: $(cat filtered_dirs.txt | tr '\n' ' ')"

      - name: Check for changes in each function
        id: check_changes
        shell: bash
        run: |
          # 讀取已部署的 commits
          DEPLOYED_COMMITS=$(cat deployed_commits.json)
          echo "Current deployed commits: $DEPLOYED_COMMITS"

          # 讀取所有 function 資料夾
          FUNCTION_DIRS=${{ steps.find_functions.outputs.FUNCTION_DIRS }}

          # 初始化需要更新的函式清單
          UPDATED_FUNCTIONS=()

          # 檢查每個 function 是否有變更
          for dir in $(echo $FUNCTION_DIRS | jq -r '.[]'); do
            # 取得該 function 最後部署的 commit
            # 如果 function 不存在於 deployed_commits.json 中，jq 會回傳 'null'
            # 這表示這是一個從未部署過的新 function
            LAST_DEPLOYED_SHA=$(echo $DEPLOYED_COMMITS | jq -r ".\"$dir\" // null")

            # 如果是新的 function（不存在於 deployed_commits.json 中）
            # 'null' 值明確表示這是首次部署，無需進行 git diff 比較
            if [ "$LAST_DEPLOYED_SHA" = "null" ]; then
              echo "New function detected: $dir (never deployed before)"
              UPDATED_FUNCTIONS+=("$dir")
              continue
            fi

            # 檢查該 commit 是否存在
            if ! git cat-file -e $LAST_DEPLOYED_SHA 2>/dev/null; then
              echo "Warning: Commit $LAST_DEPLOYED_SHA for $dir not found. Will deploy all changes for this function."
              UPDATED_FUNCTIONS+=("$dir")
              continue
            fi

            # 檢查是否有變更
            if [ -n "$(git diff --name-only --diff-filter=AMDR $LAST_DEPLOYED_SHA HEAD -- $dir 2>/dev/null)" ]; then
              echo "Changes detected in $dir (since $LAST_DEPLOYED_SHA)"
              UPDATED_FUNCTIONS+=("$dir")
            else
              echo "No changes in $dir (since $LAST_DEPLOYED_SHA)"
            fi
          done

          # 輸出結果
          if [ ${#UPDATED_FUNCTIONS[@]} -eq 0 ]; then
            echo "NO_UPDATED_FUNCTIONS=true" >> $GITHUB_ENV
            echo "No functions need to be updated."
          else
            echo "NO_UPDATED_FUNCTIONS=false" >> $GITHUB_ENV
            echo "UPDATED_FUNCTIONS=${UPDATED_FUNCTIONS[*]}" >> $GITHUB_ENV
            echo "The following functions will be deployed: ${UPDATED_FUNCTIONS[*]}"
          fi

      - name: Run Tests for Updated Functions
        shell: bash
        run: |
          if [ "${{ env.NO_UPDATED_FUNCTIONS }}" = "true" ]; then
            echo "No functions need to be updated. Skipping tests."
            exit 0
          fi

          # 追蹤測試失敗的函式
          FAILED_TESTS=()

          # 為每個需要更新的 function 運行測試
          for func in $UPDATED_FUNCTIONS; do
            echo "=== Running tests for $func ==="

            # 檢查是否為目錄且包含測試
            if [ ! -d "$func" ]; then
              echo "Warning: Directory $func not found. Skipping tests..."
              continue
            fi

            # 檢查是否同時有 Makefile 中的 test rule 和 tests 目錄
            if [ -f "$func/Makefile" ] && grep -q "test" "$func/Makefile" && [ -d "$func/tests" ]; then
              echo "Found Makefile with test target and tests directory in $func"
              pushd "$func"

              # 設置 Python 環境和安裝依賴
              echo "Setting up Python environment..."
              python3 -m pip install --upgrade pip

              # 安裝主要依賴
              if [ -f "requirements.txt" ]; then
                python3 -m pip install -r requirements.txt
              fi

              # 安裝測試依賴
              if [ -f "requirements-test.txt" ]; then
                python3 -m pip install -r requirements-test.txt
              fi

              # 運行測試
              echo "Running tests for $func..."
              if make test; then
                echo "✅ Tests passed for $func"
              else
                echo "❌ Tests failed for $func"
                FAILED_TESTS+=("$func")
              fi

              popd
            else
              echo "Skipping tests for $func - requires both Makefile with test rule and tests/ directory"
            fi
          done

          # 檢查是否有測試失敗的函式
          if [ ${#FAILED_TESTS[@]} -gt 0 ]; then
            echo "::error::The following functions failed tests: ${FAILED_TESTS[*]}"
            echo "::error::Deployment cancelled due to test failures"
            exit 1
          fi

          echo "All tests passed! Proceeding with deployment..."

      - name: Deploy Functions
        shell: bash
        run: |
          if [ "${{ env.NO_UPDATED_FUNCTIONS }}" = "true" ]; then
            echo "No functions need to be updated. Skipping deployment."
            exit 0
          fi

          # 初始化變數
          DEPLOYED_COMMITS=$(cat deployed_commits.json)
          CURRENT_SHA=${{ github.sha }}
          FAILED_FUNCTIONS=()

          echo "=== Starting unified function deployment ==="
          echo "Functions to deploy: $UPDATED_FUNCTIONS"

          # 為每個需要更新的函數選擇適當的部署方式
          for func in $UPDATED_FUNCTIONS; do
            echo ""
            echo "=== Deploying $func ==="

            # 檢查是否為目錄
            if [ ! -d "$func" ]; then
              echo "❌ Error: Directory $func not found. Skipping..."
              FAILED_FUNCTIONS+=("$func")
              continue
            fi

            # 部署成功標記
            DEPLOYMENT_SUCCESS=false

            # 優先使用 Terraform 部署（推薦方式）
            if [ -f "$func/terraform/main.tf" ]; then
              echo "🔧 Using Terraform deployment for $func..."

              pushd "$func/terraform"

              # Terraform 初始化
              echo "Initializing Terraform for $func..."
              if ! terraform init; then
                echo "❌ Terraform init failed for $func"
                FAILED_FUNCTIONS+=("$func")
                popd
                continue
              fi

              # Terraform 驗證
              echo "Validating Terraform configuration for $func..."
              if ! terraform validate; then
                echo "❌ Terraform validation failed for $func"
                FAILED_FUNCTIONS+=("$func")
                popd
                continue
              fi

              # Terraform plan
              echo "Planning Terraform changes for $func..."
              if ! terraform plan -out=tfplan; then
                echo "❌ Terraform plan failed for $func"
                FAILED_FUNCTIONS+=("$func")
                popd
                continue
              fi

              # Terraform apply
              echo "Applying Terraform changes for $func..."
              if terraform apply -auto-approve tfplan; then
                echo "✅ Terraform deployment successful for $func"
                DEPLOYMENT_SUCCESS=true
              else
                echo "❌ Terraform apply failed for $func"
                FAILED_FUNCTIONS+=("$func")
              fi

              # 清理
              rm -f tfplan
              popd

            # 使用 Cloud Build 部署（向後兼容）
            elif [ -f "$func/cloudbuild.yaml" ]; then
              echo "☁️ Using Cloud Build deployment for $func..."

              pushd "$func"

              # 執行 Cloud Build 部署
              echo "Running gcloud builds submit for $func..."
              if gcloud builds submit \
                --substitutions="TRIGGER_NAME=GitHub Actions,COMMIT_SHA=${CURRENT_SHA},REPO_FULL_NAME=${GITHUB_REPOSITORY},REF_NAME=${GITHUB_REF_NAME}"; then
                echo "✅ Cloud Build deployment successful for $func"
                DEPLOYMENT_SUCCESS=true
              else
                echo "❌ Cloud Build deployment failed for $func"
                FAILED_FUNCTIONS+=("$func")
              fi

              popd

            # 沒有找到部署配置
            else
              echo "❌ No deployment configuration found for $func"
              echo "   Expected: terraform/main.tf or cloudbuild.yaml"
              FAILED_FUNCTIONS+=("$func")
            fi

            # 統一的成功後處理
            if [ "$DEPLOYMENT_SUCCESS" = true ]; then
              echo "📝 Updating deployment record for $func..."
              DEPLOYED_COMMITS=$(echo $DEPLOYED_COMMITS | jq ". + {\"$func\": \"$CURRENT_SHA\"}")
              echo "Updated deployed commits: $DEPLOYED_COMMITS"
            fi
          done

          # 統一的後處理
          echo ""
          echo "=== Deployment Summary ==="

          # 將更新後的 deployed_commits 寫回檔案
          echo $DEPLOYED_COMMITS > deployed_commits.json
          echo "📄 Updated deployed_commits.json"

          # 顯示部署結果
          SUCCESSFUL_COUNT=$((${#UPDATED_FUNCTIONS[@]} - ${#FAILED_FUNCTIONS[@]}))
          echo "📊 Deployment Results:"
          echo "   ✅ Successful: $SUCCESSFUL_COUNT"
          echo "   ❌ Failed: ${#FAILED_FUNCTIONS[@]}"

          if [ ${#FAILED_FUNCTIONS[@]} -gt 0 ]; then
            echo "   Failed functions: ${FAILED_FUNCTIONS[*]}"
          fi

          echo "📋 Final deployed commits: $(cat deployed_commits.json)"

          # 檢查是否有部署失敗的函式
          if [ ${#FAILED_FUNCTIONS[@]} -gt 0 ]; then
            echo "::error::The following functions failed to deploy: ${FAILED_FUNCTIONS[*]}"
            echo "::error::Deployment job marked as failed due to deployment failures"
            exit 1
          fi

          echo "🎉 All functions deployed successfully!"

      - name: Upload deployed commits to GCS
        if: success()
        run: |
          # 上傳更新後的 deployed_commits.json 到 GCS
          echo "Uploading deployed_commits.json to GCS..."
          gsutil cp deployed_commits.json gs://${{ vars.GCS_BUCKET_NAME }}/deployed_commits.json
          echo "Upload completed."
