import functions_framework
import pandas as pd
from datetime import datetime, timedelta
from pytz import timezone
from google.oauth2 import service_account
from google.cloud import bigquery
from tqdm import tqdm
from typing import List, Dict, Set, Tuple
from functools import reduce
from utils import *
from google_sheet import *
import google_storage
import gspread
from oauth2client.service_account import ServiceAccountCredentials
from gspread_dataframe import set_with_dataframe
from googleapiclient.discovery import build
from facebook_business.api import FacebookAdsApi

@functions_framework.http
def main(request):

    request_json = request.get_json(silent=True)
    request_args = request.args

    if request_json and 'production' in request_json:
        production = int(request_json['production'])
    elif request_args and 'production' in request_args:
        production = int(request_args['production'])
    else:
        raise AttributeError("Can't find `production` in request url.")

    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    output_path = "gs://tagtoo-ml-workflow/topic10/LTA_validation"

    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    if production == 1:
        current_datetime_list = [datetime.now(timezone('Asia/Taipei')) - timedelta(days=i) for i in range(7)]
    else:
        current_datetime_list = [datetime(2024,5,13) - timedelta(days=i) for i in range(7)]
    # current_date -> [yesterday, ..., past 7 days]
    date_arguments_list = [make_date_arguments(d) for d in current_datetime_list]
    today_date = date_arguments_list[0]['current_date']

    for date_dict in date_arguments_list:
        # Query label data
        export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_label/{date_dict['label_start_date']}_{date_dict['label_end_date']}/*.parquet"
        # export_path = output_path + f"/{today_date}/label/{date_dict['current_date']}/*.parquet"
        bucket_name = 'tagtoo-ml-workflow'
        blob_name = '/'.join(export_path.split('/')[3:-1]) + '/000000000000.parquet'

        if object_exists(bucket_name, blob_name, credentials):
            print(f"{date_dict['current_date']} label data already exists.")
        else:
            sql_query = make_label_query(date_dict['label_start_date'], date_dict['label_end_date'], date_dict['predict_date'])
            QUERY = add_export_path(export_path=export_path, sql_query=sql_query)
            print(f"Start query {date_dict['current_date']} label data.")
            client = bigquery.Client(credentials=credentials)
            query_job = client.query(QUERY)  # API request
            rows = query_job.result()  # Waits for query to finish
            print("Query job complete.")

    labels_dict = {}

    # Load label data
    for date_dict in date_arguments_list:
        print(f"Start transform {date_dict['current_date']} label.")
        path = f"gs://tagtoo-ml-workflow/topic10/LTA_label/{date_dict['label_start_date']}_{date_dict['label_end_date']}/*.parquet"
        # path = output_path + f"/{today_date}/label/{date_dict['current_date']}/*.parquet"
        df_label = load_data(path)
        labels_dict[date_dict['current_date']] = label_transform(df_label)

    # Load taxonomy data
    sheet = ConnectGoogleSheet()
    df_tax = sheet.get_df()
    df_tax = preprocessed_taxonomy(df_tax)

    segment_name_to_id_dict = {
        'item_value_1': 1088, 'item_value_2':1673, 'item_value_3':1674, 'item_value_4':1675, 'item_value_5':1676, 'item_value_6':1677, 'item_value_7':1678,
        'order_value_1': 1090, 'order_value_2': 1679, 'order_value_3': 1680, 'order_value_4': 1681, 'order_value_5':1682, 'order_value_6': 1683,
        'purchase_Animals & Pet Supplies': 760, 'purchase_Apparel & Accessories': 788,
        'purchase_Arts & Entertainment': 794, 'purchase_Baby & Toddler': 804,
        'purchase_Cameras & Optics': 805, 'purchase_Electronics': 825,
        'purchase_Food, Beverages & Tobacco': 857, 'purchase_Furniture': 883,
        'purchase_Hardware': 896, 'purchase_Health & Beauty': 917,
        'purchase_Home & Garden': 937, 'purchase_Luggage & Bags': 938, 'purchase_Mature': 947,
        'purchase_Media': 955, 'purchase_Office Supplies': 966,
        'purchase_Religious & Ceremonial': 975, 'purchase_Software': 979,
        'purchase_Sporting Goods': 1011, 'purchase_Toys & Games': 1020,
        'purchase_Vehicles & Parts': 1029,
        'purchase_0': None, 'purchase_1': None, 'purchase_2': 1060,
        'purchase_3': 1061, 'purchase_4': 1062, 'purchase_5': 1064, 'purchase_8': 1065, 'purchase_9': 1076,
        'purchase_10': 1077, 'purchase_13': 1080, 'purchase_14': 1066, 'purchase_15': None, 'purchase_16': 1082,
        'purchase_17': None, 'purchase_18': None, 'purchase_19': 1067, 'purchase_20': 1068,
        'purchase_21': 1069, 'purchase_22': 1070, 'purchase_23': 1071, 'purchase_26': 1073, 'purchase_28': 1063,
        'view_0': None, 'view_1': None, 'view_2': 1034, 'view_3': 1035, 'view_4': 1036,
        'view_5': 1038, 'view_8': 1039, 'view_9': 1050, 'view_10': 1051, 'view_13': 1054, 'view_14': 1040, 'view_15': None,
        'view_16': 1056, 'view_17': None, 'view_18': None, 'view_19': 1041, 'view_20':1042, 'view_21':1043,
        'view_22': 1044, 'view_23': 1045, 'view_26': 1047, 'view_28': 1037
    }

    # Validate
    validate_result = {}
    for date_dict in date_arguments_list:
        print(f"Start validate {date_dict['predict_date']} predict result.")
        # predict result
        predict_data_path = f"gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date_dict['predict_date']}/lta_remarketing_{date_dict['predict_date']}_ttd_model.csv"

        bucket_name = "tagtoo-ml-workflow"
        blob_name = f"topic10/firstparty/first_party_{date_dict['predict_date']}/lta_remarketing_{date_dict['predict_date']}_ttd_model.csv"

        if object_exists(bucket_name, blob_name, credentials):
            # predict result  
            df_predict = load_data(predict_data_path, file_format='CSV', usecols=['track_user', 'segment_id', 'ttd_cookie'])
            grouped = aggregate_users_by_segment(df_predict)

            # label
            df_label = labels_dict[date_dict['current_date']]
            df_label = rename_label_columns(df_label, segment_name_to_id_dict)
            segment_name_to_gids = map_segment_to_group_ids(df_label)

            # merge taxonomy with predict result
            merged = grouped.merge(df_tax, on='segment_id', how='inner')
            if len(merged) != len(grouped):
                raise ValueError('segment_id is not matched')
            # merged 是預測出來的標籤，item purchase 第一層以外的都是 random sample，不需要驗證
            merge_filtered = merged[merged['segment_id'].isin(list(map(lambda x: str(x), list(segment_name_to_gids.keys()))))]
            
            # validate
            validate_result[date_dict['predict_date']] = calculate_evaluation_metrics(merge_filtered, segment_name_to_gids)

        else:
            print(f"{blob_name} does not exist in {bucket_name}.")

    # 使用 reduce 進行連續合併
    result = reduce(combine_and_cleanup_dataframes, validate_result.values())

    result['Precision'] = result['TP'] / result['Volume']
    result['Recall'] = result['TP'] / result['True_data']
    result['F1'] = 2 * result['Precision'] * result['Recall'] / (result['Precision'] + result['Recall'])

    result = result.fillna(0)
    result_path = f"/tmp/LTA_validation_{date_arguments_list[-1]['predict_date']}_{date_arguments_list[0]['predict_date']}.csv"
    result.to_csv(result_path, index=False)

    result_export_path = output_path + f"/{today_date}/result/LTA_validation_{date_arguments_list[-1]['predict_date']}_{date_arguments_list[0]['predict_date']}.csv"
    GSIO.upload_file(localpath=result_path, gsuri=result_export_path)
    
    # 獲取 FB 受眾預估人數
    # 初始化Facebook SDK
    access_token = 'EAAE0P45kC8gBO7ciunQE8XPaGe9pCBI5V437J1LkYz7Q7FmCTtpq8KTZBre69HALDMHJ1ijOaZB2OrWTdk3qofaZAwKVZBmxzoV1b7TNM2bwJ2APY85KdAsOxH3ZB0k54yLV7FrHYXqYwZAZCGPDxZBqSeb4w9f3FDG8HrkFkCOCRrzv2tsyLfMycK4wNwH93ATK'
    ad_account_id = 'act_336791573613537'

    # 初始化Facebook SDK
    FacebookAdsApi.init(access_token=access_token)

    # 调用函数
    all_audiences = fetch_all_audiences(ad_account_id, access_token)
    df_fb = pd.DataFrame(all_audiences)
    df_lta = df_fb[df_fb['name'].str.contains('tm:d')]
    df_lta['segment_id'] = df_lta['name'].apply(lambda x: x.split(' ')[0])
    df_lta['segment_id'] = df_lta['segment_id'].str.replace('tm:d', '')
    df_lta = df_lta.drop(['name', 'id'], axis=1)
    val_result = result.merge(df_lta, how='inner', on='segment_id')
    category_result = aggregate_by_lta_categories(val_result, date=today_date)

    # Write data to Google Sheet
    scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
    creds = ServiceAccountCredentials.from_json_keyfile_name('tagtoo-ml-workflow-sa.json', scope)
    client = gspread.authorize(creds)

    SHEET_NAME = f'LTA Validate - Production'

    sheet = client.open(SHEET_NAME)

    sheet_names = [
        'Category raw data', 'Buyers', 'Visitors', 'Item Purchased', 'Purchase Power', 'Raw data'
    ]
    
    sheet_dict = {}
    for name in sheet_names:
        try:
            # 嘗試獲取已存在的工作表
            sheet_dict[name] = sheet.worksheet(name)
        except gspread.WorksheetNotFound:
            raise Exception(f"Can't find worksheet: {name}.")

    write_data_to_sheet(worksheet=sheet_dict['Buyers'], data=category_result[category_result['category']=='Buyer'].values.tolist())
    write_data_to_sheet(worksheet=sheet_dict['Visitors'], data=category_result[category_result['category']=='Visitor'].values.tolist())
    write_data_to_sheet(worksheet=sheet_dict['Item Purchased'], data=category_result[category_result['category']=='Item Purchased'].values.tolist())
    write_data_to_sheet(worksheet=sheet_dict['Purchase Power'], data=category_result[category_result['category']=='Purchase Power'].values.tolist())
    # 將 dataframe 寫入工作表
    sheet_dict['Raw data'].clear()
    set_with_dataframe(sheet_dict['Raw data'], val_result)

    print("Job complete.")

    return 'Success', 200