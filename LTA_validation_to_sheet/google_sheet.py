import pandas as pd
from google.oauth2 import service_account
# from apiclient.discovery import build
from googleapiclient.discovery import build

sheet_name = '1BtG2GdoV7B2Cu5Ej7B49VU8z6MZyFqGRyjOSX5emp2Q'
page_name = 'En'

class ConnectGoogleSheet(object):
    # Google Sheet : https://docs.google.com/spreadsheets/d/1BtG2GdoV7B2Cu5Ej7B49VU8z6MZyFqGRyjOSX5emp2Q/
    SAMPLE_SPREADSHEET_ID = sheet_name
    SAMPLE_RANGE_NAME = f"""{page_name}!A1:E"""
    columns = ['TTD ID', 'FB', 'SEGMENT NAME', 'Segment Full Path', 'SEGMENT DESCRIPTION']

    def __init__(self):
        creds = service_account.Credentials.from_service_account_file('tagtoo-ml-workflow-sa.json')
        self.service = build('sheets', 'v4', credentials=creds)

    def get_df(self):
        sheet = self.service.spreadsheets()
        result = sheet.values().get(
            spreadsheetId=self.SAMPLE_SPREADSHEET_ID,
            range=self.SAMPLE_RANGE_NAME
        ).execute()
        values = result.get('values', [])
        df = pd.DataFrame(values, columns = ['segment_id', 'parent_id', 'name', 'path','description'] )
        return df

def preprocessed_taxonomy(df_tax: pd.DataFrame) -> pd.core.frame.DataFrame:
  df_tax.drop(['parent_id', 'description'], axis=1, inplace=True)
  df_tax.rename(columns=dict(zip(df_tax.columns, ['segment_id', 'name', 'path'])), inplace=True)
  df_tax['segment_id'] = df_tax['segment_id'].fillna("")
  return df_tax