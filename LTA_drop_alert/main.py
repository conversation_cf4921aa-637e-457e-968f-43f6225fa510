import os
import pandas as pd
import functions_framework
from google.cloud import bigquery
from datetime import datetime, timedelta
from google.oauth2 import service_account
from .utils import (
    get_google_sheet_client,
    send_slack_alert,
    check_today_exists,
    find_daily_drop_segments,
    save_alerts_to_bq,
    find_repeated_segments
)
from .config import (
    GOOGLE_APPLICATION_CREDENTIALS_PATH,
    PROJECT_ID,
    SLACK_WEBHOOK_URL,
    THRESHOLD,
    ALERTS_TABLE_ID
)

@functions_framework.http
def main(request):
    # --- 1. Configuration and Setup ---
    try:
        KEY_FILE_NAME = 'tagtoo-ml-workflow-61e119f3d094_new.json'
        script_dir = os.path.dirname(os.path.abspath(__file__))
        key_path = os.path.join(script_dir, KEY_FILE_NAME)

        sheet_name = 'All'
        spreadsheet_url = 'https://docs.google.com/spreadsheets/d/1BtG2GdoV7B2Cu5Ej7B49VU8z6MZyFqGRyjOSX5emp2Q'

        credentials = service_account.Credentials.from_service_account_file(key_path)
        client = bigquery.Client(credentials=credentials)

        today = datetime.now()
        yesterday = today - timedelta(days=1)
        today_str = today.strftime("%Y%m%d")
        yesterday_str = yesterday.strftime("%Y%m%d")
        print(f"Running alert check for {yesterday_str} vs {today_str}")

        table = f"{PROJECT_ID}.{ALERTS_TABLE_ID}"
        if check_today_exists(client, table):
            print("Today's data already exists. Process terminated.")
            exit(0)  # or return
        else:
            print("No data for today yet. Continuing with the process.")

    except Exception as e:
        print(f"Configuration error: {e}")
        return f"Configuration error: {e}", 500

    # --- 2. Find Daily Segment Drops ---
    drop_df = find_daily_drop_segments(client, yesterday_str, today_str, THRESHOLD)

    # --- 3. Initialize Google Sheets client ---
    sheet_client = get_google_sheet_client(key_path)
    worksheet = sheet_client.open_by_url(spreadsheet_url).worksheet(sheet_name)
    sheet_rows = worksheet.get_all_records()

    fb_to_desc = {str(row['FB']): (row['SEGMENT DESCRIPTION'] if row['SEGMENT DESCRIPTION'] else row['SEGMENT NAME']) for row in sheet_rows}

    # --- 4. Send Daily Alert and Save to BigQuery ---
    today_date_str = today.strftime('%Y-%m-%d')
    slack_msg = ""
    if drop_df.empty:
        slack_msg += f"✅ {today_date_str}: LTA Segment流量正常，沒有發現異常下降。\n"
    else:
        display_limit = 30
        total_alerts = len(drop_df)
        df_display = drop_df.head(display_limit)

        slack_msg += f"*{'⚠️'} {today_date_str} LTA Segment 流量下降警示 (共 {total_alerts} 筆)：*\n"
        if total_alerts > display_limit:
            slack_msg += f"僅顯示前 {display_limit} 筆最相關的項目：\n"
        
        slack_msg += "```"

        max_id_len = max(df_display['segment_id'].astype(str).apply(len))
        max_yesterday_len = max(df_display['count_yesterday'].astype(int).astype(str).apply(len))
        max_today_len = max(
            df_display['count_today'].apply(lambda x: len(str(int(x))) if not pd.isna(x) else 3)
        )
        ARROW = '➡️'

        for _, row in df_display.iterrows():
            segment_id = str(row['segment_id'])
            count_yesterday_str = f"{int(row['count_yesterday'])}" if not pd.isna(row["count_yesterday"]) else "N/A"
            count_today_str = "N/A" if pd.isna(row["count_today"]) else f"{int(row['count_today'])}"
            rate_str = "N/A " if pd.isna(row["rate_change"]) else f"下降 {abs(row['rate_change'])*100:.2f}%"
            desc = fb_to_desc.get(segment_id, "")
            desc_str = f"（{desc}）" if desc else ""
            slack_msg += (
                f"- {segment_id:<{max_id_len}}：從 {count_yesterday_str:>{max_yesterday_len}} {ARROW} {count_today_str:<{max_today_len}}（{rate_str:<12}）{desc_str}\n"
            )

        if total_alerts > display_limit:
            remaining_count = total_alerts - display_limit
            slack_msg += f"\n...等其他 {remaining_count} 筆異常未完全顯示。\n"

        slack_msg += "```"

        # Save the detected drops to BigQuery
        save_alerts_to_bq(client, drop_df, f"{PROJECT_ID}.{ALERTS_TABLE_ID}")

    # --- 5. Find and Alert for Repeated Segments ---
    full_table_id = f"{PROJECT_ID}.{ALERTS_TABLE_ID}"
    repeat_df = find_repeated_segments(client, full_table_id, days=7, min_alerts=2)

    if not repeat_df.empty:
        slack_msg += f"\n*{'🔁'} 近7日 LTA Segment 重複下降警示：*\n"
        # 依 alert_days 分組
        from collections import defaultdict
        group = defaultdict(list)
        for _, row in repeat_df.iterrows():
            group[row['alert_days']].append(str(row['segment_id']))
        # 依 alert_days 由大到小排序
        for days in sorted(group.keys(), reverse=True):
            segs = ', '.join(group[days])
            slack_msg += f"- {segs}：重複出現 {days} 天\n"
    else:
        print("No repeated segments found in the last 7 days.")

    send_slack_alert(SLACK_WEBHOOK_URL, slack_msg)

    return 'LTA Drop Alert process completed successfully.', 200

if __name__ == "__main__":
    main(None)
