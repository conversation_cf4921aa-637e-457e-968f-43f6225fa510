import pytest
import pandas as pd

# 測資：drop_df
@pytest.fixture
def drop_df_sample():
    return pd.DataFrame([
        {'segment_id': 'tm:d1773', 'count_yesterday': 100, 'count_today': 50, 'rate_change': -0.5},
        {'segment_id': 'tm:c_9999_3205_k_005', 'count_yesterday': 200, 'count_today': 100, 'rate_change': -0.5},
    ])

# 測資：Google Sheet rows
@pytest.fixture
def sheet_rows_sample():
    return [
        {'FB': 'tm:d1773', 'SEGMENT DESCRIPTION': '近期瀏覽過懷孕相關文章的使用者', 'SEGMENT NAME': ''},
        {'FB': 'tm:c_9999_3205_k_005', 'SEGMENT DESCRIPTION': '', 'SEGMENT NAME': '輕量戶外探索家（站外）'},
        {'FB': 'tm:c_9999_3205_k_004', 'SEGMENT DESCRIPTION': '近期曾造訪WIWI網站，並且喜歡快速、機能、舒適又具有設計感的產品', 'SEGMENT NAME': '高質感效率追求者（站外）'},
    ]

# 測資：repeat_df
@pytest.fixture
def repeat_df_sample():
    return pd.DataFrame([
        {'segment_id': 'tm:d1773', 'alert_days': 3},
        {'segment_id': 'tm:c_9999_3205_k_005', 'alert_days': 2},
    ]) 