import pytest
from unittest.mock import patch, MagicMock
import pandas as pd
from LTA_drop_alert import main as lta_main
from .test_data import drop_df_sample, sheet_rows_sample, repeat_df_sample

@patch('LTA_drop_alert.main.send_slack_alert')
@patch('LTA_drop_alert.main.save_alerts_to_bq')
@patch('LTA_drop_alert.main.find_repeated_segments')
@patch('LTA_drop_alert.main.find_daily_drop_segments')
@patch('LTA_drop_alert.main.get_google_sheet_client')
@patch('LTA_drop_alert.main.bigquery.Client')
def test_main_alerts(
    mock_bq_client, mock_get_sheet, mock_find_drop, mock_find_repeat, mock_save_bq, mock_send_slack,
    drop_df_sample, sheet_rows_sample, repeat_df_sample
):
    # Mock BigQuery client
    mock_bq_client.return_value = MagicMock()
    # Mock drop_df
    mock_find_drop.return_value = drop_df_sample
    # Mock repeat_df
    mock_find_repeat.return_value = repeat_df_sample
    # Mock Google Sheet client
    mock_sheet = MagicMock()
    mock_ws = MagicMock()
    mock_ws.get_all_records.return_value = sheet_rows_sample
    mock_sheet.open_by_url.return_value.worksheet.return_value = mock_ws
    mock_get_sheet.return_value = mock_sheet

    # 執行 main
    resp, status = lta_main.main(None)

    # 驗證 slack 訊息內容
    assert status == 200
    assert mock_send_slack.called
    slack_msg = mock_send_slack.call_args[0][1]
    # 應包含描述A、名稱B（B2 沒有描述要 fallback 名稱）
    assert '近期瀏覽過懷孕相關文章的使用者' in slack_msg
    assert '輕量戶外探索家（站外）' in slack_msg
    # 應包含重複下降警示
    assert '重複出現' in slack_msg
    # 應有每日下降警示
    assert '流量下降警示' in slack_msg
    # 應有 save_alerts_to_bq 被呼叫
    assert mock_save_bq.called
