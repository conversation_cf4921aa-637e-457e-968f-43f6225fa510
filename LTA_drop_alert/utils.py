import requests
import gspread
from google.oauth2 import service_account
from oauth2client.service_account import ServiceAccountCredentials
import pandas as pd
from google.cloud import bigquery
from datetime import datetime, date

def get_google_sheet_client(key_path):
    """Initialize and return a Google Sheets client."""
    scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
    creds = ServiceAccountCredentials.from_json_keyfile_name(key_path, scope)
    return gspread.authorize(creds)

def send_slack_alert(webhook_url, message):
    """Sends a message to a Slack webhook."""
    if not webhook_url:
        print("Slack webhook URL is not set. Cannot send message.")
        return
    payload = {"text": message}
    try:
        response = requests.post(webhook_url, json=payload)
        response.raise_for_status()  # Raise an exception for bad status codes
        print("Slack alert sent successfully.")
    except requests.exceptions.RequestException as e:
        print(f"Failed to send Slack alert: {e}")

def check_today_exists(client, table, date_field="date", date_format="%Y-%m-%d"):
    today_str = datetime.now().strftime(date_format)
    query = f"""
        SELECT COUNT(*) as cnt
        FROM `{table}`
        WHERE {date_field} = '{today_str}'
    """
    result = client.query(query).result()
    for row in result:
        return row.cnt > 0
    return False

def find_daily_drop_segments(client, yesterday_str, today_str, threshold):
    """Queries BigQuery to find segments with a significant drop."""
    query = f"""
    WITH
    yesterday AS (
        SELECT
            individual_segment_id AS segment_id,
            COUNT(DISTINCT permanent) AS count_yesterday
        FROM
            `tagtoo-ml-workflow.tagtoo_export_results.special_lta_{yesterday_str}`,
            UNNEST(SPLIT(segment_id, ',')) AS individual_segment_id
        GROUP BY
            individual_segment_id
    ),
    today AS (
        SELECT
            individual_segment_id AS segment_id,
            COUNT(DISTINCT permanent) AS count_today
        FROM
            `tagtoo-ml-workflow.tagtoo_export_results.special_lta_{today_str}`,
            UNNEST(SPLIT(segment_id, ',')) AS individual_segment_id
        GROUP BY
            individual_segment_id
    ),
    merged AS (
        SELECT
            COALESCE(t.segment_id, y.segment_id) AS segment_id,
            y.count_yesterday,
            t.count_today,
            SAFE_DIVIDE(t.count_today - y.count_yesterday, y.count_yesterday) AS rate_change
        FROM yesterday y
            LEFT JOIN today t ON y.segment_id = t.segment_id
    )
    SELECT
        *
    FROM
        merged
    WHERE
        -- 排除家樂福商品中4位數和5位數的segment_id
        NOT (
            (segment_id LIKE 'tm:c_715_pc_%' AND LENGTH(REGEXP_EXTRACT(segment_id, r'tm:c_715_pc_(\d+)$')) IN (4, 5))
            OR (segment_id LIKE 'tm:c_715_pv_%' AND LENGTH(REGEXP_EXTRACT(segment_id, r'tm:c_715_pv_(\d+)$')) IN (4, 5))
        )
        AND (
            -- 條件1: 昨天人數>0但今天為0的情況
            (
                count_yesterday > 0
                AND (count_today IS NULL OR count_today = 0)
                AND (
                    -- 非家樂福商品3位數，或家樂福商品3位數但昨天人數>100
                    NOT (
                        (segment_id LIKE 'tm:c_715_pc_%' AND LENGTH(REGEXP_EXTRACT(segment_id, r'tm:c_715_pc_(\d+)$')) = 3)
                        OR (segment_id LIKE 'tm:c_715_pv_%' AND LENGTH(REGEXP_EXTRACT(segment_id, r'tm:c_715_pv_(\d+)$')) = 3)
                    )
                    OR count_yesterday > 100
                )
            )
            OR
            -- 條件2: 一般下降情況（昨天人數>1000且下降率超過閾值）
            (
                count_yesterday > 1000
                AND rate_change < {threshold}
            )
            OR
            -- 條件3: 家樂福商品3位數的特殊情況（昨天人數>100且下降率超過閾值）
            (
                (
                    (segment_id LIKE 'tm:c_715_pc_%' AND LENGTH(REGEXP_EXTRACT(segment_id, r'tm:c_715_pc_(\d+)$')) = 3)
                    OR (segment_id LIKE 'tm:c_715_pv_%' AND LENGTH(REGEXP_EXTRACT(segment_id, r'tm:c_715_pv_(\d+)$')) = 3)
                )
                AND count_yesterday > 100
                AND rate_change < {threshold}
            )
        )
    ORDER BY
        rate_change ASC
    """
    try:
        df = client.query(query).result().to_dataframe()
        print(f"Found {len(df)} segments with drop.")
        return df
    except Exception as e:
        print(f"Error querying daily drop segments: {e}")
        return pd.DataFrame()

def save_alerts_to_bq(client, df, table_id):
    """Appends a DataFrame to a specified BigQuery table."""
    if df.empty:
        print("No data to save to BigQuery.")
        return

    # Add a date column for partitioning/logging
    df_to_load = df.copy()
    df_to_load['date'] = date.today()

    job_config = bigquery.LoadJobConfig(
        write_disposition="WRITE_APPEND",
    )
    try:
        load_job = client.load_table_from_dataframe(
            df_to_load, table_id, job_config=job_config
        )
        load_job.result()  # Wait for the job to complete
        print(f"Appended {len(df)} records to BigQuery table: {table_id}")
    except Exception as e:
        print(f"Failed to save data to BigQuery: {e}")

def find_repeated_segments(client, table_id, days=7, min_alerts=2):
    """Finds segments that have been alerted multiple times in the last N days."""
    query = f"""
    WITH recent_alerts AS (
      SELECT
        segment_id,
        DATE(date) AS alert_date
      FROM `{table_id}`
      WHERE DATE(date) >= DATE_SUB(CURRENT_DATE(), INTERVAL {days} DAY)
    ),
    alert_counts AS (
      SELECT
        segment_id,
        COUNT(DISTINCT alert_date) AS alert_days
      FROM recent_alerts
      GROUP BY segment_id
    )
    SELECT
      segment_id,
      alert_days
    FROM alert_counts
    WHERE alert_days >= {min_alerts}
      -- 排除家樂福商品中4位數和5位數的segment_id
      AND NOT (
        (segment_id LIKE 'tm:c_715_pc_%' AND LENGTH(REGEXP_EXTRACT(segment_id, r'tm:c_715_pc_(\d+)$')) IN (4, 5))
        OR (segment_id LIKE 'tm:c_715_pv_%' AND LENGTH(REGEXP_EXTRACT(segment_id, r'tm:c_715_pv_(\d+)$')) IN (4, 5))
      )
    ORDER BY alert_days DESC
    """
    try:
        repeat_df = client.query(query).result().to_dataframe()
        print(f"Found {len(repeat_df)} repeated segments in the last {days} days.")
        return repeat_df
    except Exception as e:
        print(f"Error querying repeated segments: {e}")
        return pd.DataFrame()
