# LTA Drop Alert

## 專案簡介
LTA Drop Alert 是一個自動化監控系統，負責每日偵測 LTA Segment 流量異常下降，並將警示訊息發送到 Slack，同時記錄異常事件到 BigQuery，並可比對 Google Sheet 取得 segment 描述資訊。

## 功能說明
- 每日自動比對昨日與今日 LTA Segment 流量，偵測異常下降（依 threshold 設定）。
- 針對異常 segment，查詢 Google Sheet 取得 segment 描述，並組合警示訊息。
- 將警示訊息發送到 Slack。
- 將異常事件寫入 BigQuery。
- 追蹤近 7 日內重複下降的 segment，並合併警示訊息發送。

## 執行流程
1. 取得 BigQuery 流量資料，計算每日 segment 變化。
2. 讀取 Google Sheet，取得 segment_id 對應的描述（優先用 SEGMENT DESCRIPTION，否則用 SEGMENT NAME）。
3. 組合警示訊息，發送到 Slack。
4. 將異常事件寫入 BigQuery。
5. 檢查近 7 日內重複下降的 segment，合併警示訊息發送。


## 主要設定與環境變數
- `key_path`：Service Account 金鑰路徑（如 `tagtoo-ml-workflow-61e119f3d094_new.json`）
- `GOOGLE_APPLICATION_CREDENTIALS_PATH`：可選，若用環境變數方式
- `PROJECT_ID`：GCP 專案 ID
- `ALERTS_TABLE_ID`：BigQuery 資料表（格式：`dataset.table`）
- `SLACK_WEBHOOK_URL`：Slack webhook URL
- `THRESHOLD`：流量下降判斷門檻
- `spreadsheet_url`、`sheet_name`：Google Sheet 來源

## Google Sheet 欄位說明
- `FB`：segment_id 對應欄位
- `SEGMENT DESCRIPTION`：segment 描述（優先使用）
- `SEGMENT NAME`：segment 名稱（若描述為空則 fallback）

## BigQuery 欄位說明
- `segment_id`：Segment ID
- `count_yesterday`：昨日流量
- `count_today`：今日流量
- `rate_change`：變化率
- `date`：記錄日期

## Slack 警示訊息格式
- 當日流量下降警示：
  ```
  *⚠️ 2024-06-01 LTA Segment 流量下降警示：*
  - `A1`：從 100 ➡️ 50（下降 50.00%）（描述A）
  - `B2`：從 200 ➡️ 100（下降 50.00%）（名稱B）
  ```
- 近 7 日重複下降警示：
  ```
  *🔁 近7日 LTA Segment 重複下降警示：*
  - `A1`：重複出現 3 天
  - `B2`：重複出現 2 天
  ```

## 測試
- 所有單元測試與測資皆放於 `tests/` 目錄，執行 `pytest` 可自動驗證主流程與訊息內容。

---
如需調整 Google Sheet 欄位、BigQuery schema 或 Slack 格式，請同步更新程式與本說明文件。
