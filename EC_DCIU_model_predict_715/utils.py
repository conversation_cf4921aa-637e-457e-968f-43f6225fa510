import pandas as pd
from dask import dataframe as dd
from ctypes import ArgumentError

def load_data(path: str, file_format: str = 'PARQUET', **kwargs) -> pd.core.frame.DataFrame:
  if file_format == 'PARQUET':
    df = dd.read_parquet(path, assume_missing=True, **kwargs).compute()
  elif file_format == 'CSV':
    df = dd.read_csv(path, assume_missing=True, **kwargs).compute()
  else:
    raise ArgumentError("Invalid file format")
  return df

def concate_ecid_industry(df):
    """
    times industry 10000 than plus ecid
    ex:industry:4 ecid:256 become 40256
    """
    df['inad'] = df['industry_id']*10000 + df['ec_id']
    df = df.drop(columns=['industry_id','ec_id'])
    return df

def get_ec_list(df):
    """
    use frequence and cookies_num to ranking ec
    than get ec_list
    """
    ec_cookies_num = df.groupby('inad').size()
    # ec_frequences = df.groupby('inad').date.unique()
    ec_list = []
    for i in ec_cookies_num.index:
        ec_list.append([i,ec_cookies_num[i]])
    ec_list = pd.DataFrame(ec_list)
    ec_list.columns = ['ec','cookies_num']
    ec_list = ec_list.set_index('ec')
    ec_list = ec_list.sort_values(['cookies_num'],ascending=False)
    print('total ec numbers: ',len(ec_list))
    return ec_list

def ec_onehot(df):
    ec_onehot = pd.get_dummies(df['inad'])
    ec_onehot.columns = ec_onehot.columns.astype('str')
    df = df.drop(columns = ['inad'])
    df = pd.concat([df,ec_onehot],axis=1)
    print('df done!')
    return df

def sum_other_ec(df,ec_list,ec_num):
    """
        use ec_list and ec_num to get top10(ec_num) ec
        than remove than from df to get other_ec list
        select that data in other_ec,sum into one columns
        final we got "other_track" and "other_transaction"
    """
    # select other ec_track(ec not in ec_num[:ec_num])
    length = 86 # only with cycle -> 87
    track_idx = ec_list.index[:ec_num].astype(str)
    # transaction_idx = ec_list.index[:ec_num].astype(str)+'transaction'
    other_idx = df.columns[~df.columns.isin(track_idx)][length:]
    temp = other_idx[:int(len(other_idx))]
    other_track = pd.DataFrame(df[temp].sum(axis=1))
    other_track.columns = ['other_ec']
    # temp = other_idx[int(len(other_idx)/2):]
    # other_transaction = pd.DataFrame(df[temp].sum(axis=1))
    # other_transaction.columns = ['other_transaction']

    df = pd.concat(
      [
        df[df.columns[:length]],
        df[ec_list.index[:ec_num].astype(str)],
        other_track
      ],
      axis=1
    )
    return df

def feature_transform(df_train_f: pd.DataFrame) -> pd.core.frame.DataFrame:

  df_train_f = concate_ecid_industry(df_train_f)
  ec_list = get_ec_list(df_train_f)
  df_train_f = ec_onehot(df_train_f)
  df_train_f = sum_other_ec(df_train_f, ec_list, 10)
  df_train_f = df_train_f.set_index(['group_id'])
  df_train_f = df_train_f.reset_index()

  df_mode = df_train_f[list(df_train_f.columns[:7]) + ['avg_value_view_item']]
  list_sum = list(df_train_f.columns[:1]) + list(df_train_f.columns[7:])
  list_sum.remove('avg_value_view_item')
  df_sum = df_train_f[list_sum]

  # mode or mean
  df_mode = df_mode.groupby(['group_id']).mean().reset_index()
  df_sum = df_sum.groupby(['group_id']).sum().reset_index()

  df_train_f = pd.merge(df_mode, df_sum, on=['group_id'])

  return df_train_f