import functions_framework
import pandas as pd
import numpy as np
import google_storage
from datetime import datetime
from datetime import timedelta
from pytz import timezone
import joblib # load scikit learn model
from lightgbm import LGBMClassifier
from typing import List, Set, Dict, Tuple
from google.oauth2 import service_account
from utils import *

@functions_framework.http
def main(request):
  """HTTP Cloud Function.
  Args:
      request (flask.Request): The request object.
      <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
  Returns:
      The response text, or any set of values that can be turned into a
      Response object using `make_response`
      <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
  """
  # * external argument
  request_json = request.get_json(silent=True)
  request_args = request.args

  if request_json and 'ecid' in request_json: # POST
      ecid = request_json['ecid']
  elif request_args and 'ecid' in request_args: # GET
      ecid = request_args['ecid']
  else:
      raise AttributeError("No ecid found in URL.")
  print("ECID:", ecid)

  # Credentials
  key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
  credentials = service_account.Credentials.from_service_account_file(key_path)

  # init parameter
  GSIO = google_storage.GoogleStorageIO(credentials=credentials)
  ecid = int(ecid)
  DATE_FORMAT = "%Y-%m-%d"

  current_date = datetime.now(timezone('Asia/Taipei'))
  feature_end_date = (current_date - timedelta(days=1)).strftime(DATE_FORMAT)
  feature_start_date = (current_date - timedelta(days=30+1)).strftime(DATE_FORMAT)

  output_path = "gs://tagtoo-ml-workflow/topic10/LTA_feature"
  export_path = output_path + f"/{feature_start_date}_{feature_end_date}/*.parquet"
  df = load_data(export_path, file_format='PARQUET')
  print(f"{export_path} loaded.")

  df_train_f = feature_transform(df)
  ar = np.array(df_train_f.drop(['group_id'], axis=1))

  print("Load model.")
  model_gcs_path = 'gs://tagtoo-ml-workflow/topic10/Fredrick_Workbench/First_Party_EC_Predict/model/DCIU_model_715_2023-11-13.joblib'
  model_path = '/tmp/model.joblib'
  GSIO.download_to_path(gsuri=model_gcs_path, localpath=model_path)
  model = joblib.load(model_path)
  pred = model.predict_proba(ar)

  df_pred = pd.DataFrame(df_train_f.group_id)
  df_pred['prob'] = pred[:,1]

  print("Segment user.")
  user_D = df_pred[df_pred['prob']>=df_pred['prob'].quantile(0.75)].group_id.tolist()
  user_C = df_pred[(df_pred['prob']>=df_pred['prob'].quantile(0.5)) & (df_pred['prob']<df_pred['prob'].quantile(0.75))].group_id.tolist()
  user_I = df_pred[(df_pred['prob']>=df_pred['prob'].quantile(0.25)) & (df_pred['prob']<df_pred['prob'].quantile(0.5))].group_id.tolist()
  user_U = df_pred[df_pred['prob']<df_pred['prob'].quantile(0.25)].group_id.tolist()

  conditions = [
      df_pred['group_id'].isin(user_D),
      df_pred['group_id'].isin(user_C),
      df_pred['group_id'].isin(user_I),
      df_pred['group_id'].isin(user_U)
  ]

  choices = [
      'model_D',
      'model_C',
      'model_I',
      'model_U'
  ]

  df_pred['tag'] = np.select(conditions, choices, default='')

  print("Upload dataframe to BigQuery.")
  DATE = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y%m%d")
  project_id = "tagtoo-ml-workflow"
  table_id = f'tagtoo_export_results.715_DCIU_model_temp_for_update_{DATE}'
  schema_data = [
    {"name": "group_id", "type": "STRING"},
    {"name": "tag", "type": "STRING"}
  ]
  df_pred[['group_id', 'tag']].to_gbq(table_id, project_id=project_id, credentials=credentials, table_schema=schema_data, if_exists='append')

  return 'Job complete'
