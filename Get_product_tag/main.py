import functions_framework
# time
from datetime import datetime
from pytz import timezone
# frequentily used tools
import google_storage
from google.oauth2 import service_account
from utils import *
import logging

class InsufficientDataError(Exception):
    def __init__(self, message="資料量不足，商品數量少於 10 個"):
        self.message = message
        super().__init__(self.message)  # 呼叫父類別的初始化方法，並將錯誤訊息傳遞給它

@functions_framework.http
def main(request):
    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """

    logging.basicConfig(level=logging.INFO)

    # * external argument
    request_json = request.get_json(silent=True)
    request_args = request.args

    if request_json and 'ecid' in request_json: # POST
        ecid = request_json['ecid']
    elif request_args and 'ecid' in request_args: # GET
        ecid = request_args['ecid']
    else:
        raise AttributeError("No ecid found in URL.")
    # logging.info("ECID: %s", ecid)
    
    if request_json and 'topk' in request_json: # POST
        topk = request_json['topk']
    elif request_args and 'topk' in request_args: # GET
        topk = request_args['topk']
    else:
        topk = 50
        
    logging.info("ECID: %s | TOPK: %s", ecid, topk)
    
    current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
    output_path = f'gs://tagtoo-ml-workflow/topic10/get_ec_product_tag_{ecid}_{current_date}' # output gcs path
    # path = f'gs://tagtoo-ml-workflow/topic10/get_ec_product_tag_{ecid}_{current_date}/query/*.parquet' # query from bq
    # product_tag_table_id = f'{ecid}_product_tag'
    daily_product_tag_path = f'gs://tagtoo-bigquery-export/Fredrick_Test/product_tag/daily_product_tag_path/{ecid}_daily_product_tag_path.json'
    best_product_view_threshold = 0.95
    best_product_purchase_threshold = 0.95
    ecid = int(ecid)
    topk = int(topk)

    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    # project_id = credentials.project_id
    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    # Query
    # 2024-03-21 把 query 改到 cloud function 執行，之前是用 bigquery scheduler 排程
    # logging.info("Start querying")
    client = bigquery.Client(credentials=credentials)
    job_config = bigquery.QueryJobConfig()
    sql = get_product_tag_query(ecid=ecid, past_day=14)
    query_job = client.query(sql, job_config=job_config)  # Make an API request.

    # logging.info("Start ETL.")
    df = query_job.to_dataframe()
    logging.info("%d product count: %d", ecid, len(df))

    if len(df) <= 10:
        raise InsufficientDataError()

    # df = parquet_load_data(path, storage_options={"token": key_path})
    product_id_dict = df.set_index('name')['id'].to_dict()
    df = df.drop('id', axis=1).groupby(['name'], as_index=False).sum()
    # df = df.groupby(['id', 'name'], as_index=False).sum()
    df_rm_outlier = remove_outlier(df, col='event_num')
    df = df[df['name']!=''] # remove empty product name

    # * make segment dictionary: {'tag_name': product of tag}
    segment_dict = {}
    top_event_list = df.columns[1:].tolist()

    top_event_name_list = [
        'event',
        'purchase',
        'view_item',
        'add_to_wishlist',
        'add_payment_info',
        'add_to_cart',
        'view_item_list',
        'promotion',
        'checkout',
        'ADC',
        'FBC',
    ]

    for event, event_name in zip(top_event_list, top_event_name_list):
        segment_dict[event_name] = df.sort_values(by=event, ascending=False)['name'].head(topk)

    # * add conversion rate tag to segment dictionary
    segment_dict['CVR'] = get_conversion_product(df_rm_outlier, df, k=topk)
    top_event_name_list += ['CVR']

    # * add ad conversion rate tag to segment dictionary
    segment_dict['AD_CVR'] = get_ad_conversion_product(df_rm_outlier, df, k=topk)
    top_event_name_list += ['AD_CVR']

    # * get four quadrant product tag
    hot_product, topic_product, potential_product, cold_product = get_segment_list(
        df,
        df_rm_outlier,
        col1='event_num',
        col2='purchase_num',
    )

    # * get the best product tag
    view_num_99, purchase_num_99 = get_the_best_product_threshold(
        df_rm_outlier, 'view_item_num', 'purchase_num',
        col1_threshold=best_product_view_threshold,
        col2_threshold=best_product_purchase_threshold
    )
    thebest_product = df.query("(view_item_num>@view_num_99)&(purchase_num>@purchase_num_99)").name

    # * add four quadrant and the best product to segment dictionary
    segment_name_list = ['hot', 'topic', 'potential', 'cold', 'best']
    segment_list = [hot_product, topic_product, potential_product, cold_product, thebest_product]

    for key, value in zip(segment_name_list, segment_list):
        segment_dict[key] = value
        
    # check tag name in segment dictionary is same as top_event_name + segment_name(four quadrant and the best product)
    if not len(segment_dict) == len([*top_event_name_list, *segment_name_list]):
        raise AssertionError("tag and segment name are inconsistent")
        
    for event_name in [*top_event_name_list, *segment_name_list]:
        df[f'{event_name}_tag'] = df['name'].isin(segment_dict[event_name]).astype(int)
        df[f'{event_name}_tag'] = df[f'{event_name}_tag'].apply(lambda x: f'{event_name}' if x==1 else '')
        
    # * make tag
    df['tag'] = make_tag_column(df)
    df['tag'] = df['tag'].apply(lambda x: list(filter(lambda string: string != '', x.split(','))))

    df['id'] = df['name'].apply(lambda x: product_id_dict[x])

    tag_col = ["{}_tag".format(col) for col in [*top_event_name_list, *segment_name_list]]
    df.drop(tag_col, axis=1, inplace=True)

    df_result = df[['id', 'name', 'tag']]
    df_result = df_result[df_result['name']!='']

    if not df_result.duplicated(['id', 'name']).sum() == 0:
        raise AssertionError("Product has duplicated name and id")

    # logging.info("Product tag dataframe complete.")

    # logging.info("Update latest path to daily product tag path file.")
    result_path = "/tmp/result_{}.json".format(current_date)
    gcs_result_path = output_path + "/result_{}.json".format(current_date)

    # * save as JSONL
    df_result.to_json(result_path, orient="records", lines=True)
    GSIO.upload_file(gsuri=gcs_result_path, localpath=result_path) # upload to GCS
    logging.info("%d JSON GCS path: %s", ecid, gcs_result_path)

    # * update to daily product tag path file
    # * download path dict
    product_tag_dict_gcs_path = daily_product_tag_path
    product_tag_dict_path = "/tmp/daily_product_tag_path.json"
    GSIO.download_to_path(gsuri=product_tag_dict_gcs_path, localpath=product_tag_dict_path)

    # * read and update file from local
    product_tag_dict = read_json(product_tag_dict_path)
    product_tag_dict[0]['latest'] = gcs_result_path
    product_tag_dict[0][current_date] = gcs_result_path
    write_to_json(product_tag_dict_path, product_tag_dict)
    # * upload daily product tag path file to GCS
    GSIO.upload_file(gsuri=product_tag_dict_gcs_path, localpath=product_tag_dict_path)

    # ===================================================================================================================

    # print("Delete {} product tag table's data.".format(ecid))
    # delete_query = "DELETE FROM `{}` WHERE TRUE".format(product_tag_table_id)
    # query_job = query_from_bigquery(delete_query, credentials=credentials)

    # if query_job.errors:
    #     raise Exception('Table was not successfullly refreshed in BigQuery')
    # else:
    #     time.sleep(3) # wait for table deletion
    #     upload_json_to_table(target_table_id=product_tag_table_id, data_gcs_path=gcs_result_path, credentials=credentials)

    logging.info(f"Job complete <> ECID: {ecid} ")

    return 'Success', 200