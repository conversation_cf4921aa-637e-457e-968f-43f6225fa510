import pandas as pd
from dask import dataframe
import json
from google.cloud import bigquery
import time
from typing import List, Dict

# * make product tag utils

def get_product_tag_query(ecid: int, past_day: int = 14) -> str:
    return f"""
  SELECT
    items.id,
    items.name,
    COUNT(items.name) event_num,
    COUNT(
    IF
      (event.name = "purchase", items.name, NULL)) purchase_num,
    COUNT(
    IF
      (event.name = "view_item", items.name, NULL)) view_item_num,
    COUNT(
    IF
      (event.name = "add_to_wishlist", items.name, NULL)) add_to_wishlist_num,
    COUNT(
    IF
      (event.name = "add_payment_info", items.name, NULL)) add_payment_info_num,
    COUNT(
    IF
      (event.name = "add_to_cart", items.name, NULL)) add_to_cart_num,
    COUNT(
    IF
      (event.name = "view_item_list", items.name, NULL)) view_item_list,
    COUNT(
    IF
      (event.name = "view_promotion", items.name, NULL)) view_promotion_num,
    COUNT(
    IF
      (event.name = "checkout", items.name, NULL)) checkout_num,
    COUNT(
    IF
      (session.source NOT LIKE "%direct%"
        AND session.medium NOT LIKE "%organic%"
        AND session.medium NOT LIKE "%referral%", items.name, NULL)) ad_click_num,
    COUNT(
    IF
      (((session.source LIKE "%fb%"
            OR session.source LIKE "%FB%"
            OR session.source LIKE "%Facebook%"
            OR session.source LIKE "%facebook%")
          AND session.medium NOT LIKE "%organic%"
          AND session.medium NOT LIKE "%referral%"), items.name, NULL)) fb_click_num
  FROM
    `tagtoo-tracking.event_prod.tagtoo_event`,
    UNNEST(event.items) AS items
  WHERE
    DATE(event_time, "Asia/Taipei") >= DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL {past_day} DAY)
    AND DATE(event_time, "Asia/Taipei") < CURRENT_DATE("Asia/Taipei")
    AND ec_id = {ecid}
  GROUP BY
    1,
    2 
    """

def parquet_load_data(path, **kwargs):
    """Loading the pieces in separate processes, then transferring all the data to be stitched into
    a single data-frame in the main process."""
    df = dataframe.read_parquet(path, assume_missing=True, **kwargs)
    return df.compute(scheduler="processes")

def remove_outlier(df: pd.DataFrame, col: str) -> pd.core.frame.DataFrame:
    df_no_zero = df[df[col]>0]
    upper_threshold = df_no_zero[col].mean() + 2.5*df_no_zero[col].std()
    print(f"{col} threshold:", upper_threshold)
    return df[df[col]<upper_threshold]

def get_conversion_product(
    df_rm_outlier: pd.DataFrame,
    df: pd.DataFrame,
    k: int, # top k product
) -> pd.core.series.Series:
    
    df['conversion_rate'] = df['purchase_num'] / df['event_num']
    
    event_threshold = df_rm_outlier['event_num'].quantile(0.9)
    purchase_threshold = df_rm_outlier['purchase_num'].quantile(0.9)
    
    df = df[(df['event_num']>=event_threshold) & (df['purchase_num']>=purchase_threshold)]
    
    return df.sort_values(by='conversion_rate', ascending=False)['name'].head(k)

def get_ad_conversion_product(
    df_rm_outlier: pd.DataFrame,
    df: pd.DataFrame,
    k: int, # top k product
) -> pd.core.series.Series:
    
    df['AD_CVR'] = df['ad_click_num'] / df['event_num']
    
    event_threshold = df_rm_outlier['event_num'].quantile(0.9)
    df = df[(df['event_num']>=event_threshold)]
    
    return df.sort_values(by='AD_CVR', ascending=False)['name'].head(k)

def get_segment_list(
    df: pd.DataFrame,
    df_rm_outlier: pd.DataFrame,
    col1: str,
    col2: str,
    col1_threshold_pct: float = 0.5, # median
    col2_threshold_pct: float = 0.5, # median
) -> List[pd.core.series.Series]:
    
    col1_threshold = df_rm_outlier[col1].quantile(col1_threshold_pct)
    col2_threshold = df_rm_outlier[col2].quantile(col2_threshold_pct)
    print("event num threshold:", col1_threshold)
    print("purchase num threshold:", col2_threshold)
    print("")
    
    hot_product = df[(df[col1]>col1_threshold)&(df[col2]>col2_threshold)].name
    topic_product = df[(df[col1]>col1_threshold)&(df[col2]<=col2_threshold)].name
    potential_product = df[(df[col1]<=col1_threshold)&(df[col2]>col2_threshold)].name
    cold_product = df[(df[col1]<=col1_threshold)&(df[col2]<=col2_threshold)].name
    
    ttl_num = len(hot_product) + len(topic_product) + len(potential_product) + len(cold_product)
    
    # check every product has a group
    assert ttl_num == df.shape[0]
    
    print("hot product num pct:", round((len(hot_product) / ttl_num)*100, 2), '%')
    print("topic product num pct:", round((len(topic_product) / ttl_num)*100, 2), '%')
    print("potential product num pct:", round((len(potential_product) / ttl_num)*100, 2), '%')
    print("cold product num pct:", round((len(cold_product) / ttl_num)*100, 2), '%')
    
    return hot_product, topic_product, potential_product, cold_product

def get_the_best_product_threshold(
    df: pd.DataFrame,
    col1: str,
    col2: str,
    col1_threshold: float = 0.95,
    col2_threshold: float = 0.95,
):
    col1_threshold = df[col1].quantile(col1_threshold)
    col2_threshold = df[col2].quantile(col2_threshold)
    print("view num threshold:", col1_threshold)
    print("purchase num threshold:", col2_threshold)
    
    return col1_threshold, col2_threshold

def make_tag_column(df: pd.DataFrame) -> pd.core.series.Series:
    return df.apply(lambda df: ','.join([
    df['event_tag'], 
    df['purchase_tag'], 
    df['view_item_tag'], 
    df['add_to_wishlist_tag'], 
    df['add_payment_info_tag'], 
    df['add_to_cart_tag'], 
    df['view_item_list_tag'], 
    df['promotion_tag'], 
    df['checkout_tag'], 
    df['ADC_tag'], 
    df['FBC_tag'], 
    df['CVR_tag'],
    df['AD_CVR_tag'],
    df['hot_tag'], 
    df['topic_tag'], 
    df['potential_tag'], 
    df['cold_tag'], 
    df['best_tag'], 
]), axis=1)

def read_json(path: str) -> List[Dict[str, str]]:
    with open(path, mode='r') as f:
        return json.load(f)
    
def write_to_json(path: str, data: List[Dict[str, str]]) -> None:
    with open(path, mode='w') as f:
        json.dump(data, f)

def query_from_bigquery(query: str, credentials) -> None:
    # query from BigQuery
    s = time.time()
    client = bigquery.Client(credentials=credentials)
    query_job = client.query(query)

    job_result = query_job.result()
    print("Query job : {}".format(query_job.state))
    print("Total rows : {}".format(job_result.total_rows))
    print("Spending {} seconds to delete table".format(round(time.time() - s), 4))
    print(job_result.schema)
    return query_job

def upload_json_to_table(
    target_table_id: str,
    data_gcs_path: str,
    credentials,
) -> None:
    s = time.time()
    # Construct a BigQuery client object.
    client = bigquery.Client(credentials=credentials)

    # TODO(developer): Set table_id to the ID of the table to create.
    # table_id = "your-project.your_dataset.your_table_name"
    table_id = target_table_id

    job_config = bigquery.LoadJobConfig(
        schema=[
            bigquery.SchemaField("id", "STRING", mode="REQUIRED", description="Product id"),
            bigquery.SchemaField("name", "STRING", mode="REQUIRED"),
            bigquery.SchemaField("tag", "STRING", mode="REPEATED"),
        ],
        source_format=bigquery.SourceFormat.NEWLINE_DELIMITED_JSON,
    )

    uri = data_gcs_path

    load_job = client.load_table_from_uri(
        uri,
        table_id,
        location="US",  # Must match the destination dataset location.
        job_config=job_config,
    )  # Make an API request.

    load_job.result()  # Waits for the job to complete.

    destination_table = client.get_table(table_id)
    print("Loaded {} rows.".format(destination_table.num_rows))
    print("Spending {} seconds to update table".format(round(time.time() - s), 4))

    return