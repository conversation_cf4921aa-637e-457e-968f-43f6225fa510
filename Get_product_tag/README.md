# get-product-tag

## 任務描述
聚合商品各種事件的數值，貼上標籤

## 商品標籤
- event: 各事件加總數量 Topk 的商品
- purchase: 購買事件加總數量 Topk 的商品
- view_item: 瀏覽事件加總數量 Topk 的商品
- add_to_wishlist: 加入許願池事件加總數量 Topk 的商品
- add_payment_info: 加入付款資訊事件加總數量 Topk 的商品
- add_to_cart: 加入購物車事件加總數量 Topk 的商品
- view_item_list: : 瀏覽清單事件加總數量 Topk 的商品
- promotion: 填單 / 促銷事件加總數量 Topk 的商品
- checkout: 結帳事件加總數量 Topk 的商品
- ADC: utm 來自廣告的瀏覽事件加總數量 Topk 的商品
- FBC: utm 來自 fb 的瀏覽事件加總數量 Topk 的商品
- AD_CVR: utm 來自廣告的轉換率 Topk 的商品
- CVR: 轉換率 Topk 的商品
- hot: 熱門商品 [(購買數 > 中位數) & (瀏覽數 > 中位數)]
- topic: 話題商品 [(購買數 <= 中位數) & (瀏覽數 > 中位數)]
- potential: 潛力商品 [(購買數 > 中位數) & (瀏覽數 <= 中位數)]
- cold: 冷門商品 [(購買數 <= 中位數) & (瀏覽數 <= 中位數)]
- best: 爆品 [(購買數 > 95百分位數) & (瀏覽數 > 95百分位數)]

## 使用說明
### 創建初始檔案
執行新 EC 的商品標籤檔案時，需要先跑 `EC_Product_Tag_create_files.ipynb`，裡面會創建兩個必要的檔案
- segment-id-dictionary (JSON)
    - 依照各家 EC 想要的商品標籤，對應到 <a herf="https://docs.google.com/spreadsheets/d/1BtG2GdoV7B2Cu5Ej7B49VU8z6MZyFqGRyjOSX5emp2Q/edit?usp=sharing"> Taxonomy </a> 中的 segment_id。
- daily-product-tag-dictionary (JSON)
    - 儲存商品標籤檔案路徑的檔案: `{'latest': <path>, 'current_date: <path>}`

### Cloud Scheduler
- 一家 EC 架設一個 cloud sheduler
- 內文參數 `{"ecid": "<ecid>", "topk": <topk>}`
- topk 參數可以根據電商商品數量來決定

## 商品標籤應用
- 受眾包: 將 瀏覽/購買過各種商品標籤的使用者 發送至廣告渠道
    - Meta
    - GA4
- Product Feed Label
    - Meta DPA 廣告可以貼標

## 後續開發
- 發想其他的商品標籤
