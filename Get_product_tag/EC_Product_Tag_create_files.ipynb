{"cells": [{"cell_type": "markdown", "metadata": {"id": "lpH5Zw17_4-j"}, "source": ["# First Time Instruction\n", "!! You need to create these files before run the pipeline on kubeflow !!\n", "\n", "### 1. product-tag-table-id\n", "    - Require to create an empty table in BigQuery if first time executing\n", "    - Usage: Prodct tag data retention\n", "    - Schema:\n", "```python\n", "[\n", "    {\n", "        \"name\": \"id\",\n", "        \"type\": \"STRING\",\n", "        \"mode\": \"REQUIRED\",\n", "        \"description\": \"Product id\"\n", "    },\n", "    {\n", "        \"name\": \"name\",\n", "        \"type\": \"STRING\",\n", "        \"mode\": \"REQUIRED\",\n", "        \"description\": \"Product name\"\n", "    },\n", "    {\n", "        \"name\": \"tag\",\n", "        \"type\": \"STRING\",\n", "        \"mode\": \"REPEATED\",\n", "        \"description\": \"Product category create by customized rules\"\n", "    }\n", "]\n", "```\n", "\n", "### 2. daily-product-tag-path:\n", "    - Note: Require to create an empty json file if first time executing\n", "    - Usage: For other pipeline to load daily product data\n", "    - Schema:\n", "```python\n", "[\n", "    {\n", "        'latest': '',\n", "        'some-date': '',\n", "    }\n", "]\n", "```\n", "### 3. segment-id-dictonary-path:\n", "    - Note: Require to create an empty json file if first time executing\n", "    - Usage: To specify certain *segment_id*\n", "    - Schema:\n", "```python\n", "{\n", "    'event': 'tm:c_1626_r_001',\n", "    'purchase': 'tm:c_1626_r_002',\n", "    'view_item': '',\n", "    'add_to_wishlist': '',\n", "    'add_payment_info': '',\n", "    'add_to_cart': '',\n", "    'view_item_list': '',\n", "    'promotion': '',\n", "    'checkout': '',\n", "    'ADC': '',\n", "    'FBC': '',\n", "    'AD_CVR': '',\n", "    'CVR': '',\n", "    'hot': '',\n", "    'topic': '',\n", "    'potential': '',\n", "    'cold': '',\n", "    'best': ''\n", "}\n", "```"]}, {"cell_type": "code", "execution_count": 2, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 3, "status": "ok", "timestamp": 1720768994161, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "bDl--sJO0wTC", "outputId": "197f1c37-65a0-45db-af83-20e8cc8c336b"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["/content/fredrick-disk\n"]}], "source": ["cd fredrick-disk/"]}, {"cell_type": "code", "execution_count": 3, "metadata": {"executionInfo": {"elapsed": 241, "status": "ok", "timestamp": 1720768994708, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "F6PcQ-rN_4-k"}, "outputs": [], "source": ["import pandas as pd\n", "from typing import List, Dict, Tuple\n", "import json\n", "import google_storage\n", "from datetime import datetime, timedelta\n", "from pytz import timezone\n", "from google.cloud import bigquery"]}, {"cell_type": "code", "execution_count": 4, "metadata": {"executionInfo": {"elapsed": 190, "status": "ok", "timestamp": 1720768995832, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "H7V-r11q_4-l"}, "outputs": [], "source": ["GSIO = google_storage.GoogleStorageIO()"]}, {"cell_type": "markdown", "metadata": {"id": "HLY95Up5ANrd"}, "source": ["### 2024-03-12\n", "A組\n", "- 1626 W<PERSON>\n", "- 3218 <PERSON><PERSON>r\n", "- 2917 onemore\n", "- 1345 金石堂\n", "  \n", "B組\n", "- 2808 Mars\n", "- 1474 林果良品\n", "- 107 Orbis_星傳\n", "- 1825 樂扣樂扣"]}, {"cell_type": "code", "execution_count": 29, "metadata": {"executionInfo": {"elapsed": 2, "status": "ok", "timestamp": 1720769506101, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "xe81tXsd_4-l"}, "outputs": [], "source": ["# init parameters\n", "ecid = 3511\n", "current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei'))\n", "current_date = current_date.strftime(\"%Y-%m-%d\")"]}, {"cell_type": "markdown", "metadata": {"id": "GQ4rrWtG_4-l"}, "source": ["## Create segment-id-dictonary (JSON)\n", "- Create dict()\n", "- Upload to GCS"]}, {"cell_type": "markdown", "metadata": {"id": "ZyB_mmUQ_4-m"}, "source": ["- tm:c_107_r_001: 熱門瀏覽商品\n", "- tm:c_107_r_002: 熱門加入購物車商品\n", "- tm:c_107_r_003: 熱門購買商品\n", "- tm:c_107_r_004: 高轉換率商品\n", "- tm:c_107_r_005: 高廣告轉換率商品"]}, {"cell_type": "code", "execution_count": 30, "metadata": {"executionInfo": {"elapsed": 57, "status": "ok", "timestamp": 1720769507374, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "_6IysozD_4-m"}, "outputs": [], "source": ["# * Fill in segment_id to correspond event name\n", "\n", "hot_product_segment_id = f\"tm:c_9999_{ecid}_r_001\"\n", "topic_product_segment_id = f\"tm:c_9999_{ecid}_r_002\"\n", "potential_product_segment_id = f\"tm:c_9999_{ecid}_r_003\"\n", "cold_product_segment_id = None\n", "thebest_product_segment_id = f\"tm:c_9999_{ecid}_r_004\"\n", "topk_event_segment_id = f\"tm:c_9999_{ecid}_r_005\"\n", "topk_purchase_segment_id = f\"tm:c_9999_{ecid}_r_006\"\n", "topk_view_item_segment_id = None\n", "topk_add_to_wishlist_segment_id = None\n", "topk_add_payment_info_segment_id = None\n", "topk_add_to_cart_segment_id = f\"tm:c_9999_{ecid}_r_007\"\n", "topk_view_item_list_segment_id = None\n", "topk_view_promotion_segment_id = None\n", "topk_checkout_segment_id = None\n", "topk_ad_click_segment_id = f\"tm:c_9999_{ecid}_r_008\"\n", "topk_fb_click_segment_id = f\"tm:c_9999_{ecid}_r_009\"\n", "topk_conversion_rate_segment_id = f\"tm:c_9999_{ecid}_r_010\"\n", "topk_ad_click_conversion_segment_id = f\"tm:c_9999_{ecid}_r_011\"\n", "\n", "top_event_name_list = [\n", "    'event',\n", "    'purchase',\n", "    'view_item',\n", "    'add_to_wishlist',\n", "    'add_payment_info',\n", "    'add_to_cart',\n", "    'view_item_list',\n", "    'promotion',\n", "    'checkout',\n", "    'ADC',\n", "    'FBC',\n", "    'AD_CVR',\n", "    'CVR',\n", "    'hot',\n", "    'topic',\n", "    'potential',\n", "    'cold',\n", "    'best',\n", "]\n", "\n", "# put these segment_id into a list\n", "topk_segment_id_list = [\n", "    topk_event_segment_id,\n", "    topk_purchase_segment_id,\n", "    topk_view_item_segment_id,\n", "    topk_add_to_wishlist_segment_id,\n", "    topk_add_payment_info_segment_id,\n", "    topk_add_to_cart_segment_id,\n", "    topk_view_item_list_segment_id,\n", "    topk_view_promotion_segment_id,\n", "    topk_checkout_segment_id,\n", "    topk_ad_click_segment_id,\n", "    topk_fb_click_segment_id,\n", "    topk_ad_click_conversion_segment_id,\n", "    topk_conversion_rate_segment_id,\n", "    hot_product_segment_id,\n", "    topic_product_segment_id,\n", "    potential_product_segment_id,\n", "    cold_product_segment_id,\n", "    thebest_product_segment_id,\n", "]\n", "\n", "def return_segment_id(x: str) -> str:\n", "    if x:\n", "        return x\n", "    else:\n", "        return \"\"\n", "\n", "# make segment_id dictionary\n", "#\n", "# dict = {\n", "#     tag: segment_id\n", "# }\n", "\n", "segment_id_dict = {}\n", "for event, segment_id in zip(top_event_name_list, topk_segment_id_list):\n", "    segment_id_dict[event] = return_segment_id(segment_id)"]}, {"cell_type": "code", "execution_count": 31, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 94, "status": "ok", "timestamp": 1720769508741, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "slKgUSpwP-Nu", "outputId": "2e2f7011-a17f-48c4-e90f-224dc3cceae6"}, "outputs": [{"data": {"text/plain": ["{'event': 'tm:c_9999_3511_r_005',\n", " 'purchase': 'tm:c_9999_3511_r_006',\n", " 'view_item': '',\n", " 'add_to_wishlist': '',\n", " 'add_payment_info': '',\n", " 'add_to_cart': 'tm:c_9999_3511_r_007',\n", " 'view_item_list': '',\n", " 'promotion': '',\n", " 'checkout': '',\n", " 'ADC': 'tm:c_9999_3511_r_008',\n", " 'FBC': 'tm:c_9999_3511_r_009',\n", " 'AD_CVR': 'tm:c_9999_3511_r_011',\n", " 'CVR': 'tm:c_9999_3511_r_010',\n", " 'hot': 'tm:c_9999_3511_r_001',\n", " 'topic': 'tm:c_9999_3511_r_002',\n", " 'potential': 'tm:c_9999_3511_r_003',\n", " 'cold': '',\n", " 'best': 'tm:c_9999_3511_r_004'}"]}, "execution_count": 31, "metadata": {}, "output_type": "execute_result"}], "source": ["segment_id_dict"]}, {"cell_type": "code", "execution_count": 32, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 145, "status": "ok", "timestamp": 1720769509984, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "fD-1eMY2_4-n", "outputId": "4df4bca6-76c3-4980-8fd8-7a1ebf7e0a23"}, "outputs": [{"data": {"text/plain": ["{'event': 'tm:c_9999_3511_r_005',\n", " 'purchase': 'tm:c_9999_3511_r_006',\n", " 'view_item': '',\n", " 'add_to_wishlist': '',\n", " 'add_payment_info': '',\n", " 'add_to_cart': 'tm:c_9999_3511_r_007',\n", " 'view_item_list': '',\n", " 'promotion': '',\n", " 'checkout': '',\n", " 'ADC': 'tm:c_9999_3511_r_008',\n", " 'FBC': 'tm:c_9999_3511_r_009',\n", " 'AD_CVR': 'tm:c_9999_3511_r_011',\n", " 'CVR': 'tm:c_9999_3511_r_010',\n", " 'hot': 'tm:c_9999_3511_r_001',\n", " 'topic': 'tm:c_9999_3511_r_002',\n", " 'potential': 'tm:c_9999_3511_r_003',\n", " 'cold': '',\n", " 'best': 'tm:c_9999_3511_r_004'}"]}, "execution_count": 32, "metadata": {}, "output_type": "execute_result"}], "source": ["# save as JSON\n", "with open(f\"/tmp/event_segment_id_{ecid}.json\", mode='w') as f:\n", "    json.dump(segment_id_dict, f)\n", "\n", "with open(f\"/tmp/event_segment_id_{ecid}.json\", mode='r') as f:\n", "    t = json.load(f)\n", "\n", "t"]}, {"cell_type": "code", "execution_count": 33, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 254, "status": "ok", "timestamp": 1720769516728, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "tOqp5cGf_4-n", "outputId": "31c86315-aff0-43f8-e131-214e644070e0"}, "outputs": [{"data": {"text/plain": ["<Blob: tagtoo-bigquery-export, Fredrick_Test/product_tag/event_segment_id_dictionary/event_segment_id_3511.json, 1720769516543312>"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["# upload to GCS\n", "path = f\"/tmp/event_segment_id_{ecid}.json\"\n", "gcs_path = f\"gs://tagtoo-bigquery-export/Fredrick_Test/product_tag/event_segment_id_dictionary/event_segment_id_{ecid}.json\"\n", "GSIO.upload_file(gsuri=gcs_path, localpath=path)"]}, {"cell_type": "markdown", "metadata": {"id": "fWKAM4OZ_4-o"}, "source": ["## Create daily-product-tag-dictionary (JSON)"]}, {"cell_type": "code", "execution_count": 34, "metadata": {"executionInfo": {"elapsed": 111, "status": "ok", "timestamp": 1720769517786, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "Ycbq4RNx_4-o"}, "outputs": [], "source": ["def read_json(path: str) -> List[Dict[str, str]]:\n", "    with open(path, mode='r') as f:\n", "        return json.load(f)\n", "\n", "def write_to_json(path: str, data: List[Dict[str, str]]) -> None:\n", "    with open(path, mode='w') as f:\n", "        json.dump(data, f)"]}, {"cell_type": "code", "execution_count": 35, "metadata": {"executionInfo": {"elapsed": 47, "status": "ok", "timestamp": 1720769518157, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "TPpXOy7d_4-o"}, "outputs": [], "source": ["# if first time, create daily dictionary\n", "data = [\n", "    {\n", "        'latest': '',\n", "        current_date: '',\n", "    }\n", "]"]}, {"cell_type": "code", "execution_count": 36, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 2, "status": "ok", "timestamp": 1720769518445, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "uZWg8KKz_4-o", "outputId": "e4330b55-07b0-4056-c0c5-a555ffd84449"}, "outputs": [{"data": {"text/plain": ["[{'latest': '', '2024-07-12': ''}]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["data"]}, {"cell_type": "code", "execution_count": 37, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 0, "status": "ok", "timestamp": 1720769518628, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "ryQWOeYE_4-p", "outputId": "1f96631f-106b-4230-e2fc-90ed5b2ccb37", "scrolled": true}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["[{'latest': '', '2024-07-12': ''}]\n", "path: /tmp/3511_daily_product_tag_path.json\n"]}], "source": ["path = f'/tmp/{ecid}_daily_product_tag_path.json'\n", "write_to_json(path, data=data)\n", "\n", "print(read_json(path))\n", "print(\"path: {}\".format(path))"]}, {"cell_type": "code", "execution_count": 38, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 274, "status": "ok", "timestamp": 1720769519167, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "dkwqu-Sh_4-p", "outputId": "0e380c74-566a-496b-e304-629cc5993dbe"}, "outputs": [{"data": {"text/plain": ["<Blob: tagtoo-bigquery-export, Fredrick_Test/product_tag/daily_product_tag_path/3511_daily_product_tag_path.json, 1720769518955768>"]}, "execution_count": 38, "metadata": {}, "output_type": "execute_result"}], "source": ["gcs_path = f\"gs://tagtoo-bigquery-export/Fredrick_Test/product_tag/daily_product_tag_path/{ecid}_daily_product_tag_path.json\"\n", "GSIO.upload_file(gsuri=gcs_path, localpath=path)"]}, {"cell_type": "markdown", "metadata": {"id": "lrGmWriq_4-p"}, "source": ["## Create an Empty BigQuery Table\n", "- 2024-03-21 把 query 改到 cloud function 執行，之前是用 bigquery scheduler 排程"]}, {"cell_type": "code", "execution_count": 39, "metadata": {"executionInfo": {"elapsed": 49, "status": "ok", "timestamp": 1720769519648, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "4MvCFkCv_4-p"}, "outputs": [], "source": ["# table_id = f'tagtoo-ml-workflow.tagtoo_export_results.product_tag_{ecid}'"]}, {"cell_type": "code", "execution_count": 40, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 503, "status": "ok", "timestamp": 1720769520443, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "PG_6aax-_4-p", "outputId": "538cc002-9e0f-4b83-c4cc-8a7c8d5ecf04"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Created table tagtoo-ml-workflow.tagtoo_export_results.product_tag_3511\n"]}], "source": ["# # Construct a BigQuery client object.\n", "# client = bigquery.Client()\n", "\n", "# # TOD<PERSON>(developer): Set table_id to the ID of the table to create.\n", "# # table_id = \"your-project.your_dataset.your_table_name\"\n", "\n", "# schema=[\n", "#     bigquery.SchemaField(\"id\", \"STRING\", mode=\"REQUIRED\", description=\"Product id\"),\n", "#     bigquery.SchemaField(\"name\", \"STRING\", mode=\"REQUIRED\", description=\"Product name\"),\n", "#     bigquery.SchemaField(\"tag\", \"STRING\", mode=\"REPEATED\", description=\"Product category create by customized rules\"),\n", "# ]\n", "\n", "# table = bigquery.Table(table_id, schema=schema)\n", "# table = client.create_table(table)  # Make an API request.\n", "# print(\n", "#     \"Created table {}.{}.{}\".format(table.project, table.dataset_id, table.table_id)\n", "# )"]}, {"cell_type": "code", "execution_count": 41, "metadata": {"executionInfo": {"elapsed": 57, "status": "ok", "timestamp": 1720769693595, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "kqzkXfWA_4-p"}, "outputs": [], "source": ["def get_product_tag_query(ecid: int, past_day: int = 14) -> str:\n", "    return f\"\"\"\n", "  SELECT\n", "    items.id,\n", "    items.name,\n", "    COUNT(items.name) event_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"purchase\", items.name, NULL)) purchase_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"view_item\", items.name, NULL)) view_item_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"add_to_wishlist\", items.name, NULL)) add_to_wishlist_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"add_payment_info\", items.name, NULL)) add_payment_info_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"add_to_cart\", items.name, NULL)) add_to_cart_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"view_item_list\", items.name, NULL)) view_item_list,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"view_promotion\", items.name, NULL)) view_promotion_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"checkout\", items.name, NULL)) checkout_num,\n", "    COUNT(\n", "    IF\n", "      (session.source NOT LIKE \"%direct%\"\n", "        AND session.medium NOT LIKE \"%organic%\"\n", "        AND session.medium NOT LIKE \"%referral%\", items.name, NULL)) ad_click_num,\n", "    COUNT(\n", "    IF\n", "      (((session.source LIKE \"%fb%\"\n", "            OR session.source LIKE \"%FB%\"\n", "            OR session.source LIKE \"%Facebook%\"\n", "            OR session.source LIKE \"%facebook%\")\n", "          AND session.medium NOT LIKE \"%organic%\"\n", "          AND session.medium NOT LIKE \"%referral%\"), items.name, NULL)) fb_click_num\n", "  FROM\n", "    `tagtoo-tracking.event_prod.tagtoo_event`,\n", "    UNNEST(event.items) AS items\n", "  WHERE\n", "    DATE(event_time, \"Asia/Taipei\") >= DATE_SUB(CURRENT_DATE(\"Asia/Taipei\"), INTERVAL {past_day} DAY)\n", "    AND DATE(event_time, \"Asia/Taipei\") < CURRENT_DATE(\"Asia/Taipei\")\n", "    AND ec_id = {ecid}\n", "  GROUP BY\n", "    1,\n", "    2\n", "    \"\"\""]}, {"cell_type": "code", "execution_count": 43, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 57, "status": "ok", "timestamp": 1720770275196, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "DT5Ud2tw3cbH", "outputId": "5974f477-b3f8-48bf-be5f-1ba60b7c6482"}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\n", "  SELECT\n", "    items.id,\n", "    items.name,\n", "    COUNT(items.name) event_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"purchase\", items.name, NULL)) purchase_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"view_item\", items.name, NULL)) view_item_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"add_to_wishlist\", items.name, NULL)) add_to_wishlist_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"add_payment_info\", items.name, NULL)) add_payment_info_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"add_to_cart\", items.name, NULL)) add_to_cart_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"view_item_list\", items.name, NULL)) view_item_list,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"view_promotion\", items.name, NULL)) view_promotion_num,\n", "    COUNT(\n", "    IF\n", "      (event.name = \"checkout\", items.name, NULL)) checkout_num,\n", "    COUNT(\n", "    IF\n", "      (session.source NOT LIKE \"%direct%\"\n", "        AND session.medium NOT LIKE \"%organic%\"\n", "        AND session.medium NOT LIKE \"%referral%\", items.name, NULL)) ad_click_num,\n", "    COUNT(\n", "    IF\n", "      (((session.source LIKE \"%fb%\"\n", "            OR session.source LIKE \"%FB%\"\n", "            OR session.source LIKE \"%Facebook%\"\n", "            OR session.source LIKE \"%facebook%\")\n", "          AND session.medium NOT LIKE \"%organic%\"\n", "          AND session.medium NOT LIKE \"%referral%\"), items.name, NULL)) fb_click_num\n", "  FROM\n", "    `tagtoo-tracking.event_prod.tagtoo_event`,\n", "    UNNEST(event.items) AS items\n", "  WHERE\n", "    DATE(event_time, \"Asia/Taipei\") >= DATE_SUB(CURRENT_DATE(\"Asia/Taipei\"), INTERVAL 14 DAY)\n", "    AND DATE(event_time, \"Asia/Taipei\") < CURRENT_DATE(\"Asia/Taipei\")\n", "    AND ec_id = 1474\n", "  GROUP BY\n", "    1,\n", "    2 \n", "    \n"]}], "source": ["print(get_product_tag_query(1474))"]}, {"cell_type": "code", "execution_count": 52, "metadata": {"executionInfo": {"elapsed": 1897, "status": "ok", "timestamp": 1720770830197, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "mxDe9wGj3evP"}, "outputs": [], "source": ["from dask import dataframe as dd\n", "path = \"gs://tagtoo-ml-workflow/topic10/get_ec_product_tag_1345_2024-07-12/result_2024-07-12.json\"\n", "df = dd.read_json(path).compute()"]}, {"cell_type": "code", "execution_count": 53, "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "executionInfo": {"elapsed": 9, "status": "ok", "timestamp": 1720770830198, "user": {"displayName": "", "userId": ""}, "user_tz": -480}, "id": "_DDr2oPq6eCr", "outputId": "8ff7eedc-a3da-4e4f-f284-afd73ff21ed4"}, "outputs": [{"data": {"text/html": ["\n", "  <div id=\"df-31bb07e3-2c5b-4d60-9446-68087ed13c07\" class=\"colab-df-container\">\n", "    <div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>id</th>\n", "      <th>name</th>\n", "      <th>tag</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>kingstone:product:3030000088823</td>\n", "      <td>iKeeP QuizSlide智能單字機 LiveABC英檢初級必背單字＋贈Apacer文...</td>\n", "      <td>['promotion', 'cold']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>kingstone:product:4020000038251</td>\n", "      <td>滿意寶寶 極上呵護尿布賀禮 （極上の呵護輕巧褲Mx46片＋極上の呵護黏貼型Mx62片）</td>\n", "      <td>['promotion', 'topic']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>kingstone:product:2018612207395</td>\n", "      <td># 我要說出真相</td>\n", "      <td>['promotion', 'cold']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>kingstone:product:2018612207395</td>\n", "      <td># 我要說出真相 - 結城真一郎</td>\n", "      <td>['promotion', 'cold']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>kingstone:product:2019460287102</td>\n", "      <td>#BL遊戲(全)</td>\n", "      <td>['promotion', 'cold']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109795</th>\n", "      <td>kingstone:product:2018560971959</td>\n", "      <td>ｄｅｌｅ刪除 - 本多孝好</td>\n", "      <td>['cold']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109796</th>\n", "      <td>kingstone:product:2018611702952</td>\n", "      <td>ｄｅｌｅ刪除２ - 本多孝好</td>\n", "      <td>['cold']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109797</th>\n", "      <td>kingstone:product:2018612050304</td>\n", "      <td>ｉ【五度入選本屋大賞、直木賞得主 西加奈子 震撼人心之長篇傑作】</td>\n", "      <td>['cold']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109798</th>\n", "      <td>kingstone:product:2018052670582</td>\n", "      <td>ｉＢＴ、新多益口說：獨立＋整合題型，28天拿下高分（MP3） - 李育菱</td>\n", "      <td>['cold']</td>\n", "    </tr>\n", "    <tr>\n", "      <th>109799</th>\n", "      <td>kingstone:product:3030000087993</td>\n", "      <td>𝐇𝐨𝐨𝐤嚴選 【咖吉雅GAGGIA MAGENTA PLUS 爵韻型全自動義式咖啡機 】</td>\n", "      <td>['promotion', 'cold']</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>109800 rows × 3 columns</p>\n", "</div>\n", "    <div class=\"colab-df-buttons\">\n", "\n", "  <div class=\"colab-df-container\">\n", "    <button class=\"colab-df-convert\" onclick=\"convertToInteractive('df-31bb07e3-2c5b-4d60-9446-68087ed13c07')\"\n", "            title=\"Convert this dataframe to an interactive table.\"\n", "            style=\"display:none;\">\n", "\n", "  <svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\" viewBox=\"0 -960 960 960\">\n", "    <path d=\"M120-120v-720h720v720H120Zm60-500h600v-160H180v160Zm220 220h160v-160H400v160Zm0 220h160v-160H400v160ZM180-400h160v-160H180v160Zm440 0h160v-160H620v160ZM180-180h160v-160H180v160Zm440 0h160v-160H620v160Z\"/>\n", "  </svg>\n", "    </button>\n", "\n", "  <style>\n", "    .colab-df-container {\n", "      display:flex;\n", "      gap: 12px;\n", "    }\n", "\n", "    .colab-df-convert {\n", "      background-color: #E8F0FE;\n", "      border: none;\n", "      border-radius: 50%;\n", "      cursor: pointer;\n", "      display: none;\n", "      fill: #1967D2;\n", "      height: 32px;\n", "      padding: 0 0 0 0;\n", "      width: 32px;\n", "    }\n", "\n", "    .colab-df-convert:hover {\n", "      background-color: #E2EBFA;\n", "      box-shadow: 0px 1px 2px rgba(60, 64, 67, 0.3), 0px 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "      fill: #174EA6;\n", "    }\n", "\n", "    .colab-df-buttons div {\n", "      margin-bottom: 4px;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert {\n", "      background-color: #3B4455;\n", "      fill: #D2E3FC;\n", "    }\n", "\n", "    [theme=dark] .colab-df-convert:hover {\n", "      background-color: #434B5C;\n", "      box-shadow: 0px 1px 3px 1px rgba(0, 0, 0, 0.15);\n", "      filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.3));\n", "      fill: #FFFFFF;\n", "    }\n", "  </style>\n", "\n", "    <script>\n", "      const buttonEl =\n", "        document.querySelector('#df-31bb07e3-2c5b-4d60-9446-68087ed13c07 button.colab-df-convert');\n", "      buttonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "\n", "      async function convertToInteractive(key) {\n", "        const element = document.querySelector('#df-31bb07e3-2c5b-4d60-9446-68087ed13c07');\n", "        const dataTable =\n", "          await google.colab.kernel.invokeFunction('convertToInteractive',\n", "                                                    [key], {});\n", "        if (!dataTable) return;\n", "\n", "        const docLinkHtml = 'Like what you see? Visit the ' +\n", "          '<a target=\"_blank\" href=https://colab.research.google.com/notebooks/data_table.ipynb>data table notebook</a>'\n", "          + ' to learn more about interactive tables.';\n", "        element.innerHTML = '';\n", "        dataTable['output_type'] = 'display_data';\n", "        await google.colab.output.renderOutput(dataTable, element);\n", "        const docLink = document.createElement('div');\n", "        docLink.innerHTML = docLinkHtml;\n", "        element.appendChild(docLink);\n", "      }\n", "    </script>\n", "  </div>\n", "\n", "\n", "<div id=\"df-0193bd9c-74f0-460d-b914-2295f267efa8\">\n", "  <button class=\"colab-df-quickchart\" onclick=\"quickchart('df-0193bd9c-74f0-460d-b914-2295f267efa8')\"\n", "            title=\"Suggest charts\"\n", "            style=\"display:none;\">\n", "\n", "<svg xmlns=\"http://www.w3.org/2000/svg\" height=\"24px\"viewBox=\"0 0 24 24\"\n", "     width=\"24px\">\n", "    <g>\n", "        <path d=\"M19 3H5c-1.1 0-2 .9-2 2v14c0 1.1.9 2 2 2h14c1.1 0 2-.9 2-2V5c0-1.1-.9-2-2-2zM9 17H7v-7h2v7zm4 0h-2V7h2v10zm4 0h-2v-4h2v4z\"/>\n", "    </g>\n", "</svg>\n", "  </button>\n", "\n", "<style>\n", "  .colab-df-quickchart {\n", "      --bg-color: #E8F0FE;\n", "      --fill-color: #1967D2;\n", "      --hover-bg-color: #E2EBFA;\n", "      --hover-fill-color: #174EA6;\n", "      --disabled-fill-color: #AAA;\n", "      --disabled-bg-color: #DDD;\n", "  }\n", "\n", "  [theme=dark] .colab-df-quickchart {\n", "      --bg-color: #3B4455;\n", "      --fill-color: #D2E3FC;\n", "      --hover-bg-color: #434B5C;\n", "      --hover-fill-color: #FFFFFF;\n", "      --disabled-bg-color: #3B4455;\n", "      --disabled-fill-color: #666;\n", "  }\n", "\n", "  .colab-df-quickchart {\n", "    background-color: var(--bg-color);\n", "    border: none;\n", "    border-radius: 50%;\n", "    cursor: pointer;\n", "    display: none;\n", "    fill: var(--fill-color);\n", "    height: 32px;\n", "    padding: 0;\n", "    width: 32px;\n", "  }\n", "\n", "  .colab-df-quickchart:hover {\n", "    background-color: var(--hover-bg-color);\n", "    box-shadow: 0 1px 2px rgba(60, 64, 67, 0.3), 0 1px 3px 1px rgba(60, 64, 67, 0.15);\n", "    fill: var(--button-hover-fill-color);\n", "  }\n", "\n", "  .colab-df-quickchart-complete:disabled,\n", "  .colab-df-quickchart-complete:disabled:hover {\n", "    background-color: var(--disabled-bg-color);\n", "    fill: var(--disabled-fill-color);\n", "    box-shadow: none;\n", "  }\n", "\n", "  .colab-df-spinner {\n", "    border: 2px solid var(--fill-color);\n", "    border-color: transparent;\n", "    border-bottom-color: var(--fill-color);\n", "    animation:\n", "      spin 1s steps(1) infinite;\n", "  }\n", "\n", "  @keyframes spin {\n", "    0% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "      border-left-color: var(--fill-color);\n", "    }\n", "    20% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    30% {\n", "      border-color: transparent;\n", "      border-left-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    40% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-top-color: var(--fill-color);\n", "    }\n", "    60% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "    }\n", "    80% {\n", "      border-color: transparent;\n", "      border-right-color: var(--fill-color);\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "    90% {\n", "      border-color: transparent;\n", "      border-bottom-color: var(--fill-color);\n", "    }\n", "  }\n", "</style>\n", "\n", "  <script>\n", "    async function quickchart(key) {\n", "      const quickchartButtonEl =\n", "        document.querySelector('#' + key + ' button');\n", "      quickchartButtonEl.disabled = true;  // To prevent multiple clicks.\n", "      quickchartButtonEl.classList.add('colab-df-spinner');\n", "      try {\n", "        const charts = await google.colab.kernel.invokeFunction(\n", "            'suggest<PERSON><PERSON>s', [key], {});\n", "      } catch (error) {\n", "        console.error('Error during call to suggest<PERSON>harts:', error);\n", "      }\n", "      quickchartButtonEl.classList.remove('colab-df-spinner');\n", "      quickchartButtonEl.classList.add('colab-df-quickchart-complete');\n", "    }\n", "    (() => {\n", "      let quickchartButtonEl =\n", "        document.querySelector('#df-0193bd9c-74f0-460d-b914-2295f267efa8 button');\n", "      quickchartButtonEl.style.display =\n", "        google.colab.kernel.accessAllowed ? 'block' : 'none';\n", "    })();\n", "  </script>\n", "</div>\n", "    </div>\n", "  </div>\n"], "text/plain": ["                                     id  \\\n", "0       kingstone:product:3030000088823   \n", "1       kingstone:product:4020000038251   \n", "2       kingstone:product:2018612207395   \n", "3       kingstone:product:2018612207395   \n", "4       kingstone:product:2019460287102   \n", "...                                 ...   \n", "109795  kingstone:product:2018560971959   \n", "109796  kingstone:product:2018611702952   \n", "109797  kingstone:product:2018612050304   \n", "109798  kingstone:product:2018052670582   \n", "109799  kingstone:product:3030000087993   \n", "\n", "                                                     name  \\\n", "0        iKeeP QuizSlide智能單字機 LiveABC英檢初級必背單字＋贈Apacer文...   \n", "1            滿意寶寶 極上呵護尿布賀禮 （極上の呵護輕巧褲Mx46片＋極上の呵護黏貼型Mx62片）    \n", "2                                                # 我要說出真相   \n", "3                                        # 我要說出真相 - 結城真一郎   \n", "4                                                #BL遊戲(全)   \n", "...                                                   ...   \n", "109795                                      ｄｅｌｅ刪除 - 本多孝好   \n", "109796                                     ｄｅｌｅ刪除２ - 本多孝好   \n", "109797                   ｉ【五度入選本屋大賞、直木賞得主 西加奈子 震撼人心之長篇傑作】   \n", "109798               ｉＢＴ、新多益口說：獨立＋整合題型，28天拿下高分（MP3） - 李育菱   \n", "109799       𝐇𝐨𝐨𝐤嚴選 【咖吉雅GAGGIA MAGENTA PLUS 爵韻型全自動義式咖啡機 】   \n", "\n", "                           tag  \n", "0        ['promotion', 'cold']  \n", "1       ['promotion', 'topic']  \n", "2        ['promotion', 'cold']  \n", "3        ['promotion', 'cold']  \n", "4        ['promotion', 'cold']  \n", "...                        ...  \n", "109795                ['cold']  \n", "109796                ['cold']  \n", "109797                ['cold']  \n", "109798                ['cold']  \n", "109799   ['promotion', 'cold']  \n", "\n", "[109800 rows x 3 columns]"]}, "execution_count": 53, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": null, "metadata": {"id": "lqdqE6vN6eyt"}, "outputs": [], "source": []}], "metadata": {"colab": {"name": "EC_Product_Tag_create_files.ipynb", "provenance": []}, "kernelspec": {"display_name": "Python 3.10.11 64-bit", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.11"}, "vscode": {"interpreter": {"hash": "bd385fe162c5ca0c84973b7dd5c518456272446b2b64e67c2a69f949ca7a1754"}}}, "nbformat": 4, "nbformat_minor": 0}