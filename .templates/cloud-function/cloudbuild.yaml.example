# Cloud Build configuration for {{FUNCTION_NAME}}
# 此檔案定義了 Cloud Function 的構建和部署流程

steps:
  # Step 1: Install dependencies
  - name: 'python:3.11-slim'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd {{FUNCTION_NAME}}
        pip install -r requirements.txt --target ./lib
    id: 'install-dependencies'

  # Step 2: Run tests (if exists)
  - name: 'python:3.11-slim'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd {{FUNCTION_NAME}}
        if [ -f "test_main.py" ]; then
          python -m pytest test_main.py -v
        else
          echo "No tests found, skipping test step"
        fi
    id: 'run-tests'
    waitFor: ['install-dependencies']

  # Step 3: Deploy Cloud Function
  - name: 'gcr.io/google.com/cloudsdktool/cloud-sdk:latest'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        cd {{FUNCTION_NAME}}
        gcloud functions deploy {{FUNCTION_NAME}} \
          --source=. \
          --entry-point=main \
          --runtime=python311 \
          --trigger-http \
          --allow-unauthenticated \
          --memory={{MEMORY:-256MB}} \
          --timeout={{TIMEOUT:-60s}} \
          --region={{REGION:-asia-east1}} \
          --project={{PROJECT_ID}} \
          --set-env-vars="ENV={{ENV:-production}}"
    id: 'deploy-function'
    waitFor: ['run-tests']

# Cloud Build 選項
options:
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_STANDARD_2'

# 替換變數
substitutions:
  _FUNCTION_NAME: '{{FUNCTION_NAME}}'
  _MEMORY: '{{MEMORY:-256MB}}'
  _TIMEOUT: '{{TIMEOUT:-60s}}'
  _REGION: '{{REGION:-asia-east1}}'
  _ENV: '{{ENV:-production}}'

# 超時設定
timeout: '1200s'
