"""
{{FUNCTION_NAME}} Cloud Function

此檔案是 {{FUNCTION_NAME}} Cloud Function 的主要進入點。
請根據具體需求修改此檔案。
"""

import functions_framework
from flask import Request
import json
import logging
from typing import Any, Dict

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@functions_framework.http
def main(request: Request) -> tuple[str, int]:
    """
    HTTP Cloud Function 主要進入點

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (response_body, status_code)
    """
    try:
        # 解析請求
        if request.method == 'OPTIONS':
            # 處理 CORS 預檢請求
            return handle_cors()

        elif request.method == 'POST':
            # 處理 POST 請求
            return handle_post_request(request)

        elif request.method == 'GET':
            # 處理 GET 請求
            return handle_get_request(request)

        else:
            return json.dumps({
                'error': f'不支援的請求方法: {request.method}',
                'status': 'error'
            }), 405

    except Exception as e:
        logger.error(f"處理請求時發生錯誤: {str(e)}", exc_info=True)
        return json.dumps({
            'error': '內部伺服器錯誤',
            'status': 'error'
        }), 500


def handle_cors() -> tuple[str, int]:
    """處理 CORS 預檢請求"""
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '3600'
    }
    return '', 204


def handle_post_request(request: Request) -> tuple[str, int]:
    """
    處理 POST 請求

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (response_body, status_code)
    """
    try:
        # 解析 JSON 數據
        request_json = request.get_json(silent=True)
        if not request_json:
            return json.dumps({
                'error': '無效的 JSON 格式',
                'status': 'error'
            }), 400

        logger.info(f"收到 POST 請求: {request_json}")

        # TODO: 在此處添加您的業務邏輯
        result = process_data(request_json)

        response = {
            'status': 'success',
            'data': result,
            'message': '請求處理成功'
        }

        return json.dumps(response, ensure_ascii=False), 200

    except Exception as e:
        logger.error(f"處理 POST 請求失敗: {str(e)}")
        return json.dumps({
            'error': '處理請求失敗',
            'status': 'error'
        }), 500


def handle_get_request(request: Request) -> tuple[str, int]:
    """
    處理 GET 請求

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (response_body, status_code)
    """
    try:
        # 獲取查詢參數
        params = request.args.to_dict()
        logger.info(f"收到 GET 請求，參數: {params}")

        # TODO: 在此處添加您的業務邏輯
        result = get_data(params)

        response = {
            'status': 'success',
            'data': result,
            'message': '查詢成功'
        }

        return json.dumps(response, ensure_ascii=False), 200

    except Exception as e:
        logger.error(f"處理 GET 請求失敗: {str(e)}")
        return json.dumps({
            'error': '查詢失敗',
            'status': 'error'
        }), 500


def process_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    處理業務數據（POST 請求）

    Args:
        data: 輸入數據

    Returns:
        Dict: 處理結果
    """
    # TODO: 實現您的業務邏輯
    logger.info("處理數據中...")

    # 範例處理邏輯
    processed_data = {
        'original_data': data,
        'processed_at': '2024-01-01T00:00:00Z',
        'processing_function': '{{FUNCTION_NAME}}',
        'result': 'success'
    }

    return processed_data


def get_data(params: Dict[str, str]) -> Dict[str, Any]:
    """
    獲取數據（GET 請求）

    Args:
        params: 查詢參數

    Returns:
        Dict: 查詢結果
    """
    # TODO: 實現您的查詢邏輯
    logger.info("查詢數據中...")

    # 範例查詢邏輯
    result_data = {
        'query_params': params,
        'queried_at': '2024-01-01T00:00:00Z',
        'function_name': '{{FUNCTION_NAME}}',
        'data': []  # 在此處添加實際的查詢結果
    }

    return result_data


# 用於本地測試
if __name__ == '__main__':
    from flask import Flask
    app = Flask(__name__)
    app.add_url_rule('/', 'main', main, methods=['GET', 'POST', 'OPTIONS'])
    app.run(debug=True, host='0.0.0.0', port=8080)
