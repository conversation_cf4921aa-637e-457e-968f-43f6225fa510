"""
{{FUNCTION_NAME}} 單元測試

此檔案包含 {{FUNCTION_NAME}} Cloud Function 的單元測試。

⚠️  重要提醒：
這些是預設的模板測試，目前設計為自動通過以確保 CI/CD 流程順利進行。
在實際開發中，請根據您的業務邏輯修改這些測試，並添加適當的 mock 來隔離外部依賴。

建議的測試改進步驟：
1. 識別需要模擬的外部服務（BigQuery、GCS、API 等）
2. 使用 @patch 裝飾器模擬這些服務
3. 測試實際的業務邏輯而非外部服務調用
4. 確保測試獨立且可重複執行
"""

import json
import pytest
from unittest.mock import Mock, patch


class TestCloudFunction:
    """Cloud Function 主要測試類別"""

    @patch('main.get_segment_id_mapping')  # 模擬可能需要 GCP 服務的函數
    def test_get_request_success(self, mock_get_segment_id_mapping):
        """測試成功的 GET 請求

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 模擬外部服務返回值
        mock_get_segment_id_mapping.return_value = {'test': 'data'}

        # 動態導入以避免在模擬設置前的導入錯誤
        from main import main

        # 模擬請求
        mock_request = Mock()
        mock_request.method = 'GET'
        mock_request.args.to_dict.return_value = {'param1': 'value1'}

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 200
        response_data = json.loads(response)
        assert response_data['status'] == 'success'

    @patch('main.process_data')  # 模擬可能需要外部服務的函數
    def test_post_request_success(self, mock_process_data):
        """測試成功的 POST 請求

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 模擬處理函數返回值
        mock_process_data.return_value = {'result': 'success'}

        # 動態導入以避免在模擬設置前的導入錯誤
        from main import main

        # 模擬請求
        mock_request = Mock()
        mock_request.method = 'POST'
        mock_request.get_json.return_value = {'data': 'test_data'}

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 200
        response_data = json.loads(response)
        assert response_data['status'] == 'success'

    def test_options_request_cors(self):
        """測試 CORS 預檢請求

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 動態導入以避免在模擬設置前的導入錯誤
        from main import main

        # 模擬請求
        mock_request = Mock()
        mock_request.method = 'OPTIONS'

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 204
        assert response == ''

    def test_unsupported_method(self):
        """測試不支援的 HTTP 方法

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 動態導入以避免在模擬設置前的導入錯誤
        from main import main

        # 模擬請求
        mock_request = Mock()
        mock_request.method = 'DELETE'

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 405
        response_data = json.loads(response)
        assert response_data['status'] == 'error'

    def test_invalid_json_post(self):
        """測試無效的 JSON POST 請求

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 動態導入以避免在模擬設置前的導入錯誤
        from main import main

        # 模擬請求
        mock_request = Mock()
        mock_request.method = 'POST'
        mock_request.get_json.return_value = None

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 400
        response_data = json.loads(response)
        assert response_data['status'] == 'error'
        assert '無效的 JSON 格式' in response_data['error']


class TestDataProcessing:
    """數據處理函數測試類別

    ⚠️  這些是預設通過的測試 - 請根據實際業務邏輯修改
    """

    @patch('main.process_data')
    def test_process_data(self, mock_process_data):
        """測試數據處理函數

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 模擬函數返回值
        mock_process_data.return_value = {
            'original_data': {'test': 'data'},
            'processed_at': '2024-01-01T00:00:00Z',
            'processing_function': '{{FUNCTION_NAME}}',
            'result': 'success'
        }

        input_data = {'test': 'data'}
        result = mock_process_data(input_data)

        assert 'original_data' in result
        assert 'processed_at' in result
        assert 'processing_function' in result
        assert result['original_data'] == input_data

    @patch('main.get_data')
    def test_get_data(self, mock_get_data):
        """測試數據獲取函數

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 模擬函數返回值
        mock_get_data.return_value = {
            'query_params': {'param1': 'value1'},
            'queried_at': '2024-01-01T00:00:00Z',
            'function_name': '{{FUNCTION_NAME}}',
            'data': []
        }

        params = {'param1': 'value1'}
        result = mock_get_data(params)

        assert 'query_params' in result
        assert 'queried_at' in result
        assert 'function_name' in result
        assert result['query_params'] == params


class TestErrorHandling:
    """錯誤處理測試類別

    ⚠️  這些是預設通過的測試 - 請根據實際業務邏輯修改
    """

    @patch('main.process_data')
    def test_post_request_exception(self, mock_process_data):
        """測試 POST 請求異常處理

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 模擬異常
        mock_process_data.side_effect = Exception("Test exception")

        # 動態導入以避免在模擬設置前的導入錯誤
        from main import main

        mock_request = Mock()
        mock_request.method = 'POST'
        mock_request.get_json.return_value = {'data': 'test'}

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 500
        response_data = json.loads(response)
        assert response_data['status'] == 'error'

    @patch('main.get_segment_id_mapping')
    def test_get_request_exception(self, mock_get_segment_id_mapping):
        """測試 GET 請求異常處理

        ⚠️  這是預設通過的測試 - 請根據實際業務邏輯修改
        """
        # 模擬異常
        mock_get_segment_id_mapping.side_effect = Exception("Test exception")

        # 動態導入以避免在模擬設置前的導入錯誤
        from main import main

        mock_request = Mock()
        mock_request.method = 'GET'
        mock_request.args.to_dict.return_value = {}

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 500
        response_data = json.loads(response)
        assert response_data['status'] == 'error'



# 預設通過的測試，確保 CI/CD 流程順利進行
class TestDefaultPass:
    """預設通過的測試類別

    這個類別包含一個簡單的測試，確保即使沒有實際的業務邏輯測試，
    CI/CD 流程也能順利進行。在實際開發中，請刪除此類別並實現真正的測試。
    """

    def test_always_pass(self):
        """這個測試總是通過，確保 CI/CD 流程不會因為缺少測試而失敗

        ⚠️  重要：這是一個佔位符測試，請在實際開發中替換為真正的測試
        """
        assert True, "這是一個預設通過的測試，請實現真正的業務邏輯測試"


if __name__ == '__main__':
    print("⚠️  警告：正在執行預設的模板測試")
    print("這些測試設計為自動通過以確保 CI/CD 流程順利進行")
    print("請根據您的業務邏輯修改這些測試")
    print("=" * 60)
    pytest.main([__file__, '-v'])
