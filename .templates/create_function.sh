#!/bin/bash

# Cloud Function 快速建立腳本
# 此腳本可以快速建立基於模板的新 Cloud Function

set -e

# 顏色定義
RED='\033[31m'
GREEN='\033[32m'
YELLOW='\033[33m'
BLUE='\033[34m'
RESET='\033[0m'

# 腳本資訊
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
TEMPLATES_DIR="$SCRIPT_DIR"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# 函數：顯示使用說明
show_help() {
    echo -e "${BLUE}Cloud Function 快速建立工具${RESET}"
    echo
    echo "使用方法:"
    echo "  $0 <function-name> [options]"
    echo
    echo "選項:"
    echo "  -h, --help          顯示此幫助信息"
    echo "  -t, --terraform     包含 Terraform 配置"
    echo "  -d, --directory     指定建立目錄 (預設: ../function-name)"
    echo "  --memory           記憶體大小 (預設: 256MB)"
    echo "  --timeout          超時時間 (預設: 60s)"
    echo "  --region           部署區域 (預設: asia-east1)"
    echo
    echo "範例:"
    echo "  $0 user-stats-processor"
    echo "  $0 data-analyzer --terraform --memory 512MB"
    echo "  $0 email-sender --directory /path/to/custom/location"
}

# 函數：記錄信息
log_info() {
    echo -e "${BLUE}[INFO]${RESET} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${RESET} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${RESET} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${RESET} $1"
}

# 函數：檢查模板目錄
check_templates() {
    if [ ! -d "$TEMPLATES_DIR/cloud-function" ]; then
        log_error "找不到模板目錄: $TEMPLATES_DIR/cloud-function"
        exit 1
    fi

    if [ ! -f "$TEMPLATES_DIR/cloud-function/main.py" ]; then
        log_error "找不到主要模板檔案: main.py"
        exit 1
    fi

    log_info "模板目錄檢查通過"
}

# 函數：驗證 function 名稱
validate_function_name() {
    local name="$1"

    if [[ ! "$name" =~ ^[a-z][a-z0-9-]*[a-z0-9]$ ]]; then
        log_error "無效的函數名稱: $name"
        echo "函數名稱必須:"
        echo "  - 以小寫字母開頭"
        echo "  - 只包含小寫字母、數字和連字符"
        echo "  - 以字母或數字結尾"
        echo "  - 長度在 2-63 個字符之間"
        exit 1
    fi

    if [ ${#name} -lt 2 ] || [ ${#name} -gt 63 ]; then
        log_error "函數名稱長度必須在 2-63 個字符之間"
        exit 1
    fi
}

# 函數：建立目錄結構
create_directory() {
    local target_dir="$1"

    if [ -d "$target_dir" ]; then
        log_warning "目錄已存在: $target_dir"
        read -p "是否要繼續並覆蓋現有檔案? [y/N]: " -r
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            exit 0
        fi
    fi

    mkdir -p "$target_dir"
    log_success "建立目錄: $target_dir"
}

# 函數：複製模板檔案
copy_templates() {
    local target_dir="$1"
    local include_terraform="$2"

    log_info "複製 Cloud Function 模板..."
    cp -r "$TEMPLATES_DIR/cloud-function/"* "$target_dir/"

    log_info "複製共用檔案..."
    cp "$TEMPLATES_DIR/common/.gitignore" "$target_dir/"
    cp "$TEMPLATES_DIR/makefile/Makefile" "$target_dir/"

    if [ "$include_terraform" = "true" ]; then
        log_info "複製 Terraform 模板..."
        mkdir -p "$target_dir/terraform"
        cp -r "$TEMPLATES_DIR/terraform/"* "$target_dir/terraform/"
    fi

    log_success "模板複製完成"
}

# 函數：替換模板變數
replace_variables() {
    local target_dir="$1"
    local function_name="$2"
    local memory="$3"
    local timeout="$4"
    local region="$5"

    log_info "替換模板變數..."

    # 獲取需要處理的檔案
    local files
    files=$(find "$target_dir" -type f \( -name "*.py" -o -name "*.yaml" -o -name "*.yml" -o -name "Makefile" -o -name "*.tf" -o -name "*.md" \))

    # 替換變數
    while IFS= read -r file; do
        if [[ "$OSTYPE" == "darwin"* ]]; then
            # macOS
            sed -i '' "s/{{FUNCTION_NAME}}/$function_name/g" "$file"
            sed -i '' "s/{{MEMORY}}/$memory/g" "$file"
            sed -i '' "s/{{TIMEOUT}}/$timeout/g" "$file"
            sed -i '' "s/{{REGION}}/$region/g" "$file"
        else
            # Linux
            sed -i "s/{{FUNCTION_NAME}}/$function_name/g" "$file"
            sed -i "s/{{MEMORY}}/$memory/g" "$file"
            sed -i "s/{{TIMEOUT}}/$timeout/g" "$file"
            sed -i "s/{{REGION}}/$region/g" "$file"
        fi
    done <<< "$files"

    log_success "變數替換完成"
}

# 函數：建立 README
create_readme() {
    local target_dir="$1"
    local function_name="$2"

    cat > "$target_dir/README.md" << EOF
# $function_name

此 Cloud Function 使用標準模板建立。

## 功能描述

TODO: 描述此 Cloud Function 的功能和用途

## 快速開始

\`\`\`bash
# 設定開發環境
make setup

# 本地測試
make run

# 執行測試
make test

# 部署到開發環境
make deploy-dev

# 部署到生產環境
make deploy-prod
\`\`\`

## API 文檔

### GET /

查詢資料

**查詢參數:**
- \`param1\` (string): 描述參數1

**回應:**
\`\`\`json
{
  "status": "success",
  "data": {},
  "message": "查詢成功"
}
\`\`\`

### POST /

處理資料

**請求體:**
\`\`\`json
{
  "data": "your_data_here"
}
\`\`\`

**回應:**
\`\`\`json
{
  "status": "success",
  "data": {},
  "message": "處理成功"
}
\`\`\`

## 開發說明

### 專案結構

\`\`\`
$function_name/
├── main.py              # 主要程式碼
├── requirements.txt     # Python 依賴
├── test_main.py        # 單元測試
├── cloudbuild.yaml     # Cloud Build 配置
├── Makefile           # 構建腳本
└── README.md          # 本文件
\`\`\`

### 環境變數

- \`ENV\`: 環境 (production/development)
- \`PROJECT_ID\`: GCP 專案 ID
- \`REGION\`: 部署區域

### 部署

此 Cloud Function 會在推送到 main 分支時自動部署。

## 故障排除

### 常見問題

1. **部署失敗**: 檢查 Cloud Build 日誌
2. **權限錯誤**: 確認服務帳戶權限
3. **依賴問題**: 檢查 requirements.txt

### 獲取幫助

- 執行 \`make help\` 查看可用命令
- 查看專案根目錄的文檔
EOF

    log_success "建立 README.md"
}

# 函數：顯示完成信息
show_completion() {
    local target_dir="$1"
    local function_name="$2"

    echo
    log_success "Cloud Function '$function_name' 建立完成!"
    echo
    echo -e "${BLUE}下一步:${RESET}"
    echo "1. cd $target_dir"
    echo "2. 編輯 main.py 實現您的業務邏輯"
    echo "3. make setup  # 設定開發環境"
    echo "4. make run    # 本地測試"
    echo "5. make test   # 執行測試"
    echo
    echo -e "${BLUE}有用的命令:${RESET}"
    echo "- make help         # 查看所有可用命令"
    echo "- make deploy-dev   # 部署到開發環境"
    echo "- make deploy-prod  # 部署到生產環境"
    echo
    echo -e "${YELLOW}提示:${RESET} 查看 .templates/docs/usage-guide.md 獲取詳細使用說明"
}

# 主要邏輯
main() {
    # 解析參數
    local function_name=""
    local target_dir=""
    local include_terraform="false"
    local memory="256MB"
    local timeout="60s"
    local region="asia-east1"

    while [[ $# -gt 0 ]]; do
        case $1 in
            -h|--help)
                show_help
                exit 0
                ;;
            -t|--terraform)
                include_terraform="true"
                shift
                ;;
            -d|--directory)
                target_dir="$2"
                shift 2
                ;;
            --memory)
                memory="$2"
                shift 2
                ;;
            --timeout)
                timeout="$2"
                shift 2
                ;;
            --region)
                region="$2"
                shift 2
                ;;
            -*)
                log_error "未知選項: $1"
                show_help
                exit 1
                ;;
            *)
                if [ -z "$function_name" ]; then
                    function_name="$1"
                else
                    log_error "多餘的參數: $1"
                    show_help
                    exit 1
                fi
                shift
                ;;
        esac
    done

    # 檢查必要參數
    if [ -z "$function_name" ]; then
        log_error "請提供 Cloud Function 名稱"
        show_help
        exit 1
    fi

    # 設定預設目標目錄
    if [ -z "$target_dir" ]; then
        target_dir="$ROOT_DIR/$function_name"
    fi

    # 驗證輸入
    validate_function_name "$function_name"
    check_templates

    # 建立專案
    log_info "開始建立 Cloud Function: $function_name"
    log_info "目標目錄: $target_dir"

    create_directory "$target_dir"
    copy_templates "$target_dir" "$include_terraform"
    replace_variables "$target_dir" "$function_name" "$memory" "$timeout" "$region"
    create_readme "$target_dir" "$function_name"

    show_completion "$target_dir" "$function_name"
}

# 執行主要邏輯
main "$@"
