# Cloud Function 模板使用指南

此指南說明如何使用標準化的 Cloud Function 模板來建立新的專案。

## 快速開始

### 1. 建立新的 Cloud Function

```bash
# 建立新的 Cloud Function 目錄
mkdir my-new-function
cd my-new-function

# 複製基本模板
cp -r ../.templates/cloud-function/* .
cp ../.templates/common/.gitignore .
cp ../.templates/makefile/Makefile .

# 如果需要 Terraform 配置
mkdir terraform
cp -r ../.templates/terraform/* terraform/
```

### 2. 自訂配置

編輯以下檔案以符合您的需求：

#### 必須修改的檔案：

1. **main.py**: 實現您的業務邏輯
2. **cloudbuild.yaml**: 更新 `{{FUNCTION_NAME}}` 等變數
3. **requirements.txt**: 添加所需的依賴套件
4. **Makefile**: 更新 `FUNCTION_NAME` 變數

#### 可選修改的檔案：

1. **test_main.py**: 添加針對您業務邏輯的測試
2. **terraform/main.tf**: 調整基礎設施配置
3. **terraform/terraform.tfvars.example**: 複製為 `terraform.tfvars` 並自訂

### 3. 變數替換

使用以下命令快速替換模板中的變數：

```bash
# 設定您的 function 名稱
FUNCTION_NAME="my-new-function"

# 在 macOS 使用 sed
find . -type f -name "*.py" -o -name "*.yaml" -o -name "Makefile" -o -name "*.tf" | \
  xargs sed -i '' "s/{{FUNCTION_NAME}}/$FUNCTION_NAME/g"

# 在 Linux 使用 sed
find . -type f -name "*.py" -o -name "*.yaml" -o -name "Makefile" -o -name "*.tf" | \
  xargs sed -i "s/{{FUNCTION_NAME}}/$FUNCTION_NAME/g"
```

## 詳細說明

### 目錄結構

標準的 Cloud Function 專案應該包含以下檔案：

```
my-function/
├── main.py              # 主要程式碼
├── requirements.txt     # Python 依賴
├── test_main.py        # 單元測試
├── cloudbuild.yaml     # Cloud Build 配置
├── Makefile           # 構建和部署腳本
├── .gitignore         # Git 忽略規則
├── README.md          # 專案說明
└── terraform/         # 基礎設施配置 (可選)
    ├── main.tf
    └── terraform.tfvars.example
```

### 模板變數

模板中使用以下變數，需要根據實際情況替換：

| 變數 | 說明 | 範例 |
|------|------|------|
| `{{FUNCTION_NAME}}` | Cloud Function 名稱 | `user-stats-processor` |
| `{{MEMORY}}` | 記憶體大小 | `256MB`, `512MB`, `1GB` |
| `{{TIMEOUT}}` | 超時時間 | `60s`, `300s`, `540s` |
| `{{REGION}}` | 部署區域 | `asia-east1` |
| `{{PROJECT_ID}}` | GCP 專案 ID | `tagtoo-ml-workflow` |
| `{{ENV}}` | 環境 | `production`, `development` |

### 開發工作流程

1. **設定環境**:
   ```bash
   make setup
   ```

2. **開發代碼**:
   - 修改 `main.py`
   - 運行 `make run` 進行本地測試
   - 使用 `make test` 執行測試

3. **代碼品質檢查**:
   ```bash
   make lint          # 代碼品質檢查
   make format        # 代碼格式化
   make syntax-check  # 語法檢查
   ```

4. **部署**:
   ```bash
   make deploy-dev    # 部署到開發環境
   make deploy-prod   # 部署到生產環境
   ```

### 最佳實踐

#### 1. 命名規範

- Function 名稱：使用小寫字母和連字符，如 `user-stats-processor`
- 變數名稱：使用 snake_case，如 `user_data`
- 常數名稱：使用 UPPER_CASE，如 `MAX_RETRY_COUNT`

#### 2. 錯誤處理

- 總是實現適當的錯誤處理
- 使用結構化日誌記錄
- 返回有意義的錯誤信息和 HTTP 狀態碼

#### 3. 測試

- 為所有重要函數編寫單元測試
- 使用 mock 來隔離外部依賴
- 測試覆蓋率應該達到 80% 以上

#### 4. 安全性

- 不要在代碼中硬編碼敏感信息
- 使用環境變數存儲配置
- 驗證所有輸入數據

#### 5. 效能

- 合理設定記憶體和超時時間
- 避免冷啟動問題
- 使用連接池等技術優化性能

## 進階配置

### 使用 Terraform

如果您需要管理基礎設施，可以使用提供的 Terraform 模板：

```bash
cd terraform
cp terraform.tfvars.example terraform.tfvars
# 編輯 terraform.tfvars 以符合您的需求

terraform init
terraform plan
terraform apply
```

### 自訂 Cloud Build

您可以修改 `cloudbuild.yaml` 來添加自訂的構建步驟：

```yaml
steps:
  # 添加自訂步驟
  - name: 'python:3.11-slim'
    entrypoint: 'bash'
    args:
      - '-c'
      - |
        # 您的自訂命令
        echo "Running custom build step"
```

### CI/CD 整合

模板已經與專案的 GitHub Actions 工作流程整合。當您提交代碼到 main 分支時，會自動觸發部署。

## 故障排除

### 常見問題

1. **部署失敗**:
   - 檢查 `cloudbuild.yaml` 中的配置
   - 確認 GCP 權限設定
   - 查看 Cloud Build 日誌

2. **本地測試失敗**:
   - 確認 Python 版本（建議 3.11）
   - 檢查依賴是否正確安裝
   - 查看錯誤日誌

3. **權限問題**:
   - 確認服務帳戶有適當的權限
   - 檢查 IAM 設定

### 獲取幫助

- 查看 [.docs/git-hooks-guide.md](../../.docs/git-hooks-guide.md) 了解 Git hooks 配置
- 運行 `make help` 查看可用命令
- 檢查專案的 README.md 文件

## 更新模板

模板會定期更新以包含最佳實踐和新功能。要更新現有專案：

1. 比較新舊模板的差異
2. 手動合併有用的更新
3. 測試確保功能正常

建議定期檢查模板更新，保持專案的現代化。
