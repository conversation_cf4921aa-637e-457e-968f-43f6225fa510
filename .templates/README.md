# Templates Directory

此目錄包含標準化的專案模板，用於確保所有新的 Cloud Functions 都遵循一致的部署標準。

## 目錄結構

```
.templates/
├── cloud-function/     # Cloud Function 基本模板
├── makefile/          # Makefile 模板
├── terraform/         # Terraform 配置模板
├── common/           # 共用的模板檔案
├── docs/            # 模板使用文檔
└── README.md        # 本文件
```

## 使用方法

1. **新建 Cloud Function**: 複製 `cloud-function/` 下的模板檔案
2. **配置構建流程**: 使用 `makefile/` 中的標準 Makefile
3. **基礎設施管理**: 參考 `terraform/` 中的配置模板
4. **共用配置**: 從 `common/` 目錄獲取標準配置

## 模板特色

- **標準化**: 所有專案使用相同的目錄結構和命名規範
- **最佳實踐**: 整合了團隊的最佳實踐和經驗
- **易於維護**: 統一的模板便於統一更新和維護
- **CI/CD 整合**: 與現有的 GitHub Actions 工作流程完全整合

## 注意事項

- 此目錄被 CI/CD 流程自動忽略，不會觸發部署
- 模板檔案應保持最新，定期檢查和更新
- 新增模板時請更新相關文檔

## 相關工具

使用 `manage_git_hooks.sh` 來管理 Git hooks，確保代碼質量：

```bash
# 快速安裝 Git hooks
./manage_git_hooks.sh --quick

# 互動式菜單
./manage_git_hooks.sh --menu
```
