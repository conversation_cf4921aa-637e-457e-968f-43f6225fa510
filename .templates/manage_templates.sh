#!/bin/bash

# 模板管理工具
# 此腳本提供模板系統的管理功能

set -e

# 顏色定義
RED='\033[31m'
GREEN='\033[32m'
YELLOW='\033[33m'
BLUE='\033[34m'
CYAN='\033[36m'
RESET='\033[0m'

# 腳本資訊
SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
ROOT_DIR="$(dirname "$SCRIPT_DIR")"

# 函數：顯示主選單
show_main_menu() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${BLUE}║                    模板管理工具                              ║${RESET}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${RESET}"
    echo
    echo -e "${CYAN}請選擇一個選項:${RESET}"
    echo
    echo "  1) 建立新的 Cloud Function"
    echo "  2) 列出所有現有的 Cloud Functions"
    echo "  3) 檢查模板完整性"
    echo "  4) 更新現有專案模板"
    echo "  5) 顯示模板統計信息"
    echo "  6) 查看使用指南"
    echo "  7) 管理 Git Hooks"
    echo "  0) 退出"
    echo
    echo -n -e "${YELLOW}請輸入您的選擇 [0-7]: ${RESET}"
}

# 函數：記錄信息
log_info() {
    echo -e "${BLUE}[INFO]${RESET} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${RESET} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${RESET} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${RESET} $1"
}

# 函數：等待用戶按鍵
wait_for_key() {
    echo
    echo -n -e "${CYAN}按任意鍵繼續...${RESET}"
    read -n 1 -s
    echo
}

# 函數：建立新的 Cloud Function
create_new_function() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${BLUE}║                 建立新的 Cloud Function                      ║${RESET}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${RESET}"
    echo

    # 獲取 function 名稱
    echo -n -e "${YELLOW}請輸入 Cloud Function 名稱: ${RESET}"
    read -r function_name

    if [ -z "$function_name" ]; then
        log_error "Function 名稱不能為空"
        wait_for_key
        return
    fi

    # 檢查名稱格式
    if [[ ! "$function_name" =~ ^[a-z][a-z0-9-]*[a-z0-9]$ ]]; then
        log_error "無效的名稱格式"
        echo "名稱必須以小寫字母開頭，只包含小寫字母、數字和連字符"
        wait_for_key
        return
    fi

    # 檢查是否已存在
    if [ -d "$ROOT_DIR/$function_name" ]; then
        log_warning "目錄 '$function_name' 已存在"
        echo -n -e "${YELLOW}是否要覆蓋? [y/N]: ${RESET}"
        read -r confirm
        if [[ ! $confirm =~ ^[Yy]$ ]]; then
            log_info "操作已取消"
            wait_for_key
            return
        fi
    fi

    # 詢問是否包含 Terraform
    echo -n -e "${YELLOW}是否包含 Terraform 配置? [y/N]: ${RESET}"
    read -r include_terraform

    # 設定選項
    local terraform_flag=""
    if [[ $include_terraform =~ ^[Yy]$ ]]; then
        terraform_flag="--terraform"
    fi

    # 詢問自訂配置
    echo -n -e "${YELLOW}記憶體大小 (預設: 256MB): ${RESET}"
    read -r memory
    memory=${memory:-256MB}

    echo -n -e "${YELLOW}超時時間 (預設: 60s): ${RESET}"
    read -r timeout
    timeout=${timeout:-60s}

    echo -n -e "${YELLOW}部署區域 (預設: asia-east1): ${RESET}"
    read -r region
    region=${region:-asia-east1}

    echo
    log_info "建立 Cloud Function: $function_name"

    # 執行建立腳本
    if "$SCRIPT_DIR/create_function.sh" "$function_name" $terraform_flag --memory "$memory" --timeout "$timeout" --region "$region"; then
        echo
        log_success "Cloud Function '$function_name' 建立完成!"
        echo
        echo -e "${CYAN}建議的下一步:${RESET}"
        echo "1. cd $function_name"
        echo "2. 編輯 main.py 實現業務邏輯"
        echo "3. make setup && make run"
    else
        log_error "建立失敗"
    fi

    wait_for_key
}

# 函數：列出現有的 Cloud Functions
list_functions() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${BLUE}║                現有的 Cloud Functions                        ║${RESET}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${RESET}"
    echo

    log_info "掃描專案中的 Cloud Functions..."
    echo

    # 找出所有包含 cloudbuild.yaml 的目錄
    local functions_found=0

    while IFS= read -r -d '' dir; do
        local dir_name=$(basename "$dir")
        local dir_path=$(dirname "$dir")

        # 排除模板和特殊目錄
        if [[ "$dir_path" == *".templates"* ]] || [[ "$dir_path" == *".github"* ]]; then
            continue
        fi

        # 檢查是否有 main.py
        local has_main=""
        if [ -f "$dir_path/main.py" ]; then
            has_main="✓"
        else
            has_main="✗"
        fi

        # 檢查是否有測試
        local has_tests=""
        if [ -f "$dir_path/test_main.py" ] || [ -d "$dir_path/tests" ]; then
            has_tests="✓"
        else
            has_tests="✗"
        fi

        # 檢查是否有 Terraform
        local has_terraform=""
        if [ -d "$dir_path/terraform" ]; then
            has_terraform="✓"
        else
            has_terraform="✗"
        fi

        # 顯示信息
        printf "%-30s │ Main: %s │ Tests: %s │ Terraform: %s\n" \
               "$dir_name" "$has_main" "$has_tests" "$has_terraform"

        ((functions_found++))

    done < <(find "$ROOT_DIR" -maxdepth 2 -name "cloudbuild.yaml" -print0)

    echo
    if [ $functions_found -eq 0 ]; then
        log_warning "沒有找到 Cloud Functions"
    else
        log_success "找到 $functions_found 個 Cloud Functions"
    fi

    wait_for_key
}

# 函數：檢查模板完整性
check_template_integrity() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${BLUE}║                  檢查模板完整性                              ║${RESET}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${RESET}"
    echo

    log_info "檢查模板目錄結構..."

    local errors=0

    # 檢查主要目錄
    local required_dirs=("cloud-function" "common" "docs" "makefile" "terraform")
    for dir in "${required_dirs[@]}"; do
        if [ -d "$SCRIPT_DIR/$dir" ]; then
            echo -e "✓ 目錄: ${GREEN}$dir${RESET}"
        else
            echo -e "✗ 缺少目錄: ${RED}$dir${RESET}"
            ((errors++))
        fi
    done

    echo
    log_info "檢查必要檔案..."

    # 檢查必要檔案
    local required_files=(
        "cloud-function/main.py"
        "cloud-function/requirements.txt"
        "cloud-function/cloudbuild.yaml"
        "cloud-function/test_main.py"
        "common/.gitignore"
        "common/env.yaml"
        "makefile/Makefile"
        "terraform/main.tf"
        "docs/usage-guide.md"
        "create_function.sh"
        "README.md"
    )

    for file in "${required_files[@]}"; do
        if [ -f "$SCRIPT_DIR/$file" ]; then
            echo -e "✓ 檔案: ${GREEN}$file${RESET}"
        else
            echo -e "✗ 缺少檔案: ${RED}$file${RESET}"
            ((errors++))
        fi
    done

    echo
    log_info "檢查腳本權限..."

    if [ -x "$SCRIPT_DIR/create_function.sh" ]; then
        echo -e "✓ create_function.sh 可執行"
    else
        echo -e "✗ create_function.sh 不可執行"
        ((errors++))
    fi

    echo
    if [ $errors -eq 0 ]; then
        log_success "模板完整性檢查通過!"
    else
        log_error "發現 $errors 個問題"
    fi

    wait_for_key
}

# 函數：顯示模板統計信息
show_template_stats() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${BLUE}║                  模板統計信息                                ║${RESET}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${RESET}"
    echo

    log_info "收集模板統計信息..."
    echo

    # 檔案統計
    local total_files=$(find "$SCRIPT_DIR" -type f | wc -l | tr -d ' ')
    local python_files=$(find "$SCRIPT_DIR" -name "*.py" | wc -l | tr -d ' ')
    local yaml_files=$(find "$SCRIPT_DIR" -name "*.yaml" -o -name "*.yml" | wc -l | tr -d ' ')
    local tf_files=$(find "$SCRIPT_DIR" -name "*.tf" | wc -l | tr -d ' ')
    local md_files=$(find "$SCRIPT_DIR" -name "*.md" | wc -l | tr -d ' ')

    echo "檔案統計:"
    echo "  總檔案數:      $total_files"
    echo "  Python 檔案:   $python_files"
    echo "  YAML 檔案:     $yaml_files"
    echo "  Terraform 檔案: $tf_files"
    echo "  Markdown 檔案:  $md_files"
    echo

    # 程式碼行數統計
    local total_lines=$(find "$SCRIPT_DIR" -name "*.py" -o -name "*.yaml" -o -name "*.yml" -o -name "*.tf" -o -name "*.sh" | xargs wc -l 2>/dev/null | tail -1 | awk '{print $1}')

    echo "程式碼統計:"
    echo "  總行數:        $total_lines"
    echo

    # 目錄大小
    local template_size=$(du -sh "$SCRIPT_DIR" 2>/dev/null | cut -f1)

    echo "目錄信息:"
    echo "  模板大小:      $template_size"
    echo "  最後更新:      $(stat -f '%Sm' "$SCRIPT_DIR" 2>/dev/null || stat -c '%y' "$SCRIPT_DIR" 2>/dev/null || echo '未知')"
    echo

    # 模板使用情況
    log_info "分析專案中的模板使用情況..."

    local functions_with_makefile=0
    local functions_with_terraform=0
    local functions_with_tests=0

    while IFS= read -r -d '' dir; do
        local dir_path=$(dirname "$dir")

        # 排除模板目錄
        if [[ "$dir_path" == *".templates"* ]]; then
            continue
        fi

        if [ -f "$dir_path/Makefile" ]; then
            ((functions_with_makefile++))
        fi

        if [ -d "$dir_path/terraform" ]; then
            ((functions_with_terraform++))
        fi

        if [ -f "$dir_path/test_main.py" ] || [ -d "$dir_path/tests" ]; then
            ((functions_with_tests++))
        fi

    done < <(find "$ROOT_DIR" -maxdepth 2 -name "cloudbuild.yaml" -print0)

    echo "模板採用情況:"
    echo "  使用 Makefile:     $functions_with_makefile 個專案"
    echo "  使用 Terraform:    $functions_with_terraform 個專案"
    echo "  包含測試:          $functions_with_tests 個專案"

    wait_for_key
}

# 函數：查看使用指南
show_usage_guide() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${BLUE}║                    使用指南                                  ║${RESET}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${RESET}"
    echo

    if [ -f "$SCRIPT_DIR/docs/usage-guide.md" ]; then
        log_info "使用 less 查看完整使用指南 (按 q 退出)..."
        echo
        sleep 2
        less "$SCRIPT_DIR/docs/usage-guide.md"
    else
        log_error "找不到使用指南檔案"
        wait_for_key
    fi
}

# 函數：管理 Git Hooks
manage_git_hooks() {
    clear
    echo -e "${BLUE}╔══════════════════════════════════════════════════════════════╗${RESET}"
    echo -e "${BLUE}║                  Git Hooks 管理                             ║${RESET}"
    echo -e "${BLUE}╚══════════════════════════════════════════════════════════════╝${RESET}"
    echo

    if [ -f "$ROOT_DIR/manage_git_hooks.sh" ]; then
        log_info "啟動 Git Hooks 管理工具..."
        "$ROOT_DIR/manage_git_hooks.sh" --menu
    else
        log_error "找不到 Git Hooks 管理腳本"
        wait_for_key
    fi
}

# 主要邏輯
main() {
    while true; do
        show_main_menu
        read -r choice

        case $choice in
            1)
                create_new_function
                ;;
            2)
                list_functions
                ;;
            3)
                check_template_integrity
                ;;
            4)
                log_info "更新功能尚未實現"
                wait_for_key
                ;;
            5)
                show_template_stats
                ;;
            6)
                show_usage_guide
                ;;
            7)
                manage_git_hooks
                ;;
            0)
                echo
                log_success "感謝使用模板管理工具!"
                exit 0
                ;;
            *)
                echo
                log_error "無效選擇，請重新輸入"
                sleep 1
                ;;
        esac
    done
}

# 檢查是否在正確的目錄
if [ ! -f "$SCRIPT_DIR/README.md" ]; then
    log_error "請在 .templates 目錄中執行此腳本"
    exit 1
fi

# 執行主要邏輯
main "$@"
