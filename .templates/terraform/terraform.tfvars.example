# Terraform 變數定義檔案
# 在部署時可以通過 terraform.tfvars 檔案覆蓋這些預設值

# 必要變數
project_id = "tagtoo-ml-workflow"
region     = "asia-east1"

# Cloud Function 配置
function_name = "{{FUNCTION_NAME}}"
runtime       = "python311"
cpu           = "1"
memory        = 256
timeout       = 60
max_instances = 10

# 環境變數
environment_variables = {
  ENV         = "production"
  PROJECT_ID  = "tagtoo-ml-workflow"
  REGION      = "asia-east1"
  # 根據需要添加其他環境變數
  # LOG_LEVEL   = "INFO"
  # API_KEY     = "your-api-key"
}

# 網路配置 (可選)
# vpc_connector = "projects/tagtoo-ml-workflow/locations/asia-east1/connectors/your-vpc-connector"

# 標籤
# 注意：標籤會被自動添加，這裡只是展示可能的配置
# labels = {
#   environment = "production"
#   team        = "ml-team"
#   cost_center = "ml-workflow"
# }
