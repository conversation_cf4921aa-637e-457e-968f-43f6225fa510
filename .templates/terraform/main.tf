# Terraform configuration for {{FUNCTION_NAME}} Cloud Function
# 此檔案定義 Cloud Function 的基礎設施

terraform {
  required_version = ">= 1.5.0"

  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
  }

  # 建議：使用遠端狀態存儲
  # backend "gcs" {
  #   bucket = "your-terraform-state-bucket"
  #   prefix = "cloud-functions/{{FUNCTION_NAME}}"
  # }
}

# Google Cloud Provider 配置
provider "google" {
  project = var.project_id
  region  = var.region
}

# 變數定義
variable "project_id" {
  description = "GCP 專案 ID"
  type        = string
  default     = "tagtoo-ml-workflow"
}

variable "region" {
  description = "部署區域"
  type        = string
  default     = "asia-east1"
}

variable "function_name" {
  description = "Cloud Function 名稱"
  type        = string
  default     = "{{FUNCTION_NAME}}"
}

variable "runtime" {
  description = "Runtime 版本"
  type        = string
  default     = "python311"
}

# Ref: https://cloud.google.com/run/docs/configuring/services/memory-limits
variable "memory" {
  description = "記憶體大小 (MB)"
  type        = number
  default     = 256
}

# Ref: https://cloud.google.com/run/docs/configuring/request-timeout
variable "timeout" {
  description = "超時時間 (秒)"
  type        = number
  default     = 60
}

# Ref: https://cloud.google.com/run/docs/configuring/services/cpu#cpu-memory
# Note: The type is 'string' because the underlying Terraform provider expects a string.
# This provides flexibility for fractional CPUs (e.g., "0.5") and is future-proof.
variable "cpu" {
  description = "vCPU 數量"
  type        = string
  default     = "1"
}

variable "environment_variables" {
  description = "環境變數"
  type        = map(string)
  default = {
    ENV = "production"
  }
}

variable "max_instances" {
  description = "最大執行個體數量"
  type        = number
  default     = 10
}

variable "vpc_connector" {
  description = "VPC 連接器名稱 (可選)"
  type        = string
  default     = null
}

# 啟用必要的 GCP APIs
resource "google_project_service" "cloudfunctions_api" {
  service            = "cloudfunctions.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudrun_api" {
  service            = "run.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudbuild_api" {
  service            = "cloudbuild.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "storage_api" {
  service            = "storage.googleapis.com"
  disable_on_destroy = false
}

# 使用現有的共用存儲桶，在 source/ 目錄下存放 Cloud Functions 源代碼
data "google_storage_bucket" "shared_bucket" {
  name = "${var.project_id}-cloud-functions"
}

# 壓縮源代碼
data "archive_file" "source_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../"
  output_path = "${path.module}/source.zip"
  excludes    = [
    "terraform/",
    ".terraform/",
    "*.tf",
    "*.tfstate*",
    "source.zip",
    ".git/",
    "__pycache__/",
    "*.pyc",
    "venv/",
    ".venv/"
  ]
}

# 上傳源代碼到共用存儲桶的 source/ 目錄下
resource "google_storage_bucket_object" "source_archive" {
  name   = "source/${var.function_name}/${var.function_name}-${data.archive_file.source_zip.output_md5}.zip"
  bucket = data.google_storage_bucket.shared_bucket.name
  source = data.archive_file.source_zip.output_path
}

# 使用統一的服務帳戶
# 所有 Cloud Functions 使用相同的服務帳戶以簡化權限管理
locals {
  service_account_email = "<EMAIL>"
}

# 注意：<EMAIL> 已經存在
# 並且已經具備必要的權限，無需額外創建或配置 IAM 權限

# 注意：kubeflow-user 服務帳戶已經具備以下權限：
# - BigQuery 相關權限
# - Storage 相關權限
# - Cloud Functions 相關權限
# - Logging 權限
# 如需額外權限，請在 GCP Console 中為 <EMAIL> 添加

# Cloud Function 2nd Gen
resource "google_cloudfunctions2_function" "function" {
  name        = var.function_name
  description = "Cloud Function 2nd Gen for ${var.function_name}"
  location    = var.region

  build_config {
    runtime     = var.runtime
    entry_point = "main"

    source {
      storage_source {
        bucket = data.google_storage_bucket.shared_bucket.name
        object = google_storage_bucket_object.source_archive.name
      }
    }
  }

  service_config {
    max_instance_count    = var.max_instances
    available_memory      = "${var.memory}Mi"
    available_cpu         = var.cpu
    timeout_seconds       = var.timeout
    service_account_email = local.service_account_email

    environment_variables = var.environment_variables

    # VPC 連接器 (可選)
    vpc_connector = var.vpc_connector
  }

  labels = {
    environment = "production"
    function    = var.function_name
    managed_by  = "terraform"
  }
}

# IAM 策略 - 允許未經身份驗證的調用 (根據需要調整)
resource "google_cloud_run_service_iam_member" "invoker" {
  project  = google_cloudfunctions2_function.function.project
  location = google_cloudfunctions2_function.function.location
  service  = google_cloudfunctions2_function.function.name

  role   = "roles/run.invoker"
  member = "allUsers"
}

# 輸出
output "function_url" {
  description = "Cloud Function 2nd Gen 的 HTTP 觸發器 URL"
  value       = google_cloudfunctions2_function.function.service_config[0].uri
}

output "function_name" {
  description = "Cloud Function 2nd Gen 名稱"
  value       = google_cloudfunctions2_function.function.name
}

output "service_account_email" {
  description = "Cloud Function 使用的服務帳戶郵箱"
  value       = local.service_account_email
}

output "source_bucket" {
  description = "共用源代碼存儲桶名稱"
  value       = data.google_storage_bucket.shared_bucket.name
}
