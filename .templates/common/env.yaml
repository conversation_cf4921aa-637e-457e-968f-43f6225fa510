# Cloud Function 項目標準配置

## 通用環境變數
# 所有 Cloud Functions 都應該包含的標準環境變數

ENV=production
PROJECT_ID=tagtoo-ml-workflow
REGION=asia-east1
LOG_LEVEL=INFO
TIMEZONE=Asia/Taipei

## BigQuery 配置
# 如果 Cloud Function 需要訪問 BigQuery
BIGQUERY_DATASET=ml_workflow
BIGQUERY_LOCATION=asia-east1

## Cloud Storage 配置
# 如果 Cloud Function 需要訪問 Cloud Storage
STORAGE_BUCKET_PREFIX=tagtoo-ml-workflow
STORAGE_LOCATION=asia-east1

## Firestore 配置
# 如果 Cloud Function 需要訪問 Firestore
FIRESTORE_PROJECT_ID=tagtoo-ml-workflow
FIRESTORE_DATABASE_ID=(default)

## 日誌配置
LOG_FORMAT=json
LOG_TIMESTAMP_FORMAT=iso

## 錯誤處理配置
ERROR_NOTIFICATION_ENABLED=true
ERROR_THRESHOLD=5

## 效能配置
TIMEOUT_SECONDS=60
MEMORY_MB=256
MAX_INSTANCES=10

## 安全配置
CORS_ENABLED=true
CORS_ORIGINS=*
RATE_LIMIT_ENABLED=false

## 開發/測試配置
# 開發和測試環境可以覆蓋這些值
DEBUG_MODE=false
MOCK_EXTERNAL_SERVICES=false

## 監控配置
ENABLE_TRACE=true
ENABLE_METRICS=true
HEALTH_CHECK_ENABLED=true
