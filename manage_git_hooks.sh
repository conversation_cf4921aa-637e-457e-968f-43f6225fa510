#!/bin/bash

# =============================================================================
# Git Hooks 統一管理腳本
# 整合所有 Git hooks 相關功能於一個腳本中
# =============================================================================

VERSION="1.0.0"
SCRIPT_NAME="manage_git_hooks.sh"

# 顏色定義
RED='\# 檢查格式：type(scope): description 或 type: description
# 使用 case 語句來替代正則表達式，兼容 macOS bash
case "$commit_msg" in
    feat:*|feat\(*\):*|fix:*|fix\(*\):*|docs:*|docs\(*\):*|style:*|style\(*\):*|refactor:*|refactor\(*\):*|test:*|test\(*\):*|chore:*|chore\(*\):*|perf:*|perf\(*\):*|ci:*|ci\(*\):*|build:*|build\(*\):*|revert:*|revert\(*\):*)
        exit 0
        ;;
esac0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# 日誌函數
log_info() { echo -e "${BLUE}ℹ️  $1${NC}"; }
log_success() { echo -e "${GREEN}✅ $1${NC}"; }
log_warning() { echo -e "${YELLOW}⚠️  $1${NC}"; }
log_error() { echo -e "${RED}❌ $1${NC}"; }

# 顯示幫助信息
show_help() {
    echo "🚀 Git Hooks 統一管理腳本 v${VERSION}"
    echo "=================================================="
    echo ""
    echo "用法："
    echo "  $SCRIPT_NAME [選項]"
    echo ""
    echo "選項："
    echo "  -h, --help           顯示此幫助信息"
    echo "  -v, --version        顯示版本信息"
    echo "  -i, --install        安裝 Git hooks（靜默模式）"
    echo "  -q, --quick          新同事快速安裝（互動模式）"
    echo "  -m, --menu           顯示管理選單"
    echo "  -s, --status         檢查 hooks 狀態"
    echo "  -b, --backup         備份現有 hooks"
    echo "  --guide             查看使用指南"
    echo ""
    echo "範例："
    echo "  $SCRIPT_NAME              # 智能偵測模式"
    echo "  $SCRIPT_NAME -q           # 新同事入職"
    echo "  $SCRIPT_NAME -i           # CI/CD 靜默安裝"
    echo "  $SCRIPT_NAME -m           # 管理選單"
    echo ""
}

# 檢查環境
check_environment() {
    if [ ! -d ".git" ]; then
        log_error "請在 Git 倉庫根目錄執行此腳本"
        exit 1
    fi

    local git_root=$(git rev-parse --show-toplevel 2>/dev/null)
    if [ "$PWD" != "$git_root" ]; then
        log_warning "建議在 Git 倉庫根目錄執行此腳本"
        log_info "當前目錄: $PWD"
        log_info "Git 根目錄: $git_root"
    fi
}

# 檢查 hooks 狀態
check_hooks_status() {
    log_info "檢查 Git hooks 狀態..."
    echo ""

    local all_installed=true
    echo "已安裝的 hooks："
    for hook in pre-commit pre-push commit-msg; do
        if [ -f ".git/hooks/$hook" ]; then
            local size=$(stat -f%z ".git/hooks/$hook" 2>/dev/null || echo "unknown")
            log_success "$hook ($size bytes)"
        else
            log_error "$hook (未安裝)"
            all_installed=false
        fi
    done

    echo ""
    echo "Git 配置："
    echo "  autocrlf: $(git config core.autocrlf || echo '未設置')"
    echo "  push.default: $(git config push.default || echo '未設置')"
    echo "  user.name: $(git config user.name || echo '未設置')"
    echo "  user.email: $(git config user.email || echo '未設置')"

    return $($all_installed && echo 0 || echo 1)
}

# 備份現有 hooks
backup_hooks() {
    log_info "備份現有 hooks..."
    local backup_dir=".git/hooks/backup_$(date +%Y%m%d_%H%M%S)"
    mkdir -p "$backup_dir"

    local backed_up=0
    for hook in pre-commit pre-push commit-msg; do
        if [ -f ".git/hooks/$hook" ]; then
            cp ".git/hooks/$hook" "$backup_dir/"
            log_success "備份了 $hook"
            backed_up=1
        fi
    done

    if [ $backed_up -eq 1 ]; then
        log_success "備份位置: $backup_dir"
    else
        log_info "沒有找到需要備份的 hooks"
        rmdir "$backup_dir" 2>/dev/null
    fi
}

# 安裝 Git hooks 的核心邏輯
install_hooks_core() {
    log_info "開始安裝 Git hooks..."

    # 創建 hooks 目錄
    mkdir -p .git/hooks

    # 備份現有 hooks
    local backup_dir=".git/hooks/backup_$(date +%Y%m%d_%H%M%S)"
    local has_backup=false

    for hook in pre-commit pre-push commit-msg; do
        if [ -f ".git/hooks/$hook" ]; then
            [ "$has_backup" = false ] && mkdir -p "$backup_dir" && has_backup=true
            cp ".git/hooks/$hook" "$backup_dir/"
        fi
    done

    [ "$has_backup" = true ] && log_info "已備份現有 hooks 到: $backup_dir"

    # 安裝 pre-commit hook
    cat > .git/hooks/pre-commit << 'EOF'
#!/bin/bash
# Git pre-commit hook - 自動修復尾隨空格並檢查 Python 語法

set -e

# 檢查是否有可提交的文件
if ! git diff --cached --name-only --diff-filter=ACMR | head -1 | grep -q .; then
    echo "沒有檔案要提交"
    exit 0
fi

# 獲取暫存的文件列表
files=$(git diff --cached --name-only --diff-filter=ACMR | grep -E '\.(py|md|txt|yaml|yml|json|sh|sql|js|ts|css|html)$' || true)

if [ -z "$files" ]; then
    echo "沒有需要處理的文本文件"
    exit 0
fi

echo "🔧 Pre-commit: 檢查和修復代碼品質..."

# 處理尾隨空格
echo "正在移除尾隨空格..."
modified=0

for file in $files; do
    if [ -f "$file" ]; then
        if sed -i '' 's/[[:space:]]*$//' "$file" 2>/dev/null; then
            echo "  ✓ 處理: $file"
            git add "$file"
            modified=1
        fi
    fi
done

# Python 語法檢查
py_files=$(echo "$files" | grep '\.py$' || true)
if [ -n "$py_files" ]; then
    echo "檢查 Python 語法..."
    python_error=0

    for py_file in $py_files; do
        if [ -f "$py_file" ]; then
            if ! python -m py_compile "$py_file" 2>/dev/null; then
                echo "❌ Python 語法錯誤: $py_file"
                python_error=1
            else
                echo "  ✓ 語法正確: $py_file"
            fi
        fi
    done

    if [ $python_error -eq 1 ]; then
        echo ""
        echo "❌ Python 語法檢查失敗！請修復上述錯誤後重新提交。"
        exit 1
    fi
fi

[ $modified -eq 1 ] && echo "✅ 已自動修復並重新暫存文件"
echo "✅ Pre-commit 檢查完成"
EOF

    # 安裝 pre-push hook
    cat > .git/hooks/pre-push << 'EOF'
#!/bin/bash
# Git pre-push hook - 完整代碼品質檢查（唯讀模式）

set -e

echo "🔍 Pre-push: 完整代碼品質檢查..."

# 獲取要推送的文件
files=$(git diff --name-only HEAD~1 HEAD 2>/dev/null | grep -E '\.(py|md|txt|yaml|yml|json|sh|sql|js|ts|css|html)$' || true)

if [ -z "$files" ]; then
    echo "沒有需要檢查的文本文件"
    exit 0
fi

error_count=0

# 檢查尾隨空格
echo "檢查尾隨空格..."
for file in $files; do
    if [ -f "$file" ] && grep -q '[[:space:]]$' "$file" 2>/dev/null; then
        echo "❌ 發現尾隨空格: $file"
        error_count=$((error_count + 1))
    fi
done

# 檢查 merge conflict markers
echo "檢查 merge conflict markers..."
for file in $files; do
    if [ -f "$file" ]; then
        if grep -q '^<<<<<<< \|^=======$\|^>>>>>>> ' "$file" 2>/dev/null; then
            echo "❌ 發現 merge conflict markers: $file"
            error_count=$((error_count + 1))
        fi
    fi
done

# Python 語法檢查
py_files=$(echo "$files" | grep '\.py$' || true)
if [ -n "$py_files" ]; then
    echo "檢查 Python 語法..."
    for py_file in $py_files; do
        if [ -f "$py_file" ]; then
            if ! python -m py_compile "$py_file" 2>/dev/null; then
                echo "❌ Python 語法錯誤: $py_file"
                error_count=$((error_count + 1))
            fi
        fi
    done
fi

# 檢查大文件
echo "檢查大文件..."
for file in $files; do
    if [ -f "$file" ]; then
        size=$(stat -f%z "$file" 2>/dev/null || echo 0)
        if [ $size -gt 1048576 ]; then  # 1MB
            echo "⚠️  大文件警告: $file ($(($size / 1048576))MB)"
        fi
    fi
done

if [ $error_count -gt 0 ]; then
    echo ""
    echo "❌ 代碼品質檢查失敗！發現 $error_count 個問題"
    echo "請修復上述問題後重新推送"
    echo "或使用 git push --no-verify 跳過檢查"
    exit 1
fi

echo "✅ Pre-push 檢查完成"
EOF

    # 安裝 commit-msg hook
    cat > .git/hooks/commit-msg << 'EOF'
#!/bin/bash
# Git commit-msg hook - 檢查提交訊息格式

commit_msg_file="$1"
commit_msg=$(cat "$commit_msg_file")

# 允許的類型
valid_types="feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert"

# 檢查格式：[type](scope): description 或 type(scope): description 或 type: description
if echo "$commit_msg" | grep -qE '^\[([a-z]+)\]\([^)]+\):[[:space:]]' || \
   echo "$commit_msg" | grep -qE '^(feat|fix|docs|style|refactor|test|chore|perf|ci|build|revert)(\([^)]+\))?:[[:space:]]'; then
    exit 0
fi

# 檢查是否是 merge commit
case "$commit_msg" in
    Merge*)
        exit 0
        ;;
esac

# 檢查是否是 revert commit
case "$commit_msg" in
    Revert*)
        exit 0
        ;;
esac

echo "❌ 提交訊息格式錯誤！"
echo ""
echo "正確格式："
echo "  [type](scope): description"
echo "  type(scope): description"
echo "  type: description"
echo ""
echo "允許的 types: $valid_types"
echo ""
echo "範例："
echo "  [feat](user): 新增用戶登入功能"
echo "  fix: 修復密碼驗證錯誤"
echo "  docs(readme): 更新安裝說明"
echo ""
echo "您的訊息: $commit_msg"

exit 1
EOF

    # 設置執行權限
    chmod +x .git/hooks/pre-commit
    chmod +x .git/hooks/pre-push
    chmod +x .git/hooks/commit-msg

    log_success "Git hooks 安裝完成！"
    echo ""
    echo "已安裝的 hooks："
    echo "  ✓ pre-commit  - 自動修復尾隨空格、檢查 Python 語法"
    echo "  ✓ pre-push    - 完整代碼品質檢查（唯讀模式）"
    echo "  ✓ commit-msg  - 提交訊息格式檢查"
}

# 快速安裝模式（新同事友好）
quick_install() {
    echo "🚀 團隊 Git Hooks 快速部署"
    echo "================================"

    echo "📋 當前專案：$(basename $(pwd))"
    echo "📁 Git 倉庫：$(git remote get-url origin 2>/dev/null || echo '本地倉庫')"
    echo ""

    # 檢查未提交變更
    if ! git diff-index --quiet HEAD -- 2>/dev/null; then
        log_warning "您有未提交的變更"
        echo "建議先提交或暫存變更再安裝 hooks"
        echo ""
        read -p "是否繼續安裝？(y/N): " -n 1 -r
        echo
        if [[ ! $REPLY =~ ^[Yy]$ ]]; then
            log_error "安裝已取消"
            exit 1
        fi
    fi

    # 顯示將要安裝的 hooks
    echo "📝 將要安裝的 Git Hooks："
    echo "  ✓ pre-commit  - 提交前自動修復和檢查"
    echo "  ✓ pre-push    - 推送前完整品質檢查"
    echo "  ✓ commit-msg  - 提交訊息格式檢查"
    echo ""

    # 確認安裝
    read -p "開始安裝？(Y/n): " -n 1 -r
    echo
    if [[ $REPLY =~ ^[Nn]$ ]]; then
        log_error "安裝已取消"
        exit 1
    fi

    install_hooks_core

    # 安裝後提示
    echo ""
    echo "🎉 安裝完成！"
    echo ""
    echo "📖 接下來您可以："
    echo "  1. 測試提交: echo 'print(\"test\")' > test.py && git add test.py && git commit -m 'test: 測試 hooks'"
    echo "  2. 查看指南: $SCRIPT_NAME --guide"
    echo "  3. 檢查狀態: $SCRIPT_NAME --status"
}

# 靜默安裝模式（CI/CD 友好）
silent_install() {
    log_info "靜默安裝模式..."
    install_hooks_core
}

# 管理選單
show_menu() {
    echo "🚀 Git Hooks 統一管理腳本"
    echo "=========================="
    echo ""
    echo "📋 可用的操作："
    echo "  1. 安裝/更新 Git hooks"
    echo "  2. 查看 hooks 狀態"
    echo "  3. 備份現有 hooks"
    echo "  4. 查看使用指南"
    echo "  5. 測試 hooks 功能"
    echo "  6. 退出"
    echo ""

    while true; do
        read -p "請選擇操作 (1-6): " -n 1 -r
        echo

        case $REPLY in
            1)
                install_hooks_core
                break
                ;;
            2)
                check_hooks_status
                echo ""
                ;;
            3)
                backup_hooks
                echo ""
                ;;
            4)
                show_guide
                echo ""
                ;;
            5)
                test_hooks
                echo ""
                ;;
            6)
                log_info "再見！"
                exit 0
                ;;
            *)
                log_error "無效的選擇，請輸入 1-6"
                ;;
        esac
    done
}

# 顯示使用指南
show_guide() {
    if [ -f ".docs/git-hooks-guide.md" ]; then
        log_info "打開使用指南..."
        if command -v code &> /dev/null; then
            code .docs/git-hooks-guide.md
        elif command -v vim &> /dev/null; then
            vim .docs/git-hooks-guide.md
        else
            cat .docs/git-hooks-guide.md
        fi
    else
        log_error "找不到使用指南文件: .docs/git-hooks-guide.md"
    fi
}

# 測試 hooks 功能
test_hooks() {
    log_info "測試 Git hooks 功能..."

    # 創建測試文件
    echo 'print("hello world")   ' > test_hook_temp.py  # 故意加入尾隨空格
    git add test_hook_temp.py

    echo ""
    log_info "測試提交（會觸發 pre-commit hook）..."
    if git commit -m "test: 測試 Git hooks 功能"; then
        log_success "pre-commit hook 測試通過"

        # 清理測試
        git reset --soft HEAD~1
        git reset HEAD test_hook_temp.py
        rm -f test_hook_temp.py

        log_success "測試完成，已清理測試文件"
    else
        log_error "pre-commit hook 測試失敗"
        rm -f test_hook_temp.py
    fi
}

# 智能偵測模式
smart_detect() {
    check_hooks_status > /dev/null 2>&1
    local hooks_installed=$?

    if [ $hooks_installed -eq 0 ]; then
        echo "🎉 Git hooks 已安裝完成！"
        echo ""
        echo "您可以："
        echo "  • 查看狀態: $SCRIPT_NAME --status"
        echo "  • 查看指南: $SCRIPT_NAME --guide"
        echo "  • 管理選單: $SCRIPT_NAME --menu"
    else
        echo "🚀 歡迎使用 Git Hooks 管理腳本！"
        echo ""
        echo "偵測到您尚未安裝 Git hooks，建議您："
        echo "  • 快速安裝: $SCRIPT_NAME --quick"
        echo "  • 靜默安裝: $SCRIPT_NAME --install"
        echo "  • 查看幫助: $SCRIPT_NAME --help"
    fi
}

# 主程序
main() {
    check_environment

    case "${1:-}" in
        -h|--help)
            show_help
            ;;
        -v|--version)
            echo "Git Hooks 管理腳本 v${VERSION}"
            ;;
        -i|--install)
            silent_install
            ;;
        -q|--quick)
            quick_install
            ;;
        -m|--menu)
            show_menu
            ;;
        -s|--status)
            check_hooks_status
            ;;
        -b|--backup)
            backup_hooks
            ;;
        --guide)
            show_guide
            ;;
        --test)
            test_hooks
            ;;
        "")
            smart_detect
            ;;
        *)
            log_error "未知選項: $1"
            echo "使用 $SCRIPT_NAME --help 查看幫助"
            exit 1
            ;;
    esac
}

# 執行主程序
main "$@"
