from dask import dataframe
from block_timer.timer import Timer


@Timer(title="Load data")
def load_data(path, **kwargs):
    """Loading the pieces in separate processes, then transferring all the data to be stitched into
    a single data-frame in the main process."""
    df = dataframe.read_csv(path, assume_missing=True, **kwargs)
    return df.compute(scheduler="processes")

@Timer(title="Parquet load data")
def parquet_load_data(path, **kwargs):
    """Loading the pieces in separate processes, then transferring all the data to be stitched into
    a single data-frame in the main process."""
    df = dataframe.read_parquet(path, assume_missing=True, **kwargs)
    return df.compute(scheduler="processes")