import requests
from bs4 import BeautifulSoup
import gspread
from google.oauth2 import service_account
from oauth2client.service_account import ServiceAccountCredentials
from gspread_dataframe import set_with_dataframe, get_as_dataframe
import pandas as pd
import openai

def get_google_sheet_client(key_path):
    """Initialize and return a Google Sheets client."""
    scope = ['https://spreadsheets.google.com/feeds', 'https://www.googleapis.com/auth/drive']
    creds = ServiceAccountCredentials.from_json_keyfile_name(key_path, scope)
    return gspread.authorize(creds)

def fetch_and_parse_data(url, sheet):
    """Fetch data from the API and parse XML, returning a dictionary of items and processed IDs."""
    # Send request and get response
    response = requests.get(url)
    data = response.text

    # Parse XML
    soup = BeautifulSoup(data, 'xml')

    # Extract items
    items = soup.find_all('item')
    id_to_details = {}
    for item in items:
        item_id = item.find('g:id')
        title = item.find('g:title')
        image_link = item.find('g:image_link')
        item_link = item.find('g:link')

        # Add to dictionary if all elements exist
        if item_id and title and image_link and item_link:
            id_to_details[item_id.text] = {
                'title': title.text,
                'image_link': image_link.text,
                'item_link': item_link.text
            }

    processed_ids = sheet.col_values(1)[1:]
    return id_to_details, processed_ids

def generate_descriptions(api_key, id_to_details, processed_ids):
    """Generate product descriptions using OpenAI."""
    openai.api_key = api_key  # Replace with your actual OpenAI API Key
    # client = OpenAI(api_key=api_key)
    descriptions = []

    for item_id, details in id_to_details.items():
        if item_id not in processed_ids:
            print(f"Processing new product ID: {item_id}")
            image_link = details['image_link']
            item_link = details['item_link']
            title = details['title']

            try:
                response = openai.ChatCompletion.create(
                    model="gpt-4o",
                    messages=[
                        {
                            "role": "user",
                            "content": [
                                {"type": "text", "text": """If it’s a Women's bags e-commerce website,
        The product title is: "{title}". Based on this title and this picture,
        Can you help me follow the following points to describe this product:
        1. Style characteristics: Describe the style of product, such as fashion, casual, retro, formal, etc. This helps customers understand the overall style of the garment to determine if it meets their preferences and needs.
        2. Suitable occasions: Describe the occasions the product is suitable for, such as daily wear, parties, sports, office, etc. This can help the customer decide on what occasion the garment will be worn.
        3. Target audience: Describe the target audience for which the product is suitable, such as young people, professional women, casual dads, etc. This helps clients determine if it is a good fit for their age group, career or lifestyle.
        And based on these description, give some keywords of the product.
        Think step by step.Answer in ZH-TW.
        follow the response format :
        - 風格特徵:
        - 適合場合:
        - 目標受眾:
        - 產品關鍵字:"""},
                                {
                                "type": "image_url",
                                "image_url": {
                                    "url": f"{image_link}",
                                },
                                },
                            ],
                        }
                    ],
                    max_tokens=500,
                )

                print(f"ID: {item_id}, Title: {title}, Image Link: {image_link}, Description: {response.choices[0].message.content}")
                descriptions.append({'ID': item_id, 'Image Link': image_link, 'Item Link': item_link, 'Translated Description': response.choices[0].message.content})
            except Exception as e:
                print(f"Error processing ID: {item_id}, Title: {title}, Error: {str(e)}")
                descriptions.append({'ID': item_id, 'Image Link': image_link, 'Item Link': item_link, 'Translated Description': ''})

    return descriptions

def update_google_sheet(sheet, descriptions):
    """Update the Google Sheet with new descriptions."""
    df_new = pd.DataFrame(descriptions)

    # Read existing data from Google Sheet
    df_existing = get_as_dataframe(sheet).dropna(how="all").dropna(axis=1, how="all")

    # Combine existing and new data
    df_combined = pd.concat([df_existing, df_new], ignore_index=True)

    # Write back to Google Sheet
    set_with_dataframe(sheet, df_combined)
    print("Results saved to Google Sheet")
