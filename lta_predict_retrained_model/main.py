import functions_framework
import logging
import numpy as np
import pandas as pd
import random
import block_timer
import dask_utils
from dask_utils import load_data, parquet_load_data
import joblib
import google_storage
import dask.dataframe as dd
from google.cloud import bigquery
import datetime
from datetime import datetime, timedelta
from pytz import timezone
from typing import List, Tuple, Dict, Set
import json
from google.oauth2 import service_account
import pandas_gbq

GSIO = google_storage.GoogleStorageIO()

def trans(alist: pd.Series) -> List[int]:
  temp = list(alist.index[alist > 0])      
  return temp

def str_isin(x,input_str):
  return (input_str == x)

def make_date_arguments(current_datetime: datetime) -> Dict[str, str]:

  DATE_FORMAT = "%Y-%m-%d"
  tmp_dict = {}
  
  tmp_dict['start_date'] = (current_datetime - timedelta(days=31)).strftime(DATE_FORMAT)
  tmp_dict['14_days'] = (current_datetime - timedelta(days=15)).strftime(DATE_FORMAT)
  tmp_dict['end_date'] = (current_datetime - timedelta(days=1)).strftime(DATE_FORMAT)

  return tmp_dict

dictionary = {'760': ['761', '762', '763', '764', '765'], 
'776': ['766', '767', '768', '769', '770', '771', '772', '773', '774', '775', '777', '778', '779', '780', '781'], '788': ['776', '782', '783', '784', '785', '786', '787'],
'794': ['789', '792', '793'],
'792': ['790', '791'], '804': ['795', '796', '797', '798', '799', '800', '801', '802', '803'],
'825': ['806', '807', '808', '809', '810', '811', '812', '813', '814', '815', '816', '817', '819', '821', '824'],
'819': ['818', '820'], '821': ['822', '823'], '857': ['826', '838', '856'],
'826': ['827', '828', '829', '830', '831', '832', '833', '834', '835', '836', '837'],
'838': ['839', '840', '841', '842', '843', '844', '845', '846', '847', '848', '849', '850', '851', '852', '853', '854', '855'],
'883': ['858', '859', '860', '861', '862', '863', '864', '865', '866', '867', '868', '869', '870', '871', '872', '873', '874', '875', '876', '877', '878', '879', '880', '881', '882'],
'896': ['884', '885', '886', '887', '888', '889', '890', '891', '892', '893', '894', '895'],
'917': ['897', '898', '910'],
'910': ['899', '900', '901', '902', '903', '904', '905', '906', '907', '908', '909', '911', '912', '913', '914', '915', '916'],
'937': ['918', '919', '920', '921', '922', '923', '924', '925', '926', '927', '928', '929', '930', '931', '932', '933', '934', '935', '936'],
'947': ['939', '946'], '939': ['940', '941', '942', '943', '944', '945'], '955': ['948', '949', '950', '953', '954'], '950': ['951', '952'],
'966': ['956', '957', '958', '959', '960', '961', '962', '963', '964', '965'], '975': ['967', '969', '973'],
'967': ['968'], '969': ['970', '971', '972'], '973': ['974'], '979': ['976', '977', '978'],
'987': ['980', '981', '982', '983', '984', '985', '986', '988', '989', '990'], '1011': ['987', '991', '992', '997'], 
'992': ['993', '994', '995', '996'], '997': ['998', '999', '1000', '1001', '1002', '1003', '1004', '1005', '1006', '1007', '1008', '1009', '1010'],
'1020': ['1012', '1015', '1016', '1017'], '1012': ['1013', '1014'], '1017': ['1018', '1019'], '1023': ['1021', '1022', '1024', '1025', '1026'], '1029': ['1023', '1027', '1028']}

def update_list_with_dictionary_values(input_list, dictionary):
    input_set = set(input_list)  # Convert input_list to a set for faster lookup
    result = []
    for key, values in dictionary.items():
        if any(value in input_set for value in values):  # Check if any value is in input_list
            result.append(key)
    return list(set(input_list + result))

def process_in_chunks(df, chunk_size=1000):
    """
    將 DataFrame 分批處理以減少記憶體使用
    """
    logging.info(f"Starting batch processing with chunk size: {chunk_size}")
    results = []
    
    # 計算總批次數
    total_chunks = (len(df) + chunk_size - 1) // chunk_size
    
    for i in range(0, len(df), chunk_size):
        chunk = df.iloc[i:i+chunk_size]
        chunk_result = chunk.drop('permanent', axis=1).apply(lambda row: trans(row), axis=1)
        results.extend(chunk_result.tolist())
        
        # 記錄進度
        current_chunk = (i // chunk_size) + 1
        logging.info(f"Processed chunk {current_chunk}/{total_chunks}")
        
    return pd.Series(results, index=df.index)

@functions_framework.http
def main(request):
    # 設置 logging
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(message)s'
    )
    
    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)

    GSIO = google_storage.GoogleStorageIO(credentials=credentials)
    today = datetime.now(timezone('Asia/Taipei'))
    date_dict = make_date_arguments(today)

    # ttd mapping
    print("start ttd mapping query")
    # export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature/{date_dict['start_date']}_{date_dict['end_date']}/ttd_mapping_*.csv"
    # print(export_path)
    # df_ttd_mapping = load_data(export_path)
    client = bigquery.Client(credentials=credentials)

    ttd_query = f'''
        SELECT
      tagtoo_cookie AS permanent,
      ttd_cookie
    FROM (
      SELECT
        ROW_NUMBER() OVER(PARTITION BY tagtoo_cookie ORDER BY tagtoo_cookie, created DESC ) AS row_number,
        tagtoo_cookie,
        ttd_cookie,
        created
      FROM
        `gothic-province-823.tagtooad.ttd_cookie_mapping` ) ordered
      inner join `tagtoo-ml-workflow.tagtoo_export_results.lta_retrained_model_fb_permanent` p
      on ordered.tagtoo_cookie = p.permanent
    WHERE
      ordered.row_number = 1
      AND ordered.tagtoo_cookie != ''
    '''
    print(ttd_query)
    ttd_query_job = client.query(ttd_query)
    df_ttd_mapping = ttd_query_job.to_dataframe()
    logging.info("\n=== TTD Mapping DataFrame ===")
    logging.info(f"\nShape: {df_ttd_mapping.shape}")
    logging.info(f"\nColumns: {df_ttd_mapping.columns.tolist()}")
    logging.info(f"\nSample data:\n{df_ttd_mapping.head()}")
    print("ttd mapping query finish")

    # load data
    export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature_v2/{date_dict['start_date']}_{date_dict['end_date']}/transformed/*.ftr"
    print(export_path)
    # df = parquet_load_data(export_path)
    df = pd.read_feather(export_path)
    # df = df.iloc[:500]
    df = df.reset_index(drop=True)
    df["cycle"] = 1
    logging.info("\n=== Initial Feature DataFrame ===")
    logging.info(f"\nShape: {df.shape}")
    logging.info(f"\nColumns: {df.columns.tolist()}")
    logging.info(f"\nSample data:\n{df.head()}")
 
    # start predicting
    user = df.iloc[:, :1]
    df_feature = df.iloc[:,1:]
    feature = np.array(df_feature)
    logging.info("\n=== Feature Matrix ===")
    logging.info(f"\nFeature shape: {feature.shape}")

    # load model 1
    print('loading model')
    GSIO.download_to_path(gsuri='gs://tagtoo-ml-workflow/topic10/firstparty/model/lta_retrain_v1.pkl', localpath='/tmp/lta_retrain_v1.pkl')
    clf = joblib.load('/tmp/lta_retrain_v1.pkl')

    # start predicting
    user = df.iloc[:, :1]
    df_feature = df.iloc[:,1:]
    feature = np.array(df_feature)

    print('start predicting')
    predictions = clf.predict(feature)
    print('finish predicting')

    df = pd.concat([user, pd.DataFrame(predictions)], axis=1)
    logging.info("\n=== Prediction Results DataFrame ===")
    logging.info(f"\nShape: {df.shape}")
    logging.info(f"\nSample predictions:\n{df.head()}")

    #labeling
    cols = [
      "permanent","item_value_1", "item_value_2", "item_value_3", "item_value_4", "item_value_5", "item_value_6", "item_value_7",
      "order_value_1", "order_value_2", "order_value_3", "order_value_4", "order_value_5", "order_value_6",
      "purchase_", "purchase_DVD/影音", "purchase_一次性浴巾", "purchase_一般事務用品", "purchase_一般體育配備", "purchase_上衣",
      "purchase_中島/推車", "purchase_中式食品", "purchase_书本配件", "purchase_乳製品", "purchase_事務用品", "purchase_五穀雜糧產品",
      "purchase_五穀雜糧食品", "purchase_休閒娛樂", "purchase_休閒服飾", "purchase_休閒食品", "purchase_住宿", "purchase_保健與美容",
      "purchase_保健飲品", "purchase_保養品", "purchase_保養用品", "purchase_保養美容", "purchase_保養與美容", "purchase_個人清潔",
      "purchase_個人清潔用品", "purchase_個人衛生", "purchase_個人衛生用品", "purchase_個人護理", "purchase_健康照護", "purchase_健康產品",
      "purchase_健康零食", "purchase_健康食品", "purchase_健康飲品", "purchase_健康飲料", "purchase_傳統食品", "purchase_優惠產品", "purchase_兒童家具",
      "purchase_兒童服裝", "purchase_兒童服飾", "purchase_兒童用品", "purchase_內衣/襪子", "purchase_冬季運動", "purchase_冷凍甜品", "purchase_冷凍蔬菜",
      "purchase_冷凍食品", "purchase_刮鬍修容", "purchase_制服", "purchase_功能性飲品", "purchase_功能性飲料", "purchase_動漫周邊", "purchase_動漫周邊商品",
      "purchase_動物", "purchase_動畫周邊", "purchase_包袋", "purchase_化妝品", "purchase_卡牌遊戲", "purchase_即溶咖啡", "purchase_即食食品",
      "purchase_即食麵", "purchase_即飲品", "purchase_即飲飲品", "purchase_口腔保健", "purchase_咖啡", "purchase_咖啡豆", "purchase_哺育和餵食",
      "purchase_唱片/卡帶/錄製品", "purchase_商品卡", "purchase_單車", "purchase_堅果食品", "purchase_塔羅牌", "purchase_墊腳軟凳", "purchase_壁爐",
      "purchase_外套", "purchase_天然食品", "purchase_女性向遊戲", "purchase_女性服飾", "purchase_女性衛生用品", "purchase_女童服裝", "purchase_女童服飾",
      "purchase_奶茶", "purchase_娛樂", "purchase_娛樂中心/電視架", "purchase_娛樂商品", "purchase_娛樂產品", "purchase_婚禮/派對禮服", "purchase_婚禮用品",
      "purchase_媒體", "purchase_嬰兒內褲", "purchase_嬰兒包巾", "purchase_嬰兒外出用品", "purchase_嬰兒外出用品配件", "purchase_嬰兒安全", "purchase_嬰兒服",
      "purchase_嬰兒服飾", "purchase_嬰兒沐浴", "purchase_嬰兒照護", "purchase_嬰兒玩具和嬰兒活動設備", "purchase_嬰兒用品", "purchase_嬰幼兒家具",
      "purchase_嬰幼兒用品", "purchase_嬰幼兒童裝", "purchase_宗教和慶典", "purchase_宗教用品", "purchase_宗教用面紗/頭巾", "purchase_家具", "purchase_家具組",
      "purchase_家居用品", "purchase_家居香氛", "purchase_家庭清潔", "purchase_家庭電視遊樂器配件", "purchase_家政產品", "purchase_家用電器",
      "purchase_家用電器配件", "purchase_家電", "purchase_寵物用品", "purchase_尿布", "purchase_居家/企業保全", "purchase_居家和庭園",
      "purchase_居家生活用品", "purchase_工具", "purchase_工具配件", "purchase_床包被套", "purchase_床和配件", "purchase_廚房家電", "purchase_廚房與餐廳",
      "purchase_建築耗材", "purchase_彌月商品", "purchase_情色 DVD/影片", "purchase_情色書刊", "purchase_情趣商品", "purchase_情趣服飾", "purchase_情趣玩具",
      "purchase_情趣用品", "purchase_慶祝用品", "purchase_成人文學", "purchase_成人書籍", "purchase_成人用品", "purchase_成人遊戲", "purchase_成人雜誌",
      "purchase_戒枕和戒指座", "purchase_戲服和配件", "purchase_戶外家具", "purchase_戶外用品", "purchase_戶外遊戲", "purchase_戶外遊戲器具",
      "purchase_手提包和皮夾配件", "purchase_抗菌產品", "purchase_按摩和休閒", "purchase_排球", "purchase_攀爬運動", "purchase_收納層架",
      "purchase_收納整理用品", "purchase_收藏品", "purchase_收集品", "purchase_教育", "purchase_教育用品", "purchase_教育資源", "purchase_教育類",
      "purchase_教育類玩具", "purchase_數位商品", "purchase_數位商品和貨幣", "purchase_文化商品", "purchase_文化產品", "purchase_文化用品", "purchase_文學",
      "purchase_文學作品", "purchase_方便食品", "purchase_旅遊住宿", "purchase_日常用品", "purchase_日式床墊架", "purchase_早餐穀物", "purchase_春聯",
      "purchase_書本配件", "purchase_書籍", "purchase_有機產品", "purchase_服務券", "purchase_服裝", "purchase_服裝造型組合", "purchase_服飾",
      "purchase_服飾與配件", "purchase_果乾", "purchase_果汁", "purchase_桌上遊戲", "purchase_桌子", "purchase_棉球", "purchase_棉花棒",
      "purchase_棒球和壘球", "purchase_椅子", "purchase_椅用配件", "purchase_植物", "purchase_植物奶", "purchase_植物飲料", "purchase_樂器",
      "purchase_機動車輛", "purchase_櫥櫃和收納", "purchase_止汗香氛劑", "purchase_武器", "purchase_毛衣", "purchase_水", "purchase_水上運動",
      "purchase_水果", "purchase_水果乾", "purchase_水果風味飲品", "purchase_水災/消防/氣體安全", "purchase_汽機車零配件", "purchase_汽機車電子裝置",
      "purchase_沖泡式飲品", "purchase_沙發", "purchase_沾醬和抹醬", "purchase_泳池與 SPA", "purchase_泳衣", "purchase_洗手產品", "purchase_洗浴用品",
      "purchase_活動票券", "purchase_派對和節慶", "purchase_派對用品", "purchase_浴室配件", "purchase_海味零食", "purchase_海帶芽", "purchase_海鮮口味零食",
      "purchase_海鮮零食", "purchase_消臭噴霧", "purchase_清潔用品", "purchase_滑板用品", "purchase_潤滑劑", "purchase_潤膚產品", "purchase_潤膚皂",
      "purchase_潤膚膏", "purchase_潮流服飾", "purchase_濃湯/湯品", "purchase_火鍋", "purchase_火鍋食材", "purchase_烘焙和烘焙食材", "purchase_烘焙坊",
      "purchase_烘焙材料", "purchase_烹調和烘焙食材", "purchase_烹飪和烘焙食材", "purchase_無加糖飲品", "purchase_無添加飲品", "purchase_無糖飲品",
      "purchase_無糖飲料", "purchase_照明", "purchase_照明配件", "purchase_熱巧克力", "purchase_牛奶", "purchase_物品運送", "purchase_特殊醫療食品",
      "purchase_特色食品", "purchase_狗用品", "purchase_玩具", "purchase_玩具和遊戲", "purchase_珠寶", "purchase_甜食", "purchase_甜點", "purchase_生活用品",
      "purchase_田徑", "purchase_男性香水", "purchase_皮革保養", "purchase_益智遊戲", "purchase_睡衣/居家服", "purchase_短褲", "purchase_短褲裙",
      "purchase_硬體配件", "purchase_碳酸飲料", "purchase_社會公益", "purchase_祈福用品", "purchase_禮券", "purchase_禮物", "purchase_禮盒",
      "purchase_禱告念珠", "purchase_穀物零食", "purchase_穀物食品", "purchase_童裝", "purchase_筆記型電腦", "purchase_簡報用品", "purchase_籃球",
      "purchase_糖果和巧克力", "purchase_糖類產品", "purchase_紙張處理設備", "purchase_結婚主題", "purchase_網路", "purchase_罐頭食品", "purchase_美妝",
      "purchase_美妝保養", "purchase_美妝用品", "purchase_美容", "purchase_美容保養", "purchase_美容保養品", "purchase_美容器具", "purchase_美容工具",
      "purchase_美容產品", "purchase_美容用品", "purchase_美容相關", "purchase_美容與保養", "purchase_美容護理", "purchase_美容護膚", "purchase_美容配件",
      "purchase_義大利麵/麵食", "purchase_耳部保養", "purchase_肉/海鮮/蛋", "purchase_背部保養", "purchase_舞鞋", "purchase_芳香擴香", "purchase_茶葉",
      "purchase_茶類飲品", "purchase_草皮和花園", "purchase_菸草產品", "purchase_蔬果", "purchase_藝文類", "purchase_藝術/娛樂", "purchase_藝術品和手工藝品",
      "purchase_虛擬商品", "purchase_衛生清潔", "purchase_衛生產品", "purchase_衛生用品", "purchase_衛生紙", "purchase_衣物清潔", "purchase_衣著配件",
      "purchase_裙", "purchase_裝飾品", "purchase_裝飾用品", "purchase_褲子", "purchase_褲裙", "purchase_西裝", "purchase_視力保健", "purchase_調味品和香料",
      "purchase_調味料/醬料", "purchase_調理食品", "purchase_護膚品", "purchase_豆奶", "purchase_豆漿", "purchase_豆腐/豆類/蔬食產品", "purchase_豪華露營",
      "purchase_貓用品", "purchase_足球", "purchase_足部保養", "purchase_車輛", "purchase_車輛保養與裝飾", "purchase_車輛收納空間/載貨車廂",
      "purchase_辦公室家具", "purchase_辦公室玩具", "purchase_辦公室用品", "purchase_辦公室設備", "purchase_通訊設備", "purchase_速食食品",
      "purchase_速食麵", "purchase_連身裙", "purchase_遊戲", "purchase_遊戲相關", "purchase_運動/能量飲料", "purchase_運動用品", "purchase_運動與健身",
      "purchase_運動鞋", "purchase_酒精飲料", "purchase_醃製品", "purchase_長椅/長凳", "purchase_長褲", "purchase_防曬乳", "purchase_防曬噴霧",
      "purchase_防曬產品", "purchase_防盜包", "purchase_防蚊商品", "purchase_防蚊香膏", "purchase_陽傘/雨傘", "purchase_雜誌", "purchase_零食",
      "purchase_電動遊戲機", "purchase_電子器材", "purchase_電子產品", "purchase_電子票券", "purchase_電子遊戲", "purchase_電子配件", "purchase_電玩遊戲軟體",
      "purchase_電腦周邊", "purchase_電腦螢幕", "purchase_電腦軟體", "purchase_電視", "purchase_露營和健行", "purchase_非乳製品飲料", "purchase_鞋",
      "purchase_鞋類", "purchase_鞋類配件", "purchase_音訊設備", "purchase_頭髮保養", "purchase_食品", "purchase_食品包裝", "purchase_食品包裝用品",
      "purchase_食品飲料", "purchase_食物、飲料和菸草", "purchase_食物保存袋", "purchase_食物銀行", "purchase_飲品", "purchase_飲品容器",
      "purchase_飲品製作器具", "purchase_飲料", "purchase_飲料用品", "purchase_飲食", "purchase_飲食與飲料", "purchase_養魚用品", "purchase_餐飲商品",
      "purchase_餐飲服務", "purchase_首飾清潔保養", "purchase_香氛", "purchase_香水", "purchase_香皂", "purchase_骨灰罈", "purchase_體育用品",
      "purchase_鳥類用品",
      "view_0", "view_1", "view_2", "view_3", "view_4", "view_5", "view_6", "view_8", "view_9", "view_10", "view_13", "view_14",
      "view_16", "view_17", "view_19", "view_20", "view_21", "view_22", "view_23", "view_26", "view_28",
      "purchase_0", "purchase_1", "purchase_2", "purchase_3", "purchase_4", "purchase_5", "purchase_8", "purchase_9", "purchase_14",
      "purchase_19", "purchase_20", "purchase_21", "purchase_23", "purchase_26", "purchase_28"
      ]



    GSIO.download_to_path(
      gsuri="gs://tagtoo-ml-workflow/topic10/firstparty/lta_retrain/label_to_segment_id_updated.json",
      localpath="/tmp/label_to_segment_id.json"
    )
    with open("/tmp/label_to_segment_id.json", "r") as file:
        dict1 = json.load(file)
    print('dictionary loaded')

    for i in range(len(cols[1:])):
      try:
        cols[i+1] = str(dict1[cols[i+1]])
      except:
        pass
    
    df.columns = cols
    df = df[['permanent']+[col for col in df.columns if col.isnumeric()]]
    logging.info("\n=== After Column Renaming ===")
    logging.info(f"\nShape: {df.shape}")
    # logging.info(f"\nColumns: {df.columns.tolist()}")
    # logging.info(f"\nSample data:\n{df.head()}")
    print('prediction dataframe done')
    
    logging.info("Starting segment_id transformation...")
    df['segment_id'] = process_in_chunks(df, chunk_size=1000)
    logging.info("Segment_id transformation completed")
    
    df['segment_id'] = df['segment_id'].apply(lambda x: update_list_with_dictionary_values(x, dictionary))
    print('finish layers')
    df = df[['permanent', 'segment_id']]
    logging.info("\n=== Final DataFrame ===")
    logging.info(f"\nShape: {df.shape}")
    # logging.info(f"\nSample data:\n{df.head()}")

    date = datetime.now(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
    # new name for retrained model
    name = f'lta_retrained_remarketing_{date}'

    # ttd mapping
    print("start making tables")
    df_ttd = df.copy()
    df['segment_id'] = df['segment_id'].apply(lambda x: ','.join(['tm:d'+i for i in x]))

    df_ttd = df_ttd.merge(df_ttd_mapping, on='permanent', how='inner')
    df_ttd = df_ttd[['permanent', 'segment_id', 'ttd_cookie']]
    df_ttd.columns = ['track_user', 'segment_id', 'ttd_cookie']

    print("tables done")
    
    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    schema = [
    {"name": "permanent", "type": "STRING"},
    {"name": 'segment_id', "type": "STRING"}
    ]

    project_id = "tagtoo-ml-workflow"
    #table id for retrained model 
    table_id = f'tagtoo_export_results.lta_retrain_model_fb_{date}'
    df.to_gbq(table_id, project_id=project_id, table_schema = schema, if_exists = 'replace') 
    table_id = f'tagtoo_export_results.lta_model_fb'
    df.to_gbq(table_id, project_id=project_id, table_schema = schema, if_exists = 'replace') 

    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    rname = f'{name}_ttd_model.csv'
    # rname = f'{name}_ttd_model_first_layer.csv'
    df_ttd.to_csv(f'/tmp/{rname}', index=False)
    GSIO.upload_file(f'/tmp/{rname}', f'gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date}/{rname}')
            
    print('Job complete.')
    print('fb: ', len(df))
    print('ttd: ', len(df_ttd))

    return 'Success', 200
