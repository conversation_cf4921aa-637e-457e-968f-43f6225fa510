import logging
import pandas as pd
import argparse
import sys
from google_storage import CustomGoogleStorageIO
from utils import *
from make_query import make_entity_query

def main(request=None):
    """
    主要執行函數，支援 Cloud Function 和本機測試兩種模式
    
    Args:
        request: Flask request 物件 (Cloud Function 模式) 或 None (本機測試模式)
    """
    
    # 判斷是否為本機測試模式
    if request is None:
        # 本機測試模式：使用 argparse 解析命令列參數
        parser = argparse.ArgumentParser(description='Feebee Audience to FB 處理程式')
        parser.add_argument(
            '--date', 
            required=True, 
            help='處理日期 (格式: YYYY-MM-DD 或其他指定格式)'
        )
        
        args = parser.parse_args()
        FILE_PATH = args.date
        
        logging.info(f"本機測試模式：處理日期 = {FILE_PATH}")
        
    else:
        # Cloud Function 模式：解析 HTTP 請求參數
        request_json = request.get_json(silent=True)
        if request_json and 'date' in request_json:
            FILE_PATH = request_json['date']
        else:
            return 'Missing date', 400
        
        logging.info(f"Cloud Function 模式：處理日期 = {FILE_PATH}")


    GOOGLE_APPLICATION_CREDENTIALS_PATH = "tagtoo-ml-workflow-kubeflow.json"
    EC_ID_FOR_MAPPING_PIXAL = "8888888"

    path = 'gs://tagtoo-ml-workflow/feebee/{}'.format(FILE_PATH)
    gs_rel_path = 'feebee/{}'.format(FILE_PATH)

    google_credentials, _ = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    GSIO = CustomGoogleStorageIO(credentials=google_credentials)

    # 下載 GCS 檔案
    gcs_path_list = list_gcs_files(GSIO, path, gs_rel_path)
    gcs_path_list = list(map(replace_path_format, gcs_path_list))
    file_list = download_files(GSIO, gcs_path_list)

    # 讀取 Taxonomy
    df_tax = read_taxonomy(google_credentials)
    df_tax['segment_id'] = df_tax['segment_id'].fillna("")
    df_tax = df_tax[df_tax.segment_id.str.contains('2607')]
    key_list = df_tax['segment_id'].tolist()
    logging.info(f"TTD Taxonomy 中共有 {len(key_list)} 個 Feebee受眾包")

    # 讀取檔案並存回字典: df_dict[segment_id]: dataframe
    df_dict = process_segment_files(file_list)

    # 重新設定欄位名稱和索引
    for k, v in df_dict.items():
        df_dict[k] = reset_columns(v)

    # 新增 segment_id 欄位
    missing_key = update_segment_ids(key_list, df_dict)

    # 刪除缺失值
    drop_na(df_dict)

    # 合併所有 DataFrame
    df = pd.concat(list(df_dict.values()), axis=0, sort=False)
    df = df[~df['segment_id'].isnull()]

    # 分組並合併 segment_id
    gb_df = df.groupby('partner_user_id', as_index=False).agg({'segment_id': lambda x: ','.join(x)})
    logging.info(f"所有 dataframe 聚合完成")

    result_path = f"/tmp/{FILE_PATH}.csv"
    gcs_result_path = f"gs://tagtoo-ml-workflow/feebee/result/{FILE_PATH}.csv"
    gb_df.to_csv(result_path, index=False)
    GSIO.upload_file(gsuri=gcs_result_path, localpath=result_path)
    logging.info(f"檔案上傳到 {gcs_result_path}")

    table_id =load_data_to_bigquery(gb_df, google_credentials, FILE_PATH)
    logging.info(f"資料寫入 BigQuery 完成")

    entity_query = make_entity_query(table_id)

    query_and_export(
        credentials=google_credentials,
        query=entity_query,
        project_id=table_id.split('.')[0],
        dataset_id=table_id.split('.')[1],
        table_id=f'entity_{FILE_PATH}',
        output='gs://tagtoo-ml-workflow/feebee/entity/',
        date=FILE_PATH,
        file_format='AVRO',
    )

    avro_filepath = f"gs://tagtoo-ml-workflow/feebee/entity/feebee_{FILE_PATH}_000000000000.avro"
    avro_filepath_list = get_avro_file_paths(avro_filepath, GSIO)
    logging.info(f"共會發送 {len(avro_filepath_list)} 個 AVRO 檔案")

    publish_messages(avro_filepath_list, google_credentials, EC_ID_FOR_MAPPING_PIXAL)
    
    return '執行完畢', 200

if __name__ == "__main__":
    """
    本機測試入口點
    
    使用方式:
    python main.py --date 2024-01-15
    """
    # 設定 logging 格式
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(levelname)s - %(message)s'
    )
    
    try:
        result = main()
        print(f"執行結果: {result}")
    except Exception as e:
        logging.error(f"執行過程中發生錯誤: {str(e)}")
        sys.exit(1)