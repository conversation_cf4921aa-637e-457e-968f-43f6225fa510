from apiclient.discovery import build
from google.oauth2 import service_account
import pandas as pd
import logging
from google.cloud import bigquery
from typing import Dict
import os
from publisher import PublisherClient

def get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH):
    # verify account
    google_credentials = service_account.Credentials.from_service_account_file(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    project_id = google_credentials.project_id
    return google_credentials, project_id

def list_gcs_files(gsio, path, prefix):
    """列出指定路徑下的所有 GCS 檔案"""
    gcs_path_list = []
    for file in gsio.list_files(path, prefix=prefix):
        file_url = file.public_url.split('/')
        gcs_path = 'gs://' + '/'.join(file_url[3:])
        gcs_path_list.append(gcs_path)
    return gcs_path_list

def replace_path_format(gcs_path):
    """替換 GCS 路徑格式"""
    lst = gcs_path.split('/')
    last_path = lst[-1].replace('%3A', ':')
    path = '/'.join(lst[:-1])
    return f"{path}/{last_path}"

def download_files(gsio, gcs_path_list):
    """下載 GCS 檔案到本地"""
    df = pd.DataFrame(gcs_path_list, columns=['path'])
    file_list = df.path.apply(lambda x: '/tmp/' + x.split('/')[-1]).tolist()

    for gcs_path, local_path in zip(gcs_path_list, file_list):
        gsio.download_to_path(gsuri=gcs_path, localpath=local_path)

    logging.info("Files downloaded.")

    return file_list

def read_taxonomy(credentials) -> pd.DataFrame:
    """讀取 Taxonomy 資料並返回 DataFrame"""
    spreadsheet_id = '1BtG2GdoV7B2Cu5Ej7B49VU8z6MZyFqGRyjOSX5emp2Q'
    page_name = 'All'
    columns = ['segment_id', 'name', 'path', 'description']
    cell_range = 'B2:E'
    
    # creds = service_account.Credentials.from_service_account_file(credentials_file)
    service = build('sheets', 'v4', credentials=credentials)
    
    sheet = service.spreadsheets()
    result = sheet.values().get(spreadsheetId=spreadsheet_id, range=f"{page_name}!{cell_range}").execute()
    values = result.get('values', [])
    
    if not values:
        logging.info("未找到資料。")
        return pd.DataFrame()
    
    df = pd.DataFrame(values, columns=columns)
    return df

def process_segment_files(file_list: list) -> dict:
    """處理段落檔案並返回字典"""
    df_dict = {}
    for path in file_list:
        key = path.split('/')[-1]
        if key != '.DS_Store':
            df_dict[key] = pd.read_csv(path, engine='python', on_bad_lines='skip')
    return df_dict

def reset_columns(df: pd.DataFrame) -> pd.core.frame.DataFrame:
    df.loc[-1] = [df.columns[0]]  # adding a row
    df.index = df.index + 1  # shifting index
    df = df.sort_index()  # sorting by index
    df.rename(columns={df.columns[0]: 'partner_user_id'}, inplace=True)
    return df

def update_segment_ids(key_list: list, df_dict: dict) -> list:
    """更新段落ID並返回缺失的鍵列表"""
    missing_key = []
    for key in key_list:
        if key in df_dict:
            df_dict[key]['segment_id'] = key  # 直接更新段落ID
        else:
            missing_key.append(key)
    logging.info(f"本次共缺失 {len(missing_key)} 個受眾包")
    return missing_key


def drop_na(df_dict: Dict[str, pd.DataFrame]) -> None:
    for k, v in df_dict.items():
        df_dict[k] =v.dropna()

def load_data_to_bigquery(gb_df: pd.DataFrame, google_credentials, file_path: str) -> None:
    """將 DataFrame 載入 BigQuery"""
    schema = [
        bigquery.SchemaField("partner_user_id", "STRING"),
        bigquery.SchemaField("segment_id", "STRING"),
    ]

    client = bigquery.Client(credentials=google_credentials)
    table_id = f"tagtoo-ml-workflow.feebee.result_{file_path}"
    job = client.load_table_from_dataframe(gb_df, table_id, job_config=bigquery.LoadJobConfig(schema=schema))
    job.result()
    logging.info(f"Loaded {job.output_rows} rows into {table_id}.")
    return table_id

def query_and_export(
    credentials,
    query: str,
    project_id: str,
    dataset_id: str,
    table_id: str,
    output: str,
    date: str,
    file_format: str = 'CSV' # CSV, JSON, Avro, Parquet
    ):

    client = bigquery.Client(credentials=credentials)

    job_config = bigquery.QueryJobConfig()
    table_ref = client.dataset(dataset_id).table(table_id)
    job_config.create_disposition = \
        bigquery.job.CreateDisposition.CREATE_IF_NEEDED
    job_config.write_disposition = \
        bigquery.job.WriteDisposition.WRITE_TRUNCATE
    job_config.destination = table_ref

    query_job = client.query(query, job_config=job_config)

    job_result = query_job.result()  # Wait for the query to finish

    result = {
        'destination_table': table_ref.path,
        'total_rows': job_result.total_rows,
        'total_bytes_processed': query_job.total_bytes_processed,
        'schema': [f.to_api_repr() for f in job_result.schema],
        }

    logging.info("查詢成功")

    # Large file: specify a uri including * to shard export
    # * avalible file foramt - CSV, JSON, Avro, Parquet
    files = os.path.join(output, f'feebee_{date}_*.{file_format.lower()}')
    job_config = bigquery.job.ExtractJobConfig(
        destination_format=file_format
    )
    extract_job = client.extract_table(table_ref, files, job_config=job_config)
    extract_job.result()  # Wait for export to finish

    logging.info("匯出成功")

    return result

def get_avro_file_paths(filepath: str, GSIO) -> list:
    """取得指定路徑下的所有 avro 格式檔案路徑"""
    # data = f"gs://tagtoo-ml-workflow/feebee/entity/feebee_{file_path}_000000000000.avro"
    prefix = filepath[24:-18]
    bucket_name = filepath[5:].split('/')[0]
    data_path_list = GSIO.get_wildcard_file_names(bucket_name, prefix)
    
    # 篩選出 avro 格式的檔案
    avro_files = [f'gs://{bucket_name}/' + file for file in data_path_list if file.endswith('.avro')]
    
    return avro_files

def publish_messages(data_path_list: list, google_credentials, ec_id: str) -> None:
    """發佈消息到 Pub/Sub"""
    PROJECT_ID = "tagtoo-tracking"
    TOPIC_NAME = "lta-prod"
    with PublisherClient(project_id=PROJECT_ID, topic_name=TOPIC_NAME, credentials=google_credentials) as publisher:
        for file in data_path_list:
            publisher.add_message(
                '',
                file_name=file,
                version='v1',
                ec_id=ec_id,
            )
            logging.info(f"訊息發佈 for file: {file}")
    logging.info(f"所有訊息成功發佈到 {PROJECT_ID} 專案的 {TOPIC_NAME} 主題")