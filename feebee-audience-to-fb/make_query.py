def make_entity_query(table: str) -> str:
    query = f"""
    WITH
      fbp_fbc AS (
      SELECT
        DISTINCT partner_user_id AS group_id,
        fbp,
        fbc,
        ip
      FROM
        `gothic-province-823.tagtooad.feebee_id_mapping`
      INNER JOIN
        `{table}`
      USING
        (partner_user_id)
      WHERE
        fbp != ""
        OR fbc != "" ),
      fbp_fbc_array AS (
      SELECT
        group_id,
        CASE
          WHEN fbp = "" THEN ARRAY<STRING>[""]
        ELSE
        ARRAY<STRING>[fbp]
      END
        AS fbp_array,
        CASE
          WHEN fbp = "" THEN ARRAY<STRING>[""]
        ELSE
        ARRAY<STRING>[fbc]
      END
        AS fbc_array,
        CASE
          WHEN (ip = "") OR (ip IS NULL) THEN ARRAY<STRING>[""]
        ELSE
        ARRAY<STRING>[ip]
      END
        AS ip_array
      FROM
        fbp_fbc ),
      user_array AS (
      SELECT
        group_id,
        ARRAY_CONCAT(fbp_array, fbc_array, ip_array) AS fbp_fbc_ip_array
      FROM
        fbp_fbc_array ),
      target_audience AS (
      SELECT
        partner_user_id AS group_id,
        SPLIT(segment_id, ', ') AS segment_id
      FROM
        `{table}` ),

    nested_user_array AS (
    SELECT
      group_id,
      ARRAY_AGG(STRUCT(fbp_fbc_ip_array AS fbp_fbc_ip)) AS fb_info,
    FROM
      user_array
    GROUP BY
      1)

      SELECT
        group_id,
        fb_info,
        target_audience.segment_id
      FROM
        nested_user_array
      LEFT JOIN
        target_audience
      USING
        (group_id)
    """
    return query