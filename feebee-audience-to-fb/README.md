# Feebee Audience to Facebook

## 簡介

`feebee-audience-to-fb` 是一個 Google Cloud Function，用於處理 Feebee 受眾數據並將其上傳到 Facebook。此功能會從 Google Cloud Storage 下載數據，處理數據後將其上傳到 BigQuery，並最終將結果發佈到 Pub/Sub。

## 前置作業
客戶會將 Feebee 多份受眾檔案寄到我們的信箱，需要手動下載，並將多份 CSV 合併到同一個資料夾，最後將檔案上傳到 Google Cloud Storage>

資料夾名稱：名稱是當天日期，例如：2024-09-25，格式是 `YYYY-MM-DD`
GCS 路徑：gs://tagtoo-ml-workflow/feebee/<當天日期>

## 手動觸發 Cloud Function
```bash
curl -X POST https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/feebee-audience-to-fb \
-H "Authorization: bearer $(gcloud auth print-identity-token)" \
-H "Content-Type: application/json" \
-d '{ "date": "<當天日期>"}'
```

## 目錄結構

- `main.py`: 主函數入口，處理請求並執行主要邏輯。
- `utils.py`: 包含輔助函數，如下載文件、讀取 Taxonomy、處理段落文件等。
- `google_storage.py`: 封裝了 Google Cloud Storage 的操作。
- `publisher.py`: 封裝了 Pub/Sub 的操作。
- `make_query.py`: 生成 BigQuery 查詢語句。
- `cloudbuild.yaml`: 用於部署 Cloud Function 的 Cloud Build 配置文件。
- `requirements.txt`: Python 依賴包列表。
- `tagtoo-ml-workflow-834e7467c939.json`: Google Cloud Service Account 憑證文件。
