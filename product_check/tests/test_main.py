import pytest
from unittest.mock import patch, MagicMock
import pandas as pd
import sys
import os

# 添加父目錄到 sys.path 以便導入 main 模組
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))
from main import main

# 測試 product_check 功能的郵件發送邏輯
    
@patch('smtplib.SMTP_SSL')
@patch('pandas.DataFrame.from_dict')
@patch('google.cloud.bigquery.Client.query')
def test_send_email_when_abnormal_products_exist(mock_query, mock_from_dict, mock_smtp):
        """測試當有異常商品時應發送郵件"""
        # 模擬 request 對象
        mock_request = MagicMock()
        
        # 模擬 BigQuery 查詢結果
        mock_df = pd.DataFrame({
            'ec_id': ['1', '2'],
            'id': ['101', '102'],
            'item_name': ['異常商品1', '異常商品2'],
            'price': [1000, 2000],
            'bar': [500, 1000],
            'industry_id': ['101', '102'],
            'category1': ['類別A', '類別B'],
            'category2': ['子類別A', '子類別B'],
            'category3': ['細類別A', '細類別B']
        })
        mock_query.return_value.to_dataframe.return_value = mock_df
        
        # 模擬 worksheet 獲取的數據
        mock_from_dict.return_value = pd.DataFrame({
            'id': ['103'],
            'item_name': ['正常商品']
        })
        
        # 模擬 Google Sheets 操作
        with patch('gspread.authorize') as mock_authorize:
            mock_spreadsheet = MagicMock()
            mock_worksheet = MagicMock()
            mock_today_worksheet = MagicMock()
            mock_whitelist_worksheet = MagicMock()
            
            mock_authorize.return_value.open_by_url.return_value = mock_spreadsheet
            mock_spreadsheet.get_worksheet.side_effect = [mock_worksheet, mock_whitelist_worksheet, mock_today_worksheet]
            mock_whitelist_worksheet.get_all_records.return_value = []
            
            # 模擬 set_with_dataframe 函數
            with patch('gspread_dataframe.set_with_dataframe'):
                # 模擬 google_storage.GoogleStorageIO
                with patch('google_storage.GoogleStorageIO'):
                    # 模擬 _read_json 函數
                    with patch('main._read_json') as mock_read_json:
                        mock_read_json.return_value = {'category2': 500, '1': 500, '2': 1000}
                        
                        # 執行主函數
                        main(mock_request)
                        
                        # 驗證是否調用了郵件發送方法
                        mock_smtp_instance = mock_smtp.return_value
                        assert mock_smtp_instance.sendmail.called
                        assert mock_smtp_instance.sendmail.call_count == 3  # 3個收件人
    
@patch('smtplib.SMTP_SSL')
@patch('pandas.DataFrame.from_dict')
@patch('google.cloud.bigquery.Client.query')
def test_no_email_when_no_abnormal_products(mock_query, mock_from_dict, mock_smtp):
        """測試當沒有異常商品時不應發送郵件"""
        # 模擬 request 對象
        mock_request = MagicMock()
        
        # 模擬 BigQuery 查詢結果 - 所有商品價格都低於閾值
        mock_df = pd.DataFrame({
            'ec_id': ['1', '2'],
            'id': ['101', '102'],
            'item_name': ['正常商品1', '正常商品2'],
            'price': [400, 800],
            'bar': [500, 1000],
            'industry_id': ['101', '102'],
            'category1': ['類別A', '類別B'],
            'category2': ['子類別A', '子類別B'],
            'category3': ['細類別A', '細類別B']
        })
        mock_query.return_value.to_dataframe.return_value = mock_df
        
        # 模擬 worksheet 獲取的數據
        mock_from_dict.return_value = pd.DataFrame({
            'id': ['103'],
            'item_name': ['正常商品']
        })
        
        # 模擬 Google Sheets 操作
        with patch('gspread.authorize') as mock_authorize:
            mock_spreadsheet = MagicMock()
            mock_worksheet = MagicMock()
            mock_today_worksheet = MagicMock()
            mock_whitelist_worksheet = MagicMock()
            
            mock_authorize.return_value.open_by_url.return_value = mock_spreadsheet
            mock_spreadsheet.get_worksheet.side_effect = [mock_worksheet, mock_whitelist_worksheet, mock_today_worksheet]
            mock_whitelist_worksheet.get_all_records.return_value = []
            
            # 模擬 set_with_dataframe 函數
            with patch('gspread_dataframe.set_with_dataframe'):
                # 模擬 google_storage.GoogleStorageIO
                with patch('google_storage.GoogleStorageIO'):
                    # 模擬 _read_json 函數
                    with patch('main._read_json') as mock_read_json:
                        mock_read_json.return_value = {'category2': 500, '1': 500, '2': 1000}
                        
                        # 執行主函數
                        main(mock_request)
                        
                        # 驗證未調用郵件發送方法
                        mock_smtp_instance = mock_smtp.return_value
                        assert not mock_smtp_instance.sendmail.called

# pytest 不需要入口點，可以直接使用 pytest 命令運行
