import functions_framework
import numpy as np
import pandas as pd
import os
import google_storage
from google.oauth2 import service_account
from oauth2client.service_account import ServiceAccountCredentials
from google.cloud import bigquery
import gspread
from gspread_dataframe import set_with_dataframe
import smtplib
from email.mime.multipart import MIMEMult<PERSON>art
from email.mime.text import MIMEText
from email.mime.application import MIMEApplication
from os.path import basename
from datetime import datetime, timedelta
import json

def _read_json(filepath: str):
    with open(filepath, mode='r') as f:
        data = json.load(f)
    return data

def make_daily_product_query():
    sql = """
WITH
  DailyProduct AS (
  SELECT
    DISTINCT ec_id,
    items.name AS tagtoo_event_item_name,
    items.price AS tagtoo_event_item_price,
    items.id AS tagtoo_event_item_id
  FROM
    `tagtoo-tracking.event_prod.tagtoo_event`,
    UNNEST(event.items) items
  WHERE
    DATE(event_time, 'Asia/Taipei') = DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY)
    AND event.currency = 'TWD' )
SELECT
  DISTINCT DP.ec_id,
  IND.industry_id,
  DP.tagtoo_event_item_id AS id,
  DP.tagtoo_event_item_price AS price,
  pcmn.item_name,
  pcmn.category1,
  pcmn.category2,
  pcmn.category3,
FROM
  `tagtoo-ml-workflow.data_prod.Product_info` AS pcmn
INNER JOIN
  DailyProduct AS DP
ON
  DP.tagtoo_event_item_name = pcmn.item_name
INNER JOIN
  `gothic-province-823.tagtoo_from_cloudsql.ECID_to_IndustryID` IND
ON
  IND.ec_id = DP.ec_id
    """
    return sql

def calculate_bar(row, category_threshold_map, industry_threshold_map, ec_threshold_map):
    # 使用 get 方法並指定默認值 0
    thresholds = [
        category_threshold_map.get(row['category2'], 0),
        industry_threshold_map.get(row['industry_id'], 0),
        ec_threshold_map.get(row['ec_id'], 0)
    ]

    # 移除值為 0 的門檻值
    valid_thresholds = [value for value in thresholds if value != 0]

    # 如果沒有有效的門檻值，返回 None
    if not valid_thresholds:
        return None

    # 計算有效門檻值的平均值
    return sum(valid_thresholds) / len(valid_thresholds)


@functions_framework.http
def main(request):
    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """

    # Define the scope of the application
    scope = [
        "https://spreadsheets.google.com/feeds",
        "https://www.googleapis.com/auth/drive",
        "https://www.googleapis.com/auth/bigquery",
        "https://www.googleapis.com/auth/devstorage.full_control"
    ]

    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path, scopes=scope)
    bq_client = bigquery.Client(credentials=credentials)
    gs_client = gspread.authorize(credentials)

    # Open the spreadsheet by its URL
    spreadsheet_url = 'https://docs.google.com/spreadsheets/d/1EZ8n0bB64U5gv_GA8Y2Xi23QI6UrtZ0v_L0z-j2dHQ8/'
    spreadsheet = gs_client.open_by_url(spreadsheet_url)

    worksheet = spreadsheet.get_worksheet(0)  # First worksheet
    today_worksheet = spreadsheet.get_worksheet(2)  # 今日商品價格異常名單

    df_p = pd.DataFrame.from_dict(worksheet.get_all_records(), orient='columns').dropna()

    for col in df_p.columns:
        df_p = df_p[~(df_p[col] == '')]
    
    # 下載門檻值
    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    category_path = '/tmp/category_second_layer_threshold.json'
    category_gcs_path = 'gs://tagtoo-ml-workflow/topic10/Fredrick_Workbench/Product_check/threshold/category_second_layer_threshold.json'
    GSIO.download_to_path(gsuri=category_gcs_path, localpath=category_path)
    category_threshold_map = _read_json(category_path)

    industry_path = '/tmp/industry_threshold.json'
    industry_gcs_path = 'gs://tagtoo-ml-workflow/topic10/Fredrick_Workbench/Product_check/threshold/industry_threshold.json'
    GSIO.download_to_path(gsuri=industry_gcs_path, localpath=industry_path)
    industry_threshold_map = _read_json(industry_path)

    ec_path = '/tmp/ec_threshold.json'
    ec_gcs_path = 'gs://tagtoo-ml-workflow/topic10/Fredrick_Workbench/Product_check/threshold/ec_threshold.json'
    GSIO.download_to_path(gsuri=ec_gcs_path, localpath=ec_path)
    ec_threshold_map = _read_json(ec_path)

    # 取得白名單
    whitelist = pd.DataFrame.from_dict(spreadsheet.get_worksheet(1).get_all_records(), orient='columns')
    
    # 取得昨日商品
    sql_query = make_daily_product_query()
    df = bq_client.query(sql_query).to_dataframe()

    # 轉換成字串, 因為字典中的 key 是字串
    df['ec_id'] = df['ec_id'].astype(str)
    df['industry_id'] = df['industry_id'].astype(str)

    # 計算每行的 bar 值
    # df['bar'] = df.apply(calculate_bar, axis=1)
    df['bar'] = df.apply(lambda row: calculate_bar(row, category_threshold_map, industry_threshold_map, ec_threshold_map), axis=1)

    df_result = df[df['price'] > df['bar']][['ec_id', 'id', 'item_name', 'price', 'bar']]
    df_result['price'] = df_result['price'].astype(int)
    df_result['bar'] = df_result['bar'].astype(int) 
    df_result = df_result.reset_index(drop=True)

    # 合并 'id' 和 'name' 成新的字串, 過濾掉在 whitelist 中的組合字串
    df_result['item_id_name'] = df_result['id'] + '_' + df_result['item_name']
    whitelist['item_id_name'] = whitelist['id'] + '_' + whitelist['item_name']
    df_result = df_result[~df_result['item_id_name'].isin(whitelist['item_id_name'])]
    df_result = df_result.drop(columns=['item_id_name'])

    # Clear existing data from the worksheet
    today_worksheet.clear()
    # Upload the DataFrame to the worksheet
    set_with_dataframe(today_worksheet, df_result)

    df_result = pd.concat([df_result, df_p]).drop_duplicates().sort_values('price', ascending = False).reset_index(drop=True)

    # Clear existing data from the worksheet
    worksheet.clear()
    # Upload the DataFrame to the worksheet
    set_with_dataframe(worksheet, df_result)

    today = datetime.today()
    yesterday = today - timedelta(days=1)

    # file_name = '商品價格異常_{}.csv'.format(yesterday.strftime("%Y-%m-%d"))
    # df_result.to_csv(f'/tmp/{file_name}', index=False)

    # Gmail credentials and recipient
    sender_email = "<EMAIL>"
    password = "ywhc fmca mjwv vhyy"
    # receiver_email = "<EMAIL>"
    recipients = ["<EMAIL>", "<EMAIL>", "<EMAIL>"]

    # Email Subject
    subject = "商品價格異常 - {}".format(yesterday.strftime("%Y-%m-%d"))

    # Email Body
    body =  """
    Hi Team,

    昨日商品價格可能有異常的列表如下, 請查閱。
    https://docs.google.com/spreadsheets/d/1EZ8n0bB64U5gv_GA8Y2Xi23QI6UrtZ0v_L0z-j2dHQ8/edit#gid=0

    Best,
    Data Team
    """

    # # File to attach
    # file_path = f'/tmp/{file_name}'

    # 只有當有異常商品時才發送郵件通知
    if len(df_result) > 0:
        # Create the email message
        for receiver_email in recipients:
            # Create the email message
            message = MIMEMultipart()
            message['From'] = sender_email
            message['To'] = receiver_email
            message['Subject'] = subject
            message.attach(MIMEText(body, "plain"))

        # # Attach the file
        # with open(file_path, "rb") as file:
        #     part = MIMEApplication(
        #         file.read(),
        #         Name=basename(file_path)
        #     )
        # part['Content-Disposition'] = 'attachment; filename="{}"'.format(basename(file_path))
        # message.attach(part)

            # Send the email
            try:
                server = smtplib.SMTP_SSL('smtp.gmail.com', 465)
                server.login(sender_email, password)
                server.sendmail(sender_email, receiver_email, message.as_string())
                server.quit()
                print(f"Email sent successfully to {receiver_email}!")
            except Exception as e:
                print("Failed to send email:", e)
    
    return 'Success', 200
