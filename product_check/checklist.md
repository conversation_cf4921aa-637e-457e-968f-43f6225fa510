# Product Check 功能測試檢查清單

## 測試環境設置
- [x] 創建 tests 目錄結構
- [x] 設置基本測試框架 (pytest)
- [x] 導入必要的模組和依賴

## 測試案例實現
- [x] 實現有異常商品時發送郵件的測試案例
- [x] 實現沒有異常商品時不發送郵件的測試案例
- [x] 添加測試運行入口點

## 測試執行和驗證
- [ ] 運行所有測試案例
- [ ] 驗證測試結果符合預期
- [ ] 確認沒有實際發送郵件

## 文檔完善
- [x] 創建測試任務狀態檢查清單
- [ ] 添加測試運行說明
- [ ] 記錄測試結果和發現的問題

## 注意事項
- 測試不應該實際發送郵件
- 測試應該模擬所有外部依賴
- 測試應該獨立運行，不依賴外部服務

## 測試運行說明
要運行測試，請在命令行中執行以下命令：

```bash
cd /Users/<USER>/Desktop/ml-workflow-cloud-functions/product_check
pytest tests/test_main.py -v
```

如果需要安裝 pytest，請執行：

```bash
pip install pytest
```

## 測試內容說明

1. **有異常商品時發送郵件的測試**
   - 模擬 df_result 包含異常商品數據
   - 驗證郵件發送函數被調用

2. **沒有異常商品時不發送郵件的測試**
   - 模擬 df_result 為空（所有商品價格低於閾值）
   - 驗證郵件發送函數未被調用
