import functions_framework
import datetime
import requests
from google.auth.transport.requests import Request
from google.oauth2 import id_token
import logging
from pytz import timezone
import time
import aiohttp
import asyncio

@functions_framework.http
def deploy_ttd_common(request):
  
    logging.basicConfig(level=logging.INFO)

    target_date = datetime.datetime.now(timezone('Asia/Taipei')).strftime('%Y-%m-%d')

    # 获取默认服务账户的身份令牌
    audience = "https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/Deploy_ttd_common"
    token = id_token.fetch_id_token(Request(), audience)

    async def fetch(session, url, headers, data, i):
        async with session.post(url, headers=headers, json=data, timeout=10) as response:
            print(f"Posted {i}")
            return await response.text()

    async def main():
        async with aiohttp.ClientSession() as session:
            tasks = []
            url = "https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/Deploy_ttd_common"
            for i in range(1, 20):
                path = f"gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{target_date}/lta_remarketing_{target_date}_ttd_chunk_{i}.csv"
                print(f"Trigger URL with path: {path}")
                # 构建请求数据和headers
                data = {"path": path}
                headers = {"Authorization": f"Bearer {token}", "Content-Type": "application/json"}
                # 添加任务
                tasks.append(fetch(session, url, headers, data, i))
            responses = await asyncio.gather(*tasks)
            for response in responses:
                print(response)

    # 运行异步主函数
    asyncio.run(main())
        
    return "Success", 200