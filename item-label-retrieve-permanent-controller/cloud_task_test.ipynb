{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["# pip install google-cloud-tasks"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import requests\n", "import json\n", "import argparse\n", "import google.auth.transport.requests\n", "import google.oauth2.id_token\n", "from google.cloud import tasks_v2\n", "from google.protobuf import timestamp_pb2, duration_pb2\n", "import datetime\n", "import os"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["def create_cloud_task(project_id, location_id, queue_id, payload, target_uri=None, delay_seconds=0, service_account_email=None):\n", "    \"\"\"建立一個 Cloud Task 以觸發 HTTP 端點，並使用指定的服務帳號進行 OIDC 驗證。\n", "\n", "    Args:\n", "        project_id (str): Google Cloud 專案 ID。\n", "        location_id (str): Cloud Tasks 佇列所在的區域。\n", "        queue_id (str): Cloud Tasks 佇列名稱。\n", "        payload (dict): 要發送到目標 URI 的 JSON payload。\n", "        target_uri (str): Cloud Task 要觸發的目標 HTTP(S) URL。\n", "        delay_seconds (int): 任務延遲執行的秒數，預設為 0 (立即執行)。\n", "        service_account_email (str): 用於產生 OIDC token 的服務帳號 Email。\n", "                                      如果為 None，則不會設定 OIDC token (適用於允許未驗證的目標)。\n", "    \"\"\"\n", "    client = tasks_v2.CloudTasksClient()\n", "\n", "    # 建立完整的佇列名稱\n", "    parent = client.queue_path(project_id, location_id, queue_id)\n", "\n", "    # 建立基礎任務內容\n", "    task_dict = {\n", "        \"http_request\": {\n", "            \"http_method\": tasks_v2.HttpMethod.POST,\n", "            \"url\": target_uri,\n", "            \"headers\": {\"Content-Type\": \"application/json\"},\n", "            \"body\": json.dumps(payload).encode('utf-8'),\n", "        },\n", "        # 設定合理的 dispatch deadline (任務執行超時時間)\n", "        \"dispatch_deadline\": duration_pb2.Duration(seconds=1800) # 30 分鐘\n", "    }\n", "\n", "    # 如果提供了服務帳號，則設定 OIDC token 進行身份驗證\n", "    if service_account_email:\n", "        print(f\"為任務設定 OIDC token，使用服務帳號: {service_account_email}, 目標 audience: {target_uri}\")\n", "        task_dict[\"http_request\"][\"oidc_token\"] = {\n", "            \"service_account_email\": service_account_email,\n", "            \"audience\": target_uri\n", "        }\n", "    else:\n", "        print(f\"警告：未指定 service_account_email，將不使用 OIDC token 進行身份驗證。\")\n", "        print(f\"目標 {target_uri} 必須設定為允許未經驗證的調用。\")\n", "\n", "\n", "    # 如果需要延遲執行\n", "    if delay_seconds > 0:\n", "        now = datetime.datetime.utcnow()\n", "        schedule_time = now + datetime.timedelta(seconds=delay_seconds)\n", "        timestamp = timestamp_pb2.Timestamp()\n", "        timestamp.FromDatetime(schedule_time)\n", "        task_dict[\"schedule_time\"] = timestamp\n", "        print(f\"任務將被安排在 {schedule_time.isoformat()} UTC 執行 (延遲 {delay_seconds} 秒)。\")\n", "\n", "    try:\n", "        print(f\"準備發送任務到佇列: {parent}\")\n", "        print(f\"任務目標 URL: {target_uri}\")\n", "        # 將 dict 轉換為 Task 物件 (如果需要更精確的類型檢查)\n", "        # task_object = tasks_v2.Task(**task_dict) # 可能需要遞迴處理 nested dict\n", "        response = client.create_task(parent=parent, task=task_dict) # 直接傳遞 dict 通常也可以\n", "        print(f\"成功建立任務: {response.name}\")\n", "        return response.name\n", "    except Exception as e:\n", "        print(f\"錯誤：無法建立 Cloud Task: {e}\")\n", "        # 可以考慮引發異常或返回更詳細的錯誤資訊\n", "        raise # Reraise the exception after logging\n", "        # return None"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["將使用服務帳號 <EMAIL> 建立驗證過的任務。\n", "為任務設定 OIDC token，使用服務帳號: <EMAIL>, 目標 audience: https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/item-label-retrieve-permanent\n", "準備發送任務到佇列: projects/tagtoo-ml-workflow/locations/asia-east1/queues/item-label-retrieve-permanent\n", "任務目標 URL: https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/item-label-retrieve-permanent\n", "成功建立任務: projects/tagtoo-ml-workflow/locations/asia-east1/queues/item-label-retrieve-permanent/tasks/5005155209978831047\n", "\n", "--- 測試腳本執行完畢 ---\n"]}], "source": ["# --- 設定參數 ---\n", "project = 'tagtoo-ml-workflow'\n", "location = 'asia-east1'\n", "queue = 'item-label-retrieve-permanent'\n", "target_function_url = 'https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/item-label-retrieve-permanent'\n", "# !! 重要：確認這個服務帳號在目標函數上有 Invoker 權限 !!\n", "invoker_service_account = '<EMAIL>'\n", "task_delay_seconds = 0 # 立即執行\n", "file_path = \"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/********/************.parquet\"\n", "\n", "# 定義 Payload\n", "task_payload = {\n", "    \"page_name\": \"prod\", # 修改 page_name 以便識別來源\n", "    \"save_gate\": 1,\n", "    \"upload_gate\": 0,\n", "    \"parallel_gate\": 0,\n", "    \"user_item_gcs_path\": file_path\n", "}\n", "\n", "# --- 執行建立任務 ---\n", "if not target_function_url:\n", "    print(\"錯誤：必須提供 target_function_url 才能發送 Cloud Task。\")\n", "elif not invoker_service_account:\n", "    print(f\"警告：未指定 invoker_service_account。將嘗試匿名調用 {target_function_url}。\")\n", "    # 如果要匿名調用，確保函數允許未驗證\n", "    try:\n", "        create_cloud_task(\n", "            project_id=project,\n", "            location_id=location,\n", "            queue_id=queue,\n", "            payload=task_payload,\n", "            target_uri=target_function_url,\n", "            delay_seconds=task_delay_seconds,\n", "            service_account_email=None # 明確設為 None\n", "        )\n", "    except Exception as e:\n", "        print(f\"建立匿名任務時發生錯誤: {e}\")\n", "else:\n", "    print(f\"將使用服務帳號 {invoker_service_account} 建立驗證過的任務。\")\n", "    try:\n", "        create_cloud_task(\n", "            project_id=project,\n", "            location_id=location,\n", "            queue_id=queue,\n", "            payload=task_payload,\n", "            target_uri=target_function_url,\n", "            delay_seconds=task_delay_seconds,\n", "            service_account_email=invoker_service_account # 傳遞服務帳號\n", "        )\n", "    except Exception as e:\n", "         print(f\"建立驗證任務時發生錯誤: {e}\")\n", "\n", "\n", "print(\"\\n--- 測試腳本執行完畢 ---\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tensorflow", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}