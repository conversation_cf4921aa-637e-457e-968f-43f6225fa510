from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import pandas as pd
import time
import random
import logging

def read_audience_packages(credentials, page_name: str, max_retries=5, initial_delay=1) -> pd.DataFrame:
    """讀取 Taxonomy 資料並返回 DataFrame，包含重試邏輯

    Args:
        credentials: Google API 憑證
        page_name: 工作表名稱
        max_retries: 最大重試次數，預設 5 次
        initial_delay: 初始延遲秒數，預設 1 秒
    
    Returns:
        DataFrame: 包含所有資料的 DataFrame，若無資料則返回空 DataFrame
        
    Raises:
        Exception: 重試耗盡仍無法獲取資料時拋出
    """
    spreadsheet_id = '1VZxkSc6XsPSKfFPP8M9V8IyXUh4GxGpc6lq63h1Veco'
    columns = ['name', 'description', 'path', 'item_source', 'item_source_id', 
               'segment_id', 'or_keywords', 'and_keywords', 'user_source_id']
    cell_range = 'A2:I'
    
    service = build('sheets', 'v4', credentials=credentials)
    sheet = service.spreadsheets()
    
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            result = sheet.values().get(
                spreadsheetId=spreadsheet_id, 
                range=f"{page_name}!{cell_range}"
            ).execute()
            
            values = result.get('values', [])
            
            if not values:
                logging.info("未找到資料。")
                return pd.DataFrame()
            
            df = pd.DataFrame(values, columns=columns)
            return df
            
        except HttpError as error:
            retry_count += 1
            
            # 判斷是否為可重試的錯誤（服務不可用、伺服器錯誤或速率限制）
            if error.resp.status in [429, 500, 503, 502]:
                # 計算指數級退避時間（帶隨機抖動）
                delay = (initial_delay * (2 ** (retry_count - 1))) + (random.random())
                logging.warning(
                    f"Google Sheets API 暫時不可用 (狀態碼: {error.resp.status}). "
                    f"將在 {delay:.2f} 秒後重試... (嘗試 {retry_count}/{max_retries})"
                )
                time.sleep(delay)
            else:
                # 其他類型的 HTTP 錯誤，直接拋出
                logging.error(f"Google Sheets API 發生錯誤: {error}")
                raise
    
    # 重試次數耗盡
    error_msg = f"嘗試 {max_retries} 次後仍無法從 Google Sheets 獲取資料"
    logging.error(error_msg)
    raise Exception(error_msg)