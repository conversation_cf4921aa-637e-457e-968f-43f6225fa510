LABEL_QUERY = """
SELECT 
  id,
  name,
  ec_name,
  TO_JSON_STRING(structured_labels) AS structured_labels_str,
  ec_id,
  industry_id,
  industry_name
FROM `tagtoo-ml-workflow.data_prod.item_labels`
"""

# USER_QUERY = """
# SELECT
#   DISTINCT permanent,
#   items.name,
#   label.ec_id
# FROM
#   `tagtoo-tracking.event_prod.tagtoo_event`,
#   UNNEST(event.items) items
# JOIN
#   `tagtoo-ml-workflow.data_prod.item_labels` label
# ON
#   items.name = label.name
# WHERE
#   DATE(event_time, 'Asia/Taipei') = DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY)
#   AND event.name = 'view_item'
# """

# **2025-04-07 改成過去30天內有事件的商品，並且昨天有進站的使用者**
USER_QUERY = """
WITH
  users_active_yesterday AS (
    -- 找出昨天有進站的使用者
  SELECT
    DISTINCT permanent
  FROM
    `tagtoo-tracking.event_prod.tagtoo_event`
  WHERE
    DATE(event_time, 'Asia/Taipei') = DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 1 DAY) ),
  item_views_last_30days AS (
    -- 找出過去 30 天內瀏覽過商品的使用者和商品
  SELECT
    DISTINCT permanent,
    items.name,
    ec_id
  FROM
    `tagtoo-tracking.event_prod.tagtoo_event`,
    UNNEST(event.items) items
  WHERE
    DATE(event_time, 'Asia/Taipei') BETWEEN DATE_SUB(CURRENT_DATE('Asia/Taipei'), INTERVAL 30 DAY)
    AND CURRENT_DATE('Asia/Taipei')
    AND event.name IN ('view_item',
      'add_to_cart',
      'purchase') )
  -- 合併兩個條件：昨天有進站 AND 過去 30 天有瀏覽過商品
SELECT
  item_views.permanent,
  item_views.name,
  item_views.ec_id
FROM
  item_views_last_30days item_views
JOIN
  users_active_yesterday active_users
ON
  item_views.permanent = active_users.permanent
"""