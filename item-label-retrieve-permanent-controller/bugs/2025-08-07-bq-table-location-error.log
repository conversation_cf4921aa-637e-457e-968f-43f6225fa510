Exception on / [POST]
Traceback (most recent call last):
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/flask/app.py", line 1511, in wsgi_app
    response = self.full_dispatch_request()
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/flask/app.py", line 919, in full_dispatch_request
    rv = self.handle_user_exception(e)
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/flask/app.py", line 917, in full_dispatch_request
    rv = self.dispatch_request()
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/flask/app.py", line 902, in dispatch_request
    return self.ensure_sync(self.view_functions[rule.endpoint])(**view_args)  # type: ignore[no-any-return]
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/functions_framework/execution_id.py", line 106, in wrapper
    return view_function(*args, **kwargs)
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/functions_framework/__init__.py", line 142, in view_func
    return function(request._get_current_object())
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/functions_framework/__init__.py", line 121, in wrapper
    return func(*args, **kwargs)
  File "/workspace/main.py", line 339, in main
    save_query_results(
  File "/workspace/main.py", line 176, in save_query_results
    query_job.result()  # 等待查詢完成
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/google/cloud/bigquery/job/query.py", line 1681, in result
    while not is_job_done():
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/google/api_core/retry/retry_unary.py", line 293, in retry_wrapped_func
    return retry_target(
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/google/api_core/retry/retry_unary.py", line 153, in retry_target
    _retry_error_helper(
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/google/api_core/retry/retry_base.py", line 212, in _retry_error_helper
    raise final_exc from source_exc
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/google/api_core/retry/retry_unary.py", line 144, in retry_target
    result = target()
  File "/layers/google.python.pip/pip/lib/python3.10/site-packages/google/cloud/bigquery/job/query.py", line 1630, in is_job_done
    raise job_failed_exception
google.api_core.exceptions.NotFound: 404 Not found: Dataset tagtoo-tracking:event_prod was not found in location US; reason: notFound, message: Not found: Dataset tagtoo-tracking:event_prod was not found in location US

Location: US
Job ID: d87a3687-3fc8-4450-ba71-5893721f63cb
