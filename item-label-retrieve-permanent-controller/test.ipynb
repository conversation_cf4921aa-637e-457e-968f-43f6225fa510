{"cells": [{"cell_type": "code", "execution_count": 1, "id": "c6f6f21d", "metadata": {}, "outputs": [], "source": ["from dask import dataframe as dd\n", "import pandas as pd"]}, {"cell_type": "code", "execution_count": null, "id": "96221652", "metadata": {}, "outputs": [], "source": ["path = \"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/20250423/*.parquet\"\n", "df = dd.read_parquet(path, engine=\"pyarrow\", assume_missing=True).compute()"]}, {"cell_type": "code", "execution_count": 3, "id": "fceabc0c", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>permanent</th>\n", "      <th>name</th>\n", "      <th>ec_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>e9254620a1e7c6109ec91a003aab3072</td>\n", "      <td>19809 芯生悠肌能精華 - 補充包 (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>a683c842f14468639609bcc8f04f79b2</td>\n", "      <td>19817 澄淨卸妝露 - 補充包 (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>072fef3dd18091b90371c34fa1e50e0d</td>\n", "      <td>19861 極光悠逆時化妝水 - 瓶裝 (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>4fec09abe4a0f08775d1f7aaf9907438</td>\n", "      <td>ORBIS棉花糖手提袋</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>4e7be4189d39ce17135cb38612f8d220</td>\n", "      <td>ORBIS棉花糖手提袋</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>258265</th>\n", "      <td>9c3d1fca5690cf01562590d682d9e29d</td>\n", "      <td>【現貨】TERRA 戰神黑瑪卡2.0</td>\n", "      <td>3860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>258266</th>\n", "      <td>e940eb6d48e0c6b44eec731d9d8b7b83</td>\n", "      <td>【現貨】TERRA 戰神黑瑪卡2.0</td>\n", "      <td>3860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>258267</th>\n", "      <td>65d54ed57de52b28a6238898c834adc8</td>\n", "      <td>【現貨】TERRA 戰神黑瑪卡2.0</td>\n", "      <td>3860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>258268</th>\n", "      <td>adedf6c107835ca93d79e5783be59e36</td>\n", "      <td>【現貨】TERRA 戰神黑瑪卡2.0</td>\n", "      <td>3860</td>\n", "    </tr>\n", "    <tr>\n", "      <th>258269</th>\n", "      <td>1928e838793c6504e2dff290584fae79</td>\n", "      <td>黃金抓餅（葷）</td>\n", "      <td>3873</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>21938590 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                               permanent                                name  \\\n", "0       e9254620a1e7c6109ec91a003aab3072  19809 芯生悠肌能精華 - 補充包 (期限品特賣-恕無提供退貨)   \n", "1       a683c842f14468639609bcc8f04f79b2    19817 澄淨卸妝露 - 補充包 (期限品特賣-恕無提供退貨)   \n", "2       072fef3dd18091b90371c34fa1e50e0d  19861 極光悠逆時化妝水 - 瓶裝 (期限品特賣-恕無提供退貨)   \n", "3       4fec09abe4a0f08775d1f7aaf9907438                         ORBIS棉花糖手提袋   \n", "4       4e7be4189d39ce17135cb38612f8d220                         ORBIS棉花糖手提袋   \n", "...                                  ...                                 ...   \n", "258265  9c3d1fca5690cf01562590d682d9e29d                  【現貨】TERRA 戰神黑瑪卡2.0   \n", "258266  e940eb6d48e0c6b44eec731d9d8b7b83                  【現貨】TERRA 戰神黑瑪卡2.0   \n", "258267  65d54ed57de52b28a6238898c834adc8                  【現貨】TERRA 戰神黑瑪卡2.0   \n", "258268  adedf6c107835ca93d79e5783be59e36                  【現貨】TERRA 戰神黑瑪卡2.0   \n", "258269  1928e838793c6504e2dff290584fae79                             黃金抓餅（葷）   \n", "\n", "        ec_id  \n", "0         107  \n", "1         107  \n", "2         107  \n", "3         107  \n", "4         107  \n", "...       ...  \n", "258265   3860  \n", "258266   3860  \n", "258267   3860  \n", "258268   3860  \n", "258269   3873  \n", "\n", "[21938590 rows x 3 columns]"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["df"]}, {"cell_type": "code", "execution_count": 5, "id": "d6a1c5c3", "metadata": {}, "outputs": [{"data": {"text/plain": ["ec_id\n", "3542    17226268\n", "2851     1779852\n", "715       527773\n", "1878      362561\n", "1626      226565\n", "          ...   \n", "2930           2\n", "1463           1\n", "1604           1\n", "3025           1\n", "3134           1\n", "Name: count, Length: 151, dtype: int64"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["df['ec_id'].value_counts()"]}, {"cell_type": "code", "execution_count": 6, "id": "5a596c9f", "metadata": {}, "outputs": [], "source": ["from utils import read_audience_packages\n", "from google.oauth2 import service_account\n", "from config import GOO<PERSON>LE_APPLICATION_CREDENTIALS_PATH\n", "from google_storage import GoogleStorageIO\n", "\n", "page_name = \"prod\"\n", "\n", "def get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH):\n", "    google_credentials = service_account.Credentials.from_service_account_file(GOOGLE_APPLICATION_CREDENTIALS_PATH)\n", "    project_id = google_credentials.project_id\n", "    return google_credentials, project_id\n", "\n", "# 初始化客戶端\n", "google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)\n", "GSIO = GoogleStorageIO(credentials=google_credentials)\n", "\n", "# 5. 根據關鍵字大表中的所有 segments 與其關鍵字規則，篩選出符合條件的商品，並查找瀏覽過商品的 permanent\n", "page_name = page_name or \"prod\"\n", "audience_packages_df = read_audience_packages(google_credentials, page_name)"]}, {"cell_type": "code", "execution_count": null, "id": "c073bc8c", "metadata": {}, "outputs": [], "source": ["target_user_source_ids = audience_packages_df['user_source_id'].dropna().unique().astype(int).tolist()"]}, {"cell_type": "code", "execution_count": 20, "id": "218a5edf", "metadata": {}, "outputs": [], "source": ["target_user_source_df = df[df['ec_id'].isin(target_user_source_ids)]"]}, {"cell_type": "code", "execution_count": 30, "id": "a6834194", "metadata": {}, "outputs": [{"data": {"text/plain": ["[2808, 183, 1465, 107]"]}, "execution_count": 30, "metadata": {}, "output_type": "execute_result"}], "source": ["target_user_source_ids"]}, {"cell_type": "code", "execution_count": 35, "id": "a521a44f", "metadata": {}, "outputs": [], "source": ["from datetime import datetime\n", "from pytz import timezone\n", "import os"]}, {"cell_type": "code", "execution_count": null, "id": "51ae6990", "metadata": {}, "outputs": [], "source": ["\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_source_id/{current_date}\""]}, {"cell_type": "code", "execution_count": 44, "id": "8a33d651", "metadata": {}, "outputs": [], "source": ["import logging\n", "from datetime import datetime\n", "from pytz import timezone\n", "import os\n", "from typing import Union, Optional\n", "import pandas as pd\n", "\n", "\n", "def save_user_permanents_to_gcs(\n", "    ec_id: int,\n", "    df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "    gcs_base_path: str,\n", "    storage_client: Optional[object] = None,\n", "    *,\n", "    timezone_name: str = 'Asia/Taipei'\n", ") -> Union[str, None]:\n", "    \"\"\"將特定電商的用戶 permanent IDs 儲存至 Google Cloud Storage。\n", "\n", "    Args:\n", "        ec_id: 電商 ID\n", "        df: 包含 permanent 欄位的 DataFrame\n", "        gcs_base_path: GCS 基礎路徑\n", "        storage_client: Google Storage IO 客戶端實例\n", "        timezone_name: 時區名稱，預設為 'Asia/Taipei'\n", "\n", "    Returns:\n", "        str: 成功時返回 GCS URI\n", "        None: 當輸入 DataFrame 為空時返回\n", "\n", "    Raises:\n", "        ValueError: 當必要參數未提供或格式錯誤時\n", "        IOError: 當檔案操作失敗時\n", "    \"\"\"\n", "    if storage_client is None:\n", "        raise ValueError(\"GoogleStorageIO is required\")\n", "    \n", "    if df.empty:\n", "        return None\n", "\n", "    # 準備資料\n", "    permanent_ids = (\n", "        pd.DataFrame(df[df['ec_id'] == ec_id]['permanent']\n", "                    .drop_duplicates()\n", "                    .reset_index(drop=True))\n", "    )\n", "    \n", "    if permanent_ids.empty:\n", "        logging.warning(f\"{ec_id} 沒有找到對應的 permanent IDs\")\n", "        return None\n", "\n", "    # 設定檔案路徑\n", "    current_date = datetime.now(timezone(timezone_name)).strftime(\"%Y%m%d\")\n", "    local_path = f'/tmp/{ec_id}.parquet'\n", "    gcs_uri = f'{gcs_base_path}/{current_date}/{ec_id}.parquet.snappy'\n", "\n", "    try:\n", "        # 儲存到本地\n", "        permanent_ids.to_parquet(\n", "            local_path,\n", "            engine='pyarrow',\n", "            compression='snappy',\n", "            index=False\n", "        )\n", "\n", "        # 上傳到 GCS\n", "        storage_client.upload_file(localpath=local_path, gsuri=gcs_uri)\n", "        \n", "        return gcs_uri\n", "\n", "    except Exception as e:\n", "        raise IOError(f\"Failed to save permanents for EC {ec_id}: {str(e)}\")\n", "    \n", "    finally:\n", "        # 清理暫存檔案\n", "        if os.path.exists(local_path):\n", "            os.remove(local_path)"]}, {"cell_type": "code", "execution_count": 45, "id": "4f8236aa", "metadata": {}, "outputs": [], "source": ["for ec_id in target_user_source_ids:\n", "    save_user_permanents_to_gcs(\n", "        ec_id = ec_id,\n", "        df=target_user_source_df,\n", "        gcs_base_path=\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids\",\n", "        storage_client=GSIO\n", "    )"]}, {"cell_type": "code", "execution_count": null, "id": "06170959", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "id": "f61b62b3", "metadata": {}, "outputs": [{"data": {"text/plain": ["0       7480753b73199753ede2ce64c6ae2117\n", "1       6fb4d830079ad7e693285f9afdaf159a\n", "2       8c4292dd61a361cb5b20a9188b5c5c7a\n", "3       2e26ae34a899480d1ef6b9c6d9ef1ce3\n", "4       94490625245f9941fdf7308834bf6452\n", "                      ...               \n", "3195    7c50ccfbc71eb78f0248ede0c6ef4309\n", "3196    28e5eefae707f251521fbfa6f08606bf\n", "3197    6cfece863b1b66cd55c565955a94c3d0\n", "3198    41bc2e155bdda90914d5576a130d79cb\n", "3199    642674e94bc7796d567594487fd5d1ab\n", "Name: permanent, Length: 3200, dtype: string"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["ec_id = 2808\n", "\n", "target_user_source_df = target_user_source_df[target_user_source_df['ec_id']==ec_id]['permanent'].drop_duplicates().reset_index(drop=True)\n", "localpath = f'/tmp/{ec_id}.parquet'\n", "target_user_source_df.to_parquet(\n", "    localpath,\n", "    engine='pyarrow',\n", "    compression='snappy',  # 好的壓縮比和讀取速度平衡\n", "    index=False\n", ")\n", "\n", "current_date = datetime.now(timezone('Asia/Taipei')).strftime(\"%Y%m%d\")\n", "gsuri = gcs_prefix + f'/{ec_id}.parquet'\n", "\n", "GSIO.upload_file(localpath=localpath, gsuri=)\n", "os.remove(localpath)"]}, {"cell_type": "code", "execution_count": 33, "id": "57c8e4f2", "metadata": {}, "outputs": [{"data": {"text/plain": ["0       575183d4d437186927e2abd882905fab\n", "1       3414d002081bb82420512b42209f774e\n", "2       f85edb171df6040de157013d7fc56225\n", "3       2a4d727e4c5aed631b3a9ebe4e0a2301\n", "4       9ce74246ed5cd7c38fcfb05c1e0d6a1b\n", "                      ...               \n", "5646    b8f3906217d01cb0ee70a1a479c82b1a\n", "5647    8df9ac43af8cab3827d25a6e43afdcbd\n", "5648    fede6b803e2613ac4e723fc6b4703d6a\n", "5649    3b323c0ab1a8eebca76f10da3fb7ec13\n", "5650    26a5a063ed830a4a2cae836d6931ec04\n", "Name: permanent, Length: 5651, dtype: string"]}, "execution_count": 33, "metadata": {}, "output_type": "execute_result"}], "source": ["target_user_source_df[target_user_source_df['ec_id']==1465]['permanent'].drop_duplicates().reset_index(drop=True)"]}, {"cell_type": "code", "execution_count": null, "id": "d0e162a8", "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 48, "id": "6229a3b5", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>description</th>\n", "      <th>path</th>\n", "      <th>item_source</th>\n", "      <th>item_source_id</th>\n", "      <th>segment_id</th>\n", "      <th>or_keywords</th>\n", "      <th>and_keywords</th>\n", "      <th>user_source_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>169</th>\n", "      <td>運動健身愛好者</td>\n", "      <td>對運動健身有興趣的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 2808 &gt; 運動健身愛好者</td>\n", "      <td>Mars</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_2808_r_024</td>\n", "      <td>健身, 運動, 重量訓練, 有氧運動, 跑步, 騎行, 體能, 營養補給, 蛋白粉, 瑜伽墊...</td>\n", "      <td></td>\n", "      <td>2808</td>\n", "    </tr>\n", "    <tr>\n", "      <th>170</th>\n", "      <td>健康與體態管理族群</td>\n", "      <td>對健康維持體態有興趣的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 2808 &gt; 健康與體態管理族群</td>\n", "      <td>Mars</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_2808_r_025</td>\n", "      <td>體態, 減重, 減脂, 上班族, 家庭</td>\n", "      <td></td>\n", "      <td>2808</td>\n", "    </tr>\n", "    <tr>\n", "      <th>171</th>\n", "      <td>戶外運動＆冒險玩家</td>\n", "      <td>對戶外運動有興趣的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 2808 &gt; 戶外運動＆冒險玩家</td>\n", "      <td>Mars</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_2808_r_026</td>\n", "      <td>登山, 攀岩, 越野, 極限運動, 戶外探險, 戶外產品, 戶外裝備, 機能型補給品, 能量...</td>\n", "      <td></td>\n", "      <td>2808</td>\n", "    </tr>\n", "    <tr>\n", "      <th>199</th>\n", "      <td>街頭風格愛好者</td>\n", "      <td>近期曾造訪 Caco 網站，且在站外關注街頭風商品或內容的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 183 &gt; 街頭潮流愛好者（站外）</td>\n", "      <td>Caco</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_183_r_023</td>\n", "      <td>街頭風格, 街頭文化, 街頭時尚, 拼接, 拼色, 美式風格, 寬褲, 工裝, 滑板, 青年...</td>\n", "      <td></td>\n", "      <td>183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>舒適休閒風格愛好者</td>\n", "      <td>近期曾造訪 Caco 網站，且在站外關注舒適休閒風商品的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 183 &gt; 舒適休閒風格愛好者（站外）</td>\n", "      <td>Caco</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_183_r_024</td>\n", "      <td>休閒, 舒適, 日常</td>\n", "      <td></td>\n", "      <td>183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>IP/動漫文化粉絲</td>\n", "      <td>近期曾造訪 Caco 網站，且在站外關注 IP 或動漫商品的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 183 &gt;  IP/動漫文化粉絲（站外）</td>\n", "      <td>Caco</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_183_r_025</td>\n", "      <td>動漫, 卡通, 聯名, 漫畫, 動畫</td>\n", "      <td></td>\n", "      <td>183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>戶外運動愛好者</td>\n", "      <td>近期曾造訪 Caco 網站，且在站外關注戶外運動相關商品或資訊的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 183 &gt;  戶外運動愛好者（站外）</td>\n", "      <td>Caco</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_183_r_026</td>\n", "      <td>戶外運動, 戶外活動, 登山, 健行, 露營, 防風</td>\n", "      <td></td>\n", "      <td>183</td>\n", "    </tr>\n", "    <tr>\n", "      <th>206</th>\n", "      <td>海灘度假與運動愛好者</td>\n", "      <td>近期曾造訪 Wave Shine 網站，且在站外關注海灘度假或運動相關內容的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 1465 &gt; 海灘度假與運動愛好者（站外）</td>\n", "      <td>Wave Shine</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_1465_r_012</td>\n", "      <td>度假,  渡假, 沙灘, 海灘, 海島, 海景, 海岸, 跳島, 浮潛, 水上樂園, 潛水,...</td>\n", "      <td></td>\n", "      <td>1465</td>\n", "    </tr>\n", "    <tr>\n", "      <th>207</th>\n", "      <td>日常舒適與機能需求者</td>\n", "      <td>近期曾造訪 Wave Shine 網站，且在站外關注舒適或機能性商品的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 1465 &gt; 日常舒適與機能需求者（站外）</td>\n", "      <td>Wave Shine</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_1465_r_013</td>\n", "      <td>休閒, 舒適, 排汗, 發熱, 吸濕, 透氣</td>\n", "      <td></td>\n", "      <td>1465</td>\n", "    </tr>\n", "    <tr>\n", "      <th>208</th>\n", "      <td>潮流風格與品牌聯名支持者</td>\n", "      <td>近期曾造訪 Wave Shine 網站，且在站外關注潮流風格或聯名商品的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 1465 &gt;  潮流風格與品牌聯名支持者（站外）</td>\n", "      <td>Wave Shine</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_1465_r_014</td>\n", "      <td>動漫, 卡通, 聯名, 漫畫, 動畫, 街頭風格, 街頭文化, 街頭時尚, 拼接, 拼色, ...</td>\n", "      <td></td>\n", "      <td>1465</td>\n", "    </tr>\n", "    <tr>\n", "      <th>209</th>\n", "      <td>Orbis 保養入門者</td>\n", "      <td>近期曾造訪Orbis網站，在站外關注入門保養商品或議題的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 107 &gt;  Orbis 保養入門者</td>\n", "      <td>Orbis</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_107_c_032</td>\n", "      <td>護膚, 溫和配方, 無添加, 基礎保養, 簡單保養, 保養入門, 入門保養, 隔離霜, 卸妝...</td>\n", "      <td></td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>210</th>\n", "      <td>Orbis 理性消費者</td>\n", "      <td>近期曾造訪Orbis網站，在站外關注促銷資訊，也會留意投資或財務議題</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 107 &gt;  Orbis 理性消費者</td>\n", "      <td>Orbis</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_107_c_033</td>\n", "      <td>股票, 促銷, 優惠, 特價, 財經, 台股, 美股, 平價, 中低價, 低價, 配息, 殖...</td>\n", "      <td></td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>211</th>\n", "      <td>Orbis 敏感肌消費者</td>\n", "      <td>近期曾造訪Orbis網站，在站外關注敏感肌保養商品及議題的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 107 &gt;  Orbis 敏感肌消費者</td>\n", "      <td>Orbis</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_107_c_034</td>\n", "      <td>天然成分, 敏感肌, 低敏, 無刺激配方, 天然成分, 無添加, 刺激成分, 敏肌, 舒緩肌...</td>\n", "      <td></td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>212</th>\n", "      <td>Orbis 成熟保養需求者</td>\n", "      <td>近期曾造訪Orbis網站，在站外關注成熟保養商品及議題的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 107 &gt;  Orbis 成熟保養需求者</td>\n", "      <td>Orbis</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_107_c_035</td>\n", "      <td>老化, 緊緻, 奢華護膚, 延緩衰老, 抗衰老, 抗皺, 皺紋, 暗沈, 細紋, 法令紋, ...</td>\n", "      <td></td>\n", "      <td>107</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["              name                                description  \\\n", "169        運動健身愛好者                               對運動健身有興趣的使用者   \n", "170      健康與體態管理族群                             對健康維持體態有興趣的使用者   \n", "171      戶外運動＆冒險玩家                               對戶外運動有興趣的使用者   \n", "199        街頭風格愛好者           近期曾造訪 Caco 網站，且在站外關注街頭風商品或內容的使用者   \n", "200      舒適休閒風格愛好者            近期曾造訪 Caco 網站，且在站外關注舒適休閒風商品的使用者   \n", "201      IP/動漫文化粉絲          近期曾造訪 Caco 網站，且在站外關注 IP 或動漫商品的使用者   \n", "202        戶外運動愛好者        近期曾造訪 Caco 網站，且在站外關注戶外運動相關商品或資訊的使用者   \n", "206     海灘度假與運動愛好者  近期曾造訪 Wave Shine 網站，且在站外關注海灘度假或運動相關內容的使用者   \n", "207     日常舒適與機能需求者     近期曾造訪 Wave Shine 網站，且在站外關注舒適或機能性商品的使用者   \n", "208   潮流風格與品牌聯名支持者    近期曾造訪 Wave Shine 網站，且在站外關注潮流風格或聯名商品的使用者   \n", "209    Orbis 保養入門者            近期曾造訪Orbis網站，在站外關注入門保養商品或議題的使用者   \n", "210    Orbis 理性消費者         近期曾造訪Orbis網站，在站外關注促銷資訊，也會留意投資或財務議題   \n", "211   Orbis 敏感肌消費者           近期曾造訪Orbis網站，在站外關注敏感肌保養商品及議題的使用者   \n", "212  Orbis 成熟保養需求者            近期曾造訪Orbis網站，在站外關注成熟保養商品及議題的使用者   \n", "\n", "                                             path item_source item_source_id  \\\n", "169            根目錄 > 塔圖科技 > 消費意圖 > 2808 > 運動健身愛好者        Mars           9999   \n", "170          根目錄 > 塔圖科技 > 消費意圖 > 2808 > 健康與體態管理族群        Mars           9999   \n", "171          根目錄 > 塔圖科技 > 消費意圖 > 2808 > 戶外運動＆冒險玩家        Mars           9999   \n", "199         根目錄 > 塔圖科技 > 消費意圖 > 183 > 街頭潮流愛好者（站外）        Caco           9999   \n", "200       根目錄 > 塔圖科技 > 消費意圖 > 183 > 舒適休閒風格愛好者（站外）        Caco           9999   \n", "201      根目錄 > 塔圖科技 > 消費意圖 > 183 >  IP/動漫文化粉絲（站外）        Caco           9999   \n", "202        根目錄 > 塔圖科技 > 消費意圖 > 183 >  戶外運動愛好者（站外）        Caco           9999   \n", "206     根目錄 > 塔圖科技 > 消費意圖 > 1465 > 海灘度假與運動愛好者（站外）  Wave Shine           9999   \n", "207     根目錄 > 塔圖科技 > 消費意圖 > 1465 > 日常舒適與機能需求者（站外）  Wave Shine           9999   \n", "208  根目錄 > 塔圖科技 > 消費意圖 > 1465 >  潮流風格與品牌聯名支持者（站外）  Wave Shine           9999   \n", "209        根目錄 > 塔圖科技 > 消費意圖 > 107 >  Orbis 保養入門者       Orbis           9999   \n", "210        根目錄 > 塔圖科技 > 消費意圖 > 107 >  Orbis 理性消費者       Orbis           9999   \n", "211       根目錄 > 塔圖科技 > 消費意圖 > 107 >  Orbis 敏感肌消費者       Orbis           9999   \n", "212      根目錄 > 塔圖科技 > 消費意圖 > 107 >  Orbis 成熟保養需求者       Orbis           9999   \n", "\n", "               segment_id                                        or_keywords  \\\n", "169  tm:c_9999_2808_r_024  健身, 運動, 重量訓練, 有氧運動, 跑步, 騎行, 體能, 營養補給, 蛋白粉, 瑜伽墊...   \n", "170  tm:c_9999_2808_r_025                                體態, 減重, 減脂, 上班族, 家庭   \n", "171  tm:c_9999_2808_r_026  登山, 攀岩, 越野, 極限運動, 戶外探險, 戶外產品, 戶外裝備, 機能型補給品, 能量...   \n", "199   tm:c_9999_183_r_023  街頭風格, 街頭文化, 街頭時尚, 拼接, 拼色, 美式風格, 寬褲, 工裝, 滑板, 青年...   \n", "200   tm:c_9999_183_r_024                                         休閒, 舒適, 日常   \n", "201   tm:c_9999_183_r_025                                 動漫, 卡通, 聯名, 漫畫, 動畫   \n", "202   tm:c_9999_183_r_026                         戶外運動, 戶外活動, 登山, 健行, 露營, 防風   \n", "206  tm:c_9999_1465_r_012  度假,  渡假, 沙灘, 海灘, 海島, 海景, 海岸, 跳島, 浮潛, 水上樂園, 潛水,...   \n", "207  tm:c_9999_1465_r_013                             休閒, 舒適, 排汗, 發熱, 吸濕, 透氣   \n", "208  tm:c_9999_1465_r_014  動漫, 卡通, 聯名, 漫畫, 動畫, 街頭風格, 街頭文化, 街頭時尚, 拼接, 拼色, ...   \n", "209   tm:c_9999_107_c_032  護膚, 溫和配方, 無添加, 基礎保養, 簡單保養, 保養入門, 入門保養, 隔離霜, 卸妝...   \n", "210   tm:c_9999_107_c_033  股票, 促銷, 優惠, 特價, 財經, 台股, 美股, 平價, 中低價, 低價, 配息, 殖...   \n", "211   tm:c_9999_107_c_034  天然成分, 敏感肌, 低敏, 無刺激配方, 天然成分, 無添加, 刺激成分, 敏肌, 舒緩肌...   \n", "212   tm:c_9999_107_c_035  老化, 緊緻, 奢華護膚, 延緩衰老, 抗衰老, 抗皺, 皺紋, 暗沈, 細紋, 法令紋, ...   \n", "\n", "    and_keywords user_source_id  \n", "169                        2808  \n", "170                        2808  \n", "171                        2808  \n", "199                         183  \n", "200                         183  \n", "201                         183  \n", "202                         183  \n", "206                        1465  \n", "207                        1465  \n", "208                        1465  \n", "209                         107  \n", "210                         107  \n", "211                         107  \n", "212                         107  "]}, "execution_count": 48, "metadata": {}, "output_type": "execute_result"}], "source": ["audience_packages_df[~audience_packages_df['user_source_id'].isnull()]"]}, {"cell_type": "code", "execution_count": 59, "id": "8f4df3f9", "metadata": {}, "outputs": [], "source": ["a = {'seg_1': ['a']}"]}, {"cell_type": "code", "execution_count": 60, "id": "b88c83d4", "metadata": {}, "outputs": [], "source": ["new_a = {'seg_1': ['a', 'b', 'c'], 'seg_2': ['a', 'b']}"]}, {"cell_type": "code", "execution_count": 62, "id": "a2956adf", "metadata": {}, "outputs": [], "source": ["a.update(new_a)"]}, {"cell_type": "code", "execution_count": 63, "id": "c365cf03", "metadata": {}, "outputs": [{"data": {"text/plain": ["{'seg_1': ['a', 'b', 'c'], 'seg_2': ['a', 'b']}"]}, "execution_count": 63, "metadata": {}, "output_type": "execute_result"}], "source": ["a"]}, {"cell_type": "code", "execution_count": null, "id": "43883fc9", "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tensorflow", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 5}