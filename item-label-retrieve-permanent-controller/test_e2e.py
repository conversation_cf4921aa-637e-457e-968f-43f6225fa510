#!/usr/bin/env python3
"""
E2E 測試腳本：模擬 Cloud Functions 的 curl 請求
測試 BigQuery 區域從 US 更改為 asia-east1 的修復
"""

import json
import logging
import sys
import os
from flask import Flask, Request

# 添加當前目錄到 Python 路徑
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from main import main, setup_local_logging


def test_bigquery_region_fix():
    """測試 BigQuery 區域修復功能"""
    
    # 設定日誌
    setup_local_logging()
    logging.info("=== 開始 BigQuery 區域修復 E2E 測試 ===")
    
    # 模擬您提供的 curl 請求參數，但將 query_gate 設為 1 來測試 BigQuery 功能
    test_payload = {
        "page_name": "prod",
        "upload_gate": "0", 
        "save_gate": "1",
        "query_gate": 1,  # 啟用 BigQuery 查詢來測試區域修復
        "parallel_gate": "0",
        "test_mode": "True"
    }
    
    logging.info(f"測試參數: {json.dumps(test_payload, indent=2)}")
    
    try:
        # 創建模擬的 Flask Request 對象
        app = Flask(__name__)
        with app.test_request_context(
            method='POST',
            json=test_payload,
            content_type='application/json'
        ):
            request = Request.from_values(
                json=test_payload,
                content_type='application/json'
            )
            
            # 執行主函數
            logging.info("執行 main() 函數...")
            response = main(request)
            
            # 檢查結果
            if isinstance(response, tuple):
                response_body, status_code = response
                logging.info(f"響應狀態碼: {status_code}")
                logging.info(f"響應內容: {response_body}")
                
                if status_code == 200:
                    logging.info("✅ 測試成功！BigQuery 區域配置正常工作")
                    return True
                else:
                    logging.error(f"❌ 測試失敗！狀態碼: {status_code}")
                    return False
            else:
                logging.info(f"響應: {response}")
                logging.info("✅ 測試成功！")
                return True
                
    except Exception as e:
        logging.error(f"❌ 測試失敗！錯誤: {str(e)}")
        logging.exception("詳細錯誤訊息:")
        return False


def test_configuration_values():
    """測試配置值是否正確設置"""
    logging.info("=== 檢查配置值 ===")
    
    from main import save_query_results
    import inspect
    
    # 檢查函數簽名中的預設值
    sig = inspect.signature(save_query_results)
    query_job_location_param = sig.parameters.get('query_job_location')
    
    if query_job_location_param:
        default_value = query_job_location_param.default
        logging.info(f"save_query_results 預設 query_job_location: {default_value}")
        
        if default_value == "asia-east1":
            logging.info("✅ 預設區域已正確設置為 asia-east1")
            return True
        else:
            logging.error(f"❌ 預設區域仍為: {default_value}，應該是 asia-east1")
            return False
    else:
        logging.error("❌ 找不到 query_job_location 參數")
        return False


if __name__ == "__main__":
    print("BigQuery 區域修復 E2E 測試")
    print("=" * 50)
    
    # 測試配置值
    config_ok = test_configuration_values()
    
    # 如果配置正確，執行完整測試
    if config_ok:
        test_ok = test_bigquery_region_fix()
        
        if test_ok:
            print("\n🎉 所有測試通過！")
            sys.exit(0)
        else:
            print("\n💥 測試失敗！")
            sys.exit(1)
    else:
        print("\n💥 配置檢查失敗！")
        sys.exit(1)