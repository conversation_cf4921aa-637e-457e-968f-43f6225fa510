#
# This file is autogenerated by pip-compile with Python 3.10
# by the following command:
#
#    pip-compile --output-file=requirements.txt requirements.in
#
aiohappyeyeballs==2.6.1
    # via aiohttp
aiohttp==3.11.18
    # via gcsfs
aiosignal==1.3.2
    # via aiohttp
async-timeout==5.0.1
    # via aiohttp
attrs==25.3.0
    # via aiohttp
blinker==1.9.0
    # via flask
cachetools==5.5.2
    # via google-auth
certifi==2025.1.31
    # via requests
charset-normalizer==3.4.1
    # via requests
click==8.1.8
    # via
    #   dask
    #   flask
    #   functions-framework
cloudevents==1.11.0
    # via functions-framework
cloudpickle==3.1.1
    # via dask
dask==2025.4.0
    # via -r requirements.in
decorator==5.2.1
    # via gcsfs
deprecated==1.2.18
    # via opentelemetry-api
deprecation==2.1.0
    # via cloudevents
exceptiongroup==1.2.2
    # via pytest
flask==3.1.0
    # via
    #   -r requirements.in
    #   functions-framework
frozenlist==1.6.0
    # via
    #   aiohttp
    #   aiosignal
fsspec==2025.3.2
    # via
    #   -r requirements.in
    #   dask
    #   gcsfs
functions-framework==3.8.2
    # via -r requirements.in
gcsfs==2025.3.2
    # via -r requirements.in
google-api-core[grpc]==2.24.2
    # via
    #   -r requirements.in
    #   google-api-python-client
    #   google-cloud-appengine-logging
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-logging
    #   google-cloud-storage
    #   google-cloud-tasks
google-api-python-client==2.167.0
    # via -r requirements.in
google-auth==2.39.0
    # via
    #   -r requirements.in
    #   gcsfs
    #   google-api-core
    #   google-api-python-client
    #   google-auth-httplib2
    #   google-auth-oauthlib
    #   google-cloud-appengine-logging
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-logging
    #   google-cloud-storage
    #   google-cloud-tasks
google-auth-httplib2==0.2.0
    # via
    #   -r requirements.in
    #   google-api-python-client
google-auth-oauthlib==1.2.2
    # via
    #   -r requirements.in
    #   gcsfs
google-cloud-appengine-logging==1.6.1
    # via
    #   -r requirements.in
    #   google-cloud-logging
google-cloud-audit-log==0.3.2
    # via
    #   -r requirements.in
    #   google-cloud-logging
google-cloud-bigquery==3.31.0
    # via -r requirements.in
google-cloud-core==2.4.3
    # via
    #   -r requirements.in
    #   google-cloud-bigquery
    #   google-cloud-logging
    #   google-cloud-storage
google-cloud-logging==3.12.1
    # via -r requirements.in
google-cloud-storage==3.1.0
    # via
    #   -r requirements.in
    #   gcsfs
google-cloud-tasks==2.19.2
    # via -r requirements.in
google-crc32c==1.7.1
    # via
    #   google-cloud-storage
    #   google-resumable-media
google-resumable-media==2.7.2
    # via
    #   google-cloud-bigquery
    #   google-cloud-storage
googleapis-common-protos[grpc]==1.70.0
    # via
    #   google-api-core
    #   google-cloud-audit-log
    #   grpc-google-iam-v1
    #   grpcio-status
grpc-google-iam-v1==0.14.2
    # via
    #   google-cloud-logging
    #   google-cloud-tasks
grpcio==1.71.0
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
grpcio-status==1.71.0
    # via google-api-core
gunicorn==23.0.0
    # via functions-framework
httplib2==0.22.0
    # via
    #   google-api-python-client
    #   google-auth-httplib2
idna==3.10
    # via
    #   requests
    #   yarl
importlib-metadata==8.6.1
    # via
    #   dask
    #   opentelemetry-api
iniconfig==2.1.0
    # via pytest
itsdangerous==2.2.0
    # via flask
jinja2==3.1.6
    # via flask
locket==1.0.0
    # via partd
markupsafe==3.0.2
    # via
    #   jinja2
    #   werkzeug
multidict==6.4.3
    # via
    #   aiohttp
    #   yarl
numpy==2.2.5
    # via pandas
oauthlib==3.2.2
    # via requests-oauthlib
opentelemetry-api==1.32.1
    # via google-cloud-logging
packaging==25.0
    # via
    #   dask
    #   deprecation
    #   google-cloud-bigquery
    #   gunicorn
    #   pytest
pandas==2.2.3
    # via -r requirements.in
partd==1.4.2
    # via dask
pluggy==1.5.0
    # via pytest
propcache==0.3.1
    # via
    #   aiohttp
    #   yarl
proto-plus==1.26.1
    # via
    #   google-api-core
    #   google-cloud-appengine-logging
    #   google-cloud-logging
    #   google-cloud-tasks
protobuf==5.29.4
    # via
    #   google-api-core
    #   google-cloud-appengine-logging
    #   google-cloud-audit-log
    #   google-cloud-logging
    #   google-cloud-tasks
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   proto-plus
pyarrow==19.0.1
    # via -r requirements.in
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pyparsing==3.2.3
    # via httplib2
pytest==8.3.5
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via
    #   google-cloud-bigquery
    #   pandas
pytz==2025.2
    # via
    #   -r requirements.in
    #   pandas
pyyaml==6.0.2
    # via dask
requests==2.32.3
    # via
    #   gcsfs
    #   google-api-core
    #   google-cloud-bigquery
    #   google-cloud-storage
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via google-auth-oauthlib
rsa==4.9.1
    # via google-auth
six==1.17.0
    # via python-dateutil
tomli==2.2.1
    # via pytest
toolz==1.0.0
    # via
    #   dask
    #   partd
typing-extensions==4.13.2
    # via multidict
tzdata==2025.2
    # via pandas
uritemplate==4.1.1
    # via google-api-python-client
urllib3==2.4.0
    # via requests
watchdog==6.0.0
    # via functions-framework
werkzeug==3.1.3
    # via
    #   flask
    #   functions-framework
wrapt==1.17.2
    # via deprecated
yarl==1.20.0
    # via aiohttp
zipp==3.21.0
    # via importlib-metadata
