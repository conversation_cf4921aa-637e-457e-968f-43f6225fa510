import functions_framework
import sys
import logging
import time
import google.cloud.logging
import json
import argparse
import google.auth.transport.requests
import google.oauth2.id_token
from google.oauth2 import service_account
from google.cloud import tasks_v2, storage
from google.protobuf import timestamp_pb2, duration_pb2
from datetime import datetime
from pytz import timezone
from google.cloud import bigquery
from google_storage import GoogleStorageIO
from make_query import LABEL_QUERY, USER_QUERY
from config import (
    GOOGLE_APPLICATION_CREDENTIALS_PATH,
    PROJECT_ID,
    LOCATION_ID,
    QUEUE_ID,
    SERVICE_ACCOUNT_EMAIL,
    TARGET_URI
)
import os
from typing import Union, Optional
import pandas as pd
from dask import dataframe as dd
from utils import read_audience_packages



def save_user_permanents_to_gcs(
    ec_id: int,
    df: pd.DataFrame,
    gcs_base_path: str,
    storage_client: Optional[object] = None,
    *,
    timezone_name: str = 'Asia/Taipei'
) -> Union[str, None]:
    """將特定電商的用戶 permanent IDs 儲存至 Google Cloud Storage。

    Args:
        ec_id: 電商 ID
        df: 包含 permanent 欄位的 DataFrame
        gcs_base_path: GCS 基礎路徑
        storage_client: Google Storage IO 客戶端實例
        timezone_name: 時區名稱，預設為 'Asia/Taipei'

    Returns:
        str: 成功時返回 GCS URI
        None: 當輸入 DataFrame 為空時返回

    Raises:
        ValueError: 當必要參數未提供或格式錯誤時
        IOError: 當檔案操作失敗時
    """
    if storage_client is None:
        raise ValueError("GoogleStorageIO is required")
    
    if df.empty:
        return None

    # 準備資料
    permanent_ids = (
        pd.DataFrame(df[df['ec_id'] == ec_id]['permanent']
                    .drop_duplicates()
                    .reset_index(drop=True))
    )
    logging.info(f"{ec_id} 找到 {len(permanent_ids)} 個 permanent IDs")
    
    if permanent_ids.empty:
        logging.warning(f"{ec_id} 沒有找到對應的 permanent IDs")
        return None

    # 設定檔案路徑
    current_date = datetime.now(timezone(timezone_name)).strftime("%Y%m%d")
    local_path = f'/tmp/{ec_id}.parquet'
    gcs_uri = f'{gcs_base_path}/{current_date}/{ec_id}.parquet.snappy'

    try:
        # 儲存到本地
        permanent_ids.to_parquet(
            local_path,
            engine='pyarrow',
            compression='snappy',
            index=False
        )

        # 上傳到 GCS
        storage_client.upload_file(localpath=local_path, gsuri=gcs_uri)
        
        return gcs_uri

    except Exception as e:
        raise IOError(f"Failed to save permanents for EC {ec_id}: {str(e)}")
    
    finally:
        # 清理暫存檔案
        if os.path.exists(local_path):
            os.remove(local_path)

def list_gcs_files(bucket_name, prefix):
    """列出 GCS 中指定路徑下的所有檔案

    Args:
        bucket_name (str): GCS bucket 名稱
        prefix (str): 要搜尋的路徑前綴

    Returns:
        list: 符合條件的檔案路徑列表
    """
    storage_client = storage.Client()
    bucket = storage_client.bucket(bucket_name)
    blobs = bucket.list_blobs(prefix=prefix)
    
    # 只返回 .parquet 檔案
    files = [f"gs://{bucket_name}/{blob.name}" for blob in blobs if blob.name.endswith('.parquet')]
    
    if not files:
        print(f"警告: 在路徑 {prefix} 下沒有找到任何 .parquet 檔案")
    else:
        print(f"找到 {len(files)} 個 .parquet 檔案")
        for file in files:
            print(f"- {file}")
            
    return files

def save_query_results(QUERY: str, credentials, temp_table_id_base: str, destination_uri: str, query_job_location: str = "asia-east1"):
    # google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    bq_client = bigquery.Client(credentials=credentials, location=query_job_location) # 設定 client 預設 location
    
    # 解析 temp_table_id_base 以獲取項目和基礎表格名稱
    # 格式: project.dataset.table_basename
    parts = temp_table_id_base.split('.')
    project_id = parts[0]
    # dataset_name_base = parts[1] # 基礎資料集名稱 (例如 temp_tables)
    table_name_base = parts[2] # 基礎表名 (例如 user_query_results)
    
    import uuid
    import time
    # 產生唯一的臨時資料集 ID 和表 ID，確保它們在指定的 query_job_location 中
    unique_suffix = f"{uuid.uuid4().hex[:8]}_{int(time.time())}"
    temp_dataset_id_dynamic = f"temp_ds_{unique_suffix}" # 不包含 project_id，因為 Dataset 物件會處理
    temp_table_id_dynamic = f"{table_name_base}_{unique_suffix}"

    full_temp_dataset_ref_str = f"{project_id}.{temp_dataset_id_dynamic}"
    logging.info(f"將在 {query_job_location} 建立臨時資料集: {full_temp_dataset_ref_str}")

    dataset_ref = bigquery.DatasetReference(project_id, temp_dataset_id_dynamic)
    dataset = bigquery.Dataset(dataset_ref)
    dataset.location = query_job_location
    
    try:
        dataset = bq_client.create_dataset(dataset, exists_ok=True, timeout=30) # exists_ok=True 避免重複建立錯誤
        logging.info(f"位於 {query_job_location} 的臨時資料集 {dataset.dataset_id} 已建立/確認存在.")

        # 使用位於 query_job_location 的臨時資料集創建表格
        full_temp_table_ref_str = f"{project_id}.{temp_dataset_id_dynamic}.{temp_table_id_dynamic}"
        logging.info(f"將在 {full_temp_table_ref_str} (位於 {query_job_location}) 建立臨時表")
        
        # 使用 CREATE OR REPLACE TABLE 來儲存查詢結果
        # 查詢本身會在 query_job_location 執行
        create_table_query = f"""
        CREATE OR REPLACE TABLE `{full_temp_table_ref_str}` AS
        {QUERY}
        """
        
        query_job_config = bigquery.QueryJobConfig()
        # 如果 QUERY 中有參照到未指定 project_id 的 table，可以設定 default_dataset
        # query_job_config.default_dataset = dataset_ref 

        logging.info(f"準備在 {query_job_location} 執行查詢以建立 {full_temp_table_ref_str}")
        query_job = bq_client.query(create_table_query, job_config=query_job_config) # location 會從 client 繼承
        query_job.result()  # 等待查詢完成
        logging.info(f"查詢完成，資料已存入 {full_temp_table_ref_str}")
        
        # 將結果從位於 query_job_location 的臨時表匯出到 GCS
        table_ref_for_extract = dataset.table(temp_table_id_dynamic)
        
        extract_job_config = bigquery.ExtractJobConfig()
        extract_job_config.destination_format = bigquery.DestinationFormat.PARQUET
        
        logging.info(f"準備從 {full_temp_table_ref_str} (位於 {query_job_location}) 匯出資料到 {destination_uri}")
        extract_job = bq_client.extract_table(
            table_ref_for_extract,
            destination_uri,
            job_config=extract_job_config
            # location 會從 client 繼承
        )
        extract_job.result() # 等待匯出完成
        
        logging.info(f"資料已從 {full_temp_table_ref_str} (位於 {query_job_location}) 成功匯出到 {destination_uri}")
    
    finally:
        # 刪除在 query_job_location 建立的臨時數據集及其內容
        logging.info(f"準備刪除位於 {query_job_location} 的臨時資料集: {full_temp_dataset_ref_str}")
        bq_client.delete_dataset(
            dataset_ref, delete_contents=True, not_found_ok=True
        )
        logging.info(f"已刪除臨時資料集: {full_temp_dataset_ref_str}")

    return

def create_cloud_task(project_id, location_id, queue_id, payload, target_uri=None, delay_seconds=0, service_account_email=None):
    """建立一個 Cloud Task 以觸發 HTTP 端點，並使用指定的服務帳號進行 OIDC 驗證。

    Args:
        project_id (str): Google Cloud 專案 ID。
        location_id (str): Cloud Tasks 佇列所在的區域。
        queue_id (str): Cloud Tasks 佇列名稱。
        payload (dict): 要發送到目標 URI 的 JSON payload。
        target_uri (str): Cloud Task 要觸發的目標 HTTP(S) URL。
        delay_seconds (int): 任務延遲執行的秒數，預設為 0 (立即執行)。
        service_account_email (str): 用於產生 OIDC token 的服務帳號 Email。
    """
    client = tasks_v2.CloudTasksClient()
    parent = client.queue_path(project_id, location_id, queue_id)

    task_dict = {
        "http_request": {
            "http_method": tasks_v2.HttpMethod.POST,
            "url": target_uri,
            "headers": {"Content-Type": "application/json"},
            "body": json.dumps(payload).encode('utf-8'),
        },
        "dispatch_deadline": duration_pb2.Duration(seconds=1800)  # 30 分鐘
    }

    if service_account_email:
        print(f"為任務設定 OIDC token，使用服務帳號: {service_account_email}, 目標 audience: {target_uri}")
        task_dict["http_request"]["oidc_token"] = {
            "service_account_email": service_account_email,
            "audience": target_uri
        }
    else:
        print(f"警告：未指定 service_account_email，將不使用 OIDC token 進行身份驗證。")
        print(f"目標 {target_uri} 必須設定為允許未經驗證的調用。")

    if delay_seconds > 0:
        now = datetime.datetime.utcnow()
        schedule_time = now + datetime.timedelta(seconds=delay_seconds)
        timestamp = timestamp_pb2.Timestamp()
        timestamp.FromDatetime(schedule_time)
        task_dict["schedule_time"] = timestamp
        print(f"任務將被安排在 {schedule_time.isoformat()} UTC 執行 (延遲 {delay_seconds} 秒)。")

    try:
        print(f"準備發送任務到佇列: {parent}")
        print(f"任務目標 URL: {target_uri}")
        response = client.create_task(parent=parent, task=task_dict)
        print(f"成功建立任務: {response.name}")
        return response.name
    except Exception as e:
        print(f"錯誤：無法建立 Cloud Task: {e}")
        raise

def setup_local_logging():
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    root_logger.setLevel(logging.DEBUG)
    
    console_handler = logging.StreamHandler(sys.stdout)
    console_handler.setLevel(logging.DEBUG)
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)

def get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH):
    google_credentials = service_account.Credentials.from_service_account_file(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    project_id = google_credentials.project_id
    return google_credentials, project_id

@functions_framework.http
def main(request):
    """Cloud Function 入口點"""
    try:
        logging_client = google.cloud.logging.Client()
        logging_client.setup_logging()
    except Exception:
        setup_local_logging()
        logging.info("使用本地端logging設定")

    request_json = request.get_json(silent=True)
    if not request_json:
        return '請求必須包含 JSON 資料', 400
        
    required_params = {
        'page_name': '頁面名稱',
        # 'delay_seconds': '任務延遲執行的秒數',
        'save_gate': '保存開關（0 或 1）',
        'query_gate': '查詢開關（0 或 1）',
        'upload_gate': '上傳開關（0 或 1）',
        'parallel_gate': '平行處理開關（0 或 1）'
    }

    # 驗證必要參數
    params = {}
    for param_name, param_desc in required_params.items():
        if param_name not in request_json:
            return f'缺少必要參數 {param_name}: {param_desc}', 400
        params[param_name] = request_json[param_name]

    # 解構參數
    page_name = params.get('page_name')
    upload_gate = int(params.get('upload_gate'))
    save_gate = int(params.get('save_gate'))
    query_gate = int(params.get('query_gate'))
    logging.info(f"[MAIN_LOGIC] Parsed query_gate: {query_gate}, type: {type(query_gate)}")
    parallel_gate = int(params.get('parallel_gate'))
    current_date = datetime.now(timezone('Asia/Taipei')).strftime("%Y%m%d")

    # 設定路徑
    user_item_gcs_path = f"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/{current_date}/*.parquet"
    label_gcs_path = f"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/label_query_results/{current_date}/*.parquet"
    temp_table_id = "tagtoo-ml-workflow.temp_tables.user_query_results"

    # 初始化客戶端
    google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    GSIO = GoogleStorageIO(credentials=google_credentials)
    logging.info({"message": "GSIO 已建立", "project_id": project_id})

    # ** --- 執行 BigQuery 查詢 --- **
    logging.info(f"[MAIN_LOGIC] Checking query_gate condition. query_gate value: {query_gate}")
    if query_gate:
        save_query_results(
            QUERY=LABEL_QUERY,
            credentials=google_credentials,
            temp_table_id_base=temp_table_id,  # Renamed parameter
            destination_uri=label_gcs_path,
            query_job_location="asia-east1"  # Specify job location
        )
        logging.info(f"[MAIN_LOGIC] Finished calling save_query_results for LABEL_QUERY.")
        
        logging.info(f"[MAIN_LOGIC] Calling save_query_results for USER_QUERY.")
        save_query_results(
            QUERY=USER_QUERY,
            credentials=google_credentials,
            temp_table_id_base=temp_table_id,  # Renamed parameter
            destination_uri=user_item_gcs_path,
            query_job_location="asia-east1"  # Specify job location
        )
        logging.info(f"[MAIN_LOGIC] Finished calling save_query_results for USER_QUERY.")
        logging.info({"message": "BigQuery 查詢完成", "temp_table_id": temp_table_id})
        logging.info(f"[MAIN_LOGIC] Successfully completed query_gate block.")
    else:
        logging.info(f"[MAIN_LOGIC] query_gate is false or 0. Skipping BigQuery saving. Entering Cloud Task creation logic.")
        logging.info({
            "message": "BigQuery 查詢已跳過，直接使用 Cloud Storage 檔案",
            "temp_table_id": temp_table_id,
            "label_gcs_path": label_gcs_path,
            "user_item_gcs_path": user_item_gcs_path
        })

    # 確保 BigQuery 查詢完成，檔案成功匯出到 GCS
    time.sleep(10)

    # 列出並處理檔案
    bucket_name = "tagtoo-ml-workflow-cloud-functions"
    prefix = f"item_label/query_data/user_item_query_results/{current_date}/"
    files = list_gcs_files(bucket_name, prefix)
    
    if not files:
        return f'在路徑 {prefix} 下沒有找到任何 .parquet 檔案', 400

    # ** --- 建立各 ec_id 完整的 permanent IDs --- **

    # 讀取 user_item 資料
    df = dd.read_parquet(user_item_gcs_path, engine="pyarrow", assume_missing=True).compute()

    # 讀取關鍵字大表
    page_name = page_name or "prod"
    audience_packages_df = read_audience_packages(google_credentials, page_name)

    # 篩選出目前在關鍵字大表中的 `user_source_id`
    target_user_source_ids = audience_packages_df['user_source_id'].dropna().unique().astype(int).tolist()
    target_user_source_df = df[df['ec_id'].isin(target_user_source_ids)]

    # 建立各 ec_id 完整的 permanent IDs
    for ec_id in target_user_source_ids:
        gcs_uri = save_user_permanents_to_gcs(
            ec_id=ec_id,
            df=target_user_source_df,
            gcs_base_path="gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids",
            storage_client=GSIO
        )
        logging.info({"message": "ec_id 完整的 permanent IDs 已建立", "ec_id": ec_id, "gcs_uri": gcs_uri})

    # 檢查是否為測試模式
    test_mode = request_json.get('test_mode', False)
    if test_mode:
        logging.info("測試模式啟用，僅處理前三個檔案")
        files = files[:3]
    
    # 為每個檔案建立 Cloud Task，每 10 個任務休息 10 秒
    for i, file_path in enumerate(files, 1):
        
        # 以 file_path 最後的 3 個字元作為 log_idx
        # e.g. 000, 001, 002
        log_idx = file_path.split('/')[-1].split('.')[0][-3:]
        
        payload = {
            "page_name": page_name,
            "save_gate": save_gate,
            "upload_gate": upload_gate,
            "parallel_gate": parallel_gate,
            "user_item_gcs_path": file_path,
            "log_idx": log_idx
        }

        try:
            task_name = create_cloud_task(
                project_id=PROJECT_ID,
                location_id=LOCATION_ID,
                queue_id=QUEUE_ID,
                payload=payload,
                target_uri=TARGET_URI,
                delay_seconds=request_json.get('delay_seconds', 0),
                service_account_email=SERVICE_ACCOUNT_EMAIL
            )
            logging.info(f"成功為檔案 {file_path} 建立任務: {task_name}")
        except Exception as e:
            logging.error(f"處理檔案 {file_path} 時發生錯誤: {e}")
            return f'建立 Cloud Task 時發生錯誤: {str(e)}', 500

        if i % 10 == 0:
            logging.info(f"已建立 {i} 個任務，休息 60 秒")
            time.sleep(60)

    return '所有任務已成功建立', 200

if __name__ == "__main__":
    import argparse
    import flask
    
    # 設定命令列參數
    parser = argparse.ArgumentParser(description='測試 Cloud Function')
    parser.add_argument('--page_name', type=str, default='prod', help='頁面名稱')
    parser.add_argument('--save_gate', type=int, default=0, choices=[0, 1], help='保存開關（0 或 1）')
    parser.add_argument('--query_gate', type=int, default=0, choices=[0, 1], help='查詢開關（0 或 1）')
    parser.add_argument('--upload_gate', type=int, default=0, choices=[0, 1], help='上傳開關（0 或 1）')
    parser.add_argument('--parallel_gate', type=int, default=0, choices=[0, 1], help='平行處理開關（0 或 1）')
    parser.add_argument('--test_mode', action='store_true', help='測試模式，僅處理前三個檔案')
    
    args = parser.parse_args()
    
    # 設定日誌記錄
    setup_local_logging()
    logging.info("在主模組中初始化日誌")
    
    # 創建一個模擬的請求對象
    mock_request = flask.Request.from_values(
        json={
            'page_name': args.page_name,
            'save_gate': args.save_gate,
            'query_gate': args.query_gate,
            'upload_gate': args.upload_gate,
            'parallel_gate': args.parallel_gate,
            'test_mode': args.test_mode
        },
        content_type='application/json'
    )
    
    # 直接調用主函數
    response = main(mock_request)
    
    # 輸出響應
    print(f"\n結果: {response}")