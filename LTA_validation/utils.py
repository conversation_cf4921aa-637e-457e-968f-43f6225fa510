import pandas as pd
import dask.dataframe as dd
from datetime import datetime, timedelta
from pytz import timezone
from google.oauth2 import service_account
from google.cloud import storage
from tqdm import tqdm
import ast
from typing import List, Dict, Set, Tuple
from ctypes import ArgumentError

def make_label_query(start_date: str, end_date: str, predict_date: str) -> str:
  return f"""
    WITH
      lastday AS(
      SELECT
        DISTINCT gid.group_id
      FROM
        `tagtoo-tracking.event_prod.user_unify_group_permanent` AS gid
      INNER JOIN
        `tagtoo-tracking.event_prod.tagtoo_event` tr
      ON
        tr.permanent = gid.permanent
      WHERE
        DATE(event_time) = "{predict_date}"
      ),
      purchaser AS(
      SELECT
        DISTINCT permanent,
        SUM(items.price) AS sum_value,
        COUNT(DISTINCT
        IF
          (price > 0, event.custom_data.order_id, NULL)) AS count_tran,
        SUM(items.quantity) AS count_items
      FROM
        `tagtoo-tracking.event_prod.tagtoo_event` AS log,
        UNNEST(event.items) items
      JOIN
        `gothic-province-823.tagtoo_from_cloudsql.ECID_to_IndustryID` AS ind
      ON
        ind.ec_id = log.ec_id
      WHERE
        event_time >= '{start_date}'
        AND event_time <= '{end_date}'
        AND event.name = 'purchase'
        AND event.currency IN ('TWD',
          'NTD')
        AND items.price > 1
        AND items.price < 500000
        AND items.quantity > 0
        AND items.quantity < 100
        AND ind.industry_id NOT IN (9,
          13,
          15,
          16)
      GROUP BY
        1 )
    SELECT
      DISTINCT gid.group_id,
      tr.ec_id,
      event.name,
      category,
      indust.industry_id,
    IF
      ((sum_value / count_items > 0)
        AND (sum_value / count_items <500),1,0) AS item_value_1,
    IF
      ((sum_value / count_items >= 500)
        AND (sum_value / count_items <1000),1,0) AS item_value_2,
    IF
      ((sum_value / count_items >= 1000)
        AND (sum_value / count_items <2000),1,0) AS item_value_3,
    IF
      ((sum_value / count_items >= 2000)
        AND (sum_value / count_items <3000),1,0) AS item_value_4,
    IF
      ((sum_value / count_items >= 3000)
        AND (sum_value / count_items <4000),1,0) AS item_value_5,
    IF
      ((sum_value / count_items >= 4000)
        AND (sum_value / count_items <7500),1,0) AS item_value_6,
    IF
      ((sum_value / count_items >= 7500),1,0) AS item_value_7,
    IF
      ((sum_value / count_tran > 0)
        AND (sum_value / count_tran <1000),1,0) AS order_value_1,
    IF
      ((sum_value / count_tran >= 1000)
        AND (sum_value / count_tran <2000),1,0) AS order_value_2,
    IF
      ((sum_value / count_tran >= 2000)
        AND (sum_value / count_tran <3000),1,0) AS order_value_3,
    IF
      ((sum_value / count_tran >= 3000)
        AND (sum_value / count_tran <4000),1,0) AS order_value_4,
    IF
      ((sum_value / count_tran >= 4000)
        AND (sum_value / count_tran <7500),1,0) AS order_value_5,
    IF
      ((sum_value / count_tran >= 7500),1,0) AS order_value_6
    FROM
      `tagtoo-tracking.event_prod.tagtoo_event` tr,
      UNNEST(event.items) AS items
    LEFT JOIN
      purchaser
    ON
      tr.permanent=purchaser.permanent
    LEFT JOIN
      `tagtoo-ml-workflow.data_prod.Product_category_main` AS concat_gmc_predict
    ON
      concat_gmc_predict.id = items.id
    JOIN
      `gothic-province-823.tagtoo_from_cloudsql.ECID_to_IndustryID` AS indust
    ON
      indust.ec_id = CAST(tr.ec_id AS INT64)
    INNER JOIN
      `tagtoo-tracking.event_prod.user_unify_group_permanent` AS gid
    ON
      gid.permanent = tr.permanent
    INNER JOIN
      lastday
    USING
      (group_id)
    WHERE
      event_time >= '{start_date}'
      AND event_time <= '{end_date}'
    """

def add_export_path(export_path: str, sql_query: str) -> str:
  return f"""
    EXPORT DATA
    OPTIONS (
        uri = '{export_path}',
        format = 'PARQUET',
        overwrite = true)
    AS (
      {sql_query}
    );
  """

def make_date_arguments(current_datetime: datetime) -> Dict[str, str]:

  DATE_FORMAT = "%Y-%m-%d"
  tmp_dict = {}
  
  tmp_dict['label_start_date'] = (current_datetime - timedelta(days=7)).strftime(DATE_FORMAT)
  tmp_dict['label_end_date'] = (current_datetime - timedelta(days=1)).strftime(DATE_FORMAT)
  tmp_dict['predict_date'] = (current_datetime - timedelta(days=8)).strftime(DATE_FORMAT)
  tmp_dict['current_date'] = current_datetime.strftime(DATE_FORMAT)

  return tmp_dict

def object_exists(bucket_name: str, blob_name: str, credentials: service_account.Credentials) -> bool:
    """Check if an object exists in the GCS bucket.
    
    Args:
    - bucket_name: Name of the GCS bucket.
    - blob_name: Name (path) of the object within the bucket.
    
    Returns:
    - True if the object exists, otherwise False.
    """
    storage_client = storage.Client(credentials=credentials)
    bucket = storage_client.bucket(bucket_name)
    blob = bucket.blob(blob_name)
    return blob.exists()

def load_data(path: str, file_format: str = 'PARQUET', **kwargs) -> pd.core.frame.DataFrame:
  if file_format == 'PARQUET':
    df = dd.read_parquet(path, assume_missing=True, **kwargs).compute()
  elif file_format == 'CSV':
    df = dd.read_csv(path, assume_missing=True, **kwargs).compute()
  else:
    raise ArgumentError("Invalid file format")
  return df

def extract_categories(df_label: pd.DataFrame) -> List[str]:
  # Extracting categories
  categories = df_label['category'].str.split(' > ').str[0].unique()
  categories = [cat for cat in categories if (cat is not None) & (cat != 'Business & Industrial')]
  return categories

def label_purchase_segment(df_label: pd.DataFrame, categories: List[str]) -> pd.core.frame.DataFrame:

  # Labeling for purchased categories
  df_purchase = df_label[df_label['name']=='purchase'].copy()

  for cat in categories:
      df_purchase[f'purchase_{cat}'] = df_purchase['category'].apply(lambda x: int(cat in x) if x is not None else 0)

  for ind in df_label['industry_id'].unique():
      df_purchase[f'purchase_{ind}'] = (df_purchase['industry_id'] == ind).astype(int)

  # Aggregate for purchases
  df_purchase = df_purchase.drop(columns=['ec_id', 'name', 'category', 'industry_id']).groupby('group_id').sum().reset_index()

  return df_purchase

def label_view_segment(df_label: pd.DataFrame) -> pd.core.frame.DataFrame:
  
  # Labeling for viewed industries
  df_view = df_label[df_label['name']!='purchase'].copy()
  for ind in df_label['industry_id'].unique():
      df_view[f'view_{ind}'] = (df_view['industry_id'] == ind).astype(int)

  # Aggregate for views
  df_view = df_view.drop(columns=['ec_id', 'name', 'category', 'industry_id']).groupby('group_id').sum().reset_index()

  return df_view

def label_transform(df_label: pd.DataFrame) -> pd.core.frame.DataFrame:

  # Extracting categories
  categories = extract_categories(df_label)
  # label purchase categories and industry buyers
  df_purchase = label_purchase_segment(df_label, categories)
  # label industry viewers
  df_view = label_view_segment(df_label)
  # Merge and finalize the result
  df_result = pd.concat([df_purchase, df_view]).groupby('group_id').sum().reset_index()
  df_result[df_result.columns[1:]] = (df_result[df_result.columns[1:]] >= 1).astype(int)

  return df_result

def aggregate_users_by_segment(df_predict: pd.DataFrame) -> pd.core.frame.DataFrame:
  
  df_predict['segment_id'] = df_predict['segment_id'].apply(lambda x: [int(i) for i in ast.literal_eval(x)])
  exploded_df = df_predict.explode('segment_id')
  grouped = exploded_df.groupby('segment_id')['track_user'].agg(list).reset_index()
  grouped['segment_id'] = grouped['segment_id'].astype(str)

  return grouped

def rename_label_columns(df: pd.DataFrame, segment_name_to_id_dict: Dict[str, int]) -> pd.core.frame.DataFrame:
  # rename label columns from `segment_name` to `segment_id`
  rename_dict = {}
  for col in df.drop('group_id', axis=1).columns:
    if col in segment_name_to_id_dict.keys():
        rename_dict[col] = segment_name_to_id_dict[col]
  
  df.rename(columns=rename_dict, inplace=True)
  df.drop([col for col in df.columns if col is None], axis=1, inplace=True)

  return df

def map_segment_to_group_ids(df: pd.DataFrame)-> Dict[str, List[str]]:
  # {1088: ['id_a', 'id_b', ...], 1063: [...]}
  segment_name_to_gids = {}

  for seg in tqdm(df.drop('group_id', axis=1).columns.tolist()):
    segment_name_to_gids[seg] = df[df[seg]==1].group_id.tolist()

  return segment_name_to_gids

def get_true_positives(df: pd.DataFrame, segment_name_to_gids: Dict[int, List[str]]) -> int:
  
  pred = pd.Series(df['track_user'])
  y_true = pd.Series(segment_name_to_gids[int(df['segment_id'])])
  TP = (pred.isin(y_true)).sum()

  return TP

def calculate_evaluation_metrics(merge_filtered: pd.DataFrame, segment_name_to_gids: Dict[int, List[str]]) -> pd.core.frame.DataFrame:
  
  merge_filtered['TP'] = merge_filtered.apply(get_true_positives, axis=1, args=(segment_name_to_gids,))
  merge_filtered['Volume'] = merge_filtered['track_user'].apply(lambda x: len(x))
  merge_filtered['True_data'] = merge_filtered['segment_id'].apply(lambda x: len(segment_name_to_gids[int(x)]))
  merge_filtered['Precision'] = merge_filtered['TP'] / merge_filtered['Volume']
  merge_filtered['Recall'] = merge_filtered['TP'] / merge_filtered['True_data']
  merge_filtered['F1'] = 2*merge_filtered['Precision']*merge_filtered['Recall'] / (merge_filtered['Precision']+merge_filtered['Recall'])
  
  return merge_filtered

def combine_and_cleanup_dataframes(df_x: pd.DataFrame, df_y: pd.DataFrame) -> pd.DataFrame:
  """
  Merge two dataframes on 'segment_id' and perform cleanup operations.

  This function takes two dataframes, drops specified columns, then merges them based on the 'segment_id'. 
  Post-merging, the function computes combined values for 'TP', 'Volume', and 'True_data', 
  drops redundant columns, and renames certain columns for clarity.

  Args:
  - df_x (pd.DataFrame): The first dataframe.
  - df_y (pd.DataFrame): The second dataframe.

  Returns:
  - pd.DataFrame: A merged and cleaned dataframe.
  """

  drop_columns = ['track_user', 'Precision', 'Recall', 'F1']
  
  # Only drop columns if they exist in the dataframe
  cols_to_drop_x = [col for col in drop_columns if col in df_x.columns]
  cols_to_drop_y = [col for col in drop_columns if col in df_y.columns]
  
  _df_x = df_x.drop(cols_to_drop_x, axis=1)
  _df_y = df_y.drop(cols_to_drop_y, axis=1)

  m = pd.merge(_df_x, _df_y, on='segment_id', how='outer')
  m = m.fillna(0)

  m['TP'] = m['TP_x'] + m['TP_y']
  m['Volume'] = m['Volume_x'] + m['Volume_y']
  m['True_data'] = m['True_data_x'] + m['True_data_y']

  merge_drop_columns = ['name_y', 'path_y', 'TP_x', 'Volume_x', 'True_data_x', 'TP_y', 'Volume_y', 'True_data_y']
  m.drop(merge_drop_columns, axis=1, inplace=True)
  m.rename(columns={'name_x': 'name', 'path_x': 'path'}, inplace=True)

  return m
