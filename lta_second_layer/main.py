import functions_framework
import logging
import numpy as np
import pandas as pd
import random
import block_timer
import dask_utils
from dask_utils import load_data, parquet_load_data
import joblib
import google_storage
import dask.dataframe as dd
from google.cloud import bigquery
import datetime
from pytz import timezone
from typing import List, Tuple, Dict, Set
import json
from datetime import datetime, timedelta
from google.oauth2 import service_account
from tqdm import tqdm
import os

def trans(alist: pd.Series) -> List[int]:
    """
    Extract values from a dictionary based on indices of a list if the list value > 0.

    Args:
    - alist (List[int]): Input list with string indices and integer values.
    - dict1 (Dict[str, int]): Dictionary with string keys and integer values -> {label_name: semgent_id}.

    Returns:
    - List: A list containing unique **segment_id** extracted from the dictionary.
    """
    # dict2 = {
    # 760: [761, 762, 763, 764, 765],
    # 788: [776, 782, 783, 784, 785, 786, 787],
    # 794: [789, 792, 793],
    # 804: [795, 796, 797, 798, 799, 800, 801, 802, 803],
    # 825: [806, 807, 808, 809, 810, 811, 812, 813, 814, 815, 816, 817, 819, 821, 824],
    # 857: [826, 838, 856],
    # 883: [858, 859, 860, 861, 862, 863, 864, 865, 866, 867, 868, 869, 870, 871, 872, 873, 874, 875, 876, 877, 878, 879, 880, 881, 882],
    # 896: [884, 885, 886, 887, 888, 889, 890, 891, 892, 893, 894, 895],
    # 917: [897, 898, 910],
    # 937: [918, 919, 920, 921, 922, 923, 924, 925, 926, 927, 928, 929, 930, 931, 932, 933, 934, 935, 936],
    # 947: [939, 946],
    # 955: [948, 949, 950, 953, 954],
    # 966: [956, 957, 958, 959, 960, 961, 962, 963, 964, 965],
    # 975: [967, 969, 973],
    # 979: [976, 977, 978],
    # 1011: [987, 991, 992, 997],
    # 1020: [1012, 1015, 1016, 1017],
    # 1029: [1023, 1027, 1028]}

    temp = list(alist.index[alist > 0])
    # temp = [col for col in alist.index]       
    temp = [*set(temp)] # Remove duplicates from the temporary list
    temp = [item for item in temp if item is not None]

    # temp2 = []
    # for key, values in dict2.items():
    #   for v in temp:
    #     if v in values:
    #       temp2.append(key)

    return temp

def str_isin(x, input_str):
  return (input_str == x)

def make_date_arguments(current_datetime: datetime) -> Dict[str, str]:

  DATE_FORMAT = "%Y-%m-%d"
  tmp_dict = {}
  
  tmp_dict['start_date'] = (current_datetime - timedelta(days=31)).strftime(DATE_FORMAT)
  tmp_dict['end_date'] = (current_datetime - timedelta(days=1)).strftime(DATE_FORMAT)

  return tmp_dict

@functions_framework.http
def main(request):
    
    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)

    GSIO = google_storage.GoogleStorageIO(credentials=credentials)
    today = datetime.now(timezone('Asia/Taipei'))
    date_dict = make_date_arguments(today)

    # ttd mapping
    # print("start ttd mapping query")
    # client = bigquery.Client(credentials=credentials)

    # ttd_query = '''
    #     SELECT
    #   tagtoo_cookie AS permanent,
    #   ttd_cookie
    # FROM (
    #   SELECT
    #     ROW_NUMBER() OVER(PARTITION BY tagtoo_cookie ORDER BY tagtoo_cookie, created DESC ) AS row_number,
    #     tagtoo_cookie,
    #     ttd_cookie,
    #     created
    #   FROM
    #     `gothic-province-823.tagtooad.ttd_cookie_mapping` ) ordered
    # WHERE
    #   ordered.row_number = 1
    #   AND ordered.tagtoo_cookie != ''
    # '''
    # ttd_query_job = client.query(ttd_query)
    # df_ttd_mapping = ttd_query_job.to_dataframe()
    # # df_ttd_mapping = pd.read_gbq(ttd_query, dialect="standard")
    # print("ttd mapping query finish")

    # load data
    # export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature/{date_dict['start_date']}_{date_dict['end_date']}/transformed/*.parquet"
    export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature/{date_dict['start_date']}_{date_dict['end_date']}/transformed/*.ftr"
    print(export_path)
    # df = parquet_load_data(export_path)
    df = pd.read_feather(export_path)
    # df = parquet_load_data(export_path).iloc[:500]
    df = df.reset_index(drop=True)
 
    # start predicting
    user = df.iloc[:, :1]
    df_feature = df.iloc[:,1:]
    feature = np.array(df_feature)

    # load model 2
    print('loading model 2')
    GSIO.download_to_path(gsuri='gs://tagtoo-ml-workflow/topic10/firstparty/model/second_layer.pkl', localpath='/tmp/second_layer.pkl')
    clf2 = joblib.load('/tmp/second_layer.pkl')

    print('start predicting')
    predictions2 = clf2.predict(feature)
    print('finish predicting')

    df = pd.concat([user, pd.DataFrame(predictions2)], axis=1)

    # labeling
    cols = [
      'permanent',
      'purchase_Arcade Equipment',
      'purchase_Athletics',
      'purchase_Audio',
      'purchase_Baby & Toddler Furniture',
      'purchase_Baby Bathing',
      'purchase_Baby Health',
      'purchase_Baby Safety',
      'purchase_Baby Toys & Activity Equipment',
      'purchase_Baby Transport',
      'purchase_Baby Transport Accessories',
      'purchase_Bathroom Accessories',
      'purchase_Beds & Accessories',
      'purchase_Benches',
      'purchase_Beverages',
      'purchase_Bird Supplies',
      'purchase_Book Accessories',
      'purchase_Books',
      'purchase_Building Consumables',
      'purchase_Building Materials',
      'purchase_Business & Home Security',
      'purchase_Cabinets & Storage',
      'purchase_Carts & Islands',
      'purchase_Cat Supplies',
      'purchase_Chair Accessories',
      'purchase_Chairs',
      'purchase_Circuit Boards & Components',
      'purchase_Clothing',
      'purchase_Clothing Accessories',
      'purchase_Communications',
      'purchase_Components',
      'purchase_Computer Software',
      'purchase_Computers',
      'purchase_Costumes & Accessories',
      'purchase_DVDs & Videos',
      'purchase_Decor',
      'purchase_Desktop Computers',
      'purchase_Diapering',
      'purchase_Digital Goods & Currency',
      'purchase_Dog Supplies',
      'purchase_Electronics Accessories',
      'purchase_Emergency Preparedness',
      'purchase_Entertainment Centers & TV Stands',
      'purchase_Erotic',
      'purchase_Event Tickets',
      'purchase_Exercise & Fitness',
      'purchase_Fireplaces',
      'purchase_Fish Supplies',
      'purchase_Flood, Fire & Gas Safety',
      'purchase_Food Items',
      'purchase_Furniture Sets',
      'purchase_Futon Frames',
      'purchase_Futon Pads',
      'purchase_Futons',
      'purchase_GPS Navigation Systems',
      'purchase_Games',
      'purchase_General Office Supplies',
      'purchase_Handbag & Wallet Accessories',
      'purchase_Hardware Accessories',
      'purchase_Hardware Pumps',
      'purchase_Health Care',
      'purchase_Heating, Ventilation & Air Conditioning',
      'purchase_Hobbies & Creative Arts',
      'purchase_Household Appliance Accessories',
      'purchase_Household Appliances',
      'purchase_Household Supplies',
      'purchase_Indoor Games',
      'purchase_Jewelry',
      'purchase_Jewelry Cleaning & Care',
      'purchase_Kitchen & Dining',
      'purchase_Laptops',
      'purchase_Lawn & Garden',
      'purchase_Lighting',
      'purchase_Lighting Accessories',
      'purchase_Linens & Bedding',
      'purchase_Locks & Keys',
      'purchase_Magazines & Newspapers',
      'purchase_Memorial Ceremony Supplies',
      'purchase_Motor Vehicles',
      'purchase_Music & Sound Recordings',
      'purchase_Networking',
      'purchase_Nursing & Feeding',
      'purchase_Office & Chair Mats',
      'purchase_Office Carts',
      'purchase_Office Equipment',
      'purchase_Office Furniture',
      'purchase_Office Furniture Accessories',
      'purchase_Office Instruments',
      'purchase_Ottomans',
      'purchase_Outdoor Furniture',
      'purchase_Outdoor Furniture Accessories',
      'purchase_Outdoor Play Equipment',
      'purchase_Outdoor Recreation',
      'purchase_Parasols & Rain Umbrellas',
      'purchase_Party & Celebration',
      'purchase_Personal Care',
      'purchase_Plants',
      'purchase_Plumbing',
      'purchase_Pool & Spa',
      'purchase_Power & Electrical Supplies',
      'purchase_Presentation Supplies',
      'purchase_Print, Copy, Scan & Fax',
      'purchase_Puzzles',
      'purchase_Religious Items',
      'purchase_Reptile & Amphibian Supplies',
      'purchase_Room Divider Accessories',
      'purchase_Room Dividers',
      'purchase_Sheet Music',
      'purchase_Shelving',
      'purchase_Shelving Accessories',
      'purchase_Shipping Supplies',
      'purchase_Shoe Accessories',
      'purchase_Shoes',
      'purchase_Small Engines',
      'purchase_Smoking Accessories',
      'purchase_Sofas',
      'purchase_Storage Tanks',
      'purchase_Swaddling & Receiving Blankets',
      'purchase_Table Accessories',
      'purchase_Tables',
      'purchase_Tobacco Products',
      'purchase_Tool Accessories',
      'purchase_Tools',
      'purchase_Toys',
      'purchase_Umbrella Sleeves & Cases',
      'purchase_Vehicle Parts & Accessories',
      'purchase_Video',
      'purchase_Video Game Console Accessories',
      'purchase_Video Game Consoles',
      'purchase_Video Game Software',
      'purchase_Weapons',
      'purchase_Wedding Ceremony Supplies'
    ]

    GSIO.download_to_path(
      gsuri="gs://tagtoo-ml-workflow/topic10/firstparty/segment_name_to_id/2023-12-15/label_to_segment_id.json",
      localpath="/tmp/label_to_segment_id.json"
    )
    with open("/tmp/label_to_segment_id.json", "r") as file:
        dict1 = json.load(file)
    print('dictionary loaded')

    for i in range(len(cols[1:])):
      cols[i+1] = dict1[cols[i+1]]

    df.columns = cols
    print('prediction dataframe done')

    # df['segment_id'] = df.apply(lambda row: trans(row[2:]), axis = 1)
    df['segment_id'] = df.drop('permanent', axis=1).apply(lambda row: trans(row), axis=1)
    df = df[['permanent', 'segment_id']]
    print("second layer done")

    # third layer randomize
    print('third layer starts')

    # dic = {776: [766, 767, 768, 769, 770, 771, 772, 773, 774, 775, 777, 778, 779, 780, 781],
    #       # 792: [790, 791], 819: [818, 820], 821: [822, 823],
    #        826: [827, 828, 829, 830, 831, 832, 833, 834, 835, 836, 837],
    #         838: [839, 840, 841, 842, 843, 844, 845, 846, 847, 848, 849, 850, 851, 852, 853, 854, 855],
    #         910: [899, 900, 901, 902, 903, 904, 905, 906, 907, 908, 909, 911, 912, 913, 914, 915, 916],
    #         939: [940, 941, 942, 943, 944, 945],
    #         # 950: [951, 952], 967: [968], 969: [970, 971, 972],
    #         973: [974], 987: [980, 981, 982, 983, 984, 985, 986, 988, 989, 990], 992: [993, 994, 995, 996],
    #         997: [998, 999, 1000, 1001, 1002, 1003, 1004, 1005, 1006, 1007, 1008, 1009, 1010],
    #         # 1012: [1013, 1014], 1017: [1018, 1019], 1023: [1021, 1022, 1024, 1025, 1026]
    #         }

    # for c_segs in tqdm(dic.keys()):
    #   for c_seg in dic[c_segs]:
    #     random_rate = random.randrange(70,95)/100
    #     users = df[df.segment_id.apply(lambda x:c_segs in x)].sample(frac=random_rate)['permanent']
    #     df[df.permanent.isin(users)].segment_id.apply(lambda x:x.append(c_seg))
    # print('third layer done')

    date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
    name = f'lta_remarketing_{date}'

    df_third_layer = parquet_load_data(f'gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_third_layer.parquet')
    df = pd.merge(df, df_third_layer, on = ['permanent'])
    df['segment_id'] = df['segment_id_x'] + df['segment_id_y'].apply(lambda x: x.tolist())
    df = df[['permanent', 'segment_id']]
    print('third layer done')

    print("start making tables")
    df['segment_id'] = df['segment_id'].apply(lambda x: list(set(x)))
    df['segment_id'] = df['segment_id'].apply(lambda x: [str(idx) for idx in x])
    print("tables done")

    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    rname = f'{name}_ttd_model_second_layer.parquet'
    df.to_parquet(f'/tmp/{rname}', index=False)
    GSIO.upload_file(f'/tmp/{rname}', f'gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date}/{rname}')
            
    print('Job complete.')

    return 'Success', 200
