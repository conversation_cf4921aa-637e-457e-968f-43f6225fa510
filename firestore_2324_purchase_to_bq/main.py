import functions_framework
from google.cloud import firestore, bigquery
import pandas as pd
import hashlib
from datetime import datetime, timedelta
import pytz
from google.cloud.exceptions import NotFound
from google.oauth2 import service_account
from google_storage import GoogleStorageIO

GSIO = GoogleStorageIO()

@functions_framework.http
def main(request):
    # 設定專案與 Collection
    # GCP 設定
    import requests
    
    print("start")
    project_id = "tagtoo-tracking"
    collection_name = "2324-purchase-sha256"
    bq_project = "tagtoo-ml-workflow"
    dataset_id = "tagtoo_export_results"
    table_id = "2324_firestore_to_bq_prod"

    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    print(f"使用的 Service Account 是：{credentials.service_account_email}")

    # 初始化 GCP Clients
    db = firestore.Client(credentials=credentials, project=project_id)
    print("firestore connected")
    bq_client = bigquery.Client(credentials=credentials, project=bq_project)
    print("bigquery connected")

    # 設定時區與昨天範圍
    tz_tw = pytz.timezone('Asia/Taipei')
    now = datetime.now(tz_tw)
    yesterday_start = tz_tw.localize(datetime(now.year, now.month, now.day) - timedelta(days=1))
    yesterday_end = yesterday_start + timedelta(days=1)

    # 抓取 Firestore 新資料
    new_data = []

    try:
        docs = db.collection(collection_name) \
            .where("created_at", ">=", yesterday_start.isoformat()) \
            .where("created_at", "<", yesterday_end.isoformat()) \
            .stream()
    except Exception as e:
        print(f"Firestore stream 發生錯誤: {e}")
        return 'Firestore query failed', 500
    
    for doc in docs:
        doc_data = doc.to_dict()
        if not doc_data:
            continue

        created_at_str = doc_data.get("created_at")

        if not created_at_str:
            continue

        try:
            # 解析 created_at 字串成 datetime
            created_at = datetime.fromisoformat(created_at_str)
            created_at_tw = created_at.astimezone(tz_tw)
        except Exception as e:
            print(f"無法解析時間: {created_at_str} - {e}")
            continue


        total_price_set = doc_data.get("total_price", {})
        value = total_price_set.get("shop_money", {}).get("amount")
        currency = total_price_set.get("shop_money", {}).get("currency_code")

        line_items = doc_data.get("products", [])
        item_titles = [item.get("title") for item in line_items if isinstance(item, dict)]
        item_ids = [item.get("product_id") for item in line_items if isinstance(item, dict)]
        item_quantity = [item.get("quantity") for item in line_items if isinstance(item, dict)]
        item_price = [item.get("price") for item in line_items if isinstance(item, dict)]

        flat_data = {
        "ec_id": doc_data.get("ec_id"),
        "partner_user_id": doc_data.get("customer_id"),
        "hashed_email": doc_data.get("email"),
        "hashed_phone": doc_data.get("phone"),
        "hashed_gphone": doc_data.get("gphone"),
        "value": value,
        "currency": currency,
        "item_names": item_titles,
        "item_id": item_ids,
        "item_quantity": item_quantity,
        "item_price": item_price,
        "event_name": "purchase",
        "created_time": created_at_tw.isoformat()
        }

        new_data.append(flat_data)

    # 轉成 DataFrame
    df = pd.DataFrame(new_data)
    df['event_name'] = 'purchase'
    print("須更新的資料量：", len(df))
    # ✅ 將 list 欄位轉為 list of string，避免 ArrowTypeError
    list_columns = ['item_names', 'item_id', 'item_quantity', 'item_price']
    for col in list_columns:
        df[col] = df[col].apply(lambda x: [str(i) for i in x] if isinstance(x, list) else [])
        
    table_ref = bq_client.dataset(dataset_id).table(table_id)

    # 檢查表格是否存在，不存在就建立
    try:
        bq_client.get_table(table_ref)
        print(f"BigQuery 表格已存在：{table_id}")
    except NotFound:
        print(f"BigQuery 表格不存在，正在使用 DataFrame schema 建立：{table_id}")
        
        # 建立 schema（根據 df）
        schema = []
        for col, dtype in df.dtypes.items():
            if pd.api.types.is_integer_dtype(dtype):
                field_type = "INT64"
            elif pd.api.types.is_float_dtype(dtype):
                field_type = "FLOAT64"
            elif pd.api.types.is_bool_dtype(dtype):
                field_type = "BOOL"
            elif pd.api.types.is_datetime64_any_dtype(dtype):
                field_type = "TIMESTAMP"
            elif pd.api.types.is_object_dtype(dtype):
                sample_val = df[col].dropna().iloc[0] if not df[col].dropna().empty else ""

                # 處理 list 欄位 → REPEATED STRING
                if isinstance(sample_val, list):
                    field_type = "STRING"
                    schema.append(bigquery.SchemaField(col, field_type, mode="REPEATED"))
                    continue
                else:
                    field_type = "STRING"
            else:
                field_type = "STRING"

            schema.append(bigquery.SchemaField(col, field_type))

        table = bigquery.Table(table_ref, schema=schema)
        bq_client.create_table(table)
        print(f"已建立新表格 {table_id}")

    # 上傳資料（append 模式）
    job_config = bigquery.LoadJobConfig(
        write_disposition=bigquery.WriteDisposition.WRITE_APPEND
    )
    job = bq_client.load_table_from_dataframe(df, table_ref, job_config=job_config)
    job.result()

    print(f"成功追加上傳 {len(df)} 筆資料到 {dataset_id}.{table_id}")
    return 'Success', 200
