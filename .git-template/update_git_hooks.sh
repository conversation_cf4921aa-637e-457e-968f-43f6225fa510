#!/bin/bash

echo "開始更新所有子專案的 Git hooks..."

# 確保在專案根目錄執行
if [ ! -f "manage_git_hooks.sh" ]; then
    echo "❌ 請在專案根目錄執行此腳本"
    echo "正確用法: ./.git-template/update_git_hooks.sh"
    exit 1
fi

# 找出所有子目錄中的 .git 目錄
for dir in $(find . -maxdepth 2 -name .git -type d); do
    project_dir=$(dirname $dir)
    echo "更新 $project_dir 的 Git hooks..."

    # 複製 pre-commit hook 到子專案
    if [ -f ".git-template/hooks/pre-commit" ]; then
        cp .git-template/hooks/pre-commit $dir/hooks/pre-commit
        chmod +x $dir/hooks/pre-commit
        echo "✅ 已完成 $project_dir"
    else
        echo "⚠️ 找不到 .git-template/hooks/pre-commit"
    fi
done

echo "所有子專案的 Git hooks 已更新!"

