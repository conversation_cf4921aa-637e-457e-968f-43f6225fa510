# Git Hooks 模板

這個目錄包含用於 Tagtoo ml-workflow-cloud-functions 儲存庫的 Git hooks 模板和管理工具。

## 📁 目錄結構

```
.git-template/
├── README.md               # 本說明文件
├── hooks/                  # Git hooks 模板
│   └── pre-commit         # pre-commit hook 模板
└── update_git_hooks.sh    # 批量更新工具
```

## 🔧 推薦使用方式

### 新開發者或新專案
使用根目錄的主要管理腳本：
```bash
# 在專案根目錄執行
./manage_git_hooks.sh --quick    # 互動式安裝
./manage_git_hooks.sh --install  # 靜默安裝
./manage_git_hooks.sh --status   # 檢查狀態
```

### 管理員批量更新
使用本目錄的批量更新工具：
```bash
# 在專案根目錄執行
./.git-template/update_git_hooks.sh
```

## 📋 目前包含的 Hooks

### pre-commit
- 自動移除尾隨空格
- 檢查 Python 語法
- 自動修復常見格式問題

### pre-push
- 完整代碼品質檢查
- 檢查 merge conflict markers
- 大文件警告

### commit-msg
- 提交訊息格式檢查
- 支援 Conventional Commits 格式

## 🔄 手動應用到新專案

當你在 ml-workflow-cloud-functions 下創建新專案時，可以通過以下命令應用這些 hooks：

```bash
cd 你的新專案目錄
git init
cp -r ../.git-template/hooks/* .git/hooks/
chmod +x .git/hooks/*
```

**但建議使用主要管理腳本 `./manage_git_hooks.sh` 來安裝，功能更完整。**

## 🛠️ 維護說明

### 更新所有子專案的 Hooks
```bash
cd ~/tagtoo/ml-workflow-cloud-functions
./.git-template/update_git_hooks.sh
```

### 新增或修改 Hooks
1. 更新 `.git-template/hooks/` 目錄中的相應文件
2. 運行 `update_git_hooks.sh` 將變更同步到所有子專案
3. 或者使用 `./manage_git_hooks.sh --install` 重新安裝到當前專案
