#!/bin/bash

# 尾隨空格檢測器 - pre-commit hook
# 這個腳本會檢測並自動移除 .py 文件中的尾隨空格

echo "正在檢查尾隨空格..."

# 獲取將要被提交的文件
files=$(git diff --cached --name-only --diff-filter=ACM | grep '\.py$')

if [ -z "$files" ]; then
    echo "未發現 Python 文件變更，跳過尾隨空格檢查。"
    exit 0
fi

# 保存當前工作目錄狀態
stash_output=$(git stash push -q --keep-index)
stash_pushed=$?

cleanup() {
    # 恢復工作目錄狀態（如果之前有進行過 stash）
    if [ "$stash_pushed" -eq 0 ] && [ "$stash_output" != "No local changes to save" ]; then
        echo "恢復工作目錄狀態..."
        git stash pop -q
    fi
}

# 確保在腳本退出時執行清理操作
trap cleanup EXIT

# 處理每個文件
echo "正在移除下列文件中的尾隨空格："

modified=0

for file in $files; do
    # 檢測是否是相對於父目錄的路徑（針對子目錄中的專案）
    # 例如：在 ml-workflow-cloud-functions/lta-user-stats/ 目錄中執行，但路徑是 lta-user-stats/file.py
    if [[ "$file" == *"/"* ]]; then
        # 獲取當前目錄名稱
        current_dir=$(basename $(pwd))
        # 檢查路徑是否以當前目錄開頭
        if [[ "$file" == "$current_dir/"* ]]; then
            # 移除目錄前綴
            local_file=$(echo "$file" | sed "s|^$current_dir/||")
            echo "調整路徑: $file -> $local_file"
        else
            local_file="$file"
        fi
    else
        local_file="$file"
    fi
    
    # 檢查文件是否存在（嘗試兩種路徑）
    if [ -f "$file" ]; then
        actual_file="$file"
        echo "處理: $file"
    elif [ -f "$local_file" ]; then
        actual_file="$local_file"
        echo "處理: $local_file (原路徑: $file)"
    else
        echo "檔案不存在: $file 或 $local_file"
        continue
    fi
    
    # 檢查文件中是否有尾隨空格
    if grep -q '[[:space:]]$' "$actual_file"; then
        # 移除尾隨空格
        sed -i '' 's/[[:space:]]*$//' "$actual_file"
        git add "$actual_file"
        modified=1
        echo "- 已移除 $actual_file 中的尾隨空格"
    fi
done

if [ "$modified" -eq 0 ]; then
    echo "未發現尾隨空格。"
else
    echo "已移除尾隨空格並更新暫存區。繼續提交..."
fi

exit 0
