import functions_framework
import pandas as pd
import dask.dataframe as dd
from datetime import datetime
from datetime import timedelta
from pytz import timezone
from typing import List, Dict, Tuple, Set
from ctypes import ArgumentError
from google.oauth2 import service_account
import google_storage
from google.cloud import bigquery
from google.cloud import storage

def load_data(path: str, file_format: str = 'PARQUET', **kwargs) -> pd.core.frame.DataFrame:
  if file_format == 'PARQUET':
    df = dd.read_parquet(path, assume_missing=True, **kwargs).compute()
  elif file_format == 'CSV':
    df = dd.read_csv(path, assume_missing=True, **kwargs).compute()
  else:
    raise ArgumentError("Invalid file format")
  return df


def object_exists(bucket_name: str, blob_name: str, credentials: service_account.Credentials) -> bool:
  """Check if an object exists in the GCS bucket.

  Args:
  - bucket_name: Name of the GCS bucket.
  - blob_name: Name (path) of the object within the bucket.

  Returns:
  - True if the object exists, otherwise False.
  """
  storage_client = storage.Client(credentials=credentials)
  bucket = storage_client.bucket(bucket_name)
  blob = bucket.blob(blob_name)
  return blob.exists()

@functions_framework.http
def main(request):
  
  # * external argument
  current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
  # uid = str(uuid.uuid4())
  output_path = f'gs://tagtoo-ml-workflow/topic10/estate_category_user_only_region/{current_date}' # output gcs path
  query_path = output_path + '/query/*.parquet'
  
  # Credentials
  key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
  credentials = service_account.Credentials.from_service_account_file(key_path)
  project_id = credentials.project_id
  GSIO = google_storage.GoogleStorageIO(credentials=credentials)

  # * Query if not exist
  bucket_name = 'tagtoo-ml-workflow'
  blob_name = '/'.join(query_path.split('/')[3:-1]) + '/************.parquet'
  if object_exists(bucket_name, blob_name, credentials):
    print(f"{query_path} exist.")
  else:
    QUERY = f"""
    EXPORT DATA
    OPTIONS (
        uri = '{query_path}',
        format = 'PARQUET',
        overwrite = true)
    AS (
      SELECT
  DISTINCT permanent,
  item.name,
  location.region_name,
  location.city_name
FROM
  `tagtoo-tracking.event_prod.tagtoo_event`,
  UNNEST(event.items) AS item
WHERE
  DATE(event_time) >= DATE_SUB(CURRENT_DATE(), INTERVAL 30 DAY)
  AND DATE(event_time) <= DATE(CURRENT_DATE())
  AND item.name IS NOT NULL
  AND (item.name LIKE '%房市%'
	OR item.name LIKE '%房價%'
	OR item.name LIKE '%租屋%'
	OR item.name LIKE '%租金%'
	OR item.name LIKE '%豪宅%'
	OR item.name LIKE '%住宅%'
	OR item.name LIKE '%賣房%'
	OR item.name LIKE '%房產%'
	OR item.name LIKE '%不動產%'
	OR item.name LIKE '%房地產%'
	OR item.name LIKE '%購房%'
	OR item.name LIKE '%買房%'
  OR item.name LIKE '%首購%'
  OR item.name LIKE '%租賃%'
  OR item.name LIKE '%房子%'
  OR item.name LIKE '%建商%'
  OR item.name LIKE '%屋主%')
  AND(item.name NOT LIKE '%生活 | 三立新聞網%')
  AND( item.name NOT LIKE '%社會 | 三立新聞網%')
  AND( item.name NOT LIKE '%國際 | 三立新聞網%')
  AND( item.name NOT LIKE '%政治 | 三立新聞網%')
  AND( item.name NOT LIKE '%娛樂星聞 | 三立新聞網%')
  AND( item.name NOT LIKE '%兩岸 | 三立新聞網%')
  AND( item.name NOT LIKE '%女孩 | 三立新聞網%')
  AND( item.name NOT LIKE '%汽車 | 三立新聞網%')
  AND( location.region_name is not null)
  AND( location.city_name is not null)
  AND( location.country_code = "TW")
    );
    """
    client = bigquery.Client(credentials=credentials)
    query_job = client.query(QUERY)  # API request
    rows = query_job.result()  # Waits for query to finish
    print("Query job complete.")

  # * Load data
  data = load_data(query_path, file_format='PARQUET')
  data.reset_index(drop=True, inplace=True)
  # Define the mapping of regions to Northern, Central, Southern Taiwan
  region_mapping = {
      'Taipei': 'Northern', 'New Taipei': 'Northern', 'Taoyuan': 'Northern', 'Hsinchu': 'Northern',
      'Keelung': 'Northern', 'Yilan': 'Northern', 'Taichung': 'Central', 'Changhua': 'Central',
      'Nantou': 'Central', 'Yunlin': 'Central', 'Miaoli': 'Central', 'Tainan': 'Southern',
      'Kaohsiung': 'Southern', 'Chiayi': 'Southern', 'Pingtung': 'Southern', 'Taitung': 'Southern',
      'Hualien': 'Eastern', 'Penghu': 'Islands', 'Kinmen': 'Islands', 'Lienchiang': 'Islands'
  }

  # Apply the mapping to the 'region_name' column
  data['Region_Category'] = data['region_name'].map(region_mapping)

  # 定義一個函數來創建 segment_id
  def create_segment_id(row):
      mapping = {
        ('Northern'): "tm:d1803",
        ('Central'): "tm:d1804",
        ('Southern'): "tm:d1805",
    }
      return mapping.get((row['Region_Category']), 'Unknown')

  # 應用函數創建新欄位
  data['segment_id'] = data.apply(create_segment_id, axis=1)
  result = data[data['segment_id']!='Unknown']
  result = result[['permanent','segment_id']]

  print("Upload to GCS.")
  path = "/tmp/result.csv"
  gcs_path = output_path + '/result/result.csv'
  result.to_csv(path, index=False)
  GSIO.upload_file(gsuri=gcs_path, localpath=path)
  print("Result GCS path:", gcs_path)

  print("Upload dataframe to BigQuery.")
  DATE = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y%m%d")
  project_id = "tagtoo-ml-workflow"
  table_id = f'tagtoo_export_results.special_lta_temp_for_update_{DATE}'
  schema_data = [
    {"name": "permanent", "type": "STRING"},
    {"name": "segment_id", "type": "STRING"}
  ]
  result.to_gbq(table_id, project_id=project_id, credentials=credentials, table_schema=schema_data, if_exists='append')

  return 'Success', 200
