import functions_framework
import logging
import numpy as np
import pandas as pd
import random
import block_timer
import dask_utils
from dask_utils import load_data, parquet_load_data
import joblib
import google_storage
import dask.dataframe as dd
from google.cloud import bigquery
import datetime
from pytz import timezone
from typing import List, Tuple, Dict, Set
import json
from datetime import datetime, timedelta
from google.oauth2 import service_account
from tqdm import tqdm
import os

def trans(alist: pd.Series) -> List[int]:
    """
    Extract values from a dictionary based on indices of a list if the list value > 0.

    Args:
    - alist (List[int]): Input list with string indices and integer values.
    - dict1 (Dict[str, int]): Dictionary with string keys and integer values -> {label_name: semgent_id}.

    Returns:
    - List: A list containing unique **segment_id** extracted from the dictionary.
    """

    temp = list(alist.index[alist > 0])

    return temp

def str_isin(x, input_str):
  return (input_str == x)

def make_date_arguments(current_datetime: datetime) -> Dict[str, str]:

  DATE_FORMAT = "%Y-%m-%d"
  tmp_dict = {}
  
  tmp_dict['start_date'] = (current_datetime - timedelta(days=31)).strftime(DATE_FORMAT)
  tmp_dict['end_date'] = (current_datetime - timedelta(days=1)).strftime(DATE_FORMAT)

  return tmp_dict

@functions_framework.http
def main(request):
    
    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)

    GSIO = google_storage.GoogleStorageIO(credentials=credentials)
    today = datetime.now(timezone('Asia/Taipei'))
    date_dict = make_date_arguments(today)

    # load data
    # export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature/{date_dict['start_date']}_{date_dict['end_date']}/transformed/*.parquet"
    export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature/{date_dict['start_date']}_{date_dict['end_date']}/transformed/*.ftr"
    print(export_path)
    # df = parquet_load_data(export_path)
    df = pd.read_feather(export_path)
    # df = parquet_load_data(export_path).iloc[:500]
    df = df.reset_index(drop=True)
 
    # start predicting
    user = df.iloc[:, :1]
    df_feature = df.iloc[:,1:]
    feature = np.array(df_feature)

    # load model 2
    print('loading model 2')
    GSIO.download_to_path(gsuri='gs://tagtoo-ml-workflow/topic10/firstparty/model/third_layer.pkl', localpath='/tmp/third_layer.pkl')
    clf = joblib.load('/tmp/third_layer.pkl')

    print('start predicting')
    predictions2 = clf.predict(feature)
    print('finish predicting')

    df = pd.concat([user, pd.DataFrame(predictions2)], axis=1)

    # labeling
    cols = ['permanent',
    'purchase_Arts & Crafts',
    'purchase_Baby & Toddler Clothing',
    'purchase_Bakery',
    'purchase_Baseball & Softball',
    'purchase_Basketball',
    'purchase_Board Games',
    'purchase_Boxing & Martial Arts',
    'purchase_Camping & Hiking',
    'purchase_Candy & Chocolate',
    'purchase_Card Games',
    'purchase_Coffee',
    'purchase_Condiments & Sauces',
    'purchase_Cosmetics',
    'purchase_Cotton Balls',
    'purchase_Cotton Swabs',
    'purchase_Cycling',
    'purchase_Dairy Products',
    'purchase_Deodorant & Anti-Perspirant',
    'purchase_Dresses',
    'purchase_Ear Care',
    'purchase_Educational Toys',
    'purchase_Executive Toys',
    'purchase_Feminine Sanitary Supplies',
    'purchase_Fishing',
    'purchase_Foot Care',
    'purchase_Grains, Rice & Cereal',
    'purchase_Hair Care',
    'purchase_Hot Chocolate',
    'purchase_Magazines',
    'purchase_Massage & Relaxation',
    'purchase_Meat, Seafood & Eggs',
    'purchase_Milk',
    'purchase_Motor Vehicle Parts',
    'purchase_Musical Instruments',
    'purchase_Newspapers',
    'purchase_Nuts & Seeds',
    'purchase_Oral Care',
    'purchase_Outerwear',
    'purchase_Pants',
    'purchase_Pasta & Noodles',
    'purchase_Ping Pong',
    'purchase_Religious Veils',
    'purchase_Seasonings & Spices',
    'purchase_Sex Toys',
    'purchase_Shaving & Grooming',
    'purchase_Shirts & Tops',
    'purchase_Shorts',
    'purchase_Skirts',
    'purchase_Skorts',
    'purchase_Sleeping Aids',
    'purchase_Sleepwear & Loungewear',
    'purchase_Soccer',
    'purchase_Soda',
    'purchase_Soups & Broths',
    'purchase_Sports & Energy Drinks',
    'purchase_Spray Tanning Tents',
    'purchase_Suits',
    'purchase_Swimwear',
    'purchase_Tarot Cards',
    'purchase_Tea & Infusions',
    'purchase_Team Handball',
    'purchase_Televisions',
    'purchase_Tennis',
    'purchase_Throwing Darts',
    'purchase_Tofu, Soy & Vegetarian Products',
    'purchase_Underwear & Socks',
    'purchase_Uniforms',
    'purchase_Vehicle Maintenance, Care & Decor',
    'purchase_Vision Care',
    'purchase_Wallyball Equipment',
    'purchase_Water',
    'purchase_Wedding & Bridal Party Dresses']

    # GSIO.download_to_path(
    #   gsuri="gs://tagtoo-ml-workflow/topic10/firstparty/segment_name_to_id/2023-12-15/label_to_segment_id.json",
    #   localpath="/tmp/label_to_segment_id.json"
    # )
    # with open("/tmp/label_to_segment_id.json", "r") as file:
    #     dict1 = json.load(file)
    # print('dictionary loaded')

    dict1 = {'purchase_Baby & Toddler Clothing': 766,
    'purchase_Outerwear': 767,
    'purchase_Pants': 768,
    'purchase_Shorts': 769,
    'purchase_Sleepwear & Loungewear': 770,
    'purchase_Swimwear': 771,
    'purchase_Shirts & Tops': 772,
    'purchase_Underwear & Socks': 773,
    'purchase_Skirts': 774,
    'purchase_Suits': 775,
    'purchase_Dresses': 777,
    'purchase_Uniforms': 778,
    'purchase_Skorts': 779,
    'purchase_Wedding & Bridal Party Dresses': 780,
    'purchase_Outfit Sets': 781,
    'purchase_Arts & Crafts': 790,
    'purchase_Musical Instruments': 791,
    'purchase_Computer Monitors': 818,
    'purchase_Televisions': 820,
    'purchase_Home Game Console Accessories': 822,
    'purchase_Portable Game Console Accessories': 823,
    'purchase_Hot Chocolate': 827,
    'purchase_Milk': 828,
    'purchase_Water': 829,
    'purchase_Coffee': 830,
    'purchase_Tea & Infusions': 831,
    'purchase_Soda': 832,
    'purchase_Juice': 833,
    'purchase_Sports & Energy Drinks': 834,
    'purchase_Powdered Beverage Mixes': 835,
    'purchase_Fruit Flavored Drinks': 836,
    'purchase_Alcoholic Beverages': 837,
    'purchase_Snack Foods': 839,
    'purchase_Condiments & Sauces': 840,
    'purchase_Dairy Products': 841,
    'purchase_Fruits & Vegetables': 842,
    'purchase_Grains, Rice & Cereal': 843,
    'purchase_Meat, Seafood & Eggs': 844,
    'purchase_Nuts & Seeds': 845,
    'purchase_Pasta & Noodles': 846,
    'purchase_Bakery': 847,
    'purchase_Soups & Broths': 848,
    'purchase_Cooking & Baking Ingredients': 849,
    'purchase_Seasonings & Spices': 850,
    'purchase_Candy & Chocolate': 851,
    'purchase_Dips & Spreads': 852,
    'purchase_Frozen Desserts & Novelties': 853,
    'purchase_Tofu, Soy & Vegetarian Products': 854,
    'purchase_Prepared Foods': 855,
    'purchase_Cosmetics': 899,
    'purchase_Deodorant & Anti-Perspirant': 900,
    'purchase_Feminine Sanitary Supplies': 901,
    'purchase_Hair Care': 902,
    'purchase_Back Care': 903,
    'purchase_Ear Care': 904,
    'purchase_Foot Care': 905,
    'purchase_Oral Care': 906,
    'purchase_Shaving & Grooming': 907,
    'purchase_Personal Lubricants': 908,
    'purchase_Vision Care': 909,
    'purchase_Cotton Swabs': 911,
    'purchase_Sleeping Aids': 912,
    'purchase_Cotton Balls': 913,
    'purchase_Massage & Relaxation': 914,
    'purchase_Spray Tanning Tents': 915,
    'purchase_Enema Kits & Supplies': 916,
    'purchase_Erotic Clothing': 940,
    'purchase_Erotic DVDs & Videos': 941,
    'purchase_Sex Toys': 942,
    'purchase_Erotic Books': 943,
    'purchase_Erotic Magazines': 944,
    'purchase_Erotic Games': 945,
    'purchase_Magazines': 951,
    'purchase_Newspapers': 952,
    'purchase_Memorial Urns': 968,
    'purchase_Religious Veils': 970,
    'purchase_Prayer Beads': 971,
    'purchase_Tarot Cards': 972,
    'purchase_Ring Pillows & Holders': 974,
    'purchase_Team Handball': 980,
    'purchase_Track & Field': 981,
    'purchase_Tennis': 982,
    'purchase_Baseball & Softball': 983,
    'purchase_Basketball': 984,
    'purchase_Soccer': 985,
    'purchase_Volleyball': 986,
    'purchase_Boxing & Martial Arts': 988,
    'purchase_General Purpose Athletic Equipment': 989,
    'purchase_Wallyball Equipment': 990,
    'purchase_Billiards': 993,
    'purchase_Bowling': 994,
    'purchase_Throwing Darts': 995,
    'purchase_Ping Pong': 996,
    'purchase_Camping & Hiking': 998,
    'purchase_Cycling': 999,
    'purchase_Equestrian': 1000,
    'purchase_Golf': 1001,
    'purchase_Skateboarding': 1002,
    'purchase_Fishing': 1003,
    'purchase_Riding Scooters': 1004,
    'purchase_Climbing': 1005,
    'purchase_Inline & Roller Skating': 1006,
    'purchase_Boating & Water Sports': 1007,
    'purchase_Hunting & Shooting': 1008,
    'purchase_Winter Sports & Activities': 1009,
    'purchase_Outdoor Games': 1010,
    'purchase_Board Games': 1013,
    'purchase_Card Games': 1014,
    'purchase_Educational Toys': 1018,
    'purchase_Executive Toys': 1019,
    'purchase_Motor Vehicle Parts': 1021,
    'purchase_Vehicle Maintenance, Care & Decor': 1022,
    'purchase_Vehicle Storage & Cargo': 1024,
    'purchase_Vehicle Safety & Security': 1025,
    'purchase_Motor Vehicle Electronics': 1026}

    for i in range(len(cols[1:])):
      cols[i+1] = str(dict1[cols[i+1]])

    df.columns = cols
    print('prediction dataframe done')

    # df['segment_id'] = df.apply(lambda row: trans(row[2:]), axis = 1)
    df['segment_id'] = df.drop('permanent', axis=1).apply(lambda row: trans(row), axis=1)
    df = df[['permanent', 'segment_id']]
    print("third layer done")


    print("start making tables")
    # df_ttd = df.copy()
    df = df.groupby('permanent', as_index=False).sum()
    df['segment_id'] = df['segment_id'].apply(lambda x: list(set(x)))

    print("tables done")

    # import os
    # os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    # schema = [
    # {"name": "permanent", "type": "STRING"},
    # {"name": 'segment_id', "type": "STRING"}
    # ]
    date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
    name = f'lta_remarketing_{date}'

    # project_id = "tagtoo-ml-workflow"
    # table_id = f'tagtoo_export_results.lta_model_fb'
    # df.to_gbq(table_id, project_id=project_id, table_schema = schema, if_exists = 'replace') 

    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    rname = f'{name}_ttd_model_third_layer.parquet'
    df.to_parquet(f'/tmp/{rname}', index=False)
    GSIO.upload_file(f'/tmp/{rname}', f'gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date}/{rname}')
            
    print('Job complete.')

    return 'Success', 200
