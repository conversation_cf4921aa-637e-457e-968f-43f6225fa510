"""
Cloud Scheduler 觸發測試

此檔案模擬 Cloud Scheduler 的 HTTP 觸發，測試 Cloud Function 是否能正確處理來自排程器的請求。
包含 OIDC 認證模擬和排程參數測試。

執行方式:
  python3 test_scheduler.py

注意：這是模擬測試，不會實際觸發 Cloud Scheduler
"""

import json
import time
from datetime import datetime, timezone
from unittest.mock import Mock


class CloudSchedulerTriggerTest:
    """Cloud Scheduler 觸發測試類別"""

    def __init__(self):
        self.scheduler_configs = [
            {"name": "03am", "schedule_time": "03:00", "source": "scheduler-03am"},
            {"name": "05am", "schedule_time": "05:00", "source": "scheduler-05am"},
            {"name": "07am", "schedule_time": "07:00", "source": "scheduler-07am"},
        ]

    def create_scheduler_request(self, scheduler_config):
        """建立模擬的 Cloud Scheduler 請求"""
        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.headers = {
            "Content-Type": "application/json",
            "User-Agent": "Google-Cloud-Scheduler",
        }

        # 模擬 Scheduler 發送的 JSON 資料
        scheduler_payload = {
            "project_id": "gothic-province-823",
            "dataset_id": "tagtooad",
            "table_id": "partner_uid_mapping",
            "source": scheduler_config["source"],
            "scheduled_time": scheduler_config["schedule_time"],
        }

        mock_request.get_json.return_value = scheduler_payload
        return mock_request, scheduler_payload

    def test_scheduler_request_processing(self, scheduler_config):
        """測試 Cloud Scheduler 請求處理"""
        print(f"🕐 測試 {scheduler_config['name']} 排程觸發...")

        try:
            from main import main

            # 建立模擬的 Scheduler 請求
            mock_request, expected_payload = self.create_scheduler_request(
                scheduler_config
            )

            # 執行函數
            start_time = time.time()
            response, status_code = main(mock_request)
            execution_time = time.time() - start_time

            # 驗證回應
            if status_code != 200:
                print(f"   ❌ 狀態碼錯誤: {status_code}")
                response_data = json.loads(response)
                print(f"   錯誤訊息: {response_data.get('error', 'Unknown error')}")
                return False

            # 解析回應
            response_data = json.loads(response)

            # 驗證回應結構
            required_fields = [
                "has_streaming_buffer",
                "table_reference",
                "status",
                "queried_at",
            ]
            for field in required_fields:
                if field not in response_data:
                    print(f"   ❌ 回應中缺少必要欄位: {field}")
                    return False

            # 驗證表格引用
            table_ref = response_data["table_reference"]
            if (
                table_ref["project_id"] != expected_payload["project_id"]
                or table_ref["dataset_id"] != expected_payload["dataset_id"]
                or table_ref["table_id"] != expected_payload["table_id"]
            ):
                print(f"   ❌ 表格引用不正確")
                return False

            # 驗證成功狀態
            if response_data["status"] != "success":
                print(f"   ❌ 回應狀態不是成功: {response_data['status']}")
                return False

            print(f"   ✅ {scheduler_config['name']} 排程測試成功")
            print(f"   ⏱️  執行時間: {execution_time:.3f} 秒")
            print(
                f"   📊 串流緩衝區: {'有' if response_data['has_streaming_buffer'] else '無'}"
            )
            print(f"   📈 預估行數: {response_data['estimated_rows']}")

            return True

        except Exception as e:
            print(f"   ❌ 排程測試失敗: {str(e)}")
            return False

    def test_scheduler_authentication_headers(self):
        """測試 Cloud Scheduler 認證標頭處理"""
        print("🔐 測試 Cloud Scheduler 認證標頭...")

        try:
            from main import main

            # 建立帶有認證標頭的請求
            mock_request = Mock()
            mock_request.method = "POST"
            mock_request.headers = {
                "Content-Type": "application/json",
                "User-Agent": "Google-Cloud-Scheduler",
                "Authorization": "Bearer mock-oidc-token",  # 模擬 OIDC token
            }
            mock_request.get_json.return_value = {
                "project_id": "gothic-province-823",
                "dataset_id": "tagtooad",
                "table_id": "partner_uid_mapping",
                "source": "scheduler-test",
            }

            # 執行函數
            response, status_code = main(mock_request)

            # 函數應該正常處理 (不會驗證實際的 OIDC token)
            if status_code == 200:
                print("   ✅ 認證標頭處理正常")
                return True
            else:
                print(f"   ❌ 認證標頭處理失敗: {status_code}")
                return False

        except Exception as e:
            print(f"   ❌ 認證標頭測試失敗: {str(e)}")
            return False

    def test_scheduler_error_scenarios(self):
        """測試 Cloud Scheduler 錯誤場景"""
        print("⚠️  測試 Cloud Scheduler 錯誤場景...")

        error_scenarios = [
            {
                "name": "無效 JSON",
                "request_setup": lambda req: setattr(req, "get_json", lambda: None),
                "expected_status": 400,
            },
            {
                "name": "空的 JSON",
                "request_setup": lambda req: req.get_json.return_value.update({}),
                "expected_status": 200,  # 會使用預設值
            },
            {
                "name": "錯誤的表格",
                "request_setup": lambda req: req.get_json.return_value.update(
                    {
                        "project_id": "invalid-project",
                        "dataset_id": "invalid-dataset",
                        "table_id": "invalid-table",
                    }
                ),
                "expected_status": 404,  # 表格不存在
            },
        ]

        passed = 0
        total = len(error_scenarios)

        for scenario in error_scenarios:
            try:
                from main import main

                # 建立基本請求
                mock_request = Mock()
                mock_request.method = "POST"
                mock_request.headers = {"Content-Type": "application/json"}
                mock_request.get_json.return_value = {
                    "project_id": "gothic-province-823",
                    "dataset_id": "tagtooad",
                    "table_id": "partner_uid_mapping",
                    "source": "scheduler-error-test",
                }

                # 應用錯誤場景設定
                scenario["request_setup"](mock_request)

                # 執行函數
                response, status_code = main(mock_request)

                # 驗證預期的錯誤狀態碼
                if status_code == scenario["expected_status"]:
                    print(f"   ✅ {scenario['name']} 錯誤處理正確")
                    passed += 1
                else:
                    print(
                        f"   ❌ {scenario['name']} 錯誤處理失敗: 預期 {scenario['expected_status']}, 實際 {status_code}"
                    )

            except Exception as e:
                print(f"   ❌ {scenario['name']} 測試異常: {str(e)}")

        print(f"   📊 錯誤場景測試: {passed}/{total} 通過")
        return passed == total

    def test_scheduler_timing_simulation(self):
        """測試 Cloud Scheduler 時間模擬"""
        print("⏰ 測試 Cloud Scheduler 時間模擬...")

        # 模擬一天中的三個排程時間
        current_time = datetime.now(timezone.utc)

        for config in self.scheduler_configs:
            print(f"   🕐 模擬 {config['schedule_time']} 觸發...")

            # 計算觸發時間（模擬）
            scheduled_hour = int(config["schedule_time"].split(":")[0])
            trigger_time = current_time.replace(
                hour=scheduled_hour, minute=0, second=0, microsecond=0
            )

            # 建立帶時間資訊的請求
            mock_request = Mock()
            mock_request.method = "POST"
            mock_request.get_json.return_value = {
                "project_id": "gothic-province-823",
                "dataset_id": "tagtooad",
                "table_id": "partner_uid_mapping",
                "source": config["source"],
                "scheduled_time": config["schedule_time"],
                "trigger_time": trigger_time.isoformat(),
            }

            try:
                from main import main

                response, status_code = main(mock_request)

                if status_code == 200:
                    response_data = json.loads(response)
                    print(f"      ✅ {config['schedule_time']} 時間模擬成功")
                    print(f"      📅 查詢時間: {response_data['queried_at']}")
                else:
                    print(
                        f"      ❌ {config['schedule_time']} 時間模擬失敗: {status_code}"
                    )

            except Exception as e:
                print(f"      ❌ {config['schedule_time']} 時間模擬異常: {str(e)}")

        return True

    def run_all_scheduler_tests(self):
        """執行所有 Cloud Scheduler 測試"""
        print("📅 開始 Cloud Scheduler 觸發測試")
        print("=" * 50)

        tests_passed = 0
        total_tests = 0

        # 1. 測試各個排程觸發
        print("\n1️⃣ 測試排程觸發:")
        for config in self.scheduler_configs:
            total_tests += 1
            if self.test_scheduler_request_processing(config):
                tests_passed += 1

        # 2. 測試認證標頭
        print("\n2️⃣ 測試認證標頭:")
        total_tests += 1
        if self.test_scheduler_authentication_headers():
            tests_passed += 1

        # 3. 測試錯誤場景
        print("\n3️⃣ 測試錯誤場景:")
        total_tests += 1
        if self.test_scheduler_error_scenarios():
            tests_passed += 1

        # 4. 測試時間模擬
        print("\n4️⃣ 測試時間模擬:")
        total_tests += 1
        if self.test_scheduler_timing_simulation():
            tests_passed += 1

        # 總結
        print("\n" + "=" * 50)
        print(f"🎯 Cloud Scheduler 測試結果: {tests_passed}/{total_tests} 通過")

        if tests_passed == total_tests:
            print("🎉 所有 Cloud Scheduler 觸發測試通過！")
            print("\n✅ 功能驗證:")
            print("   - Cloud Scheduler POST 請求處理正常")
            print("   - 三個排程時間 (03:00, 05:00, 07:00) 都能正確觸發")
            print("   - OIDC 認證標頭處理正常")
            print("   - 錯誤場景處理正確")
            print("   - 時間參數處理正常")

            print("\n📝 部署後驗證步驟:")
            print("   1. 檢查 Cloud Scheduler 作業狀態")
            print("   2. 手動觸發測試排程")
            print("   3. 檢視 Cloud Function 日誌")
            print("   4. 驗證 BigQuery 查詢成功")
        else:
            print("⚠️  部分 Cloud Scheduler 測試失敗，請檢查問題。")

        return tests_passed == total_tests


def print_scheduler_test_guide():
    """列印 Cloud Scheduler 測試指南"""
    print(
        """
📅 Cloud Scheduler 測試指南

🎯 測試目的:
- 驗證 Cloud Function 能正確處理 Cloud Scheduler 的 HTTP 觸發
- 測試三個排程時間的觸發邏輯
- 驗證 OIDC 認證標頭處理
- 測試錯誤場景和異常處理

🚀 執行測試:
python3 test_scheduler.py

📋 測試涵蓋範圍:
✓ 03:00 AM 排程觸發
✓ 05:00 AM 排程觸發  
✓ 07:00 AM 排程觸發
✓ OIDC 認證標頭處理
✓ 錯誤場景處理
✓ 時間參數驗證

🔧 實際部署後驗證:
1. gcloud scheduler jobs list --location=asia-east1
2. gcloud scheduler jobs run JOB_NAME --location=asia-east1
3. gcloud functions logs read FUNCTION_NAME --region=asia-east1

⚠️  注意事項:
- 這是模擬測試，不會實際觸發 Cloud Scheduler
- 實際的 OIDC 認證需要在 GCP 環境中驗證
- 排程時間使用 Asia/Taipei 時區 (UTC+8)
"""
    )


if __name__ == "__main__":
    import sys

    if len(sys.argv) > 1 and sys.argv[1] == "--guide":
        print_scheduler_test_guide()
    else:
        scheduler_test = CloudSchedulerTriggerTest()
        success = scheduler_test.run_all_scheduler_tests()
        sys.exit(0 if success else 1)
