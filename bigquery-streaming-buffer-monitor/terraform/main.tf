# Terraform configuration for BigQuery Streaming Buffer Monitor Cloud Function
# 此檔案定義 BigQuery 串流緩衝區監控 Cloud Function 的基礎設施

terraform {
  required_version = ">= 1.5.0"
  required_providers {
    google = {
      source  = "hashicorp/google"
      version = "~> 4.0"
    }
    archive = {
      source  = "hashicorp/archive"
      version = "~> 2.4"
    }
  }
  backend "gcs" {
    bucket = "tagtoo-ml-workflow-cloud-functions"
    prefix = "terraform-state/bigquery-streaming-buffer-monitor"
  }
}

# 啟用必要 API
resource "google_project_service" "functions_api" {
  project            = var.project_id
  service            = "cloudfunctions.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudrun_api" {
  project            = var.project_id
  service            = "run.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudbuild_api" {
  project            = var.project_id
  service            = "cloudbuild.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "bigquery_api" {
  project            = var.project_id
  service            = "bigquery.googleapis.com"
  disable_on_destroy = false
}

resource "google_project_service" "cloudscheduler_api" {
  project            = var.project_id
  service            = "cloudscheduler.googleapis.com"
  disable_on_destroy = false
}

# Google Cloud Provider 配置
provider "google" {
  project = var.project_id
  region  = var.region
}

# 變數定義
variable "project_id" {
  description = "GCP 專案 ID"
  type        = string
  default     = "tagtoo-ml-workflow"
}

variable "region" {
  description = "部署區域"
  type        = string
  default     = "asia-east1"
}

variable "function_name" {
  description = "Cloud Function 名稱"
  type        = string
  default     = "bigquery-streaming-buffer-monitor"
}

variable "runtime" {
  description = "Runtime 版本"
  type        = string
  default     = "python311"
}

variable "memory" {
  description = "記憶體大小 (MB)"
  type        = number
  default     = 256
}

variable "cpu" {
  description = "vCPU 數量"
  type        = string
  default     = "1"
}

variable "timeout" {
  description = "超時時間 (秒)"
  type        = number
  default     = 60
}

variable "environment_variables" {
  description = "環境變數"
  type        = map(string)
  default = {
    ENV = "production"
  }
}

variable "max_instances" {
  description = "最大執行個體數量"
  type        = number
  default     = 5
}

# 使用現有的共用存儲桶，在 source/ 目錄下存放 Cloud Functions 源代碼
data "google_storage_bucket" "shared_bucket" {
  name = "${var.project_id}-cloud-functions"
}

# 壓縮源代碼
data "archive_file" "source_zip" {
  type        = "zip"
  source_dir  = "${path.module}/../"
  output_path = "${path.module}/source.zip"
  excludes = [
    "terraform/",
    ".terraform/",
    "*.tf",
    "*.tfstate*",
    "source.zip",
    ".git/",
    "__pycache__/",
    "*.pyc",
    "venv/",
    ".venv/"
  ]
}

# 上傳源代碼到共用存儲桶的 source/ 目錄下
resource "google_storage_bucket_object" "source_archive" {
  name   = "source/${var.function_name}/${var.function_name}-${data.archive_file.source_zip.output_md5}.zip"
  bucket = data.google_storage_bucket.shared_bucket.name
  source = data.archive_file.source_zip.output_path
}

# 使用統一的服務帳戶 - 基於安全考量使用 runtime service account
# 這樣 Python 程式碼中的 bigquery.Client() 會自動使用 Application Default Credentials
locals {
  service_account_email = "<EMAIL>"
}

# 注意：<EMAIL> 已經存在
# 並且已經具備必要的 BigQuery 權限，無需額外創建或配置 IAM 權限
# 這樣確保了：
# 1. 程式碼中沒有硬編碼的 service account key files
# 2. 使用 Application Default Credentials (ADC) 最佳實踐
# 3. 集中化的權限管理

# Cloud Function 2nd Gen
resource "google_cloudfunctions2_function" "function" {
  name        = var.function_name
  description = "BigQuery 串流緩衝區監控 Cloud Function"
  location    = var.region

  build_config {
    runtime     = var.runtime
    entry_point = "main"

    source {
      storage_source {
        bucket = data.google_storage_bucket.shared_bucket.name
        object = google_storage_bucket_object.source_archive.name
      }
    }
  }

  service_config {
    max_instance_count    = var.max_instances
    available_memory      = "${var.memory}Mi"
    available_cpu         = var.cpu
    timeout_seconds       = var.timeout
    service_account_email = local.service_account_email

    environment_variables = var.environment_variables
  }

  labels = {
    environment = "production"
    function    = var.function_name
    managed_by  = "terraform"
    purpose     = "bigquery-monitoring"
  }
}

# Cloud Scheduler Job - 每小時觸發一次
resource "google_cloud_scheduler_job" "hourly_monitor_job" {
  name             = "${var.function_name}-hourly"
  description      = "BigQuery 串流緩衝區監控 - 每小時觸發"
  schedule         = "0 * * * *" # 每小時的 0 分執行
  time_zone        = "Asia/Taipei"
  attempt_deadline = "60s"

  http_target {
    uri         = google_cloudfunctions2_function.function.service_config[0].uri
    http_method = "POST"
    headers = {
      "Content-Type" = "application/json"
    }
    body = base64encode(jsonencode({
      project_id     = "gothic-province-823"
      dataset_id     = "tagtooad"
      table_id       = "partner_uid_mapping"
      source         = "scheduler-hourly"
    }))

    # 使用 OIDC 認證 - 安全性最佳實踐
    oidc_token {
      service_account_email = local.service_account_email
    }
  }

  retry_config {
    retry_count = 3
  }
}

# IAM 策略 - Cloud Function 需要被 Cloud Scheduler 調用
# 不允許公開訪問，只允許經過 OIDC 認證的調用
resource "google_cloud_run_service_iam_member" "scheduler_invoker" {
  project  = google_cloudfunctions2_function.function.project
  location = google_cloudfunctions2_function.function.location
  service  = google_cloudfunctions2_function.function.name

  role   = "roles/run.invoker"
  member = "serviceAccount:${local.service_account_email}"
}

# 輸出
output "function_url" {
  description = "Cloud Function 的 HTTP 觸發器 URL"
  value       = google_cloudfunctions2_function.function.service_config[0].uri
}

output "function_name" {
  description = "Cloud Function 名稱"
  value       = google_cloudfunctions2_function.function.name
}

output "service_account_email" {
  description = "Cloud Function 使用的服務帳戶郵箱"
  value       = local.service_account_email
}

output "scheduler_job" {
  description = "Cloud Scheduler 工作名稱"
  value       = google_cloud_scheduler_job.hourly_monitor_job.name
}

output "source_bucket" {
  description = "共用源代碼存儲桶名稱"
  value       = data.google_storage_bucket.shared_bucket.name
}