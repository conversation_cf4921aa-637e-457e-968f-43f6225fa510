# Example Terraform variables file for BigQuery Streaming Buffer Monitor
# 複製此檔案為 terraform.tfvars 並根據需要修改值

# 基本配置
project_id    = "tagtoo-ml-workflow"
region        = "asia-east1"
function_name = "bigquery-streaming-buffer-monitor"

# 運行時配置
runtime = "python311"
memory  = 256
cpu     = "1"
timeout = 60

# 擴展配置
max_instances = 5

# 環境變數
environment_variables = {
  ENV = "production"
}

# 注意事項：
# 1. 此函數使用 <EMAIL> 服務帳戶
# 2. 該服務帳戶已具備必要的 BigQuery 權限
# 3. 程式碼使用 Application Default Credentials，無需 service account key files
# 4. Cloud Scheduler 使用 OIDC 認證確保安全性