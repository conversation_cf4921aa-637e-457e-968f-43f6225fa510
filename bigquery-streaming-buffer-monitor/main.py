"""
BigQuery Streaming Buffer Monitor Cloud Function

此 Cloud Function 監控 BigQuery 表格的串流緩衝區狀態，
使用 BigQuery Python SDK 和 Application Default Credentials。

目標表格: gothic-province-823.tagtooad.partner_uid_mapping

支援的 HTTP 方法:
- GET: 查詢參數中提供表格資訊
- POST: JSON body 中提供表格資訊
- OPTIONS: CORS 預檢請求

回應格式:
{
  "has_streaming_buffer": boolean,
  "estimated_rows": string,
  "estimated_bytes": string,
  "oldest_entry_time": string|null,
  "table_reference": {
    "project_id": string,
    "dataset_id": string,
    "table_id": string
  },
  "queried_at": string,
  "status": "success"|"error",
  "error": string (僅在錯誤時)
}
"""

import json
import logging
import os
from datetime import datetime, timezone
from typing import Any, Dict, Optional, Tuple

import functions_framework
from google.cloud import bigquery
from google.cloud.exceptions import Forbidden, NotFound

# 設定 Google Cloud Logging
def setup_logging():
    """設置 Google Cloud Logging"""
    logger = logging.getLogger(__name__)
    
    if os.environ.get('K_SERVICE'):
        # Cloud Functions 環境 - 使用 Google Cloud Logging
        try:
            import google.cloud.logging as cloud_logging
            client = cloud_logging.Client()
            client.setup_logging(log_level=logging.INFO)
            logger.info("✅ Google Cloud Logging configured successfully")
        except Exception as e:
            # 回退到標準 logging
            logging.basicConfig(level=logging.INFO)
            logger.warning(f"⚠️ Could not setup Cloud Logging, using standard logging: {e}")
    else:
        # 本地環境 - 使用標準 logging
        logging.basicConfig(level=logging.INFO)
        logger.info("✅ Standard logging configured for local environment")
    
    return logger

logger = setup_logging()

# 預設表格參數
DEFAULT_PROJECT_ID = "gothic-province-823"
DEFAULT_DATASET_ID = "tagtooad"
DEFAULT_TABLE_ID = "partner_uid_mapping"


@functions_framework.http
def main(request) -> Tuple[str, int]:
    """
    主要 HTTP 處理器

    處理 GET、POST 和 OPTIONS 請求，查詢 BigQuery 串流緩衝區狀態

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (response_json, status_code)
    """
    try:
        # 處理 CORS 預檢請求
        if request.method == "OPTIONS":
            return handle_cors()

        # 處理 GET 和 POST 請求
        elif request.method in ["GET", "POST"]:
            return handle_buffer_status_request(request)

        else:
            # 不支援的 HTTP 方法
            return create_error_response("Unsupported method", 405)

    except Exception as e:
        logger.error(f"Unexpected error in main handler: {str(e)}", exc_info=True)
        return create_error_response("Internal server error", 500)


def handle_cors() -> Tuple[str, int]:
    """
    處理 CORS 預檢請求

    Returns:
        tuple: (empty_response, 204)
    """
    return "", 204


def handle_buffer_status_request(request) -> Tuple[str, int]:
    """
    處理 BigQuery 串流緩衝區狀態查詢請求

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (response_json, status_code)
    """
    try:
        # 提取表格參數
        project_id, dataset_id, table_id = extract_table_parameters(request)

        # 初始化 BigQuery 客戶端 (使用 Application Default Credentials)
        client = bigquery.Client()

        # 查詢串流緩衝區狀態
        buffer_info = get_streaming_buffer_info(
            client, project_id, dataset_id, table_id
        )

        # 格式化回應
        response_data = format_success_response(
            buffer_info, project_id, dataset_id, table_id
        )

        # 記錄成功的回應到日誌中，包含關鍵數據
        logger.info(f"BigQuery streaming buffer query successful for {project_id}.{dataset_id}.{table_id}")
        logger.info(f"Streaming buffer status: has_buffer={response_data['has_streaming_buffer']}, rows={response_data['estimated_rows']}, bytes={response_data['estimated_bytes']}")
        
        # 也記錄完整的JSON回應供除錯使用
        logger.info(f"Full response: {json.dumps(response_data)}")

        return json.dumps(response_data), 200

    except ValueError as e:
        logger.warning(f"Invalid request parameters: {str(e)}")
        return create_error_response(str(e), 400)

    except NotFound as e:
        logger.warning(f"Table not found: {str(e)}")
        return create_error_response("Table not found", 404)

    except Forbidden as e:
        logger.warning(f"Insufficient permissions: {str(e)}")
        return create_error_response("Insufficient permissions", 403)

    except Exception as e:
        logger.error(f"Error querying BigQuery: {str(e)}", exc_info=True)
        return create_error_response("BigQuery API error", 500)


def extract_table_parameters(request) -> Tuple[str, str, str]:
    """
    從請求中提取表格參數

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (project_id, dataset_id, table_id)

    Raises:
        ValueError: 當請求參數無效時
    """
    try:
        if request.method == "GET":
            # 從查詢參數提取
            project_id = request.args.get("project_id", DEFAULT_PROJECT_ID)
            dataset_id = request.args.get("dataset_id", DEFAULT_DATASET_ID)
            table_id = request.args.get("table_id", DEFAULT_TABLE_ID)

        elif request.method == "POST":
            # 從 JSON body 提取
            try:
                json_data = request.get_json()
            except Exception as e:
                logger.warning(f"Failed to parse JSON: {str(e)}")
                raise ValueError("Invalid JSON format")

            if json_data is None:
                raise ValueError("Invalid JSON format")

            project_id = json_data.get("project_id", DEFAULT_PROJECT_ID)
            dataset_id = json_data.get("dataset_id", DEFAULT_DATASET_ID)
            table_id = json_data.get("table_id", DEFAULT_TABLE_ID)

        else:
            raise ValueError("Unsupported request method")

        # 基本參數驗證 - 只檢查空字串，不檢查 None（None 會使用預設值）
        if project_id == "" or dataset_id == "" or table_id == "":
            raise ValueError("Project ID, dataset ID, and table ID cannot be empty")

        logger.info(f"Table parameters: {project_id}.{dataset_id}.{table_id}")
        return project_id, dataset_id, table_id

    except ValueError:
        # 重新拋出 ValueError，保持原有的錯誤處理邏輯
        raise
    except Exception as e:
        logger.error(f"Unexpected error in parameter extraction: {str(e)}")
        raise ValueError("Failed to extract request parameters")


def get_streaming_buffer_info(
    client: bigquery.Client, project_id: str, dataset_id: str, table_id: str
) -> Optional[Dict[str, Any]]:
    """
    查詢 BigQuery 表格的串流緩衝區資訊

    Args:
        client: BigQuery 客戶端
        project_id: 專案 ID
        dataset_id: 資料集 ID
        table_id: 表格 ID

    Returns:
        dict: 串流緩衝區資訊，如果沒有緩衝區則為 None

    Raises:
        NotFound: 表格不存在
        Forbidden: 權限不足
        Exception: 其他 BigQuery API 錯誤
    """
    # 建立表格引用
    table_ref = client.dataset(dataset_id, project=project_id).table(table_id)

    # 取得表格物件
    table = client.get_table(table_ref)

    # 存取串流緩衝區屬性
    streaming_buffer = table.streaming_buffer

    if streaming_buffer:
        rows = streaming_buffer.estimated_rows
        bytes_size = streaming_buffer.estimated_bytes
        logger.info(f"✅ Streaming buffer FOUND for {project_id}.{dataset_id}.{table_id} - Rows: {rows}, Bytes: {bytes_size}")
        return {
            "estimated_rows": rows,
            "estimated_bytes": bytes_size,
            "oldest_entry_time": streaming_buffer.oldest_entry_time,
        }
    else:
        logger.info(f"⚠️ No streaming buffer for {project_id}.{dataset_id}.{table_id}")
        return None


def format_success_response(
    buffer_info: Optional[Dict[str, Any]],
    project_id: str,
    dataset_id: str,
    table_id: str,
) -> Dict[str, Any]:
    """
    格式化成功回應

    Args:
        buffer_info: 串流緩衝區資訊
        project_id: 專案 ID
        dataset_id: 資料集 ID
        table_id: 表格 ID

    Returns:
        dict: 格式化的回應資料
    """
    current_time = datetime.now(timezone.utc)

    if buffer_info:
        # 有串流緩衝區
        oldest_entry_time = buffer_info["oldest_entry_time"]
        oldest_entry_iso = oldest_entry_time.isoformat() if oldest_entry_time else None

        response = {
            "has_streaming_buffer": True,
            "estimated_rows": str(buffer_info["estimated_rows"]),
            "estimated_bytes": str(buffer_info["estimated_bytes"]),
            "oldest_entry_time": oldest_entry_iso,
        }
    else:
        # 沒有串流緩衝區
        response = {
            "has_streaming_buffer": False,
            "estimated_rows": "0",
            "estimated_bytes": "0",
            "oldest_entry_time": None,
        }

    # 加入通用欄位
    response.update(
        {
            "table_reference": {
                "project_id": project_id,
                "dataset_id": dataset_id,
                "table_id": table_id,
            },
            "queried_at": current_time.isoformat(),
            "status": "success",
        }
    )

    return response


def create_error_response(error_message: str, status_code: int) -> Tuple[str, int]:
    """
    建立錯誤回應

    Args:
        error_message: 錯誤訊息
        status_code: HTTP 狀態碼

    Returns:
        tuple: (error_response_json, status_code)
    """
    error_response = {
        "status": "error",
        "error": error_message,
        "queried_at": datetime.now(timezone.utc).isoformat(),
    }

    return json.dumps(error_response), status_code
