# Standard Makefile for BigQuery Streaming Buffer Monitor Cloud Function
# 此檔案提供標準化的構建和部署命令

# 變數定義
FUNCTION_NAME ?= bigquery-streaming-buffer-monitor
PROJECT_ID ?= tagtoo-ml-workflow
REGION ?= asia-east1
RUNTIME ?= python311
MEMORY ?= 256MB
TIMEOUT ?= 60s
ENV ?= production
SERVICE_ACCOUNT ?= <EMAIL>

# 顏色定義 (用於美化輸出)
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

# 預設目標
.DEFAULT_GOAL := help

# 幫助信息
.PHONY: help
help: ## 顯示可用的命令
	@echo "$(BLUE)BigQuery Streaming Buffer Monitor - Available commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}'

# 開發環境設定
.PHONY: setup
setup: ## 設定開發環境
	@echo "$(YELLOW)Setting up development environment...$(RESET)"
	python -m venv venv
	source venv/bin/activate && pip install --upgrade pip
	source venv/bin/activate && pip install -r requirements.txt
	@echo "$(GREEN)Development environment setup complete!$(RESET)"

# 安裝依賴
.PHONY: install
install: ## 安裝 Python 依賴
	@echo "$(YELLOW)Installing dependencies...$(RESET)"
	pip install -r requirements.txt
	@echo "$(GREEN)Dependencies installed!$(RESET)"

# 代碼格式化
.PHONY: format
format: ## 格式化 Python 代碼
	@echo "$(YELLOW)Formatting code...$(RESET)"
	black .
	isort .
	@echo "$(GREEN)Code formatted!$(RESET)"

# 代碼檢查
.PHONY: lint
lint: ## 執行代碼品質檢查
	@echo "$(YELLOW)Running code quality checks...$(RESET)"
	source venv/bin/activate && flake8 --max-line-length=88 --exclude=venv,lib .
	source venv/bin/activate && mypy . --ignore-missing-imports
	@echo "$(GREEN)Code quality checks passed!$(RESET)"

# 語法檢查
.PHONY: syntax-check
syntax-check: ## 檢查 Python 語法
	@echo "$(YELLOW)Checking Python syntax...$(RESET)"
	source venv/bin/activate && python -m py_compile main.py
	@if [ -f "test_main.py" ]; then source venv/bin/activate && python -m py_compile test_main.py; fi
	@echo "$(GREEN)Syntax check passed!$(RESET)"

# 執行測試
.PHONY: test
test: ## 執行單元測試
	@echo "$(YELLOW)Running BigQuery streaming buffer monitor tests...$(RESET)"
	@if [ -f "test_main.py" ]; then \
		source venv/bin/activate && python -m pytest test_main.py -v --tb=short; \
	else \
		echo "$(RED)No tests found - test_main.py is required$(RESET)"; \
		exit 1; \
	fi
	@echo "$(GREEN)Tests completed!$(RESET)"

# 執行測試並生成覆蓋率報告
.PHONY: test-coverage
test-coverage: ## 執行測試並生成覆蓋率報告
	@echo "$(YELLOW)Running unit tests with coverage...$(RESET)"
	source venv/bin/activate && python -m pytest test_main.py -v --cov=main --cov-report=term-missing --cov-report=html
	@echo "$(GREEN)Coverage report generated in htmlcov/$(RESET)"

# 執行整合測試
.PHONY: test-integration
test-integration: ## 執行整合測試（需要 GCP 認證）
	@echo "$(YELLOW)Running integration tests...$(RESET)"
	@echo "$(BLUE)Note: These tests require valid GCP credentials and BigQuery access$(RESET)"
	@if [ -f "test_integration.py" ]; then \
		source venv/bin/activate && python -m pytest test_integration.py -m integration -v; \
	else \
		echo "$(RED)No integration tests found - test_integration.py is missing$(RESET)"; \
		exit 1; \
	fi
	@echo "$(GREEN)Integration tests completed!$(RESET)"

# 執行所有測試
.PHONY: test-all
test-all: ## 執行所有測試（單元測試 + 整合測試）
	@echo "$(YELLOW)Running all tests (unit + integration)...$(RESET)"
	source venv/bin/activate && python -m pytest test_main.py test_integration.py -v
	@echo "$(GREEN)All tests completed!$(RESET)"

# 本地執行
.PHONY: run
run: ## 在本地執行 Cloud Function
	@echo "$(YELLOW)Starting BigQuery streaming buffer monitor locally...$(RESET)"
	@echo "$(BLUE)Function will be available at: http://localhost:8080$(RESET)"
	@echo "$(BLUE)Test endpoint: http://localhost:8080/?project_id=gothic-province-823&dataset_id=tagtooad&table_id=partner_uid_mapping$(RESET)"
	functions-framework --target=main --port=8080

# 構建檢查
.PHONY: build-check
build-check: syntax-check lint test ## 執行完整的構建前檢查
	@echo "$(GREEN)All build checks passed!$(RESET)"

# 部署到開發環境
.PHONY: deploy-dev
deploy-dev: build-check ## 部署到開發環境
	@echo "$(YELLOW)Deploying to development environment...$(RESET)"
	gcloud functions deploy $(FUNCTION_NAME)-dev \
		--source=. \
		--entry-point=main \
		--runtime=$(RUNTIME) \
		--trigger-http \
		--allow-unauthenticated \
		--memory=$(MEMORY) \
		--timeout=$(TIMEOUT) \
		--region=$(REGION) \
		--project=$(PROJECT_ID) \
		--service-account=$(SERVICE_ACCOUNT) \
		--set-env-vars="ENV=development"
	@echo "$(GREEN)Deployed to development!$(RESET)"

# 部署到生產環境 (使用 Terraform)
.PHONY: deploy-prod
deploy-prod: build-check ## 部署到生產環境 (建議使用 Terraform)
	@echo "$(YELLOW)For production deployment, use Terraform:$(RESET)"
	@echo "$(BLUE)  cd terraform && terraform plan && terraform apply$(RESET)"
	@echo "$(YELLOW)If you need direct gcloud deployment:$(RESET)"
	@read -p "Continue with gcloud deployment? [y/N]: " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		gcloud functions deploy $(FUNCTION_NAME) \
			--source=. \
			--entry-point=main \
			--runtime=$(RUNTIME) \
			--trigger-http \
			--no-allow-unauthenticated \
			--memory=$(MEMORY) \
			--timeout=$(TIMEOUT) \
			--region=$(REGION) \
			--project=$(PROJECT_ID) \
			--service-account=$(SERVICE_ACCOUNT) \
			--set-env-vars="ENV=production"; \
		echo "$(GREEN)Deployed to production!$(RESET)"; \
	else \
		echo "$(YELLOW)Deployment cancelled$(RESET)"; \
	fi

# 檢查函數狀態
.PHONY: status
status: ## 檢查 Cloud Function 狀態
	@echo "$(YELLOW)Checking function status...$(RESET)"
	gcloud functions describe $(FUNCTION_NAME) --region=$(REGION) --project=$(PROJECT_ID)

# 查看函數日誌
.PHONY: logs
logs: ## 查看 Cloud Function 日誌
	@echo "$(YELLOW)Fetching function logs...$(RESET)"
	gcloud functions logs read $(FUNCTION_NAME) --region=$(REGION) --project=$(PROJECT_ID) --limit=50

# 測試函數端點
.PHONY: test-endpoint
test-endpoint: ## 測試部署的函數端點
	@echo "$(YELLOW)Testing deployed function endpoint...$(RESET)"
	@FUNCTION_URL=$$(gcloud functions describe $(FUNCTION_NAME) --region=$(REGION) --project=$(PROJECT_ID) --format="value(httpsTrigger.url)" 2>/dev/null); \
	if [ -n "$$FUNCTION_URL" ]; then \
		echo "$(BLUE)Testing endpoint: $$FUNCTION_URL$(RESET)"; \
		curl -X GET "$$FUNCTION_URL?project_id=gothic-province-823&dataset_id=tagtooad&table_id=partner_uid_mapping" \
			-H "Authorization: Bearer $$(gcloud auth print-identity-token)"; \
	else \
		echo "$(RED)Function not found or not deployed$(RESET)"; \
	fi

# 刪除函數
.PHONY: delete
delete: ## 刪除 Cloud Function
	@echo "$(RED)Deleting function $(FUNCTION_NAME)...$(RESET)"
	@read -p "Are you sure you want to delete $(FUNCTION_NAME)? [y/N]: " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		gcloud functions delete $(FUNCTION_NAME) --region=$(REGION) --project=$(PROJECT_ID); \
		echo "$(GREEN)Function deleted!$(RESET)"; \
	else \
		echo "$(YELLOW)Deletion cancelled$(RESET)"; \
	fi

# 清理
.PHONY: clean
clean: ## 清理構建產物和快取
	@echo "$(YELLOW)Cleaning up...$(RESET)"
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	rm -rf lib/ 2>/dev/null || true
	rm -rf htmlcov/ 2>/dev/null || true
	rm -rf .coverage 2>/dev/null || true
	rm -rf .pytest_cache/ 2>/dev/null || true
	@echo "$(GREEN)Cleanup complete!$(RESET)"

# 顯示配置
.PHONY: config
config: ## 顯示目前配置
	@echo "$(BLUE)BigQuery Streaming Buffer Monitor Configuration:$(RESET)"
	@echo "  Function Name:    $(FUNCTION_NAME)"
	@echo "  Project ID:       $(PROJECT_ID)"
	@echo "  Region:           $(REGION)"
	@echo "  Runtime:          $(RUNTIME)"
	@echo "  Memory:           $(MEMORY)"
	@echo "  Timeout:          $(TIMEOUT)"
	@echo "  Environment:      $(ENV)"
	@echo "  Service Account:  $(SERVICE_ACCOUNT)"

# 顯示專案信息
.PHONY: info
info: ## 顯示專案信息
	@echo "$(BLUE)Project Information:$(RESET)"
	@echo "  Directory:        $$(pwd)"
	@echo "  Function:         $(FUNCTION_NAME)"
	@echo "  Purpose:          BigQuery Streaming Buffer Monitoring"
	@echo "  Target Table:     gothic-province-823.tagtooad.partner_uid_mapping"
	@echo "  Python files:     $$(find . -name '*.py' | wc -l | tr -d ' ')"
	@if [ -f "main.py" ]; then echo "  Main script:      Available"; else echo "  Main script:      Missing"; fi
	@if [ -f "test_main.py" ]; then echo "  Tests:            Available"; else echo "  Tests:            Missing"; fi
	@if [ -f "terraform/main.tf" ]; then echo "  Terraform:        Available"; else echo "  Terraform:        Missing"; fi