"""
BigQuery Streaming Buffer Monitor Terraform 部署測試

此檔案測試 Terraform 部署配置和基礎設施驗證。
這些測試確保 Terraform 可以成功部署 Cloud Function 和 Cloud Scheduler。

執行方式:
  python3 test_deployment.py

注意：需要適當的 GCP 認證和權限
"""

import json
import subprocess
import sys
from pathlib import Path


class TerraformDeploymentTest:
    """Terraform 部署測試類別"""

    def __init__(self):
        self.project_dir = Path(__file__).parent
        self.terraform_dir = self.project_dir / "terraform"

    def test_terraform_init(self):
        """測試 Terraform 初始化"""
        print("🔧 測試 Terraform 初始化...")

        try:
            # 切換到 terraform 目錄
            result = subprocess.run(
                ["terraform", "init"],
                cwd=self.terraform_dir,
                capture_output=True,
                text=True,
                timeout=300,  # 5 分鐘超時
            )

            if result.returncode == 0:
                print("✅ Terraform 初始化成功")
                return True
            else:
                print(f"❌ Terraform 初始化失敗:")
                print(f"   stdout: {result.stdout}")
                print(f"   stderr: {result.stderr}")
                return False

        except subprocess.TimeoutExpired:
            print("❌ Terraform 初始化超時")
            return False
        except FileNotFoundError:
            print("❌ Terraform 未安裝或不在 PATH 中")
            return False
        except Exception as e:
            print(f"❌ Terraform 初始化錯誤: {e}")
            return False

    def test_terraform_validate(self):
        """測試 Terraform 配置驗證"""
        print("✅ 測試 Terraform 配置驗證...")

        try:
            result = subprocess.run(
                ["terraform", "validate"],
                cwd=self.terraform_dir,
                capture_output=True,
                text=True,
                timeout=60,
            )

            if result.returncode == 0:
                print("✅ Terraform 配置驗證成功")
                print(f"   {result.stdout.strip()}")
                return True
            else:
                print(f"❌ Terraform 配置驗證失敗:")
                print(f"   stdout: {result.stdout}")
                print(f"   stderr: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Terraform 驗證錯誤: {e}")
            return False

    def test_terraform_plan(self):
        """測試 Terraform 計劃 (不執行)"""
        print("📋 測試 Terraform 計劃...")

        try:
            result = subprocess.run(
                ["terraform", "plan", "-out=tfplan"],
                cwd=self.terraform_dir,
                capture_output=True,
                text=True,
                timeout=300,
            )

            if result.returncode == 0:
                print("✅ Terraform 計劃生成成功")

                # 檢查計劃內容
                if "google_cloudfunctions2_function" in result.stdout:
                    print("   ✓ 包含 Cloud Function 資源")
                if "google_cloud_scheduler_job" in result.stdout:
                    print("   ✓ 包含 Cloud Scheduler 資源")
                if (
                    "<EMAIL>"
                    in result.stdout
                ):
                    print("   ✓ 使用正確的服務帳戶")

                return True
            else:
                print(f"❌ Terraform 計劃失敗:")
                print(f"   stdout: {result.stdout}")
                print(f"   stderr: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ Terraform 計劃錯誤: {e}")
            return False

    def test_gcp_authentication(self):
        """測試 GCP 認證狀態"""
        print("🔐 測試 GCP 認證...")

        try:
            # 檢查 gcloud 認證狀態
            result = subprocess.run(
                ["gcloud", "auth", "list", "--format=json"],
                capture_output=True,
                text=True,
                timeout=30,
            )

            if result.returncode == 0:
                auth_accounts = json.loads(result.stdout)
                active_accounts = [
                    acc for acc in auth_accounts if acc.get("status") == "ACTIVE"
                ]

                if active_accounts:
                    print(f"✅ GCP 認證成功")
                    for account in active_accounts:
                        print(f"   - 帳戶: {account.get('account', 'Unknown')}")
                    return True
                else:
                    print("❌ 沒有活動的 GCP 認證帳戶")
                    return False
            else:
                print(f"❌ GCP 認證檢查失敗: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ GCP 認證檢查錯誤: {e}")
            return False

    def test_gcp_project_access(self):
        """測試 GCP 專案存取權限"""
        print("🏗️ 測試 GCP 專案存取...")

        try:
            # 檢查專案存取權限
            result = subprocess.run(
                [
                    "gcloud",
                    "projects",
                    "describe",
                    "tagtoo-ml-workflow",
                    "--format=json",
                ],
                capture_output=True,
                text=True,
                timeout=30,
            )

            if result.returncode == 0:
                project_info = json.loads(result.stdout)
                print("✅ GCP 專案存取成功")
                print(f"   - 專案 ID: {project_info.get('projectId')}")
                print(f"   - 專案名稱: {project_info.get('name')}")
                print(f"   - 專案狀態: {project_info.get('lifecycleState')}")
                return True
            else:
                print(f"❌ GCP 專案存取失敗: {result.stderr}")
                return False

        except Exception as e:
            print(f"❌ GCP 專案存取錯誤: {e}")
            return False

    def test_required_apis_enabled(self):
        """測試必要的 GCP API 是否啟用"""
        print("🔌 測試必要的 GCP API...")

        required_apis = [
            "cloudfunctions.googleapis.com",
            "run.googleapis.com",
            "cloudbuild.googleapis.com",
            "bigquery.googleapis.com",
            "cloudscheduler.googleapis.com",
        ]

        all_enabled = True

        for api in required_apis:
            try:
                result = subprocess.run(
                    [
                        "gcloud",
                        "services",
                        "list",
                        "--enabled",
                        f"--filter=name:{api}",
                        "--format=value(name)",
                    ],
                    capture_output=True,
                    text=True,
                    timeout=30,
                )

                if result.returncode == 0 and api in result.stdout:
                    print(f"   ✅ {api} 已啟用")
                else:
                    print(f"   ❌ {api} 未啟用")
                    all_enabled = False

            except Exception as e:
                print(f"   ❌ 檢查 {api} 時發生錯誤: {e}")
                all_enabled = False

        if all_enabled:
            print("✅ 所有必要的 API 都已啟用")
        else:
            print("❌ 部分必要的 API 未啟用")

        return all_enabled

    def test_source_files_ready(self):
        """測試源代碼文件是否就緒"""
        print("📁 測試源代碼文件...")

        required_files = ["main.py", "requirements.txt", "Makefile"]

        all_files_exist = True

        for file_name in required_files:
            file_path = self.project_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name} 存在")
            else:
                print(f"   ❌ {file_name} 不存在")
                all_files_exist = False

        if all_files_exist:
            print("✅ 所有必要的源代碼文件都存在")
        else:
            print("❌ 部分必要的源代碼文件缺失")

        return all_files_exist

    def run_all_tests(self):
        """執行所有部署測試"""
        print("🚀 開始 BigQuery Streaming Buffer Monitor 部署測試")
        print("=" * 60)

        tests = [
            ("GCP 認證", self.test_gcp_authentication),
            ("GCP 專案存取", self.test_gcp_project_access),
            ("必要 API 啟用", self.test_required_apis_enabled),
            ("源代碼文件", self.test_source_files_ready),
            ("Terraform 初始化", self.test_terraform_init),
            ("Terraform 驗證", self.test_terraform_validate),
            ("Terraform 計劃", self.test_terraform_plan),
        ]

        passed = 0
        total = len(tests)

        for test_name, test_func in tests:
            print(f"\n📋 {test_name}:")
            try:
                if test_func():
                    passed += 1
                    print(f"✅ {test_name} 通過")
                else:
                    print(f"❌ {test_name} 失敗")
            except Exception as e:
                print(f"❌ {test_name} 異常: {e}")

        print("\n" + "=" * 60)
        print(f"🎯 測試結果: {passed}/{total} 通過")

        if passed == total:
            print("🎉 所有部署測試通過！專案準備就緒，可以部署。")
            print("\n📝 下一步:")
            print("   1. 審查 Terraform 計劃: terraform show tfplan")
            print("   2. 執行部署: terraform apply")
            print("   3. 測試部署的函數")
        else:
            print("⚠️  部分測試失敗，請檢查並修復問題後再次執行。")

        return passed == total


def print_deployment_guide():
    """列印部署指南"""
    print(
        """
🚀 BigQuery Streaming Buffer Monitor 部署指南

📋 部署前檢查清單:
□ GCP 認證已配置 (gcloud auth login 或 service account)
□ 對 tagtoo-ml-workflow 專案有必要權限
□ 必要的 GCP API 已啟用
□ Terraform 已安裝並可用

🔧 部署步驟:
1. 執行部署測試: python3 test_deployment.py
2. 切換到 terraform 目錄: cd terraform/
3. 初始化 Terraform: terraform init
4. 檢查計劃: terraform plan
5. 執行部署: terraform apply
6. 測試部署的函數

⚠️  重要注意事項:
- 此測試會創建 Terraform 計劃文件但不會實際部署
- 實際部署需要手動執行 terraform apply
- 確保有適當的權限和配額
- 部署可能會產生 GCP 費用

🔍 故障排除:
- 認證問題: gcloud auth application-default login
- 權限問題: 檢查 IAM 角色和權限
- API 問題: gcloud services enable <service-name>
"""
    )


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--guide":
        print_deployment_guide()
    else:
        deployment_test = TerraformDeploymentTest()
        success = deployment_test.run_all_tests()
        sys.exit(0 if success else 1)
