# BigQuery Streaming Buffer Monitor

## 1. 總覽

此 Cloud Function 用於監控 BigQuery 表格的串流緩衝區 (streaming buffer) 狀態。它提供關於正在串流傳輸到表格中、但尚未提交至永久儲存體的資料的即時統計數據。這對於監控資料擷取管道的健康狀況以及確保機器學習工作流程的資料新鮮度至關重要。

此函式可透過 HTTP 請求或排程作業觸發。

## 2. 使用者指南

### 如何觸發函式

您可以透過向其端點發送經過驗證的 HTTP GET 或 POST 請求來調用此函式。

#### 驗證

您必須提供來自具有「Cloud Functions 呼叫者 (Cloud Functions Invoker)」角色的服務帳戶的 OIDC 身份權杖。為了方便測試，您可以先將 Function URL 和驗證權杖設定為環境變數。

```bash
# 1. 設定您的 Function URL
export FUNCTION_URL="https://asia-east1-tagtoo-ml-workflow.cloudfunctions.net/bigquery-streaming-buffer-monitor"

# 2. 取得一個新的驗證權杖
#    (這個指令會自動抓取您目前登入 gcloud 的身份權杖)
export TOKEN=$(gcloud auth print-identity-token)
```

---

### `GET` 請求範例

`GET` 請求是將所有參數放在 URL 的查詢字串中，這是最常見的查詢方式。

#### 範例 1：使用預設表格
這個請求不帶任何參數，函式將會使用預設設定 (`gothic-province-823.tagtooad.partner_uid_mapping`)。

```bash
curl -X GET "$FUNCTION_URL" \
     -H "Authorization: bearer $TOKEN"
```

#### 範例 2：指定自訂表格
這個請求透過查詢參數指定了 `project_id`、`dataset_id` 和 `table_id`。

```bash
curl -X GET "$FUNCTION_URL?project_id=my-project&dataset_id=my_dataset&table_id=my_table" \
     -H "Authorization: bearer $TOKEN"
```

---

### `POST` 請求範例

`POST` 請求是將參數包在一個 JSON 物件中，並作為請求的主體 (body) 發送。

#### 範例 1：使用預設表格
這個請求發送一個空的 JSON 主體 (`{}`)，函式將會使用預設設定。

```bash
curl -X POST "$FUNCTION_URL" \
     -H "Authorization: bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{}'
```

#### 範例 2：指定自訂表格
這個請求在 JSON 主體中指定了所有參數。

```bash
curl -X POST "$FUNCTION_URL" \
     -H "Authorization: bearer $TOKEN" \
     -H "Content-Type: application/json" \
     -d '{
           "project_id": "my-project",
           "dataset_id": "my_dataset",
           "table_id": "my_table"
         }'
```

### 參數

參數可以作為 GET 請求中的查詢字串提供，或在 POST 請求的 JSON 主體中提供。

-   `project_id` (string, optional): GCP 專案 ID。
    -   **預設值:** `gothic-province-823`
-   `dataset_id` (string, optional): BigQuery 資料集 ID。
    -   **預設值:** `tagtooad`
-   `table_id` (string, optional): BigQuery 表格 ID。
    -   **預設值:** `partner_uid_mapping`

**POST 請求主體範例:**

```json
{
  "project_id": "my-project",
  "dataset_id": "my_dataset",
  "table_id": "my_table"
}
```

### 回應範例

#### 成功 (有緩衝區)

```json
{
  "has_streaming_buffer": true,
  "estimated_rows": "68849",
  "estimated_bytes": "9338470",
  "oldest_entry_time": "2025-07-29T03:01:59+00:00",
  "table_reference": {
    "project_id": "gothic-province-823",
    "dataset_id": "tagtooad",
    "table_id": "partner_uid_mapping"
  },
  "queried_at": "2025-07-29T11:50:15+00:00",
  "status": "success"
}
```

#### 成功 (無緩衝區)

```json
{
    "has_streaming_buffer": false,
    "estimated_rows": "0",
    "estimated_bytes": "0",
    "oldest_entry_time": null,
    "table_reference": {
        "project_id": "gothic-province-823",
        "dataset_id": "tagtooad",
        "table_id": "partner_uid_mapping"
    },
    "queried_at": "2025-07-30T06:45:00.123456+00:00",
    "status": "success"
}
```

#### 錯誤 (例如，找不到表格)

```json
{
    "status": "error",
    "error": "Table not found",
    "queried_at": "2025-07-30T06:46:00.123456+00:00"
}
```

---

## 3. 開發者指南

本節提供有關設定、開發、測試和部署此功能的說明。

### 專案結構

```
.
├── .flake8           # Flake8 linter 設定
├── .gitignore        # Git 忽略規則
├── Makefile          # 包含常用指令 (test, deploy) 的 Makefile
├── main.py           # 主要函式程式碼
├── pytest.ini        # Pytest 設定
├── README.md         # 本檔案
├── requirements.in   # 主要依賴項列表
├── requirements.txt  # 已鎖定版本的依賴項
├── terraform/        # Terraform 基礎設施即程式碼
│   ├── main.tf
│   └── ...
└── test_*.py         # 單元和整合測試
```

### 本機開發設定

1.  **複製此儲存庫。**
2.  **建立並啟用 Python 虛擬環境：**
    ```bash
    python3 -m venv venv
    source venv/bin/activate
    ```
3.  **安裝依賴項：**
    ```bash
    pip install -r requirements.txt
    ```

### 執行測試

本專案使用 `pytest`，並包含一套完整的測試。

-   **執行所有測試：**
    ```bash
    make test
    ```
    或
    ```bash
    pytest
    ```

### 部署

部署是透過 Terraform 進行管理，以確保基礎設施是版本控制且可重現的。

1.  **向 GCP 進行身份驗證：**
    ```bash
    gcloud auth application-default login
    ```
2.  **初始化 Terraform：**
    ```bash
    cd terraform
    terraform init
    ```
3.  **審查並應用變更：**
    -   如果需要覆寫預設變數，請建立一個 `terraform.tfvars` 檔案。
    -   執行 `terraform plan` 來查看執行計畫。
    -   執行 `terraform apply` 來部署函式。

`Makefile` 中的指令簡化了此流程：
```bash
# 這將打包原始碼、規劃並應用變更
make deploy
```

### 如何貢獻

1.  為您的新功能或錯誤修復建立一個新分支。
2.  進行變更。
3.  新增或更新測試以涵蓋您的變更。
4.  執行 `make test` 確保所有測試都通過。
5.  如果您變更了使用者可見的行為，請更新此 `README.md`。
6.  建立一個 Pull Request。
