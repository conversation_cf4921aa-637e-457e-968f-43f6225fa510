[flake8]
max-line-length = 100
exclude = 
    venv,
    lib,
    __pycache__,
    .git
ignore = 
    # Allow f-strings without placeholders in test files (often used for print statements)
    F541,
    # Allow unused variables in test files (sometimes needed for fixtures)  
    F841,
    # Allow trailing whitespace (handled by black)
    W291,
    # Allow unused imports in test files (sometimes needed for type checking)
    F401,
    # Allow bare except in test files (acceptable for test utilities)
    E722,
    # Allow line breaks before binary operators (black style)
    W503,
    # Allow missing whitespace around operators (black handles this)
    E226
per-file-ignores =
    test_*.py:F541,F841,F401,E722,W291,E501,W503,E226
    *test*.py:F541,F841,F401,E722,W291,E501,W503,E226