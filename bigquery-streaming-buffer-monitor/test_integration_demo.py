"""
BigQuery Streaming Buffer Monitor 整合測試示範

此檔案展示整合測試的結構，但在沒有 GCP 認證的情況下會跳過實際測試。
這讓我們可以驗證測試架構是否正確，而不需要實際的 BigQuery 連接。
"""

import json
import os
from unittest.mock import Mock

import pytest


# 檢查是否有 GCP 認證
def has_gcp_credentials():
    """檢查是否有可用的 GCP 認證"""
    try:
        from google.auth import default

        credentials, project = default()
        return credentials is not None
    except Exception:
        return False


# 整合測試標記
pytestmark = pytest.mark.integration

# GCP 認證跳過裝飾器
skip_if_no_gcp = pytest.mark.skipif(
    not has_gcp_credentials(), reason="需要 GCP 認證才能執行整合測試"
)


class TestIntegrationDemo:
    """整合測試示範類別"""

    def test_integration_test_structure(self):
        """測試整合測試結構（不需要 GCP）"""
        from main import main

        # 這個測試驗證整合測試的基本結構
        assert hasattr(main, "__call__"), "main 函數應該可調用"

        # 驗證測試目標表格配置
        target_project = "gothic-province-823"
        target_dataset = "tagtooad"
        target_table = "partner_uid_mapping"

        assert target_project, "目標專案 ID 應該已定義"
        assert target_dataset, "目標資料集 ID 應該已定義"
        assert target_table, "目標表格 ID 應該已定義"

        print(f"✅ 整合測試結構驗證通過")
        print(f"   - 目標表格: {target_project}.{target_dataset}.{target_table}")

    @skip_if_no_gcp
    def test_real_bigquery_connection(self):
        """測試真實 BigQuery 連接（需要 GCP 認證）"""
        from main import main

        # 創建真實請求
        mock_request = Mock()
        mock_request.method = "GET"
        mock_request.args.get.return_value = None

        # 執行實際請求
        response, status_code = main(mock_request)

        # 驗證回應
        assert status_code in [200, 404, 403]
        response_data = json.loads(response)
        assert "status" in response_data

        if status_code == 200:
            assert "has_streaming_buffer" in response_data
            print(f"✅ 真實 BigQuery 連接成功")
            print(
                f"   - 串流緩衝區: {'有' if response_data['has_streaming_buffer'] else '無'}"
            )
        else:
            print(
                f"⚠️  狀態碼 {status_code}: {response_data.get('error', 'Unknown error')}"
            )

    @skip_if_no_gcp
    def test_application_default_credentials(self):
        """測試 Application Default Credentials（需要 GCP 認證）"""
        from google.auth import default
        from google.cloud import bigquery

        # 測試 ADC 配置
        credentials, project = default()
        assert credentials is not None

        # 測試 BigQuery 客戶端
        client = bigquery.Client()
        assert client is not None

        print(f"✅ ADC 配置驗證通過")
        print(f"   - 專案: {project or 'Not specified'}")

    def test_integration_vs_unit_test_differences(self):
        """展示整合測試與單元測試的差異"""

        # 單元測試特徵：快速、隔離、使用 mock
        unit_test_features = [
            "使用 Mock 物件",
            "不需要外部依賴",
            "執行時間 < 1 秒",
            "可重複執行",
            "不需要網路連接",
        ]

        # 整合測試特徵：真實環境、端到端
        integration_test_features = [
            "連接真實 BigQuery",
            "使用實際認證",
            "驗證端到端流程",
            "可能需要較長時間",
            "需要網路和權限",
        ]

        print("🧪 測試類型比較:")
        print("📝 單元測試特徵:")
        for feature in unit_test_features:
            print(f"   - {feature}")

        print("🔗 整合測試特徵:")
        for feature in integration_test_features:
            print(f"   - {feature}")

        # 驗證我們有適當的測試分離
        assert len(unit_test_features) > 0
        assert len(integration_test_features) > 0

    def test_environment_configuration(self):
        """測試環境配置"""

        # 檢查環境變數
        test_project = os.getenv("BQ_TEST_PROJECT", "gothic-province-823")
        test_dataset = os.getenv("BQ_TEST_DATASET", "tagtooad")
        test_table = os.getenv("BQ_TEST_TABLE", "partner_uid_mapping")

        # 驗證配置
        assert test_project, "測試專案配置不能為空"
        assert test_dataset, "測試資料集配置不能為空"
        assert test_table, "測試表格配置不能為空"

        print(f"🔧 環境配置:")
        print(f"   - BQ_TEST_PROJECT: {test_project}")
        print(f"   - BQ_TEST_DATASET: {test_dataset}")
        print(f"   - BQ_TEST_TABLE: {test_table}")


class TestCredentialsDetection:
    """認證檢測測試"""

    def test_gcp_credentials_detection(self):
        """測試 GCP 認證檢測邏輯"""

        has_creds = has_gcp_credentials()

        if has_creds:
            print("✅ 檢測到 GCP 認證")
            print("   - 整合測試將正常執行")
        else:
            print("⚠️  未檢測到 GCP 認證")
            print("   - 整合測試將被跳過")
            print("   - 這在 CI/CD 環境中是正常的")

        # 驗證檢測邏輯
        assert isinstance(has_creds, bool)

    def test_credentials_skip_logic(self):
        """測試認證跳過邏輯"""

        # 驗證跳過邏輯是否正確設定
        skip_marker = skip_if_no_gcp

        assert skip_marker is not None
        print("✅ 認證跳過邏輯配置正確")


def run_integration_test_demo():
    """執行整合測試示範"""
    print(
        """
🚀 整合測試示範執行指南

這個示範展示了：
1. ✅ 整合測試結構和配置
2. ✅ GCP 認證檢測邏輯  
3. ✅ 測試跳過機制
4. ✅ 環境變數配置
5. ✅ 單元測試 vs 整合測試的差異

實際執行指令：
pytest test_integration_demo.py -v

如果有 GCP 認證：
pytest test_integration.py -m integration -v
"""
    )


if __name__ == "__main__":
    run_integration_test_demo()
