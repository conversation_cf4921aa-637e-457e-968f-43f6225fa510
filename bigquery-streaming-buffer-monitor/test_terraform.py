"""
Terraform 配置驗證測試

此檔案測試 Terraform 配置的語法正確性和結構完整性
"""

from pathlib import Path


class TestTerraformConfiguration:
    """Terraform 配置測試類別"""

    def setup_method(self):
        """設定測試環境"""
        self.terraform_dir = Path(__file__).parent / "terraform"
        self.main_tf_path = self.terraform_dir / "main.tf"

    def test_terraform_files_exist(self):
        """測試必要的 Terraform 檔案是否存在"""
        assert self.terraform_dir.exists(), "terraform 目錄不存在"
        assert self.main_tf_path.exists(), "main.tf 檔案不存在"

    def test_terraform_syntax_validation(self):
        """測試 Terraform 語法驗證 (需要 terraform init)"""
        # 此測試需要在實際環境中運行 terraform init 後才能執行
        # 這裡檢查檔案基本結構

        with open(self.main_tf_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 檢查必要的區塊是否存在
        required_blocks = [
            "terraform {",
            'provider "google"',
            'resource "google_cloudfunctions2_function"',
            'resource "google_cloud_scheduler_job"',
            'variable "project_id"',
            'output "function_url"',
        ]

        for block in required_blocks:
            assert block in content, f"缺少必要的 Terraform 區塊: {block}"

    def test_required_variables_defined(self):
        """測試必要變數是否定義"""
        with open(self.main_tf_path, "r", encoding="utf-8") as f:
            content = f.read()

        required_variables = [
            'variable "project_id"',
            'variable "region"',
            'variable "function_name"',
            'variable "runtime"',
            'variable "memory"',
            'variable "timeout"',
        ]

        for var in required_variables:
            assert var in content, f"缺少必要變數定義: {var}"

    def test_service_account_configuration(self):
        """測試服務帳戶配置"""
        with open(self.main_tf_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 檢查是否使用正確的服務帳戶
        assert "<EMAIL>" in content
        assert "service_account_email = local.service_account_email" in content

        # 檢查 OIDC 認證配置
        assert "oidc_token" in content
        assert "service_account_email = local.service_account_email" in content

    def test_cloud_scheduler_jobs(self):
        """測試 Cloud Scheduler 工作配置"""
        with open(self.main_tf_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 檢查三個排程工作
        scheduler_jobs = [
            'resource "google_cloud_scheduler_job" "monitor_job_03am"',
            'resource "google_cloud_scheduler_job" "monitor_job_05am"',
            'resource "google_cloud_scheduler_job" "monitor_job_07am"',
        ]

        for job in scheduler_jobs:
            assert job in content, f"缺少排程工作: {job}"

        # 檢查排程時間
        assert '"0 3 * * *"' in content  # 03:00
        assert '"0 5 * * *"' in content  # 05:00
        assert '"0 7 * * *"' in content  # 07:00

        # 檢查時區設定
        assert '"Asia/Taipei"' in content

    def test_required_apis_enabled(self):
        """測試必要的 API 是否啟用"""
        with open(self.main_tf_path, "r", encoding="utf-8") as f:
            content = f.read()

        required_apis = [
            "cloudfunctions.googleapis.com",
            "run.googleapis.com",
            "cloudbuild.googleapis.com",
            "bigquery.googleapis.com",
            "cloudscheduler.googleapis.com",
        ]

        for api in required_apis:
            assert api in content, f"缺少必要的 API 服務: {api}"

    def test_security_configuration(self):
        """測試安全配置"""
        with open(self.main_tf_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 檢查不允許公開訪問 (沒有 allUsers)
        assert 'member = "allUsers"' not in content

        # 檢查使用服務帳戶進行調用
        assert 'member = "serviceAccount:${local.service_account_email}"' in content

        # 檢查 OIDC 認證
        assert "oidc_token" in content

    def test_output_values(self):
        """測試輸出值配置"""
        with open(self.main_tf_path, "r", encoding="utf-8") as f:
            content = f.read()

        required_outputs = [
            'output "function_url"',
            'output "function_name"',
            'output "service_account_email"',
            'output "scheduler_jobs"',
        ]

        for output in required_outputs:
            assert output in content, f"缺少必要的輸出值: {output}"

    def test_terraform_formatting(self):
        """測試 Terraform 格式化 (基本檢查)"""
        with open(self.main_tf_path, "r", encoding="utf-8") as f:
            content = f.read()

        # 基本格式檢查
        assert content.strip(), "檔案不應為空"
        assert not content.startswith(" "), "檔案不應以空格開始"

        # 檢查區塊結構
        open_braces = content.count("{")
        close_braces = content.count("}")
        assert open_braces == close_braces, "大括號數量不匹配"


if __name__ == "__main__":
    # 簡單的測試執行器
    test_class = TestTerraformConfiguration()
    test_methods = [method for method in dir(test_class) if method.startswith("test_")]

    passed = 0
    failed = 0

    for method_name in test_methods:
        try:
            print(f"執行測試: {method_name}")
            test_class.setup_method()
            method = getattr(test_class, method_name)
            method()
            print(f"✅ {method_name} 通過")
            passed += 1
        except Exception as e:
            print(f"❌ {method_name} 失敗: {str(e)}")
            failed += 1

    print(f"\n測試結果: {passed} 通過, {failed} 失敗")

    if failed == 0:
        print("🎉 所有 Terraform 配置測試通過!")
    else:
        print("⚠️  部分測試失敗，請檢查配置")
