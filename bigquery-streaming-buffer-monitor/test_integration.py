"""
BigQuery Streaming Buffer Monitor 整合測試

此檔案包含連接到實際 BigQuery 環境的整合測試。這些測試：
1. 使用真實的 BigQuery 連接
2. 驗證 Application Default Credentials 配置
3. 測試實際的表格和串流緩衝區查詢
4. 驗證端到端功能

注意：
- 這些測試需要有效的 GCP 認證
- 需要對目標 BigQuery 表格的讀取權限
- 測試可能需要更長的執行時間
- 使用 pytest -m integration 來單獨執行
"""

import json
import os
from datetime import datetime
from unittest.mock import Mock

import pytest

# 整合測試標記
pytestmark = pytest.mark.integration


class TestBigQueryIntegration:
    """BigQuery 整合測試類別"""

    @pytest.fixture(autouse=True)
    def setup_integration_test(self):
        """整合測試設置"""
        # 檢查是否有必要的環境變數或認證
        self.target_project = "gothic-province-823"
        self.target_dataset = "tagtooad"
        self.target_table = "partner_uid_mapping"

        # 可以通過環境變數覆蓋測試目標
        self.test_project = os.getenv("BQ_TEST_PROJECT", self.target_project)
        self.test_dataset = os.getenv("BQ_TEST_DATASET", self.target_dataset)
        self.test_table = os.getenv("BQ_TEST_TABLE", self.target_table)

        print(
            f"🔗 整合測試目標: {self.test_project}.{self.test_dataset}.{self.test_table}"
        )

    def test_bigquery_client_authentication(self):
        """測試 BigQuery 客戶端認證"""
        from main import main

        # 創建真實的 GET 請求
        mock_request = Mock()
        mock_request.method = "GET"
        mock_request.args.get.side_effect = lambda key, default=None: {
            "project_id": self.test_project,
            "dataset_id": self.test_dataset,
            "table_id": self.test_table,
        }.get(key, default)

        # 執行真實請求
        response, status_code = main(mock_request)

        # 驗證成功連接
        assert status_code in [200, 404], f"Expected 200 or 404, got {status_code}"

        response_data = json.loads(response)

        if status_code == 200:
            # 成功案例：驗證回應結構
            assert "has_streaming_buffer" in response_data
            assert "table_reference" in response_data
            assert response_data["status"] == "success"
            assert response_data["table_reference"]["project_id"] == self.test_project
            assert response_data["table_reference"]["dataset_id"] == self.test_dataset
            assert response_data["table_reference"]["table_id"] == self.test_table
            print(f"✅ 成功連接到 BigQuery 表格")
            print(
                f"📊 串流緩衝區狀態: {'有' if response_data['has_streaming_buffer'] else '無'}"
            )

        elif status_code == 404:
            # 表格不存在的情況（也是有效的認證測試）
            assert response_data["status"] == "error"
            assert "not found" in response_data["error"].lower()
            print(f"⚠️  表格不存在，但認證成功")

    def test_streaming_buffer_detection_with_real_data(self):
        """測試使用真實資料的串流緩衝區檢測"""
        from main import main

        # 測試預設表格
        mock_request = Mock()
        mock_request.method = "GET"
        mock_request.args.get.return_value = None  # 使用預設值

        response, status_code = main(mock_request)

        # 分析真實回應
        if status_code == 200:
            response_data = json.loads(response)

            # 驗證回應結構完整性
            required_fields = [
                "has_streaming_buffer",
                "estimated_rows",
                "estimated_bytes",
                "oldest_entry_time",
                "table_reference",
                "queried_at",
                "status",
            ]
            for field in required_fields:
                assert field in response_data, f"Missing field: {field}"

            # 記錄實際數據
            print(f"🔍 真實串流緩衝區資料:")
            print(f"   - 有緩衝區: {response_data['has_streaming_buffer']}")
            print(f"   - 估計行數: {response_data['estimated_rows']}")
            print(f"   - 估計位元組: {response_data['estimated_bytes']}")
            print(f"   - 最舊條目時間: {response_data['oldest_entry_time']}")

            # 驗證資料類型
            assert isinstance(response_data["has_streaming_buffer"], bool)
            assert isinstance(response_data["estimated_rows"], str)
            assert isinstance(response_data["estimated_bytes"], str)

            # 如果有串流緩衝區，驗證數據合理性
            if response_data["has_streaming_buffer"]:
                assert int(response_data["estimated_rows"]) >= 0
                assert int(response_data["estimated_bytes"]) >= 0
                if response_data["oldest_entry_time"]:
                    # 驗證時間戳格式
                    datetime.fromisoformat(
                        response_data["oldest_entry_time"].replace("Z", "+00:00")
                    )

    def test_post_request_with_real_bigquery(self):
        """測試使用真實 BigQuery 的 POST 請求"""
        from main import main

        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.get_json.return_value = {
            "project_id": self.test_project,
            "dataset_id": self.test_dataset,
            "table_id": self.test_table,
        }

        response, status_code = main(mock_request)

        # 驗證 POST 請求處理
        assert status_code in [200, 404]
        response_data = json.loads(response)

        if status_code == 200:
            assert response_data["table_reference"]["project_id"] == self.test_project
            assert response_data["table_reference"]["dataset_id"] == self.test_dataset
            assert response_data["table_reference"]["table_id"] == self.test_table
            print(f"✅ POST 請求成功處理真實 BigQuery 資料")

    def test_error_handling_with_invalid_table(self):
        """測試無效表格的錯誤處理"""
        from main import main

        # 使用不存在的表格
        mock_request = Mock()
        mock_request.method = "GET"
        mock_request.args.get.side_effect = lambda key, default=None: {
            "project_id": self.test_project,
            "dataset_id": self.test_dataset,
            "table_id": "nonexistent_table_12345",
        }.get(key, default)

        response, status_code = main(mock_request)

        # 應該返回 404 錯誤
        assert status_code == 404
        response_data = json.loads(response)
        assert response_data["status"] == "error"
        assert "not found" in response_data["error"].lower()
        print(f"✅ 無效表格錯誤處理正確")

    def test_permissions_with_real_service_account(self):
        """測試真實服務帳戶的權限"""
        from main import main

        # 使用需要權限的表格測試
        mock_request = Mock()
        mock_request.method = "GET"
        mock_request.args.get.side_effect = lambda key, default=None: {
            "project_id": self.test_project,
            "dataset_id": self.test_dataset,
            "table_id": self.test_table,
        }.get(key, default)

        response, status_code = main(mock_request)

        # 驗證不是權限錯誤（403）
        assert status_code != 403, "Service account lacks required permissions"

        if status_code == 403:
            response_data = json.loads(response)
            pytest.fail(
                f"權限錯誤: {response_data.get('error', 'Unknown permission error')}"
            )

        print(f"✅ 服務帳戶權限驗證通過")


class TestApplicationDefaultCredentials:
    """Application Default Credentials 整合測試"""

    def test_adc_configuration(self):
        """測試 Application Default Credentials 配置"""
        try:
            from google.auth import default

            # 嘗試獲取預設認證
            credentials, project = default()

            assert credentials is not None, "No default credentials found"
            print(f"✅ ADC 配置成功")
            print(f"   - 專案: {project or 'Not specified'}")
            print(f"   - 認證類型: {type(credentials).__name__}")

        except Exception as e:
            pytest.fail(f"ADC 配置失敗: {str(e)}")

    def test_bigquery_client_uses_adc(self):
        """測試 BigQuery 客戶端使用 ADC"""
        from google.cloud import bigquery

        try:
            # 創建不帶明確認證的客戶端（應該使用 ADC）
            client = bigquery.Client()

            assert client is not None
            print(f"✅ BigQuery 客戶端使用 ADC 創建成功")
            print(f"   - 專案: {client.project}")

        except Exception as e:
            pytest.fail(f"BigQuery 客戶端 ADC 初始化失敗: {str(e)}")


class TestRealWorldScenarios:
    """真實世界場景測試"""

    def test_concurrent_requests(self):
        """測試並發請求處理"""
        import concurrent.futures

        from main import main

        def make_request():
            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.return_value = None
            return main(mock_request)

        # 執行多個並發請求
        with concurrent.futures.ThreadPoolExecutor(max_workers=3) as executor:
            futures = [executor.submit(make_request) for _ in range(3)]
            results = [
                future.result() for future in concurrent.futures.as_completed(futures)
            ]

        # 驗證所有請求都成功處理
        for response, status_code in results:
            assert status_code in [200, 404, 403]  # 允許的狀態碼
            response_data = json.loads(response)
            assert "status" in response_data

        print(f"✅ 並發請求測試通過，處理了 {len(results)} 個請求")

    def test_response_time_performance(self):
        """測試回應時間性能"""
        import time

        from main import main

        mock_request = Mock()
        mock_request.method = "GET"
        mock_request.args.get.return_value = None

        # 測量執行時間
        start_time = time.time()
        response, status_code = main(mock_request)
        execution_time = time.time() - start_time

        # 驗證回應時間（應該在合理範圍內）
        assert execution_time < 30.0, f"Response time too slow: {execution_time:.2f}s"

        print(f"✅ 性能測試通過")
        print(f"   - 執行時間: {execution_time:.3f} 秒")
        print(f"   - 狀態碼: {status_code}")

        if status_code == 200:
            response_data = json.loads(response)
            print(f"   - 有串流緩衝區: {response_data['has_streaming_buffer']}")


# pytest 配置提示
def pytest_configure(config):
    """配置 pytest 整合測試標記"""
    config.addinivalue_line(
        "markers",
        "integration: mark test as integration test (requires real BigQuery access)",
    )


# 執行指南的輔助函數
def print_integration_test_guide():
    """列印整合測試執行指南"""
    print(
        """
🧪 BigQuery Streaming Buffer Monitor 整合測試指南

📋 執行前準備:
1. 確保有有效的 GCP 認證 (Application Default Credentials)
2. 確保服務帳戶有 BigQuery 資料查看權限
3. 確認目標表格存在或可接受 404 錯誤

🚀 執行命令:
# 只執行整合測試
pytest test_integration.py -m integration -v

# 執行所有測試（單元測試 + 整合測試）
pytest test_main.py test_integration.py -v

# 只執行單元測試（跳過整合測試）
pytest test_main.py -v

🔧 環境變數配置:
export BQ_TEST_PROJECT="your-test-project"
export BQ_TEST_DATASET="your-test-dataset"  
export BQ_TEST_TABLE="your-test-table"

⚠️  注意事項:
- 整合測試需要網路連接和 GCP 存取權限
- 測試可能產生小額 BigQuery 查詢費用
- 建議在 CI/CD 中分別執行單元測試和整合測試
"""
    )


if __name__ == "__main__":
    print_integration_test_guide()
