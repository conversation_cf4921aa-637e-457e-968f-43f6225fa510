#
# This file is autogenerated by pip-compile with Python 3.13
# by the following command:
#
#    pip-compile requirements.in
#
anyio==4.9.0
    # via starlette
black==25.1.0
    # via -r requirements.in
blinker==1.9.0
    # via flask
cachetools==5.5.2
    # via google-auth
certifi==2025.7.14
    # via requests
cffi==1.17.1
    # via cryptography
charset-normalizer==3.4.2
    # via requests
click==8.2.1
    # via
    #   black
    #   flask
    #   functions-framework
    #   uvicorn
cloudevents==1.11.0
    # via functions-framework
coverage[toml]==7.10.1
    # via pytest-cov
cryptography==45.0.5
    # via -r requirements.in
deprecation==2.1.0
    # via cloudevents
flake8==7.3.0
    # via -r requirements.in
flask==3.1.1
    # via
    #   -r requirements.in
    #   functions-framework
functions-framework==3.9.2
    # via -r requirements.in
google-api-core[grpc]==2.25.1
    # via
    #   google-cloud-appengine-logging
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-logging
google-auth==2.40.3
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-auth-httplib2
    #   google-auth-oauthlib
    #   google-cloud-appengine-logging
    #   google-cloud-bigquery
    #   google-cloud-core
    #   google-cloud-logging
google-auth-httplib2==0.2.0
    # via -r requirements.in
google-auth-oauthlib==1.2.2
    # via -r requirements.in
google-cloud-appengine-logging==1.6.2
    # via google-cloud-logging
google-cloud-audit-log==0.3.2
    # via google-cloud-logging
google-cloud-bigquery==3.35.1
    # via -r requirements.in
google-cloud-core==2.4.3
    # via
    #   google-cloud-bigquery
    #   google-cloud-logging
google-cloud-logging==3.12.1
    # via -r requirements.in
google-crc32c==1.7.1
    # via google-resumable-media
google-resumable-media==2.7.2
    # via google-cloud-bigquery
googleapis-common-protos[grpc]==1.70.0
    # via
    #   google-api-core
    #   google-cloud-audit-log
    #   grpc-google-iam-v1
    #   grpcio-status
grpc-google-iam-v1==0.14.2
    # via google-cloud-logging
grpcio==1.74.0
    # via
    #   google-api-core
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
grpcio-status==1.74.0
    # via google-api-core
gunicorn==23.0.0
    # via
    #   functions-framework
    #   uvicorn-worker
h11==0.16.0
    # via uvicorn
httplib2==0.22.0
    # via google-auth-httplib2
idna==3.10
    # via
    #   anyio
    #   requests
importlib-metadata==8.7.0
    # via opentelemetry-api
iniconfig==2.1.0
    # via pytest
isort==6.0.1
    # via -r requirements.in
itsdangerous==2.2.0
    # via flask
jinja2==3.1.6
    # via flask
markupsafe==3.0.2
    # via
    #   flask
    #   jinja2
    #   werkzeug
mccabe==0.7.0
    # via flake8
mypy==1.17.0
    # via -r requirements.in
mypy-extensions==1.1.0
    # via
    #   black
    #   mypy
oauthlib==3.3.1
    # via requests-oauthlib
opentelemetry-api==1.36.0
    # via google-cloud-logging
orjson==3.11.1
    # via -r requirements.in
packaging==25.0
    # via
    #   black
    #   deprecation
    #   google-cloud-bigquery
    #   gunicorn
    #   pytest
pathspec==0.12.1
    # via
    #   black
    #   mypy
platformdirs==4.3.8
    # via black
pluggy==1.6.0
    # via
    #   pytest
    #   pytest-cov
proto-plus==1.26.1
    # via
    #   google-api-core
    #   google-cloud-appengine-logging
    #   google-cloud-logging
protobuf==6.31.1
    # via
    #   google-api-core
    #   google-cloud-appengine-logging
    #   google-cloud-audit-log
    #   google-cloud-logging
    #   googleapis-common-protos
    #   grpc-google-iam-v1
    #   grpcio-status
    #   proto-plus
pyasn1==0.6.1
    # via
    #   pyasn1-modules
    #   rsa
pyasn1-modules==0.4.2
    # via google-auth
pycodestyle==2.14.0
    # via flake8
pycparser==2.22
    # via cffi
pyflakes==3.4.0
    # via flake8
pygments==2.19.2
    # via pytest
pyparsing==3.2.3
    # via httplib2
pytest==8.4.1
    # via
    #   -r requirements.in
    #   pytest-cov
    #   pytest-mock
pytest-cov==6.2.1
    # via -r requirements.in
pytest-mock==3.14.1
    # via -r requirements.in
python-dateutil==2.9.0.post0
    # via
    #   -r requirements.in
    #   google-cloud-bigquery
pytz==2025.2
    # via -r requirements.in
requests==2.32.4
    # via
    #   -r requirements.in
    #   google-api-core
    #   google-cloud-bigquery
    #   requests-oauthlib
requests-oauthlib==2.0.0
    # via google-auth-oauthlib
rsa==4.9.1
    # via google-auth
six==1.17.0
    # via python-dateutil
sniffio==1.3.1
    # via anyio
starlette==0.47.2
    # via functions-framework
types-requests==2.32.4.20250611
    # via -r requirements.in
typing-extensions==4.14.1
    # via
    #   mypy
    #   opentelemetry-api
urllib3==2.5.0
    # via
    #   requests
    #   types-requests
uvicorn==0.35.0
    # via
    #   functions-framework
    #   uvicorn-worker
uvicorn-worker==0.3.0
    # via functions-framework
watchdog==6.0.0
    # via functions-framework
werkzeug==3.1.3
    # via
    #   flask
    #   functions-framework
zipp==3.23.0
    # via importlib-metadata
