"""
BigQuery Streaming Buffer Monitor 單元測試

此檔案包含 BigQuery Streaming Buffer Monitor Cloud Function 的單元測試。
使用 TDD 方法，確保所有業務邏輯都有適當的測試覆蓋。

主要測試範圍：
1. HTTP 請求處理 (GET, POST, OPTIONS)
2. BigQuery 客戶端初始化 (使用 Application Default Credentials)
3. 串流緩衝區狀態查詢
4. 錯誤處理和邊緣情況
5. JSON 回應格式化
"""

import json
from datetime import datetime, timezone
from unittest.mock import Mock, patch

from google.cloud.exceptions import Forbidden, NotFound


class TestBigQueryStreamingBufferMonitor:
    """BigQuery Streaming Buffer Monitor 主要測試類別"""

    def test_get_request_with_default_params(self):
        """測試使用預設參數的 GET 請求"""
        from main import main

        # 模擬 BigQuery 客戶端和回應
        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            # 模擬 table 和 streaming_buffer
            mock_table = Mock()
            mock_streaming_buffer = Mock()
            mock_streaming_buffer.estimated_rows = 68849
            mock_streaming_buffer.estimated_bytes = 9338470
            mock_streaming_buffer.oldest_entry_time = datetime(
                2025, 7, 29, 3, 1, 59, tzinfo=timezone.utc
            )
            mock_table.streaming_buffer = mock_streaming_buffer

            mock_client.get_table.return_value = mock_table

            # 模擬 GET 請求 - 使用預設參數行為
            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.side_effect = lambda key, default=None: default

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證結果
            assert status_code == 200
            response_data = json.loads(response)
            assert response_data["has_streaming_buffer"] is True
            assert response_data["estimated_rows"] == "68849"
            assert response_data["estimated_bytes"] == "9338470"
            assert response_data["status"] == "success"
            assert (
                response_data["table_reference"]["project_id"] == "gothic-province-823"
            )
            assert response_data["table_reference"]["dataset_id"] == "tagtooad"
            assert response_data["table_reference"]["table_id"] == "partner_uid_mapping"

            # 驗證 BigQuery 客戶端調用
            mock_client_class.assert_called_once()
            mock_client.get_table.assert_called_once()

    def test_get_request_with_custom_params(self):
        """測試使用自訂參數的 GET 請求"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            # 模擬無串流緩衝區的情況
            mock_table = Mock()
            mock_table.streaming_buffer = None
            mock_client.get_table.return_value = mock_table

            # 模擬帶參數的 GET 請求
            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.side_effect = lambda key, default=None: {
                "project_id": "test-project",
                "dataset_id": "test-dataset",
                "table_id": "test-table",
            }.get(key, default)

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證結果
            assert status_code == 200
            response_data = json.loads(response)
            assert response_data["has_streaming_buffer"] is False
            assert response_data["estimated_rows"] == "0"
            assert response_data["estimated_bytes"] == "0"
            assert response_data["oldest_entry_time"] is None
            assert response_data["table_reference"]["project_id"] == "test-project"
            assert response_data["table_reference"]["dataset_id"] == "test-dataset"
            assert response_data["table_reference"]["table_id"] == "test-table"

    def test_post_request_with_json_body(self):
        """測試 POST 請求處理 JSON 參數"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            # 模擬有串流緩衝區的情況
            mock_table = Mock()
            mock_streaming_buffer = Mock()
            mock_streaming_buffer.estimated_rows = 12345
            mock_streaming_buffer.estimated_bytes = 567890
            mock_streaming_buffer.oldest_entry_time = datetime(
                2025, 7, 29, 5, 30, 0, tzinfo=timezone.utc
            )
            mock_table.streaming_buffer = mock_streaming_buffer
            mock_client.get_table.return_value = mock_table

            # 模擬 POST 請求
            mock_request = Mock()
            mock_request.method = "POST"
            mock_request.get_json.return_value = {
                "project_id": "post-project",
                "dataset_id": "post-dataset",
                "table_id": "post-table",
            }

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證結果
            assert status_code == 200
            response_data = json.loads(response)
            assert response_data["has_streaming_buffer"] is True
            assert response_data["estimated_rows"] == "12345"
            assert response_data["estimated_bytes"] == "567890"
            assert response_data["table_reference"]["project_id"] == "post-project"

    def test_options_request_cors(self):
        """測試 CORS 預檢請求"""
        from main import main

        # 模擬 OPTIONS 請求
        mock_request = Mock()
        mock_request.method = "OPTIONS"

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 204
        assert response == ""

    def test_unsupported_method(self):
        """測試不支援的 HTTP 方法"""
        from main import main

        # 模擬不支援的請求方法
        mock_request = Mock()
        mock_request.method = "DELETE"

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 405
        response_data = json.loads(response)
        assert response_data["status"] == "error"
        assert "unsupported method" in response_data["error"].lower()

    def test_table_not_found_error(self):
        """測試表格不存在的錯誤處理"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            # 模擬表格不存在錯誤
            mock_client.get_table.side_effect = NotFound("Table not found")

            # 模擬 GET 請求
            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.return_value = None

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證結果
            assert status_code == 404
            response_data = json.loads(response)
            assert response_data["status"] == "error"
            assert "not found" in response_data["error"].lower()

    def test_insufficient_permissions_error(self):
        """測試權限不足的錯誤處理"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            # 模擬權限不足錯誤
            mock_client.get_table.side_effect = Forbidden("Insufficient permissions")

            # 模擬 GET 請求
            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.return_value = None

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證結果
            assert status_code == 403
            response_data = json.loads(response)
            assert response_data["status"] == "error"
            assert "permission" in response_data["error"].lower()

    def test_invalid_json_post_request(self):
        """測試無效 JSON 的 POST 請求"""
        from main import main

        # 模擬無效 JSON 的 POST 請求
        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.get_json.return_value = None

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證結果
        assert status_code == 400
        response_data = json.loads(response)
        assert response_data["status"] == "error"
        assert "json" in response_data["error"].lower()

    def test_bigquery_api_general_exception(self):
        """測試 BigQuery API 一般性異常處理"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            # 模擬一般性異常
            mock_client.get_table.side_effect = Exception("BigQuery API error")

            # 模擬 GET 請求
            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.return_value = None

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證結果
            assert status_code == 500
            response_data = json.loads(response)
            assert response_data["status"] == "error"

    def test_response_format_structure(self):
        """測試回應格式結構的完整性"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            # 模擬有串流緩衝區的情況
            mock_table = Mock()
            mock_streaming_buffer = Mock()
            mock_streaming_buffer.estimated_rows = 100
            mock_streaming_buffer.estimated_bytes = 2000
            mock_streaming_buffer.oldest_entry_time = datetime(
                2025, 7, 29, 12, 0, 0, tzinfo=timezone.utc
            )
            mock_table.streaming_buffer = mock_streaming_buffer
            mock_client.get_table.return_value = mock_table

            # 模擬 GET 請求
            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.return_value = None

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證完整回應結構
            assert status_code == 200
            response_data = json.loads(response)

            # 必須包含的欄位
            required_fields = [
                "has_streaming_buffer",
                "estimated_rows",
                "estimated_bytes",
                "oldest_entry_time",
                "table_reference",
                "queried_at",
                "status",
            ]
            for field in required_fields:
                assert field in response_data, f"Missing required field: {field}"

            # table_reference 結構
            table_ref = response_data["table_reference"]
            assert "project_id" in table_ref
            assert "dataset_id" in table_ref
            assert "table_id" in table_ref

            # 確認時間戳格式
            assert "T" in response_data["queried_at"]  # ISO format
            assert "T" in response_data["oldest_entry_time"]  # ISO format


class TestBigQueryClientInitialization:
    """BigQuery 客戶端初始化測試"""

    @patch("main.bigquery.Client")
    def test_client_uses_application_default_credentials(self, mock_client_class):
        """測試 BigQuery 客戶端使用 Application Default Credentials"""
        from main import main

        mock_client = Mock()
        mock_client_class.return_value = mock_client

        # 模擬正常的表格回應
        mock_table = Mock()
        mock_table.streaming_buffer = None
        mock_client.get_table.return_value = mock_table

        # 模擬請求
        mock_request = Mock()
        mock_request.method = "GET"
        mock_request.args.get.return_value = None

        # 執行函數
        main(mock_request)

        # 驗證 BigQuery 客戶端是否使用 Application Default Credentials
        # (沒有傳入 credentials 參數)
        mock_client_class.assert_called_once_with()


class TestParameterValidation:
    """參數驗證測試"""

    def test_parameter_extraction_get_request(self):
        """測試 GET 請求參數提取"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            mock_table = Mock()
            mock_table.streaming_buffer = None
            mock_client.get_table.return_value = mock_table

            # 模擬帶參數的 GET 請求
            mock_request = Mock()
            mock_request.method = "GET"

            def args_get_side_effect(key, default=None):
                params = {
                    "project_id": "custom-project",
                    "dataset_id": "custom-dataset",
                    "table_id": "custom-table",
                }
                return params.get(key, default)

            mock_request.args.get.side_effect = args_get_side_effect

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證正確的表格引用被使用
            response_data = json.loads(response)
            assert response_data["table_reference"]["project_id"] == "custom-project"
            assert response_data["table_reference"]["dataset_id"] == "custom-dataset"
            assert response_data["table_reference"]["table_id"] == "custom-table"

    def test_parameter_extraction_post_request(self):
        """測試 POST 請求參數提取"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            mock_table = Mock()
            mock_table.streaming_buffer = None
            mock_client.get_table.return_value = mock_table

            # 模擬 POST 請求
            mock_request = Mock()
            mock_request.method = "POST"
            mock_request.get_json.return_value = {
                "project_id": "json-project",
                "dataset_id": "json-dataset",
                "table_id": "json-table",
            }

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證正確的表格引用被使用
            response_data = json.loads(response)
            assert response_data["table_reference"]["project_id"] == "json-project"
            assert response_data["table_reference"]["dataset_id"] == "json-dataset"
            assert response_data["table_reference"]["table_id"] == "json-table"


class TestEdgeCases:
    """邊緣情況和額外錯誤處理測試"""

    def test_empty_parameters_use_defaults(self):
        """測試空參數使用預設值"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            mock_table = Mock()
            mock_table.streaming_buffer = None
            mock_client.get_table.return_value = mock_table

            # 模擬空參數的 POST 請求
            mock_request = Mock()
            mock_request.method = "POST"
            mock_request.get_json.return_value = {}

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證使用預設值
            assert status_code == 200
            response_data = json.loads(response)
            assert (
                response_data["table_reference"]["project_id"] == "gothic-province-823"
            )
            assert response_data["table_reference"]["dataset_id"] == "tagtooad"
            assert response_data["table_reference"]["table_id"] == "partner_uid_mapping"

    def test_malformed_json_post_request(self):
        """測試格式錯誤的 JSON POST 請求"""
        from main import main

        # 模擬 JSON 解析異常的 POST 請求
        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.get_json.side_effect = ValueError("Invalid JSON")

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證錯誤處理
        assert status_code == 400
        response_data = json.loads(response)
        assert response_data["status"] == "error"

    def test_streaming_buffer_with_null_oldest_entry_time(self):
        """測試串流緩衝區存在但 oldest_entry_time 為 None 的情況"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            # 模擬有串流緩衝區但 oldest_entry_time 為 None
            mock_table = Mock()
            mock_streaming_buffer = Mock()
            mock_streaming_buffer.estimated_rows = 100
            mock_streaming_buffer.estimated_bytes = 2000
            mock_streaming_buffer.oldest_entry_time = None
            mock_table.streaming_buffer = mock_streaming_buffer
            mock_client.get_table.return_value = mock_table

            # 模擬請求
            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.return_value = None

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證處理 None 的 oldest_entry_time
            assert status_code == 200
            response_data = json.loads(response)
            assert response_data["has_streaming_buffer"] is True
            assert response_data["oldest_entry_time"] is None

    def test_cors_headers_and_multiple_options_requests(self):
        """測試 CORS 處理和多次 OPTIONS 請求"""
        from main import main

        # 測試多次 OPTIONS 請求都返回相同結果
        for _ in range(3):
            mock_request = Mock()
            mock_request.method = "OPTIONS"

            response, status_code = main(mock_request)

            assert status_code == 204
            assert response == ""

    def test_bigquery_client_initialization_failure(self):
        """測試 BigQuery 客戶端初始化失敗"""
        from main import main

        with patch("main.bigquery.Client") as mock_client_class:
            # 模擬客戶端初始化失敗
            mock_client_class.side_effect = Exception("Client initialization failed")

            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.return_value = None

            # 執行函數
            response, status_code = main(mock_request)

            # 驗證錯誤處理
            assert status_code == 500
            response_data = json.loads(response)
            assert response_data["status"] == "error"
            assert "bigquery api" in response_data["error"].lower()

    def test_request_method_case_sensitivity(self):
        """測試 HTTP 方法的大小寫敏感性"""
        from main import main

        # 測試小寫方法名稱
        mock_request = Mock()
        mock_request.method = "get"  # 小寫

        response, status_code = main(mock_request)

        # 應該返回不支援的方法錯誤
        assert status_code == 405
        response_data = json.loads(response)
        assert response_data["status"] == "error"

    def test_response_contains_queried_at_timestamp(self):
        """測試所有回應都包含 queried_at 時間戳"""
        from main import main

        # 測試成功回應
        with patch("main.bigquery.Client") as mock_client_class:
            mock_client = Mock()
            mock_client_class.return_value = mock_client

            mock_table = Mock()
            mock_table.streaming_buffer = None
            mock_client.get_table.return_value = mock_table

            mock_request = Mock()
            mock_request.method = "GET"
            mock_request.args.get.return_value = None

            response, status_code = main(mock_request)
            response_data = json.loads(response)
            assert "queried_at" in response_data
            assert "T" in response_data["queried_at"]

        # 測試錯誤回應
        mock_request = Mock()
        mock_request.method = "DELETE"

        response, status_code = main(mock_request)
        response_data = json.loads(response)
        assert "queried_at" in response_data
        assert "T" in response_data["queried_at"]

    def test_empty_string_parameters_validation(self):
        """測試空字串參數驗證"""
        from main import main

        # 測試 POST 請求中的空字串參數
        mock_request = Mock()
        mock_request.method = "POST"
        mock_request.get_json.return_value = {
            "project_id": "",
            "dataset_id": "tagtooad",
            "table_id": "partner_uid_mapping",
        }

        # 執行函數
        response, status_code = main(mock_request)

        # 驗證返回參數驗證錯誤
        assert status_code == 400
        response_data = json.loads(response)
        assert response_data["status"] == "error"
        assert "cannot be empty" in response_data["error"]
