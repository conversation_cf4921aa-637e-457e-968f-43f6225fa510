"""
BigQuery Streaming Buffer Monitor 全面測試驗證

此檔案執行所有測試套件，提供完整的專案驗證。
包含單元測試、整合測試、部署測試、排程測試和監控整合測試。

執行方式:
  python3 test_comprehensive.py

注意：這是最終的完整驗證，需要所有測試文件都存在
"""

import subprocess
import sys
import time
from pathlib import Path


class ComprehensiveTestRunner:
    """全面測試執行器"""

    def __init__(self):
        self.project_dir = Path(__file__).parent

        # 定義所有測試文件
        self.test_files = {
            "單元測試": "test_main.py",
            "Terraform 配置測試": "test_terraform.py",
            "部署測試": "test_deployment.py",
            "Cloud Scheduler 測試": "test_scheduler.py",
            "監控整合測試": "test_monitoring_integration.py",
        }

        # 可選的整合測試 (需要 GCP 認證)
        self.integration_tests = {"BigQuery 整合測試": "test_integration.py"}

    def check_test_files_exist(self):
        """檢查所有測試文件是否存在"""
        print("📁 檢查測試文件...")

        missing_files = []
        existing_files = []

        # 檢查必要的測試文件
        for test_name, test_file in self.test_files.items():
            file_path = self.project_dir / test_file
            if file_path.exists():
                existing_files.append((test_name, test_file))
                print(f"   ✅ {test_file} 存在")
            else:
                missing_files.append((test_name, test_file))
                print(f"   ❌ {test_file} 不存在")

        # 檢查可選的整合測試文件
        for test_name, test_file in self.integration_tests.items():
            file_path = self.project_dir / test_file
            if file_path.exists():
                existing_files.append((test_name, test_file))
                print(f"   ✅ {test_file} 存在 (整合測試)")
            else:
                print(f"   ⚠️  {test_file} 不存在 (可選的整合測試)")

        return existing_files, missing_files

    def run_test_file(self, test_name, test_file):
        """執行單個測試文件"""
        print(f"\n🧪 執行 {test_name}...")
        print("-" * 40)

        file_path = self.project_dir / test_file

        try:
            # 執行測試文件
            start_time = time.time()
            result = subprocess.run(
                [sys.executable, test_file],
                cwd=self.project_dir,
                capture_output=True,
                text=True,
                timeout=300,  # 5 分鐘超時
            )
            execution_time = time.time() - start_time

            # 顯示結果
            if result.returncode == 0:
                print(f"✅ {test_name} 通過")
                print(f"⏱️  執行時間: {execution_time:.2f} 秒")

                # 顯示測試輸出的關鍵資訊
                if result.stdout:
                    lines = result.stdout.strip().split("\n")
                    # 顯示最後幾行（通常包含測試結果摘要）
                    summary_lines = [line for line in lines[-5:] if line.strip()]
                    if summary_lines:
                        print("📊 測試摘要:")
                        for line in summary_lines:
                            print(f"   {line}")

                return True, execution_time, result.stdout
            else:
                print(f"❌ {test_name} 失敗")
                print(f"⏱️  執行時間: {execution_time:.2f} 秒")
                print(f"🔴 錯誤碼: {result.returncode}")

                # 顯示錯誤資訊
                if result.stderr:
                    print("❌ 錯誤輸出:")
                    print(result.stderr)
                if result.stdout:
                    print("📝 標準輸出:")
                    print(result.stdout)

                return False, execution_time, result.stderr

        except subprocess.TimeoutExpired:
            print(f"⏰ {test_name} 超時 (5分鐘)")
            return False, 300, "Test timeout"
        except Exception as e:
            print(f"💥 {test_name} 執行異常: {str(e)}")
            return False, 0, str(e)

    def check_main_dependencies(self):
        """檢查主要依賴文件"""
        print("🔍 檢查主要依賴文件...")

        required_files = [
            "main.py",
            "requirements.txt",
            "Makefile",
            "terraform/main.tf",
        ]

        all_exist = True
        for file_name in required_files:
            file_path = self.project_dir / file_name
            if file_path.exists():
                print(f"   ✅ {file_name} 存在")
            else:
                print(f"   ❌ {file_name} 不存在")
                all_exist = False

        return all_exist

    def run_syntax_check(self):
        """執行 Python 語法檢查"""
        print("🔧 執行 Python 語法檢查...")

        python_files = list(self.project_dir.glob("*.py"))
        syntax_errors = []

        for py_file in python_files:
            try:
                result = subprocess.run(
                    [sys.executable, "-m", "py_compile", py_file.name],
                    cwd=self.project_dir,
                    capture_output=True,
                    text=True,
                )

                if result.returncode == 0:
                    print(f"   ✅ {py_file.name} 語法正確")
                else:
                    print(f"   ❌ {py_file.name} 語法錯誤")
                    syntax_errors.append((py_file.name, result.stderr))

            except Exception as e:
                print(f"   💥 {py_file.name} 檢查異常: {str(e)}")
                syntax_errors.append((py_file.name, str(e)))

        if syntax_errors:
            print("\n🔴 語法錯誤詳情:")
            for file_name, error in syntax_errors:
                print(f"   {file_name}: {error}")

        return len(syntax_errors) == 0

    def generate_test_report(self, test_results):
        """生成測試報告"""
        print("\n" + "=" * 60)
        print("📊 BigQuery Streaming Buffer Monitor 測試報告")
        print("=" * 60)

        total_tests = len(test_results)
        passed_tests = sum(1 for result in test_results if result["passed"])
        total_time = sum(result["execution_time"] for result in test_results)

        print(f"📈 整體統計:")
        print(f"   - 總測試數: {total_tests}")
        print(f"   - 通過測試: {passed_tests}")
        print(f"   - 失敗測試: {total_tests - passed_tests}")
        print(f"   - 成功率: {(passed_tests/total_tests)*100:.1f}%")
        print(f"   - 總執行時間: {total_time:.2f} 秒")

        print(f"\n📋 詳細結果:")
        for result in test_results:
            status = "✅ 通過" if result["passed"] else "❌ 失敗"
            print(f"   {result['name']}: {status} ({result['execution_time']:.2f}s)")

        # 分析整體健康度
        success_rate = (passed_tests / total_tests) * 100

        if success_rate >= 90:
            health_status = "🟢 優秀"
            recommendation = "專案準備就緒，可以進行部署"
        elif success_rate >= 75:
            health_status = "🟡 良好"
            recommendation = "大部分功能正常，建議檢查失敗的測試"
        elif success_rate >= 50:
            health_status = "🟠 一般"
            recommendation = "存在多個問題，建議修復後再部署"
        else:
            health_status = "🔴 不佳"
            recommendation = "存在嚴重問題，需要全面檢查和修復"

        print(f"\n🎯 專案健康度: {health_status} ({success_rate:.1f}%)")
        print(f"💡 建議: {recommendation}")

        return success_rate

    def run_comprehensive_tests(self):
        """執行全面測試"""
        print("🚀 開始 BigQuery Streaming Buffer Monitor 全面測試")
        print("=" * 60)

        # 1. 檢查依賴文件
        print("\n1️⃣ 依賴檢查:")
        if not self.check_main_dependencies():
            print("❌ 依賴文件檢查失敗，終止測試")
            return False

        # 2. 語法檢查
        print("\n2️⃣ 語法檢查:")
        if not self.run_syntax_check():
            print("❌ 語法檢查失敗，終止測試")
            return False

        # 3. 檢查測試文件
        print("\n3️⃣ 測試文件檢查:")
        existing_files, missing_files = self.check_test_files_exist()

        if missing_files:
            print(f"\n⚠️  缺少 {len(missing_files)} 個測試文件，但繼續執行現有測試")

        # 4. 執行所有可用的測試
        print("\n4️⃣ 執行測試套件:")
        test_results = []

        for test_name, test_file in existing_files:
            passed, execution_time, output = self.run_test_file(test_name, test_file)
            test_results.append(
                {
                    "name": test_name,
                    "file": test_file,
                    "passed": passed,
                    "execution_time": execution_time,
                    "output": output,
                }
            )

        # 5. 生成測試報告
        success_rate = self.generate_test_report(test_results)

        # 6. 提供下一步建議
        self.provide_next_steps(success_rate, test_results)

        return success_rate >= 75  # 75% 以上認為測試通過

    def provide_next_steps(self, success_rate, test_results):
        """提供下一步建議"""
        print(f"\n📝 下一步建議:")

        if success_rate >= 90:
            print("🎉 恭喜！所有測試基本通過，專案準備就緒。")
            print("\n🚀 部署步驟:")
            print("   1. cd terraform/")
            print("   2. terraform init")
            print("   3. terraform plan")
            print("   4. terraform apply")
            print("   5. 測試部署的 Cloud Function")

        elif success_rate >= 75:
            print("✅ 大部分測試通過，建議處理失敗的測試項目。")

            failed_tests = [r for r in test_results if not r["passed"]]
            if failed_tests:
                print("\n🔧 需要處理的測試:")
                for test in failed_tests:
                    print(f"   - {test['name']}")

        else:
            print("⚠️  多個測試失敗，建議先修復問題。")

            failed_tests = [r for r in test_results if not r["passed"]]
            print(f"\n🚨 失敗的測試 ({len(failed_tests)} 個):")
            for test in failed_tests:
                print(f"   - {test['name']}: {test['file']}")

        print(f"\n📚 參考文件:")
        print("   - Spec 要求: @.agent-os/specs/.../spec.md")
        print("   - 技術規格: @.agent-os/specs/.../sub-specs/technical-spec.md")
        print("   - 任務清單: @.agent-os/specs/.../tasks.md")


def print_comprehensive_test_guide():
    """列印全面測試指南"""
    print(
        """
🧪 BigQuery Streaming Buffer Monitor 全面測試指南

🎯 測試目的:
- 執行所有可用的測試套件
- 提供完整的專案健康度評估
- 驗證部署準備狀態
- 生成詳細的測試報告

🚀 執行測試:
python3 test_comprehensive.py

📋 測試涵蓋範圍:
✓ 單元測試 (test_main.py)
✓ Terraform 配置測試 (test_terraform.py)
✓ 部署準備測試 (test_deployment.py)  
✓ Cloud Scheduler 測試 (test_scheduler.py)
✓ 監控整合測試 (test_monitoring_integration.py)
✓ BigQuery 整合測試 (test_integration.py) - 可選

🔧 執行環境要求:
- Python 3.7+
- 所有測試依賴文件
- 基本的系統工具 (對於部署測試)

⚠️  注意事項:
- 整合測試需要 GCP 認證 (可選)
- 部署測試需要 Terraform 和 gcloud (可選)
- 測試執行可能需要幾分鐘時間
- 失敗的測試會提供詳細的錯誤資訊

📊 健康度評估:
- 🟢 優秀 (90%+): 準備部署
- 🟡 良好 (75-89%): 小問題需修復
- 🟠 一般 (50-74%): 多個問題需處理
- 🔴 不佳 (<50%): 需要全面檢查
"""
    )


if __name__ == "__main__":
    if len(sys.argv) > 1 and sys.argv[1] == "--guide":
        print_comprehensive_test_guide()
    else:
        runner = ComprehensiveTestRunner()
        success = runner.run_comprehensive_tests()
        sys.exit(0 if success else 1)
