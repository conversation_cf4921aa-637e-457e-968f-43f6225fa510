import functions_framework
from google.oauth2 import service_account
from publisher import PublisherClient
import google_storage
import logging

@functions_framework.http
def main(request):
    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    request_json = request.get_json(silent=True)
    request_args = request.args

    if request_json and 'file_path' in request_json:
        file_path = request_json['file_path']
    elif request_args and 'name' in request_args:
        file_path = request_args['file_path']
    else:
        raise AttributeError("Can't find file path in request url.")

    logging.basicConfig(level=logging.INFO)

    # verify account
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)

    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    prefix = file_path[24:-18]
    bucket_name = file_path[5:].split('/')[0]
    data_path_list = GSIO.get_wildcard_file_names(bucket_name, prefix)
    data_path_list = [f'gs://{bucket_name}/' + file for file in data_path_list]
    logging.info(f"Start sending {len(data_path_list)} Files to pubsub.")

    ec_id_for_mapping_pixal = '8888888'

    with PublisherClient(project_id='tagtoo-tracking', topic_name='lta-prod', credentials=credentials) as publisher:
          for file in data_path_list:
                publisher.add_message(
                    '',
                    file_name=file,
                    version='v1',
                    ec_id=ec_id_for_mapping_pixal,
                )
    print('Job complete.')

    return 'Success', 200
