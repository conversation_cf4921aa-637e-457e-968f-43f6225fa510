import logging
from functools import partial
from collections import deque
from concurrent import futures
from google import api_core
from google.cloud import pubsub_v1


# Configure the retry settings. Defaults shown in comments are values applied
# by the library by default, instead of default values in the Retry object.
custom_retry = api_core.retry.Retry(
    initial=0.250,  # seconds (default: 0.1)
    maximum=90.0,  # seconds (default: 60.0)
    multiplier=1.45,  # default: 1.3
    deadline=120.0,  # seconds (default: 60.0)
    predicate=api_core.retry.if_exception_type(
        api_core.exceptions.Aborted,
        api_core.exceptions.DeadlineExceeded,
        api_core.exceptions.InternalServerError,
        api_core.exceptions.ResourceExhausted,
        api_core.exceptions.ServiceUnavailable,
        api_core.exceptions.Unknown,
        api_core.exceptions.Cancelled,
    ),
)

# Configure the batch to publish as soon as there are 1k messages
# or 3MB of data, or 1 second has passed.
batch_settings = pubsub_v1.types.BatchSettings(
    max_messages=1000,  # default 100
    max_bytes=3072,  # default 1 MiB
    max_latency=1,  # default 10 ms
)


class PublisherClient:
    """Context manageable publisher client configured with retry settings.
    Example:
        >>> with PublisherClient('[PROJECT]', '[TOPIC]') as publisher:
        >>>     for i in range(10):
        >>>         publisher.add_message(f"Message {i}")
    """
    def __init__(self, project_id, topic_name, concurrent=3000, credentials=None, *args, **kwargs):
        self._client = pubsub_v1.PublisherClient(batch_settings, credentials=credentials, *args, **kwargs)
        self.topic_path = self._client.topic_path(project_id, topic_name)
        self.tasks = []
        self.future_q = deque(maxlen=concurrent)

    def _produce_message(self):
        for task in self.tasks:
            future = task()
            self.future_q.appendleft(future)
            if len(self.future_q) == self.future_q.maxlen:
                print('Future queue full, yielding control ...')
                yield

    def _consume_message(self):
        while True:
            # Blocking until all messages in queue are published
            futures.wait(self.future_q, return_when=futures.ALL_COMPLETED)
            self.future_q.clear()
            print('Future queue empty, yielding control ...')
            yield

    def add_message(self, data, **kwargs):
        for k, v in kwargs.items():
            assert isinstance(v, bytes) or isinstance(v, str), (
                "Message metadata attribute should be text strings or byte strings"
            )
        # Data must be a bytestring
        data = data.encode("utf-8")
        task = partial(self._client.publish, self.topic_path, data=data, retry=custom_retry, **kwargs)
        self.tasks.append(task)

    def __enter__(self):
        return self

    def __exit__(self, exc_type, exc_val, exc_tb):
        producer = self._produce_message()
        consumer = self._consume_message()
        while True:
            try:
                next(producer)
            except StopIteration:
                break
            finally:
                next(consumer)
        if exc_type:
            logging.exception(exc_type)
        return False
