# post-lta-data-to-ga4

## 任務描述
- 將 LTA 受眾包從 Tagtoo Event query 出來，再透過 Measurement Protocol API 發送到 GA4
- 目前只有 query 特定的受眾包
    - DCIU x NAPL: cloud function `ga-orbis-chizup` 產生
    - TOP50: cloud function `Get_product` 和 `Tag_user` 產生
- 後續開發: 測試成功後，可能需要把 LTA 常態受眾包發送到 GA4（需要想大量發送作法）。
## 使用說明
### GA4 設定
- 建立自訂維度
    - 管理 → 資料顯示 → 自訂定義 → 建立自訂維度
        - lta
        - segment_id
- measurement protocol
    - 管理 → 資料串流 → 網址 → Measurement Protocol API 密鑰 → 建立
    - 提取 密鑰值 和 評估 ID
### Cloud Scheduler
1. 需要先產生 TOP50 的受眾標籤。\
    需要架設兩個 cloud scheduler 來分別觸發以下的 cloud function，詳細說明請參考各自的 cloud function 說明文件。
    - Get_product_tag
    - Tag_user
2. 一家 EC 架設一個 cloud sheduler，內文參數 `{"ecid": "<ecid>"}`

## 功能說明
1. 從 Tagtoo Event (BigQuery) 產生 DCIU x NAPL 標籤
2. 讀取 TOP50 受眾標籤檔案
3. 合併兩者
4. 發送到指定 EC 的 GA4