import functions_framework
import pandas as pd
import google_storage
from google.cloud import bigquery
from datetime import datetime
from pytz import timezone
from utils import *
from google.oauth2 import service_account

GSIO = google_storage.GoogleStorageIO()
date = datetime.now(timezone('Asia/Taipei')).strftime("%Y-%m-%d")

measurement_id_and_key_dict = {
    # orbis
    '107':
    ['zrkZP2IBQMCLTl4v2z2TrA', 'G-1LQL6C677P'],
    # chizup
    '2156':
    ['Mb_3DRpITKWVMTU6PtZ_Ow', 'G-GB39YG8939'],
    # oringo
    '1474':
    ['N18xso16RA60xGMJ619JCg', 'G-ZLW1TX80XX'],
    # locknlock
    '1825':
    ['eHw_hb0RTQCGBjR4wQOkfg', 'G-ZN4C4B6ELC'],
    # mars
    '2808':
    ['SgO8mGO4RmiOxp4bfo8Q-w', 'G-S5RFYJ71WB'],
    # onemore
    '2917':
    ['hrVvJn3GQ2-opHnuYwtNRw', 'G-MBMS4D7VER'],
    # Looder
    '3218':
    ['cFXyP603TP68EGuAXb9AZw', 'G-5PK9Q33Y5R'],
    # kingstone
    '1345':
    ['5ShLdGvPQJmjLPRrPRFsaA', 'G-KJWLL2C5FT'],
    # wstyle
    '1626':
    ['5490lRz-TaWc3x6peE7qmQ', 'G-VCK5L9NJCP'],
    # IDP
    '3262':
    ['kXIyrQDLR1ichB0ZkHY7Cw', 'G-3G8VZ53ZLX'],
    # dHConcept
    '3511':
    ['39oFk7KUQRyXpRUNN4QFUA', 'G-5H3WS72KYK'],
    # caco
    '183':
    ['jNJsYqfkTcanZYpLTVaftg', 'G-GJ4C8XKCKM'],
    # s3
    '163':
    ['mrA678t-RV-eJKdaoQuqpw', 'G-SYKTYQXZEL']
}

@functions_framework.http
def main(request):

    # * external argument
    request_json = request.get_json(silent=True)
    request_args = request.args

    if request_json and 'ecid' in request_json: # POST
        ecid = request_json['ecid']
    elif request_args and 'ecid' in request_args: # GET
        ecid = request_args['ecid']
    else:
        raise AttributeError("No ecid found in URL.")
    print("ECID:", ecid)

    dciu_query = make_dciu_query(ecid)
    napl_query = make_napl_query(ecid)
    
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    client = bigquery.Client.from_service_account_json(json_credentials_path=key_path)

    try:
        df_napl = query_bigquery_to_dataframe(client, napl_query)
        df_dciu = query_bigquery_to_dataframe(client, dciu_query)
    except Exception as e:
        print(f"Error querying BigQuery: {e}")
        return 'Failed to query data', 500

    # Concatenate df_napl and df_dciu
    df = pd.concat([df_napl, df_dciu])
    # Extract the last two elements after splitting 'ga' by '.'
    df['ga'] = df['ga'].str.split('.').str[-2:].str.join('.')
    # Group by 'ga' and 'permanent', aggregate 'segment_id' as a comma-separated string
    df = df[['ga', 'permanent', 'segment_id']].drop_duplicates().groupby(['ga', 'permanent'])['segment_id'].apply(','.join).reset_index()

    topk_path = f'gs://tagtoo-ml-workflow/topic10/ec_product_tag/{ecid}/{date}/result.csv'
    if check_gcs_object(credentials=credentials, object_path=topk_path):
        # Load df_top
        df_top = load_data(topk_path)
        # Merge df and df_top
        df_top = pd.merge(df[['ga', 'permanent']], df_top, how='inner')
        # Concatenate df and df_top, drop NaN values
        df_final = pd.concat([df, df_top], axis=0).dropna()
    else:
        df_final = df
    # Group by 'ga', aggregate 'segment_id' as a comma-separated string, and remove duplicates
    df_final = df_final[['ga', 'segment_id']].drop_duplicates().groupby('ga')['segment_id'].apply(','.join).reset_index()
    # Remove duplicate segment_id values within each group
    df_final['segment_id'] = df_final['segment_id'].apply(lambda x: ','.join(list(set(x.split(',')))))
    
    # Post data to GA4
    asyncio.run(post_all_data_to_ga4_with_throttling(df_final, ecid, measurement_id_and_key_dict))
    print(f"{ecid} done, {len(df_final)} rows processed")
    
    return 'Success', 200
