import time
import json
import aiohttp
import asyncio
from dask import dataframe
from block_timer.timer import Timer
from google.cloud import storage

@Timer(title="Load data")
def load_data(path, **kwargs):
    """Loading the pieces in separate processes, then transferring all the data to be stitched into
    a single data-frame in the main process."""
    df = dataframe.read_csv(path, assume_missing=True, **kwargs)
    return df.compute(scheduler="processes")

@Timer(title="Parquet load data")
def parquet_load_data(path, **kwargs):
    """Loading the pieces in separate processes, then transferring all the data to be stitched into
    a single data-frame in the main process."""
    df = dataframe.read_parquet(path, assume_missing=True, **kwargs)
    return df.compute(scheduler="processes")

async def post_data_to_ga4(session, url, payload):
    async with session.post(url, data=json.dumps(payload), headers={'Content-Type': 'application/json'}) as response:
        return await response.text()

async def post_all_data_to_ga4_with_throttling(df, ecid, measurement_id_and_key_dict):
    url = f'https://www.google-analytics.com/mp/collect?api_secret={measurement_id_and_key_dict[ecid][0]}&measurement_id={measurement_id_and_key_dict[ecid][1]}'
    async with aiohttp.ClientSession() as session:
        tasks = []
        for _, row in df.iterrows():
            payload = {
                "client_id": row['ga'],
                "events": [{
                    "name": "lta",
                    "params": {
                        "segment_id": row['segment_id']
                    }
                }]
            }
            tasks.append(post_data_to_ga4(session, url, payload))
            await asyncio.sleep(0.01)  # 在每個請求之間加入 0.1 秒的休眠，避免一次打太多 request 造成錯誤
        responses = await asyncio.gather(*tasks)
        return responses

def query_bigquery_to_dataframe(client, query):
    for _ in range(3):  # Retry 3 times
        try:
            return client.query(query).to_dataframe()
        except Exception as e:
            print(f"BigQuery query failed: {e}. Retrying...")
            time.sleep(30)
    raise Exception("Failed to execute BigQuery query after retries")

def make_dciu_query(ecid: str) -> str:
    """
    更動說明
    - 原本的 DCIU 使用者並不包含所有全站的使用者
        - 之前用 group_id 來聚合使用者事件數量，但在 join UU 的表時，一定得在 UU 表中找到對應的 permanent 才會回傳回來，但 UU 那張表不會有所有 permanent （之前做的分析大概有50% permanent 不在 UU 表中，因為這些人不需要被 group）
        - 改成有 gropu_id 就用 group_id, 沒有的話則用 permanent
    - GAID 只發送當天的
        - 另外原本的結果只包含前一天有進電商網站的 gaid ，沒有包含站外的 gaid，FB 那邊做法是會把 Tagtoo Event 過去一段時間的所有 user entity 都發送過去
        - 改成找 Tagtoo Event 過去30天內該使用者在站內的 gaid
    - GroupBy Key
        - 在計算使用者事件數量，是以 group_id, gaid 當作維度來聚合事件數量，可能會導致同一個 group_id 因為擁有多個 gaid ，所以事件數量被分開計算（這一點不影響 DCIU 總人數，但會影響到比例分佈）
        - 改成以 group_id 當作維度聚合, 計算完事件數量後再跟 gaid 合併
    """
    return f"""
    WITH
    YesterdayUser AS (
    SELECT
        DISTINCT permanent,
    FROM
        `tagtoo-tracking.event_prod.tagtoo_event`
    WHERE
        DATE(event_time, "Asia/Taipei") = DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 1 DAY)
        AND ec_id = {ecid} ),
    YesterdayUserEventCount AS (
    SELECT
        DISTINCT
        -- 對得到 group_id, 就用 group_id, 否則用原本的 permanent
        CASE
        WHEN UU.group_id IS NULL THEN TE.permanent
        ELSE UU.group_id
    END
        AS permanent,
        COUNT(
        IF
        (event.name='page_view', ec_id, NULL)) AS pageview_count,
        COUNT(
        IF
        (event.name IN ('view_item',
            'view_item_list'), ec_id, NULL)) AS view_item_count,
        COUNT(
        IF
        (event.name='add_to_cart', ec_id, NULL)) AS add_to_cart_count,
    FROM
        `tagtoo-tracking.event_prod.tagtoo_event` TE
    LEFT JOIN
        `tagtoo-tracking.event_prod.user_unify_group_permanent` UU
    ON
        TE.permanent = UU.permanent
    JOIN
        YesterdayUser
    ON
        YesterdayUser.permanent = TE.permanent
    WHERE
        DATE(event_time, "Asia/Taipei") >= DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 14 DAY)
        AND DATE(event_time, "Asia/Taipei") < CURRENT_DATE("Asia/Taipei")
        AND ec_id = {ecid}
    GROUP BY
        1),
    GAID AS (
    SELECT
        user.ga,
        permanent
    FROM
        `tagtoo-tracking.event_prod.tagtoo_event` TE
    JOIN
        YesterdayUser
    USING
        (permanent)
    WHERE
        DATE(event_time, "Asia/Taipei") >= DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 30 DAY)
        AND DATE(event_time, "Asia/Taipei") < CURRENT_DATE("Asia/Taipei")
        AND ec_id = {ecid} )
    SELECT
    DISTINCT permanent,
    GAID.ga,
    CASE
        WHEN ( add_to_cart_count > 0 ) THEN 'tm:c_9999_{ecid}_r_005'
        WHEN ( view_item_count > 2 )
    AND ( add_to_cart_count = 0 ) THEN 'tm:c_9999_{ecid}_r_006'
        WHEN ( view_item_count <= 2 ) AND ( add_to_cart_count = 0 ) AND (view_item_count > 0) THEN 'tm:c_9999_{ecid}_r_007'
        WHEN ( view_item_count = 0 )
    AND ( add_to_cart_count = 0 ) THEN 'tm:c_9999_{ecid}_r_008'
        ELSE 'rule_Other'
    END
        AS segment_id
    FROM
        YesterdayUserEventCount
    JOIN
        GAID
    USING
        (permanent)
    WHERE
        GAID.ga IS NOT NULL
    """

def make_napl_query(ecid: str):
    """
    更動說明
    - 原本的 NAPL 使用者並不包含所有全站的使用者
        - 之前用 group_id 來聚合使用者事件數量，但在 join UU 的表時，一定得在 UU 表中找到對應的 permanent 才會回傳回來，但 UU 那張表不會有所有 permanent （之前做的分析大概有50% permanent 不在 UU 表中，因為這些人不需要被 group）
        - 改成有 gropu_id 就用 group_id, 沒有的話則用 permanent
    - GAID 只發送當天的
        - 另外原本的結果只包含前一天有進電商網站的 gaid ，沒有包含站外的 gaid，FB 那邊做法是會把 Tagtoo Event 過去一段時間的所有 user entity 都發送過去
        - 改成找 Tagtoo Event 過去30天內該使用者在站內的 gaid
    - GroupBy Key
        - 在計算使用者事件數量，是以 group_id, gaid 當作維度來聚合事件數量，可能會導致同一個 group_id 因為擁有多個 gaid ，所以事件數量被分開計算（這一點不影響 DCIU 總人數，但會影響到比例分佈）
        - 改成以 group_id 當作維度聚合, 計算完事件數量後再跟 gaid 合併
    """
    return f"""
    WITH
    -- 過濾前一天的訪客
    YesterdayUser AS (
    SELECT
        DISTINCT permanent
    FROM
        `tagtoo-tracking.event_prod.tagtoo_event`
    WHERE
        DATE(event_time, "Asia/Taipei") = DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 1 DAY)
        AND ec_id = {ecid} ),
    -- 合併計算事件數量並過濾訪客資料
    EventCounts AS (
    SELECT
        CASE
        WHEN UU.group_id IS NULL THEN TE.permanent
        ELSE UU.group_id
    END
        AS permanent,
        COUNT(*) AS purchase_count,
        MAX(CASE
            WHEN DATE(event_time, "Asia/Taipei") >= DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 90 DAY) THEN 1
            ELSE 0
        END
        ) AS recent_purchase
    FROM
        `tagtoo-tracking.event_prod.tagtoo_event` TE
    LEFT JOIN
        `tagtoo-tracking.event_prod.user_unify_group_permanent` UU
    USING
        (permanent)
    JOIN
        YesterdayUser
    USING
        (permanent)
    WHERE
        DATE(event_time, "Asia/Taipei") >= DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 365 DAY)
        AND DATE(event_time, "Asia/Taipei") < CURRENT_DATE("Asia/Taipei")
        AND ec_id = {ecid}
        AND event.name = "purchase"
    GROUP BY
        permanent ),
    -- 過濾 GAID 資料
    GAID AS (
    SELECT
        user.ga,
        permanent
    FROM
        `tagtoo-tracking.event_prod.tagtoo_event` TE
    JOIN
        YesterdayUser
    USING
        (permanent)
    WHERE
        DATE(event_time, "Asia/Taipei") >= DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 30 DAY)
        AND DATE(event_time, "Asia/Taipei") < CURRENT_DATE("Asia/Taipei")
        AND ec_id = {ecid})
    SELECT
    DISTINCT permanent,
    GAID.ga,
    CASE
        WHEN ( EC.purchase_count = 1 ) AND ( EC.recent_purchase = 1 ) THEN 'tm:c_9999_{ecid}_r_001'
        WHEN ( EC.purchase_count = 1 )
    AND ( EC.recent_purchase = 0 ) THEN 'tm:c_9999_{ecid}_r_004'
        WHEN ( EC.purchase_count > 1 ) AND ( EC.recent_purchase = 1 ) THEN 'tm:c_9999_{ecid}_r_002'
        WHEN ( EC.purchase_count > 1 )
    AND ( EC.recent_purchase = 0 ) THEN 'tm:c_9999_{ecid}_r_003'
        ELSE 'rule_Other'
    END
        AS segment_id
    FROM
        EventCounts EC
    JOIN
        GAID
    USING
        (permanent)
    WHERE
        GAID.ga IS NOT NULL
    """

def check_gcs_object(credentials, object_path):
    # 解析物件路徑，獲取 bucket 和物件名稱
    bucket_name, object_name = object_path[5:].split("/", 1)
    # 初始化 GCS 客戶端
    client = storage.Client(credentials=credentials)
    # 獲取指定的 bucket
    bucket = client.bucket(bucket_name)
    # 檢查物件是否存在
    blob = bucket.blob(object_name)
    return blob.exists()