import functions_framework
import pandas as pd
from dask import dataframe as dd
import numpy as np
from google.cloud import bigquery
from sklearn.preprocessing import StandardScaler
import faiss
from datetime import datetime, timedelta
import pytz
import logging
from google.oauth2 import service_account
from typing import Tuple, List, Set, Dict

def query_from_bigquery(sql: str, credentials) -> pd.DataFrame:
    client = bigquery.Client(credentials=credentials)
    job_config = bigquery.QueryJobConfig()
    # Start the query, passing in the extra configuration.
    query_job = client.query(sql, job_config=job_config)  # Make an API request.
    df = query_job.result().to_dataframe()  # Wait for the job to complete.
    logging.info("Query results loaded to RAM.")
    return df

def make_date_arguments(current_datetime: datetime) -> dict:
    DATE_FORMAT = "%Y-%m-%d"
    return {
        'start_date': (current_datetime - timedelta(days=31)).strftime(DATE_FORMAT),
        'end_date': (current_datetime - timedelta(days=1)).strftime(DATE_FORMAT)
    }

def load_seed_lookalike_data(feature_path: str, seed_id: set) -> Tuple[pd.DataFrame, pd.DataFrame]:
    df = dd.read_parquet(feature_path, assume_missing=True).compute()
    df.reset_index(drop=True, inplace=True)
    feature_columns = list(df.drop(['count_view_item', 'count_view_item_list', 'count_purchase', 'count_view_cart'], axis=1).columns)
    product_category_columns = [col for col in feature_columns if col.startswith('count_view') or col.startswith('count_purchase')]
    df[product_category_columns] = df[product_category_columns].fillna(0)
    df_seed = df[df['group_id'].isin(seed_id) & df[product_category_columns].sum(axis=1) > 0]
    df_lookalike = df[~df['group_id'].isin(seed_id) & df[product_category_columns].sum(axis=1) > 0]
    return df_seed, df_lookalike, product_category_columns

def preprocess_data(vectors: np.ndarray) -> np.ndarray:
    scaler = StandardScaler()
    return scaler.fit_transform(vectors)

def l2_normalize(vectors: np.ndarray) -> np.ndarray:
    norms = np.linalg.norm(vectors, axis=1, keepdims=True)
    return vectors / norms

def find_lookalike_users(seed_data: np.ndarray, lookalike_data: np.ndarray, threshold: float) -> np.ndarray:
    dim = seed_data.shape[1]
    index = faiss.IndexFlatIP(dim)
    index.add(lookalike_data.astype(np.float32))

    k = 1
    unique_index_count = 0
    lookalike_threshold = int(seed_data.shape[0] * threshold)

    while unique_index_count < lookalike_threshold:
        _, I = index.search(seed_data.astype(np.float32), k)
        unique_index_count = len(np.unique(I))
        k = k*2
    logging.info(f"Final k:, {k}, || unique index count: {unique_index_count}")

    return np.unique(I)

@functions_framework.http
def main(request):
    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    request_json = request.get_json(silent=True)
    request_args = request.args

    if request_json and 'seed_table_id' in request_json:
        seed_table_id = request_json['seed_table_id']
    elif request_args and 'name' in request_args:
        seed_table_id = request_args['seed_table_id']
    else:
        raise AttributeError("No seed table id found in URL.")

    if request_json and 'lookalike_threshold' in request_json:
        threshold = float(request_json['lookalike_threshold'])
    elif request_args and 'name' in request_args:
        threshold = float(request_args['lookalike_threshold'])
    else:
        threshold = 1

    logging.basicConfig(level=logging.INFO)
    # verify account
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    current_date = datetime.now(pytz.timezone('Asia/Taipei')).strftime('%Y%m%d')
    today = datetime.now(pytz.timezone('Asia/Taipei'))
    date_dict = make_date_arguments(today)
    feature_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature/{date_dict['start_date']}_{date_dict['end_date']}/*.parquet"

    sql = f"""
    SELECT
        DISTINCT
        CASE
            WHEN group_id IS NULL THEN CTA.permanent
        ELSE
            group_id
        END
        AS group_id,
        segment_id
    FROM
        `tagtoo-ml-workflow.tagtoo_export_results.{seed_table_id}_{current_date}` CTA
    LEFT JOIN
        `tagtoo-tracking.event_prod.user_unify_group_permanent` UU
    ON
        CTA.permanent = UU.permanent
    """

    df_pred = query_from_bigquery(sql, credentials)
    seed_id = df_pred.group_id

    df_seed, df_lookalike, product_category_columns = load_seed_lookalike_data(feature_path, seed_id)
    logging.info(f"Seed pool count {len(df_seed)}  ||  Lookalike pool count {len(df_lookalike)}")

    # 只用商品第一層種類去擴大受眾
    scaled_ar_seed = preprocess_data(df_seed[product_category_columns].to_numpy())
    scaled_ar_lookalike = preprocess_data(df_lookalike[product_category_columns].to_numpy())

    scaled_ar_seed_normalized = l2_normalize(scaled_ar_seed)
    scaled_ar_lookalike_normalized = l2_normalize(scaled_ar_lookalike)

    lookalike_indices = find_lookalike_users(scaled_ar_seed_normalized, scaled_ar_lookalike_normalized, threshold)
    lookalike_group_ids = df_lookalike.iloc[lookalike_indices]['group_id'].unique()

    df_lookalike_result = pd.DataFrame(data=lookalike_group_ids, columns=['group_id'])
    df_lookalike_result['segment_id'] = df_pred['segment_id'].unique()[0]
    df_result = pd.concat([df_pred, df_lookalike_result])

    logging.info(f"Seed TA count {len(df_pred)} || Lookalike result count {len(df_result)}")

    df_result.to_gbq(
        destination_table=f'tagtoo_export_results.{seed_table_id}_lookalike_{current_date}',
        project_id='tagtoo-ml-workflow',
        if_exists='replace'
    )

    return 'Success', 200
