import argparse
import logging
from datetime import datetime
from pytz import timezone
import google_storage
from publisher import PublisherClient
from google.oauth2 import service_account

def main(request):
    # 這個註解是為了讓 cloudbuild 重新部署成 cloud run gen2 的版本
    ec_id_for_mapping_pixal = '8888888'
    DATE_FORMAT = '%Y%m%d'
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    # verify account
    credentials = service_account.Credentials.from_service_account_file(key_path)
    project_id = credentials.project_id
    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    current_date = datetime.now(timezone('Asia/Taipei'))
    target_date = datetime.strftime(current_date, DATE_FORMAT)
    data = 'gs://tagtoo-ml-workflow/topic10/Audience_labels/{}/************.avro'.format(target_date)
    print("File gcs path:", data)
    prefix = data[24:-18]
    bucket_name = data[5:].split('/')[0]
    data_path_list = GSIO.get_wildcard_file_names(bucket_name, prefix)
    data_path_list = [f'gs://{bucket_name}/' + file for file in data_path_list]

    with PublisherClient(project_id='tagtoo-tracking', topic_name='lta-prod', credentials=credentials) as publisher:
        for file in data_path_list:
                publisher.add_message(
                    '',
                    file_name=file,
                    version='v1',
                    ec_id=ec_id_for_mapping_pixal,
                )
            
    print('Job complete.')

    return 'Success', 200

if __name__ == '__main__':
    main()