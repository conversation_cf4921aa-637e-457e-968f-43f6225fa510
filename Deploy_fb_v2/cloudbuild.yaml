steps:
  - name: "gcr.io/google.com/cloudsdktool/cloud-sdk"
    args:
      - "gcloud"
      - "functions"
      - "deploy"
      - "Deploy_fb_v2"  # Replace it. Must be same with the directory name.
      - "--source=."

      - "--entry-point=main"
      - "--region=asia-east1"
      - "--trigger-http"
      - "--runtime=python310"
      - "--memory=256MiB"
      # - "--cpu=1"  # Only valid when --gen2 and --memory=MEMORY are specified.  Examples: .5, 2, 2.0, 2000m.
      - "--timeout=60s"  # default 60s
      - "--min-instances=0"   # default 0
      - "--max-instances=3000" # default 100. 0 means no limit.

      - "--gen2"  # Comment out this line and --concurrency for deploying gen1
      # - "--concurrency=1"  # Only applicable when the --gen2 flag is provided.

      # - "--allow-unauthenticated"  # Will only apply to the first deployment. Comment out this line for non-public access.

      # `gcloud functions deploy --help`
      # or <https://cloud.google.com/sdk/gcloud/reference/builds/submit>
      # for more info
options:
    substitutionOption: 'ALLOW_LOOSE'
