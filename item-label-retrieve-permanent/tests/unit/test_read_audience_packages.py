import pytest
import pandas as pd
from unittest.mock import MagicMock, patch
from googleapiclient.errors import HttpError
from googleapiclient.http import HttpMockSequence

# 假設上面的函數位於 utils.py 文件中
from utils import read_audience_packages

# 模擬 HttpError 的輔助函數
def create_http_error(status, reason):
    response = MagicMock()
    response.status = status
    response.reason = reason
    return HttpError(response, b"Error content")

class TestReadAudiencePackages:
    
    def setup_method(self):
        """每個測試方法執行前的設置"""
        self.mock_credentials = MagicMock()
        self.page_name = "prod"
        
        # 模擬的成功回應資料
        self.mock_values = [
            ["測試名稱1", "描述1", "路徑1", "來源1", "ID1", "段落ID1", "關鍵字1", "關鍵字A1", "用戶ID1"],
            ["測試名稱2", "描述2", "路徑2", "來源2", "ID2", "段落ID2", "關鍵字2", "關鍵字A2", "用戶ID2"]
        ]
        
        self.expected_columns = ['name', 'description', 'path', 'item_source', 'item_source_id', 
                                'segment_id', 'or_keywords', 'and_keywords', 'user_source_id']
    
    @patch('utils.build')
    def test_successful_api_call(self, mock_build):
        """測試 API 呼叫成功的情況"""
        # 設置模擬對象
        mock_service = MagicMock()
        mock_sheet = MagicMock()
        mock_values = MagicMock()
        
        mock_build.return_value = mock_service
        mock_service.spreadsheets.return_value = mock_sheet
        mock_sheet.values.return_value = mock_values
        mock_values.get.return_value = mock_values
        mock_values.execute.return_value = {'values': self.mock_values}
        
        # 執行函數
        df = read_audience_packages(self.mock_credentials, self.page_name)
        
        # 驗證結果
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 2
        assert list(df.columns) == self.expected_columns
        assert df.iloc[0, 0] == "測試名稱1"
        assert df.iloc[1, 0] == "測試名稱2"
        
        # 驗證正確的 API 調用
        mock_build.assert_called_once_with('sheets', 'v4', credentials=self.mock_credentials)
        mock_values.get.assert_called_once_with(
            spreadsheetId='1VZxkSc6XsPSKfFPP8M9V8IyXUh4GxGpc6lq63h1Veco', 
            range='prod!A2:I'
        )
    
    @patch('utils.build')
    def test_empty_response(self, mock_build):
        """測試 API 返回空資料的情況"""
        # 設置模擬對象
        mock_service = MagicMock()
        mock_sheet = MagicMock()
        mock_values = MagicMock()
        
        mock_build.return_value = mock_service
        mock_service.spreadsheets.return_value = mock_sheet
        mock_sheet.values.return_value = mock_values
        mock_values.get.return_value = mock_values
        mock_values.execute.return_value = {'values': []}
        
        # 執行函數
        df = read_audience_packages(self.mock_credentials, self.page_name)
        
        # 驗證結果
        assert isinstance(df, pd.DataFrame)
        assert df.empty
        
    @patch('utils.build')
    @patch('time.sleep', return_value=None)  # 模擬 sleep 以加速測試
    def test_retry_on_503_error(self, mock_sleep, mock_build):
        """測試遇到 503 錯誤時的重試邏輯"""
        # 設置模擬對象
        mock_service = MagicMock()
        mock_sheet = MagicMock()
        mock_values = MagicMock()
        
        mock_build.return_value = mock_service
        mock_service.spreadsheets.return_value = mock_sheet
        mock_sheet.values.return_value = mock_values
        
        # 第一次返回 503 錯誤，第二次成功
        mock_values.get.return_value = mock_values
        mock_values.execute.side_effect = [
            create_http_error(503, "Service Unavailable"),
            {'values': self.mock_values}
        ]
        
        # 執行函數
        df = read_audience_packages(self.mock_credentials, self.page_name)
        
        # 驗證結果
        assert isinstance(df, pd.DataFrame)
        assert len(df) == 2
        assert mock_values.execute.call_count == 2
        assert mock_sleep.call_count == 1
    
    @patch('utils.build')
    @patch('time.sleep', return_value=None)  # 模擬 sleep 以加速測試
    def test_max_retries_exceeded(self, mock_sleep, mock_build):
        """測試超過最大重試次數的情況"""
        # 設置模擬對象
        mock_service = MagicMock()
        mock_sheet = MagicMock()
        mock_values = MagicMock()
        
        mock_build.return_value = mock_service
        mock_service.spreadsheets.return_value = mock_sheet
        mock_sheet.values.return_value = mock_values
        
        # 所有調用都返回 503 錯誤
        http_error = create_http_error(503, "Service Unavailable")
        mock_values.get.return_value = mock_values
        mock_values.execute.side_effect = [http_error] * 5  # 使用較小的重試次數以加速測試
        
        # 執行函數，預期會拋出異常
        with pytest.raises(Exception) as excinfo:
            read_audience_packages(self.mock_credentials, self.page_name, max_retries=5)
        
        # 驗證結果
        assert "嘗試 5 次後仍無法從 Google Sheets 獲取資料" in str(excinfo.value)
        assert mock_values.execute.call_count == 5
        assert mock_sleep.call_count == 5
    
    @patch('utils.build')
    def test_non_retryable_error(self, mock_build):
        """測試不可重試的錯誤（如 400 錯誤）"""
        # 設置模擬對象
        mock_service = MagicMock()
        mock_sheet = MagicMock()
        mock_values = MagicMock()
        
        mock_build.return_value = mock_service
        mock_service.spreadsheets.return_value = mock_sheet
        mock_sheet.values.return_value = mock_values
        
        # 返回 400 錯誤
        http_error = create_http_error(400, "Bad Request")
        mock_values.get.return_value = mock_values
        mock_values.execute.side_effect = http_error
        
        # 執行函數，預期會直接拋出 HttpError
        with pytest.raises(HttpError):
            read_audience_packages(self.mock_credentials, self.page_name)
        
        # 驗證結果
        assert mock_values.execute.call_count == 1  # 只調用一次，不重試