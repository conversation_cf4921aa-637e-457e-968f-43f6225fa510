from dask import dataframe as dd
import pandas as pd
from utils import *
import psutil
import os
import logging
import concurrent.futures
import time
from datetime import datetime
from pytz import timezone


def load_data_with_dask(label_path: str, user_item_path: str) -> tuple:
    """
    使用 dask 統一讀取所需的資料檔案
    
    Args:
        label_path (str): 標籤資料的路徑
        user_item_path (str): 使用者商品資料的路徑
        segment_path (str): segment 資料的路徑
        
    Returns:
        tuple: (label_df, user_item_df, segment_df)
    """
    # 使用 dask 讀取 parquet 檔案
    label_df = dd.read_parquet(label_path, assume_missing=True).compute()
    user_item_df = dd.read_parquet(user_item_path, assume_missing=True).compute()
    
    return label_df, user_item_df

# 使用範例：
# label_df, user_item_df, new_segment_df = load_data_with_dask(
#     LABEL_GCS_PATH,
#     USER_ITEM_GCS_PATH,
#     new_segment_path
# )

# 使用函數式處理標籤欄位
def extract_label_fields(df: pd.DataFrame, columns: list) -> pd.DataFrame:
    """批量處理標籤欄位"""
    for field in columns:
        df[field] = df['structured_labels'].apply(
            lambda x: extract_all_label_fields(x)[field]
        )
    return df

def process_merged_data(user_df: pd.DataFrame, label_df: pd.DataFrame) -> pd.DataFrame:
    """處理合併後的資料"""
    # 只選取需要的欄位進行合併
    merged = (user_df
             .merge(label_df[['name', 'label_str']], on='name', how='inner')
             .assign(is_media=lambda x: x['ec_id'].apply(check_media_or_not)))
    
    return merged

def create_source_mappings(merged_df: pd.DataFrame) -> dict:
    """建立資料來源映射"""
    mappings = {}
    
    # 分別處理 media 和非 media 的資料
    media_sources = merged_df.query('is_media == 1')
    non_media_sources = merged_df.query('is_media == 0')
    
    # 將 media 和非 media 的資料都按照 ec_id 分組
    mappings.update({
        ecid: group for ecid, group in media_sources.groupby('ec_id')
    })
    
    mappings.update({
        ecid: group for ecid, group in non_media_sources.groupby('ec_id')
    })
    
    # 保留原本 9999 的映射，包含所有非 media 的資料
    mappings[9999] = non_media_sources
    
    return mappings

# ** --- 核心處理函式 --- **
def create_segment_permanent_mapping(row, item_label_map, pids_path_prefix):
    """建立單一 segment 到 permanent 的映射，先篩選 and_keywords 再篩選 or_keywords"""
    item_source_df = item_label_map[row['item_source_id']]
    filtered_df = item_source_df.copy()
    
    current_date = datetime.now(timezone('Asia/Taipei')).strftime('%Y%m%d')
    user_source_id = row['user_source_id']

    # 如果有 user_source_id，先篩選必須包含的 permanent，減少資料量
    if row['user_source_id'] is not None and row['user_source_id'] > 0:
        pids_path = pids_path_prefix + f"/{current_date}/{user_source_id}.parquet.snappy"
        user_source_permanent = dd.read_parquet(pids_path, assume_missing=True).compute()
        logging.info({"message": f"{user_source_id} 的完整 permanent IDs 已匯入", "pids_path": pids_path})
        # // user_source_permanent = set(item_label_map[row['user_source_id']]['permanent'])
        filtered_df = filtered_df[filtered_df['permanent'].isin(user_source_permanent['permanent'])]

    # 如果有 and_keywords，先篩選必須包含的關鍵字
    if isinstance(row['and_keywords'], list) and len(row['and_keywords']) > 0:
        for keyword in row['and_keywords']:
            filtered_df = filtered_df[filtered_df['label_str'].str.contains(keyword, regex=True)]

    # 再篩選符合任一 or_keywords 的資料
    if isinstance(row['or_keywords'], list) and len(row['or_keywords']) > 0:
        or_pattern = '|'.join(map(str, row['or_keywords']))  # 確保所有元素都是字串
        filtered_df = filtered_df[filtered_df['label_str'].str.contains(or_pattern, regex=True)]

    # 處理 and_item_name
    if isinstance(row['and_item_name'], list) and len(row['and_item_name']) > 0:
        for item_name in row['and_item_name']:
            filtered_df = filtered_df[filtered_df['name'].str.contains(item_name, regex=True)]

    # 處理 or_item_name
    if isinstance(row['or_item_name'], list) and len(row['or_item_name']) > 0:
        or_item_pattern = '|'.join(map(str, row['or_item_name']))
        filtered_df = filtered_df[filtered_df['name'].str.contains(or_item_pattern, regex=True)]

    permanents = filtered_df['permanent'].unique().tolist()
    return permanents if permanents else []

def concat_label_fields(row: pd.Series) -> str:
    """標籤欄位串接"""
    return ' '.join([str(val) for val in row if isinstance(val, str)])

def process_single_segment(row_tuple, item_label_map, pids_path_prefix=None, total_rows=None):
    """處理單一 segment 並回傳 segment_id 和對應的 permanents
    
    Args:
        row_tuple: 包含 (idx, row) 的元組，來自 DataFrame.iterrows()
        item_label_map: 映射表，用於查找 permanent IDs
        total_rows: 資料總列數，用於日誌記錄
        
    Returns:
        tuple: (segment_id, permanents) 或 None (如果沒有符合的 permanents)
    """
    idx, row = row_tuple
    if idx % 10 == 0 and total_rows:
        logging.info(f"處理進度: {idx}/{total_rows}")
    permanents = create_segment_permanent_mapping(row, item_label_map, pids_path_prefix)
    if permanents:  # 只保留有對應到 permanent 的項目
        return row['segment_id'], permanents
    return None

# def process_batch_segments(batch_df, item_label_map, pids_path_prefix):
#     """批次處理多個 segments
    
#     Args:
#         batch_df (pd.DataFrame): 要處理的 segments 批次
#         item_label_map (dict): 映射表，用於查找 permanent IDs
        
#     Returns:
#         dict: {segment_id: permanents} 的映射字典
#     """
#     logging.info("開始批次處理 segments...")
#     logging.info({"segment_ids": batch_df['segment_id'].tolist()})
    
#     batch_results = {}
#     for idx, row in batch_df.iterrows():
#         permanents = create_segment_permanent_mapping(row, item_label_map, pids_path_prefix)
#         if permanents:  # 只保留有對應到 permanent 的項目
#             batch_results[row['segment_id']] = permanents
#     return batch_results

def process_batch_segments(batch_df, item_label_map, pids_path_prefix):

    batch_results = []  # 使用列表代替字典
    for _, row in batch_df.iterrows():
        try:
            logging.info(f"[批次處理迴圈內] 開始處理 segment_id - {row['segment_id']}")
            permanents = create_segment_permanent_mapping(row, item_label_map, pids_path_prefix)
            if permanents:
                batch_results.append((row['segment_id'], permanents))  # 儲存元組
                logging.info(f"[批次處理迴圈內] 結果 - {row['segment_id']} 共找到 {len(permanents)} 個 pids")
        except Exception as e:
            logging.error(f"[批次處理迴圈內] segment_id 處理失敗 - {row['segment_id']}: {str(e)}")
            raise e

    return batch_results

def preprocess_audience_packages(df: pd.DataFrame, item_label_map: dict) -> pd.DataFrame:
    """
    預處理受眾包資料，包括關鍵字分割和資料類型轉換
    
    Args:
        df (pd.DataFrame): 原始受眾包資料框
        
    Returns:
        pd.DataFrame: 處理後的資料框
        
    Raises:
        TypeError: 當 or_keywords 或 and_keywords 欄位包含非字串且非 NaN 的值時，且不是列表
    """
    # 建立副本避免修改原始資料
    processed_df = df.copy()
    
    # 添加調試信息：檢查 user_source_id 欄位中的問題值
    logging.info(f"處理前 DataFrame 形狀: {processed_df.shape}")
    
    # 檢查並記錄空字串或問題值
    problematic_rows = processed_df[processed_df['user_source_id'] == '']
    # if not problematic_rows.empty:
    #     logging.warning(f"發現 {len(problematic_rows)} 行含有空字串的 user_source_id")
    #     for idx, row in problematic_rows.iterrows():
    #         logging.warning(f"問題行 {idx}: {row.to_dict()}")
    
    # 轉換資料類型
    processed_df['item_source_id'] = processed_df['item_source_id'].astype(int)
    
    # 處理空值並轉換 user_source_id
    processed_df = processed_df.copy()  # 創建副本
    processed_df['user_source_id'] = processed_df['user_source_id'].fillna(0)
    # 增加這一行來處理空字串
    processed_df['user_source_id'] = processed_df['user_source_id'].replace('', 0)
    
    try:
        processed_df['user_source_id'] = processed_df['user_source_id'].astype(int)
    except ValueError as e:
        # 如果仍然出錯，記錄更詳細的信息
        logging.error(f"轉換 user_source_id 時出錯: {e}")
        # 找出無法轉換的值
        for idx, val in processed_df['user_source_id'].items():
            try:
                int(val)
            except (ValueError, TypeError):
                logging.error(f"行 {idx} 的 user_source_id 值 '{val}' 無法轉換為整數")
        # 重新拋出異常
        raise

    # 篩選 audience_packages_df 中的 item_source_id 是否存在於 item_label_map
    # 因為我們把 user_item parquet 拆成不同檔案，所以需要篩選
    valid_keys = list(item_label_map.keys())
    valid_keys = [int(key) for key in valid_keys]
    # 先轉換成 int，否則會篩選不到
    # //processed_df['item_source_id'] = processed_df['item_source_id'].astype(int)
    # ? 會不會有存在 audiecnce_packages_df 但是不存在 item_label_map 的情況 ?
    # // 可能需要改成 -> 若 `user_source_id` 有值，則需要檢查是否存在在 item_label_map，不在的話要過濾掉
    # `user_source_id` 的完整 permanent IDs 已經另外處理（另外儲存在 GCS 中）
    # 只需要處理這個批次檔案中有的 user ec 的 event 的 segment_id 就好
    processed_df = processed_df[
        processed_df['item_source_id'].isin(valid_keys)
    ]

    # 檢查並處理 or_keywords, and_keywords, or_item_name, and_item_name
    for field in ['or_keywords', 'and_keywords', 'or_item_name', 'and_item_name']:
        # 為DataFrame的指定欄位(field)處理列表類型數據
        processed_df[field] = processed_df[field].apply(
            # 定義一個lambda函數來處理每個單元格的值x
            lambda x: x if isinstance(x, list) else (  # 如果x已經是列表類型，則保持不變
                [] if pd.isna(x) else (  # 如果x是NaN值，則將其轉換為空列表
                    x.replace(', ', ',').split(',') if x else []  # 如果x不為空，先將", "替換為","，然後按","分割成列表；如果x為空(例如空字符串)，則返回空列表
                )
            )
        )
    
    return processed_df

def sequential_process_segments(audience_packages_df: pd.DataFrame, item_label_map: dict, pids_path_prefix=None) -> pd.DataFrame:
    """使用順序處理方式處理 segment 相關邏輯"""
    
    # 監控記憶體使用情況
    process = psutil.Process(os.getpid())
    mem_usage = process.memory_info().rss / (1024 * 1024)
    mem_percent = process.memory_percent()
    logging.info(f"[順序處理] 記憶體使用情況 (初始): {mem_usage:.2f} MB ({mem_percent:.2f}%)")
    
    # 監控記憶體使用情況
    mem_usage = process.memory_info().rss / (1024 * 1024)
    mem_percent = process.memory_percent()
    logging.info(f"[順序處理] 記憶體使用情況 (關鍵字表資料前處理後): {mem_usage:.2f} MB ({mem_percent:.2f}%)")

    # 建立 segment_id 到 permanent 的映射
    logging.info(f"[順序處理] 開始建立 segment_id 到 permanent 的映射，共 {len(audience_packages_df)} 個 segments")
    segment_id_to_permanent_map = {}
    
    # 開始計時
    start_time = time.time()
    
    # 使用傳統的順序處理方式
    for idx, row in audience_packages_df.iterrows():
        if idx % 10 == 0:
            logging.info(f"[順序處理] 已處理 {idx}/{len(audience_packages_df)} 個 segments")
            # 每處理10個segments監控一次記憶體
            mem_usage = process.memory_info().rss / (1024 * 1024)
            mem_percent = process.memory_percent()
            logging.info(f"[順序處理] 記憶體使用情況 (處理第 {idx} 個 segment): {mem_usage:.2f} MB ({mem_percent:.2f}%)")
        
        permanents = create_segment_permanent_mapping(row, item_label_map, pids_path_prefix)
        if permanents:  # 只保留有對應到 permanent 的項目
            segment_id_to_permanent_map[row['segment_id']] = permanents
    
    # 結束計時
    end_time = time.time()
    elapsed_time = end_time - start_time
    logging.info(f"[順序處理] 完成處理 {len(audience_packages_df)} 個 segments，耗時 {elapsed_time:.2f} 秒")
    
    # 監控記憶體使用情況
    mem_usage = process.memory_info().rss / (1024 * 1024)
    mem_percent = process.memory_percent()
    logging.info(f"[順序處理] 記憶體使用情況 (映射建立完成): {mem_usage:.2f} MB ({mem_percent:.2f}%)")
    logging.info(f"[順序處理] 完成建立 segment_id 到 permanent 的映射，共 {len(segment_id_to_permanent_map)} 個有效 segments")
    
    # 使用原本的方法處理資料
    result_df = (pd.Series(segment_id_to_permanent_map)
            .to_frame(name='permanent')
            .reset_index()
            .rename(columns={'index': 'segment_id'})
            .explode('permanent')
            .groupby('permanent', as_index=False)
            .agg({'segment_id': lambda x: ','.join(x)})) # 用半形逗號分格，跟 gid_mapping_export (bigquery scheduler query) 的格式一致
    
    # 監控記憶體使用情況
    mem_usage = process.memory_info().rss / (1024 * 1024)
    mem_percent = process.memory_percent()
    logging.info(f"[順序處理] 記憶體使用情況 (最終結果): {mem_usage:.2f} MB ({mem_percent:.2f}%)")
    
    return result_df, elapsed_time

def parallel_process_segments(audience_packages_df: pd.DataFrame, item_label_map: dict, pids_path_prefix=None) -> pd.DataFrame:
    """使用平行處理方式處理 segment 相關邏輯"""
    
    # 建立 segment_id 到 permanent 的映射，使用 concurrent.futures 進行平行處理
    # 使用 Manager 來創建一個進程安全的字典
    from multiprocessing import Manager
    manager = Manager()
    segment_id_to_permanent_map = manager.dict()
    logging.info(f"[平行處理] 開始平行建立 segment_id 到 permanent 的映射，共 {len(audience_packages_df)} 個 segments")

    # 監控記憶體使用情況
    process = psutil.Process(os.getpid())
    mem_usage = process.memory_info().rss / (1024 * 1024)
    mem_percent = process.memory_percent()
    
    # 決定最大工作線程數 (根據 CPU 核心數和資料量調整)
    max_workers = min(os.cpu_count() or 4, max(1, len(audience_packages_df) // 10))
    # max_workers = 4
    logging.info(f"[平行處理] 使用 {max_workers} 個工作線程進行平行處理")
    
    # 如果資料量太小，直接使用序列處理
    if len(audience_packages_df) < 20:
        logging.info(f"[平行處理] 資料量小於 20 行，切換至序列處理")
        return sequential_process_segments(audience_packages_df, item_label_map, pids_path_prefix)
    
    # 分割資料為批次，減少任務數量
    batch_size = max(10, len(audience_packages_df) // max_workers)
    batches = [audience_packages_df.iloc[i:i+batch_size].copy() for i in range(0, len(audience_packages_df), batch_size)]
    logging.info(f"[平行處理] 將 {len(audience_packages_df)} 個 segments 分為 {len(batches)} 個批次，每批次約 {batch_size} 筆資料")
    
    # 開始計時
    start_time = time.time()
    
    # # 使用 ProcessPoolExecutor 進行平行處理
    # with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
    #     # 提交批次處理任務
    #     future_to_batch = {
    #         executor.submit(process_batch_segments, batch, item_label_map, pids_path_prefix): i 
    #         for i, batch in enumerate(batches)
    #     }
        
    #     # 收集結果
    #     completed_batches = 0
    #     for future in concurrent.futures.as_completed(future_to_batch):
    #         completed_batches += 1
    #         batch_idx = future_to_batch[future]
            
    #         if completed_batches % max(1, len(batches) // 10) == 0 or completed_batches == len(batches):
    #             # 定期監控記憶體
    #             mem_usage = process.memory_info().rss / (1024 * 1024)
    #             mem_percent = process.memory_percent()
    #             logging.info(f"[平行處理] 記憶體使用情況 (已完成 {completed_batches}/{len(batches)} 個批次): {mem_usage:.2f} MB ({mem_percent:.2f}%)")
            
    #         try:
    #             batch_results = future.result()
    #             segment_id_to_permanent_map.update(batch_results)
    #         except Exception as e:
    #             logging.error(f"[平行處理] 批次 {batch_idx} 處理失敗: {str(e)}")
    #             # 詳細記錄當前批次的錯誤資訊
    #             for field in ['or_keywords', 'and_keywords']:
    #                 if field in batches[batch_idx].columns:
    #                     types = {str(type(val)) for val in batches[batch_idx][field]}
    #                     logging.error(f"[平行處理] 批次 {batch_idx} 的 {field} 欄位類型: {types}")

    with concurrent.futures.ProcessPoolExecutor(max_workers=max_workers) as executor:
        futures = []
        for i, batch in enumerate(batches):
            future = executor.submit(process_batch_segments, batch, item_label_map, pids_path_prefix)
            futures.append((future, i))
        
        # 使用列表收集所有結果
        all_results = []
        for future, batch_idx in futures:
            try:
                batch_results = future.result(timeout=30)  # 添加超時設置
                all_results.append(batch_results)
                logging.info(f"[平行處理] 批次 {batch_idx} 處理完成")
            except Exception as e:
                logging.error(f"[平行處理] 批次 {batch_idx} 處理失敗: {str(e)}")
        
        # 在所有處理完成後，一次性更新結果
        # for result in all_results:
        # logging.info(f"all result: {all_results}")
        for segment_id, permanents in all_results:
            # reseult: (segment_id, permanents)
            segment_id_to_permanent_map[segment_id] = permanents
    
    # 轉換回普通字典
    segment_id_to_permanent_map = dict(segment_id_to_permanent_map)
    
    # 結束計時
    end_time = time.time()
    elapsed_time = end_time - start_time
    logging.info(f"[平行處理] 完成處理 {len(audience_packages_df)} 個 segments (分 {len(batches)} 個批次)，耗時 {elapsed_time:.2f} 秒")
    
    # 監控記憶體使用情況
    mem_usage = process.memory_info().rss / (1024 * 1024)
    mem_percent = process.memory_percent()
    logging.info(f"[平行處理] 記憶體使用情況 (映射建立完成): {mem_usage:.2f} MB ({mem_percent:.2f}%)")
    logging.info(f"[平行處理] 完成建立 segment_id 到 permanent 的映射，共 {len(segment_id_to_permanent_map)} 個有效 segments")
    
    # 使用原本的方法處理資料
    result_df = (pd.Series(segment_id_to_permanent_map)
            .to_frame(name='permanent')
            .reset_index()
            .rename(columns={'index': 'segment_id'})
            .explode('permanent')
            .groupby('permanent', as_index=False)
            .agg({'segment_id': lambda x: ','.join(x)})) # 用半形逗號分格，跟 gid_mapping_export (bigquery scheduler query) 的格式一致
    
    # 監控記憶體使用情況
    mem_usage = process.memory_info().rss / (1024 * 1024)
    mem_percent = process.memory_percent()
    logging.info(f"[平行處理] 記憶體使用情況 (最終結果): {mem_usage:.2f} MB ({mem_percent:.2f}%)")
    
    return result_df, elapsed_time

def process_segments(audience_packages_df: pd.DataFrame, item_label_map: dict, pids_path_prefix: str, parallel=True) -> tuple:
    """
    處理 segment 相關邏輯的外層介面，可選擇使用平行或順序處理
    
    Args:
        audience_packages_df (pd.DataFrame): 受眾包資料框
        item_label_map (dict): 映射表，用於查找 permanent IDs
        parallel (bool): 是否使用平行處理，預設為 True
        
    Returns:
        tuple: (result_df, elapsed_time) 處理結果資料框和處理耗時
    """
    if parallel:
        return parallel_process_segments(audience_packages_df, item_label_map, pids_path_prefix)
    else:
        return sequential_process_segments(audience_packages_df, item_label_map, pids_path_prefix)