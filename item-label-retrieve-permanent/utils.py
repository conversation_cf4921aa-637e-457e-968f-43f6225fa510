from google.oauth2 import service_account
from google.cloud import bigquery
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
import pandas as pd
import time
import random
import logging
import json

def read_json(filepath: str):
    with open(filepath, mode='r') as f:
        return json.load(f)

def check_media_or_not(ecid):
    
	media_ecids = [
		2851, # -- Setn
		1347, # -- BusinessWeekly
		1604, # -- Ctee
		2625, # -- CMoney
		2913, # -- Babyhome
		3093, # -- MamiGuide
		3195, # -- Reurl
		3542 # -- VMFive
	]

	return ecid in media_ecids

def extract_all_label_fields(structured_labels):
    """
    從 structured_labels 中提取所有可能的欄位
    若欄位不存在則返回空列表或 None
    """
    # 取得主要區塊
    audience_profile = structured_labels.get('AudienceProfile', {})
    content_tags = structured_labels.get('ContentTags', {})
    
    fields = {
        # AudienceProfile 區塊
        'Interests': audience_profile.get('Interests', []),
        'OccupationalTraits': audience_profile.get('OccupationalTraits', []),
        'PersonalOpinions': audience_profile.get('PersonalOpinions', []),
        'BrowsingMotivations': audience_profile.get('BrowsingMotivations', []),
        'PotentialPainPoints': audience_profile.get('PotentialPainPoints', []),
        
        # ContentTags 區塊
        'Category': content_tags.get('Category', []),
        'Features': content_tags.get('Features', []),
        'Industry': content_tags.get('Industry', []),
        'MarketSegmentation': content_tags.get('MarketSegmentation', []),
        'PotentialIssues': content_tags.get('PotentialIssues', []),
        'Purpose': content_tags.get('Purpose', None),
        'Tone': content_tags.get('Tone', []),
        
        # 其他主要欄位
        'CoreDescription': structured_labels.get('CoreDescription', None),
        'Keywords': structured_labels.get('Keywords', [])
    }
    
    return fields

def concat_label_fields(df):
    """連接各個欄位的標籤，處理字典類型並記錄警告"""
    def process_list(lst, field_name):
        # 如果是字串（core description），則直接把字串包裝成 list
        if isinstance(lst, str):
            return [lst]
        # 如果是 list，則處理 list 中的每一個 item
        elif isinstance(lst, list):
            processed = []
            # 處理 list 中的每一個 item，如果是 dict，則取得 dict 中的值，如果是 str，則直接加入 processed，如果是其他類型，則記錄警告
            for item in lst:
                if isinstance(item, dict):
                    # 取得字典中的值
                    dict_values = [str(v) for v in item.values()]
                    processed.extend(dict_values)
                    # logging.info(f"Field '{field_name}' contains dict: {item}, extracted values: {dict_values}")
                elif isinstance(item, str):
                    processed.append(item)
                else:
                    logging.warning(f"Field '{field_name}' contains unexpected type: {type(item)}, value: {item}")
            return processed
        else:
            # 如果 lst 不是 list 或 str，則記錄警告
            logging.warning(f"Field '{field_name}' expected list or string but got {type(lst)}: {lst}")
            return []

    fields = [
        ', '.join(process_list(df['Category'], 'Category')),
        ', '.join(process_list(df['Industry'], 'Industry')),
        ', '.join(process_list(df['Interests'], 'Interests')),
        ', '.join(process_list(df['PotentialPainPoints'], 'PotentialPainPoints')),
        ', '.join(process_list(df['PotentialIssues'], 'PotentialIssues')),
        ', '.join(process_list(df['OccupationalTraits'], 'OccupationalTraits')),
        ', '.join(process_list(df['PersonalOpinions'], 'PersonalOpinions')),
        ', '.join(process_list(df['Keywords'], 'Keywords')),
        # ', '.join(process_list(df['CoreDescription'], 'CoreDescription'))
    ]
    return ', '.join(field for field in fields if field)

def get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH):
    # verify account
    google_credentials = service_account.Credentials.from_service_account_file(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    project_id = google_credentials.project_id
    return google_credentials, project_id

def concat_audience_profile_as_string(df):
	return ', '.join(df['Interests'] + df['OccupationalTraits'] + df['PersonalOpinions'])

def read_json(filepath: str):
    with open(filepath, mode='r') as f:
        return json.load(f)

def save_query_results(QUERY: str, credentials, temp_table_id: str, destination_uri: str):
    # google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    bq_client = bigquery.Client(credentials=credentials)
    
    # 解析 temp_table_id 以獲取項目和表格名稱
    # 格式: project.dataset.table
    parts = temp_table_id.split('.')
    project_id = parts[0]
    table_name = parts[2]
    
    # 創建一個臨時數據集在 asia-east1 區域
    import uuid
    import time
    temp_dataset_id = f"temp_dataset_{uuid.uuid4().hex[:8]}_{int(time.time())}"
    
    # 創建臨時數據集
    dataset = bigquery.Dataset(f"{project_id}.{temp_dataset_id}")
    dataset.location = "asia-east1"  # 明確設置位置
    dataset = bq_client.create_dataset(dataset, timeout=30)
    
    try:
        # 使用臨時數據集創建表格
        temp_table_id_new = f"{project_id}.{temp_dataset_id}.{table_name}"
        
        # 使用 CREATE OR REPLACE TABLE 來儲存查詢結果
        create_table_query = f"""
        CREATE OR REPLACE TABLE {temp_table_id_new} AS
        {QUERY}
        """
        
        job = bq_client.query(create_table_query)
        job.result()  # 等待查詢完成
        
        # 將結果匯出到 GCS
        table_ref = bq_client.dataset(temp_dataset_id, project=project_id).table(table_name)
        
        job_config = bigquery.ExtractJobConfig()
        job_config.destination_format = bigquery.DestinationFormat.PARQUET
        
        extract_job = bq_client.extract_table(
            table_ref,
            destination_uri,
            job_config=job_config
        )
        extract_job.result()
        
        logging.info(f"資料已匯入 {destination_uri}")
    
    finally:
        # 刪除臨時數據集及其內容
        bq_client.delete_dataset(
            dataset.reference, delete_contents=True, not_found_ok=True
        )

    return

def read_audience_packages(credentials, page_name: str, max_retries=5, initial_delay=1) -> pd.DataFrame:
    """讀取 Taxonomy 資料並返回 DataFrame，包含重試邏輯

    Args:
        credentials: Google API 憑證
        page_name: 工作表名稱
        max_retries: 最大重試次數，預設 5 次
        initial_delay: 初始延遲秒數，預設 1 秒
    
    Returns:
        DataFrame: 包含所有資料的 DataFrame，若無資料則返回空 DataFrame
        
    Raises:
        Exception: 重試耗盡仍無法獲取資料時拋出
    """
    spreadsheet_id = '1VZxkSc6XsPSKfFPP8M9V8IyXUh4GxGpc6lq63h1Veco'
    columns = ['name', 'description', 'path', 'item_source', 'item_source_id', 
               'segment_id', 'or_keywords', 'and_keywords', 'user_source_id', 'or_item_name', 'and_item_name']
    cell_range = 'A2:K'
    
    service = build('sheets', 'v4', credentials=credentials)
    sheet = service.spreadsheets()
    
    retry_count = 0
    
    while retry_count < max_retries:
        try:
            logging.info(f"嘗試從 Google Sheet 讀取 {page_name} 頁面數據...")
            result = sheet.values().get(
                spreadsheetId=spreadsheet_id, 
                range=f"{page_name}!{cell_range}"
            ).execute()
            
            values = result.get('values', [])
            
            if not values:
                logging.info("未找到資料。")
                return pd.DataFrame()
            
            # 補齊每列不足欄位（防止 ValueError）
            for i, row in enumerate(values):
                if len(row) < len(columns):
                    row.extend([''] * (len(columns) - len(row)))
            
            df = pd.DataFrame(values, columns=columns)
            logging.info(f"成功讀取 {len(values)} 行數據")
            return df
            
        except HttpError as error:
            retry_count += 1
            
            # 判斷是否為可重試的錯誤（服務不可用、伺服器錯誤或速率限制）
            if error.resp.status in [429, 500, 503, 502]:
                # 計算指數級退避時間（帶隨機抖動）
                delay = (initial_delay * (2 ** (retry_count - 1))) + (random.random())
                logging.warning(
                    f"Google Sheets API 暫時不可用 (狀態碼: {error.resp.status}). "
                    f"將在 {delay:.2f} 秒後重試... (嘗試 {retry_count}/{max_retries})"
                )
                time.sleep(delay)
            else:
                # 其他類型的 HTTP 錯誤，直接拋出
                logging.error(f"Google Sheets API 發生錯誤: {error}")
                raise
    
    # 重試次數耗盡
    error_msg = f"嘗試 {max_retries} 次後仍無法從 Google Sheets 獲取資料"
    logging.error(error_msg)
    raise Exception(error_msg)