{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-28 14:11:14,857 | INFO : 這是一個 info 層級的測試訊息\n", "2025-04-28 14:11:14,858 | WARNING : 這是一個 warning 層級的測試訊息\n", "2025-04-28 14:11:14,859 | ERROR : 這是一個 error 層級的測試訊息\n", "2025-04-28 14:11:14,860 | CRITICAL : 這是一個 critical 層級的測試訊息\n"]}], "source": ["import logging\n", "import google.cloud.logging\n", "from google_storage import GoogleStorageIO\n", "from config import GOO<PERSON>LE_APPLICATION_CREDENTIALS_PATH\n", "from datetime import datetime\n", "from pytz import timezone\n", "import json\n", "import pandas as pd\n", "from dask import dataframe as dd\n", "from make_query import LABEL_QUERY, USER_QUERY\n", "from utils import *\n", "from pipeline import *\n", "\n", "google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)\n", "GSIO = GoogleStorageIO(credentials=google_credentials)\n", "logging.info({\"message\": \"GSIO 已建立\", \"project_id\": project_id})\n", "\n", "import sys\n", "\n", "# 設定 logging 基本組態\n", "logging.basicConfig(\n", "    format='%(asctime)s | %(levelname)s : %(message)s',\n", "    level=logging.INFO,  # 設定顯示等級\n", "    stream=sys.stdout,   # 重要:指定輸出到 stdout（因為 Jupyter 預設使用 stderr,所以需要指定輸出到 stdout 才會顯示在 notebook cell 中）\n", "    force=True          # Python 3.8+ 可用:強制重設既有的 handler\n", ")\n", "\n", "# 建立 logger\n", "logger = logging.getLogger('notebook')\n", "\n", "# 測試 logging\n", "logger.info('這是一個 info 層級的測試訊息')\n", "logger.warning('這是一個 warning 層級的測試訊息')\n", "logger.error('這是一個 error 層級的測試訊息')\n", "logger.critical('這是一個 critical 層級的測試訊息')"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# from typing import Union, Optional\n", "\n", "# def save_user_permanents_to_gcs(\n", "#     ec_id: int,\n", "#     df: pd.<PERSON><PERSON><PERSON><PERSON>,\n", "#     gcs_base_path: str,\n", "#     storage_client: Optional[object] = None,\n", "#     *,\n", "#     timezone_name: str = 'Asia/Taipei'\n", "# ) -> Union[str, None]:\n", "#     \"\"\"將特定電商的用戶 permanent IDs 儲存至 Google Cloud Storage。\n", "\n", "#     Args:\n", "#         ec_id: 電商 ID\n", "#         df: 包含 permanent 欄位的 DataFrame\n", "#         gcs_base_path: GCS 基礎路徑\n", "#         storage_client: Google Storage IO 客戶端實例\n", "#         timezone_name: 時區名稱，預設為 'Asia/Taipei'\n", "\n", "#     Returns:\n", "#         str: 成功時返回 GCS URI\n", "#         None: 當輸入 DataFrame 為空時返回\n", "\n", "#     Raises:\n", "#         ValueError: 當必要參數未提供或格式錯誤時\n", "#         IOError: 當檔案操作失敗時\n", "#     \"\"\"\n", "#     if storage_client is None:\n", "#         raise ValueError(\"GoogleStorageIO is required\")\n", "    \n", "#     if df.empty:\n", "#         return None\n", "\n", "#     # 準備資料\n", "#     permanent_ids = (\n", "#         pd.DataFrame(df[df['ec_id'] == ec_id]['permanent']\n", "#                     .drop_duplicates()\n", "#                     .reset_index(drop=True))\n", "#     )\n", "#     logging.info(f\"{ec_id} 找到 {len(permanent_ids)} 個 permanent IDs\")\n", "    \n", "#     if permanent_ids.empty:\n", "#         logging.warning(f\"{ec_id} 沒有找到對應的 permanent IDs\")\n", "#         return None\n", "\n", "#     # 設定檔案路徑\n", "#     current_date = datetime.now(timezone(timezone_name)).strftime(\"%Y%m%d\")\n", "#     local_path = f'/tmp/{ec_id}.parquet'\n", "#     gcs_uri = f'{gcs_base_path}/{current_date}/{ec_id}.parquet.snappy'\n", "\n", "#     try:\n", "#         # 儲存到本地\n", "#         permanent_ids.to_parquet(\n", "#             local_path,\n", "#             engine='pyarrow',\n", "#             compression='snappy',\n", "#             index=False\n", "#         )\n", "\n", "#         # 上傳到 GCS\n", "#         storage_client.upload_file(localpath=local_path, gsuri=gcs_uri)\n", "        \n", "#         return gcs_uri\n", "\n", "#     except Exception as e:\n", "#         raise IOError(f\"Failed to save permanents for EC {ec_id}: {str(e)}\")\n", "    \n", "#     finally:\n", "#         # 清理暫存檔案\n", "#         if os.path.exists(local_path):\n", "#             os.remove(local_path)\n", "\n", "# # ** --- 建立各 ec_id 完整的 permanent IDs --- **\n", "# page_name = \"20250428\"\n", "# user_item_gcs_path = f\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/{current_date}/*.parquet\"\n", "\n", "# # 讀取 user_item 資料\n", "# # path = f\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/{current_date}/*.parquet\"\n", "# df = dd.read_parquet(user_item_gcs_path, engine=\"pyarrow\", assume_missing=True).compute()\n", "\n", "# # 讀取關鍵字大表\n", "# page_name = page_name or \"prod\"\n", "# audience_packages_df = read_audience_packages(google_credentials, page_name)\n", "\n", "# # 篩選出目前在關鍵字大表中的 `user_source_id`\n", "# target_user_source_ids = audience_packages_df['user_source_id'].dropna().unique().astype(int).tolist()\n", "# target_user_source_df = df[df['ec_id'].isin(target_user_source_ids)]\n", "\n", "# # 建立各 ec_id 完整的 permanent IDs\n", "# for ec_id in target_user_source_ids:\n", "#     gcs_uri = save_user_permanents_to_gcs(\n", "#         ec_id=ec_id,\n", "#         df=target_user_source_df,\n", "#         gcs_base_path=\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids\",\n", "#         storage_client=GSIO\n", "#     )\n", "#     logging.info({\"message\": \"ec_id 完整的 permanent IDs 已建立\", \"ec_id\": ec_id, \"gcs_uri\": gcs_uri})"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["# df[df['permanent'].isin(ec_pids['permanent'])]"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import pipeline\n", "from importlib import reload\n", "reload(pipeline)\n", "from pipeline import *"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [], "source": ["from config import PIDS_PATH_PREFIX"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-28 14:13:39,796 | INFO : {'message': '[000] 參數已匯入', 'current_date': '20250428'}\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-04-28 14:13:39,859 | INFO : {'message': '[000] GSIO 已建立', 'project_id': 'tagtoo-ml-workflow'}\n", "2025-04-28 14:13:46,570 | INFO : {'message': '[000] 資料載入完成', 'unique_users': 159764, 'labeled_articles': 399774}\n", "2025-04-28 14:14:30,859 | INFO : {'message': '[000] 標籤處理完成'}\n", "2025-04-28 14:14:31,352 | INFO : {'message': '[000] 資料合併與映射完成'}\n", "2025-04-28 14:14:31,356 | WARNING : file_cache is unavailable when using oauth2client >= 4.0.0 or google-auth\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/__init__.py\", line 36, in autodetect\n", "    from google.appengine.api import memcache\n", "ModuleNotFoundError: No module named 'google.appengine'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/file_cache.py\", line 33, in <module>\n", "    from oauth2client.contrib.locked_file import LockedFile\n", "ModuleNotFoundError: No module named 'oauth2client.contrib.locked_file'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/file_cache.py\", line 37, in <module>\n", "    from oauth2client.locked_file import LockedFile\n", "ModuleNotFoundError: No module named 'oauth2client.locked_file'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/__init__.py\", line 42, in autodetect\n", "    from . import file_cache\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/file_cache.py\", line 40, in <module>\n", "    raise ImportError(\n", "ImportError: file_cache is unavailable when using oauth2client >= 4.0.0 or google-auth\n", "2025-04-28 14:14:31,358 | INFO : URL being requested: GET https://www.googleapis.com/discovery/v1/apis/sheets/v4/rest\n", "2025-04-28 14:14:32,608 | INFO : URL being requested: GET https://sheets.googleapis.com/v4/spreadsheets/1VZxkSc6XsPSKfFPP8M9V8IyXUh4GxGpc6lq63h1Veco/values/20250428%21A2%3AI?alt=json\n", "2025-04-28 14:14:33,887 | INFO : [順序處理] 記憶體使用情況 (初始): 4178.83 MB (13.01%)\n", "2025-04-28 14:14:33,888 | INFO : [順序處理] 記憶體使用情況 (關鍵字表資料前處理後): 4178.83 MB (13.01%)\n", "2025-04-28 14:14:33,889 | INFO : [順序處理] 開始建立 segment_id 到 permanent 的映射，共 14 個 segments\n", "2025-04-28 14:14:33,890 | INFO : [順序處理] 已處理 0/14 個 segments\n", "2025-04-28 14:14:33,892 | INFO : [順序處理] 記憶體使用情況 (處理第 0 個 segment): 4178.83 MB (13.01%)\n", "2025-04-28 14:14:34,259 | INFO : {'message': '107 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/107.parquet.snappy'}\n", "2025-04-28 14:14:34,644 | INFO : {'message': '107 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/107.parquet.snappy'}\n", "2025-04-28 14:14:35,018 | INFO : {'message': '107 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/107.parquet.snappy'}\n", "2025-04-28 14:14:35,327 | INFO : {'message': '107 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/107.parquet.snappy'}\n", "2025-04-28 14:14:35,659 | INFO : {'message': '2808 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/2808.parquet.snappy'}\n", "2025-04-28 14:14:35,987 | INFO : {'message': '2808 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/2808.parquet.snappy'}\n", "2025-04-28 14:14:36,308 | INFO : {'message': '2808 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/2808.parquet.snappy'}\n", "2025-04-28 14:14:36,664 | INFO : {'message': '183 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/183.parquet.snappy'}\n", "2025-04-28 14:14:37,127 | INFO : {'message': '183 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/183.parquet.snappy'}\n", "2025-04-28 14:14:37,581 | INFO : {'message': '183 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/183.parquet.snappy'}\n", "2025-04-28 14:14:37,730 | INFO : [順序處理] 已處理 10/14 個 segments\n", "2025-04-28 14:14:37,732 | INFO : [順序處理] 記憶體使用情況 (處理第 10 個 segment): 4187.73 MB (13.04%)\n", "2025-04-28 14:14:38,020 | INFO : {'message': '183 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/183.parquet.snappy'}\n", "2025-04-28 14:14:38,464 | INFO : {'message': '1465 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/1465.parquet.snappy'}\n", "2025-04-28 14:14:38,902 | INFO : {'message': '1465 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/1465.parquet.snappy'}\n", "2025-04-28 14:14:39,218 | INFO : {'message': '1465 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/1465.parquet.snappy'}\n", "2025-04-28 14:14:39,266 | INFO : [順序處理] 完成處理 14 個 segments，耗時 5.38 秒\n", "2025-04-28 14:14:39,267 | INFO : [順序處理] 記憶體使用情況 (映射建立完成): 4181.79 MB (13.02%)\n", "2025-04-28 14:14:39,267 | INFO : [順序處理] 完成建立 segment_id 到 permanent 的映射，共 14 個有效 segments\n", "2025-04-28 14:14:39,310 | INFO : [順序處理] 記憶體使用情況 (最終結果): 4181.68 MB (13.02%)\n", "2025-04-28 14:14:39,311 | INFO : {'message': '[000] 使用者查找完畢', 'result_rows': 2590, 'process_time': 5.375857830047607}\n"]}], "source": ["# 創建虛擬參數\n", "params = {\n", "    'page_name': '20250428',\n", "    'upload_gate': '0',\n", "    'save_gate': '0',\n", "    'user_item_gcs_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/20250428/000000000000.parquet',\n", "    'log_idx': '000',\n", "    'parallel_gate': '0',\n", "}\n", "\n", "def validate_binary_param(params, param_name):\n", "    try:\n", "        params[param_name] = int(params[param_name])\n", "        if params[param_name] not in [0, 1]:\n", "            return f'{param_name} 必須為 0 或 1', 400\n", "        return None\n", "    except ValueError:\n", "        return f'{param_name} 必須為數字', 400\n", "\n", "# 檢查 upload_gate\n", "error = validate_binary_param(params, 'upload_gate')\n", "if error:\n", "    raise error\n", "\n", "# 檢查 save_gate\n", "error = validate_binary_param(params, 'save_gate')\n", "if error:\n", "    raise error\n", "\n", "# 檢查 parallel\n", "error = validate_binary_param(params, 'parallel_gate')\n", "if error:\n", "    raise error\n", "\n", "# 解構參數以供後續使用\n", "page_name = params.get('page_name')\n", "upload_gate = params.get('upload_gate')\n", "save_gate = params.get('save_gate')\n", "user_item_gcs_path = params.get('user_item_gcs_path')\n", "log_idx = params.get('log_idx')\n", "parallel_gate = params.get('parallel_gate')\n", "current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime(\"%Y%m%d\")\n", "\n", "# 商品標籤的資料是固定全部匯入\n", "# 使用者才是批次處理\n", "label_gcs_path = f\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/label_query_results/{current_date}/*.parquet\"\n", "\n", "logging.info({\"message\": f\"[{log_idx}] 參數已匯入\", \"current_date\": current_date})\n", "google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)\n", "GSIO = GoogleStorageIO(credentials=google_credentials)\n", "logging.info({\"message\": f\"[{log_idx}] GSIO 已建立\", \"project_id\": project_id})\n", "\n", "\"\"\"主要處理流程\"\"\"\n", "# 1. 載入資料\n", "label_df, user_item_df = load_data_with_dask(\n", "    label_gcs_path,\n", "    user_item_gcs_path,\n", ")\n", "logging.info({\n", "    \"message\": f\"[{log_idx}] 資料載入完成\",\n", "    \"unique_users\": len(user_item_df['permanent'].drop_duplicates()),\n", "    \"labeled_articles\": len(label_df),\n", "})\n", "\n", "# 2. 處理標籤\n", "# * 會根據不同資料來源媒合受眾包\n", "label_df['structured_labels'] = label_df['structured_labels_str'].apply(lambda x: json.loads(x))\n", "columns_to_extract = [\n", "    'Interests', 'OccupationalTraits', 'PersonalOpinions', \n", "    'BrowsingMotivations', 'PotentialPainPoints',\n", "    'Category', 'Features', 'Industry', 'MarketSegmentation',\n", "    'PotentialIssues', 'Purpose', 'Tone',\n", "    'CoreDescription', 'Keywords'\n", "]\n", "label_df = extract_label_fields(label_df, columns_to_extract)\n", "label_df = label_df.assign(label_str=lambda x: x.apply(concat_label_fields, axis=1)).query('label_str != \"\"')\n", "logging.info({\"message\": f\"[{log_idx}] 標籤處理完成\",})\n", "\n", "# 3. 合併與映射處理\n", "merged_df = process_merged_data(user_item_df, label_df)\n", "\n", "# 4. 篩選出個資料來源\n", "item_label_map = create_source_mappings(merged_df)\n", "logging.info({\"message\": f\"[{log_idx}] 資料合併與映射完成\",})\n", "\n", "# 5. 根據關鍵字大表中的所有 segments 與其關鍵字規則，篩選出符合條件的商品，並查找瀏覽過商品的 permanent\n", "page_name = page_name or \"prod\"\n", "audience_packages_df = read_audience_packages(google_credentials, page_name)\n", "# 預處理受眾包資料\n", "audience_packages_df = preprocess_audience_packages(audience_packages_df, item_label_map)\n", "\n", "result_df, process_time = process_segments(audience_packages_df, item_label_map, pids_path_prefix=PIDS_PATH_PREFIX, parallel=parallel_gate)\n", "logging.info({\"message\": f\"[{log_idx}] 使用者查找完畢\", \"result_rows\": len(result_df), \"process_time\": process_time})"]}, {"cell_type": "code", "execution_count": 16, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-28 14:10:54,496 | INFO : [順序處理] 記憶體使用情況 (初始): 4171.83 MB (12.99%)\n", "2025-04-28 14:10:54,498 | INFO : [順序處理] 記憶體使用情況 (關鍵字表資料前處理後): 4171.83 MB (12.99%)\n", "2025-04-28 14:10:54,499 | INFO : [順序處理] 開始建立 segment_id 到 permanent 的映射，共 14 個 segments\n", "2025-04-28 14:10:54,500 | INFO : [順序處理] 已處理 0/14 個 segments\n", "2025-04-28 14:10:54,502 | INFO : [順序處理] 記憶體使用情況 (處理第 0 個 segment): 4171.83 MB (12.99%)\n", "2025-04-28 14:10:54,970 | INFO : {'message': '107 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/107.parquet.snappy'}\n", "2025-04-28 14:10:55,294 | INFO : {'message': '107 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/107.parquet.snappy'}\n", "2025-04-28 14:10:55,628 | INFO : {'message': '107 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/107.parquet.snappy'}\n", "2025-04-28 14:10:55,969 | INFO : {'message': '107 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/107.parquet.snappy'}\n", "2025-04-28 14:10:56,291 | INFO : {'message': '2808 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/2808.parquet.snappy'}\n", "2025-04-28 14:10:56,672 | INFO : {'message': '2808 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/2808.parquet.snappy'}\n", "2025-04-28 14:10:57,027 | INFO : {'message': '2808 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/2808.parquet.snappy'}\n", "2025-04-28 14:10:57,391 | INFO : {'message': '183 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/183.parquet.snappy'}\n", "2025-04-28 14:10:57,874 | INFO : {'message': '183 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/183.parquet.snappy'}\n", "2025-04-28 14:10:58,388 | INFO : {'message': '183 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/183.parquet.snappy'}\n", "2025-04-28 14:10:58,596 | INFO : [順序處理] 已處理 10/14 個 segments\n", "2025-04-28 14:10:58,598 | INFO : [順序處理] 記憶體使用情況 (處理第 10 個 segment): 4179.91 MB (13.02%)\n", "2025-04-28 14:10:58,913 | INFO : {'message': '183 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/183.parquet.snappy'}\n", "2025-04-28 14:10:59,411 | INFO : {'message': '1465 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/1465.parquet.snappy'}\n", "2025-04-28 14:10:59,808 | INFO : {'message': '1465 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/1465.parquet.snappy'}\n", "2025-04-28 14:11:00,150 | INFO : {'message': '1465 的完整 permanent IDs 已匯入', 'pids_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/ec_pids/20250428/1465.parquet.snappy'}\n", "2025-04-28 14:11:00,229 | INFO : [順序處理] 完成處理 14 個 segments，耗時 5.73 秒\n", "2025-04-28 14:11:00,231 | INFO : [順序處理] 記憶體使用情況 (映射建立完成): 4174.35 MB (13.00%)\n", "2025-04-28 14:11:00,232 | INFO : [順序處理] 完成建立 segment_id 到 permanent 的映射，共 14 個有效 segments\n", "2025-04-28 14:11:00,290 | INFO : [順序處理] 記憶體使用情況 (最終結果): 4174.79 MB (13.00%)\n", "2025-04-28 14:11:00,291 | INFO : {'message': '[000] 使用者查找完畢', 'result_rows': 2590, 'process_time': 5.730043172836304}\n"]}], "source": ["result_df, process_time = process_segments(audience_packages_df, item_label_map, pids_path_prefix=PIDS_PATH_PREFIX, parallel=parallel_gate)\n", "logging.info({\"message\": f\"[{log_idx}] 使用者查找完畢\", \"result_rows\": len(result_df), \"process_time\": process_time})"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"data": {"text/plain": ["9999"]}, "execution_count": 15, "metadata": {}, "output_type": "execute_result"}], "source": ["# 預處理受眾包資料\n", "audience_packages_df = preprocess_audience_packages(audience_packages_df, item_label_map)\n", "audience_packages_df['item_source_id'][0]"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>permanent</th>\n", "      <th>name</th>\n", "      <th>ec_id</th>\n", "      <th>label_str</th>\n", "      <th>is_media</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4539dd0c06af85b3c59d2ccc09bc8cfe</td>\n", "      <td>19155 芯生悠修護水凝乳 - 瓶裝  (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "      <td>3008dcee-af27-40c2-b56a-744187d784b8 19155 芯生悠...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5f0cd4ed9a98b5f3613a152593676290</td>\n", "      <td>19817 澄淨卸妝露 - 補充包 (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "      <td>8a9490cc-2c92-40fe-89fb-7cb4619d0d7f 19817 澄淨卸...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>778c38d5b01232760eee8d072768fad7</td>\n", "      <td>19823 水原力化妝水 L(清爽型)．瓶裝 (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "      <td>8d9f64d6-a902-4c2f-9293-6df1f634647c 19823 水原力...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>50dcf96202407c5801ac32d988470e63</td>\n", "      <td>丰潤精萃深度美容液</td>\n", "      <td>107</td>\n", "      <td>f1323d4a-d57f-4d2e-8f81-fba1272adab0 丰潤精萃深度美容液...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>b2a98e971aa2a352eb671b778c5dda5b</td>\n", "      <td>丰潤精萃深度美容液</td>\n", "      <td>107</td>\n", "      <td>f1323d4a-d57f-4d2e-8f81-fba1272adab0 丰潤精萃深度美容液...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92784</th>\n", "      <td>c23efac3acedba930580693fb49d274b</td>\n", "      <td>【現貨】TERRA 戰神黑瑪卡2.0</td>\n", "      <td>3860</td>\n", "      <td>752de7d2-388d-4d7e-9440-b5af8679cfd1 【現貨】TERRA...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92785</th>\n", "      <td>abdfc0bd06db5d1d17f65361749eee11</td>\n", "      <td>【現貨】TERRA 戰神黑瑪卡2.0</td>\n", "      <td>3860</td>\n", "      <td>752de7d2-388d-4d7e-9440-b5af8679cfd1 【現貨】TERRA...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92786</th>\n", "      <td>921b22c659832f0654e09ad68daa06cf</td>\n", "      <td>【現貨】TERRA 戰神黑瑪卡2.0</td>\n", "      <td>3860</td>\n", "      <td>752de7d2-388d-4d7e-9440-b5af8679cfd1 【現貨】TERRA...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92787</th>\n", "      <td>2e7f0cd5030377a724f3e0cfe760be95</td>\n", "      <td>【現貨】TERRA 戰神黑瑪卡2.0</td>\n", "      <td>3860</td>\n", "      <td>752de7d2-388d-4d7e-9440-b5af8679cfd1 【現貨】TERRA...</td>\n", "      <td>False</td>\n", "    </tr>\n", "    <tr>\n", "      <th>92788</th>\n", "      <td>a1b3034c377d0b5b6e1a54f28f04a1aa</td>\n", "      <td>黃金抓餅（葷）</td>\n", "      <td>3873</td>\n", "      <td>b2aad613-c395-4de5-a429-1f1ec93b3797 黃金抓餅（葷） {...</td>\n", "      <td>False</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>32957 rows × 5 columns</p>\n", "</div>"], "text/plain": ["                              permanent  \\\n", "0      4539dd0c06af85b3c59d2ccc09bc8cfe   \n", "1      5f0cd4ed9a98b5f3613a152593676290   \n", "2      778c38d5b01232760eee8d072768fad7   \n", "3      50dcf96202407c5801ac32d988470e63   \n", "4      b2a98e971aa2a352eb671b778c5dda5b   \n", "...                                 ...   \n", "92784  c23efac3acedba930580693fb49d274b   \n", "92785  abdfc0bd06db5d1d17f65361749eee11   \n", "92786  921b22c659832f0654e09ad68daa06cf   \n", "92787  2e7f0cd5030377a724f3e0cfe760be95   \n", "92788  a1b3034c377d0b5b6e1a54f28f04a1aa   \n", "\n", "                                        name  ec_id  \\\n", "0        19155 芯生悠修護水凝乳 - 瓶裝  (期限品特賣-恕無提供退貨)    107   \n", "1           19817 澄淨卸妝露 - 補充包 (期限品特賣-恕無提供退貨)    107   \n", "2      19823 水原力化妝水 L(清爽型)．瓶裝 (期限品特賣-恕無提供退貨)    107   \n", "3                                  丰潤精萃深度美容液    107   \n", "4                                  丰潤精萃深度美容液    107   \n", "...                                      ...    ...   \n", "92784                     【現貨】TERRA 戰神黑瑪卡2.0   3860   \n", "92785                     【現貨】TERRA 戰神黑瑪卡2.0   3860   \n", "92786                     【現貨】TERRA 戰神黑瑪卡2.0   3860   \n", "92787                     【現貨】TERRA 戰神黑瑪卡2.0   3860   \n", "92788                                黃金抓餅（葷）   3873   \n", "\n", "                                               label_str  is_media  \n", "0      3008dcee-af27-40c2-b56a-744187d784b8 19155 芯生悠...     False  \n", "1      8a9490cc-2c92-40fe-89fb-7cb4619d0d7f 19817 澄淨卸...     False  \n", "2      8d9f64d6-a902-4c2f-9293-6df1f634647c 19823 水原力...     False  \n", "3      f1323d4a-d57f-4d2e-8f81-fba1272adab0 丰潤精萃深度美容液...     False  \n", "4      f1323d4a-d57f-4d2e-8f81-fba1272adab0 丰潤精萃深度美容液...     False  \n", "...                                                  ...       ...  \n", "92784  752de7d2-388d-4d7e-9440-b5af8679cfd1 【現貨】TERRA...     False  \n", "92785  752de7d2-388d-4d7e-9440-b5af8679cfd1 【現貨】TERRA...     False  \n", "92786  752de7d2-388d-4d7e-9440-b5af8679cfd1 【現貨】TERRA...     False  \n", "92787  752de7d2-388d-4d7e-9440-b5af8679cfd1 【現貨】TERRA...     False  \n", "92788  b2aad613-c395-4de5-a429-1f1ec93b3797 黃金抓餅（葷） {...     False  \n", "\n", "[32957 rows x 5 columns]"]}, "execution_count": 8, "metadata": {}, "output_type": "execute_result"}], "source": ["item_label_map[9999]"]}, {"cell_type": "code", "execution_count": 35, "metadata": {}, "outputs": [], "source": ["ec_pids = pd.read_parquet(PIDS_PATH_PREFIX + f\"/{current_date}/107.parquet.snappy\")"]}, {"cell_type": "code", "execution_count": 36, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>permanent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4539dd0c06af85b3c59d2ccc09bc8cfe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5f0cd4ed9a98b5f3613a152593676290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>778c38d5b01232760eee8d072768fad7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ab57b5c9bdb76be0fe19fcc0c7790c35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>16455784bbffc6d708925a0419536283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2626</th>\n", "      <td>8625e251b00ef55acb9e2190e081d417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2627</th>\n", "      <td>428b19ffbfbfcc743fd9f483a8b98534</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2628</th>\n", "      <td>71f83feca7d8746a6049e30564c7b1a3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2629</th>\n", "      <td>d41842b6480452e194e84131cbbf43ee</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2630</th>\n", "      <td>5abf13babb0e5301d65a6850aaed8a82</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2631 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                             permanent\n", "0     4539dd0c06af85b3c59d2ccc09bc8cfe\n", "1     5f0cd4ed9a98b5f3613a152593676290\n", "2     778c38d5b01232760eee8d072768fad7\n", "3     ab57b5c9bdb76be0fe19fcc0c7790c35\n", "4     16455784bbffc6d708925a0419536283\n", "...                                ...\n", "2626  8625e251b00ef55acb9e2190e081d417\n", "2627  428b19ffbfbfcc743fd9f483a8b98534\n", "2628  71f83feca7d8746a6049e30564c7b1a3\n", "2629  d41842b6480452e194e84131cbbf43ee\n", "2630  5abf13babb0e5301d65a6850aaed8a82\n", "\n", "[2631 rows x 1 columns]"]}, "execution_count": 36, "metadata": {}, "output_type": "execute_result"}], "source": ["ec_pids"]}, {"cell_type": "code", "execution_count": 28, "metadata": {}, "outputs": [], "source": ["ec_27_pids = pd.read_parquet(PIDS_PATH_PREFIX + f\"/20250427/107.parquet.snappy\")\n", "ec_24_pids = pd.read_parquet(PIDS_PATH_PREFIX + f\"/20250424/107.parquet.snappy\")"]}, {"cell_type": "code", "execution_count": 27, "metadata": {}, "outputs": [{"data": {"text/plain": ["0.21496033245183227"]}, "execution_count": 27, "metadata": {}, "output_type": "execute_result"}], "source": ["ec_27_pids['permanent'].isin(ec_pids['permanent']).sum() / len(ec_27_pids)"]}, {"cell_type": "code", "execution_count": 29, "metadata": {}, "outputs": [{"data": {"text/plain": ["1.0"]}, "execution_count": 29, "metadata": {}, "output_type": "execute_result"}], "source": ["ec_27_pids['permanent'].isin(ec_24_pids['permanent']).sum() / len(ec_27_pids)"]}, {"cell_type": "code", "execution_count": 19, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>permanent</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4539dd0c06af85b3c59d2ccc09bc8cfe</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5f0cd4ed9a98b5f3613a152593676290</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>778c38d5b01232760eee8d072768fad7</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ab57b5c9bdb76be0fe19fcc0c7790c35</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>16455784bbffc6d708925a0419536283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2626</th>\n", "      <td>8625e251b00ef55acb9e2190e081d417</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2627</th>\n", "      <td>428b19ffbfbfcc743fd9f483a8b98534</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2628</th>\n", "      <td>71f83feca7d8746a6049e30564c7b1a3</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2629</th>\n", "      <td>d41842b6480452e194e84131cbbf43ee</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2630</th>\n", "      <td>5abf13babb0e5301d65a6850aaed8a82</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>2631 rows × 1 columns</p>\n", "</div>"], "text/plain": ["                             permanent\n", "0     4539dd0c06af85b3c59d2ccc09bc8cfe\n", "1     5f0cd4ed9a98b5f3613a152593676290\n", "2     778c38d5b01232760eee8d072768fad7\n", "3     ab57b5c9bdb76be0fe19fcc0c7790c35\n", "4     16455784bbffc6d708925a0419536283\n", "...                                ...\n", "2626  8625e251b00ef55acb9e2190e081d417\n", "2627  428b19ffbfbfcc743fd9f483a8b98534\n", "2628  71f83feca7d8746a6049e30564c7b1a3\n", "2629  d41842b6480452e194e84131cbbf43ee\n", "2630  5abf13babb0e5301d65a6850aaed8a82\n", "\n", "[2631 rows x 1 columns]"]}, "execution_count": 19, "metadata": {}, "output_type": "execute_result"}], "source": ["ec_pids"]}, {"cell_type": "code", "execution_count": 33, "metadata": {}, "outputs": [], "source": ["from dask import dataframe as dd\n", "full_path = f\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/{current_date}/*.parquet\"\n", "full_user_item_df = dd.read_parquet(full_path, assuming_missing=True, engine=\"pyarrow\").compute()"]}, {"cell_type": "code", "execution_count": 44, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>permanent</th>\n", "      <th>name</th>\n", "      <th>ec_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>4539dd0c06af85b3c59d2ccc09bc8cfe</td>\n", "      <td>19155 芯生悠修護水凝乳 - 瓶裝  (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>5f0cd4ed9a98b5f3613a152593676290</td>\n", "      <td>19817 澄淨卸妝露 - 補充包 (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>778c38d5b01232760eee8d072768fad7</td>\n", "      <td>19823 水原力化妝水 L(清爽型)．瓶裝 (期限品特賣-恕無提供退貨)</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>ab57b5c9bdb76be0fe19fcc0c7790c35</td>\n", "      <td>ORBIS情報誌</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>16455784bbffc6d708925a0419536283</td>\n", "      <td>ORBIS情報誌</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261046</th>\n", "      <td>ef995f92823c8ac53738d272089a867b</td>\n", "      <td>14K爪鑲小圓瑞士藍托帕石轉珠耳環</td>\n", "      <td>3741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261072</th>\n", "      <td>e8f3f763e4c4769b44df09844a09ae77</td>\n", "      <td>Triangle zircon band ring</td>\n", "      <td>3741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261076</th>\n", "      <td>bb5ea4a3b262e8fadec32084751ef977</td>\n", "      <td>【LOVEMBER】Endless Hoops</td>\n", "      <td>3741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261078</th>\n", "      <td>2db15c8f6581bd42e797d20a68588eaf</td>\n", "      <td>【LOVEMBER】LV M Key Hoops</td>\n", "      <td>3741</td>\n", "    </tr>\n", "    <tr>\n", "      <th>261103</th>\n", "      <td>c2d6c80e288c7fe7188581ba048ca7e0</td>\n", "      <td>簡約Triplets橄欖石鏈戒</td>\n", "      <td>3741</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>285308 rows × 3 columns</p>\n", "</div>"], "text/plain": ["                               permanent  \\\n", "0       4539dd0c06af85b3c59d2ccc09bc8cfe   \n", "1       5f0cd4ed9a98b5f3613a152593676290   \n", "2       778c38d5b01232760eee8d072768fad7   \n", "3       ab57b5c9bdb76be0fe19fcc0c7790c35   \n", "4       16455784bbffc6d708925a0419536283   \n", "...                                  ...   \n", "261046  ef995f92823c8ac53738d272089a867b   \n", "261072  e8f3f763e4c4769b44df09844a09ae77   \n", "261076  bb5ea4a3b262e8fadec32084751ef977   \n", "261078  2db15c8f6581bd42e797d20a68588eaf   \n", "261103  c2d6c80e288c7fe7188581ba048ca7e0   \n", "\n", "                                         name  ec_id  \n", "0         19155 芯生悠修護水凝乳 - 瓶裝  (期限品特賣-恕無提供退貨)    107  \n", "1            19817 澄淨卸妝露 - 補充包 (期限品特賣-恕無提供退貨)    107  \n", "2       19823 水原力化妝水 L(清爽型)．瓶裝 (期限品特賣-恕無提供退貨)    107  \n", "3                                    ORBIS情報誌    107  \n", "4                                    ORBIS情報誌    107  \n", "...                                       ...    ...  \n", "261046                      14K爪鑲小圓瑞士藍托帕石轉珠耳環   3741  \n", "261072              Triangle zircon band ring   3741  \n", "261076                【LOVEMBER】Endless Hoops   3741  \n", "261078               【LOVEMBER】LV M Key Hoops   3741  \n", "261103                        簡約Triplets橄欖石鏈戒   3741  \n", "\n", "[285308 rows x 3 columns]"]}, "execution_count": 44, "metadata": {}, "output_type": "execute_result"}], "source": ["full_user_item_df[full_user_item_df['permanent'].isin(ec_pids['permanent'])]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>permanent</th>\n", "      <th>name</th>\n", "      <th>ec_id</th>\n", "      <th>label_str</th>\n", "      <th>is_media</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["Empty DataFrame\n", "Columns: [permanent, name, ec_id, label_str, is_media]\n", "Index: []"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["merged_df[merged_df['permanent'].isin(ec_pids)]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["small_user_item_gcs_path = f\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/{current_date}/000000000000.parquet\"\n", "small_user_item_df = pd.read_parquet(small_user_item_gcs_path)"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"data": {"text/plain": ["21938590"]}, "execution_count": 7, "metadata": {}, "output_type": "execute_result"}], "source": ["len(user_item_df)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"data": {"text/plain": ["257677"]}, "execution_count": 9, "metadata": {}, "output_type": "execute_result"}], "source": ["len(small_user_item_df)"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/plain": ["21.93859"]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["len(user_item_df) / 1000000"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/ml-workflow-cloud-functions/item-label-retrieve-permanent/pipeline.py:171: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  processed_df['user_source_id'].fillna(0, inplace=True)\n"]}], "source": ["audience_packages_df = preprocess_audience_packages(audience_packages_df)"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [], "source": ["valid_keys = [1347, 2851, 3093, 3542, 107, 163, 183, 356, 381, 715, 723, 772, 868, 893, 1040, 1095, 1115, 1186, 1203, 1252, 1345, 1376, 1434, 1456, 1465, 1474, 1557, 1626, 1633, 1662, 1673, 1674, 1676, 1694, 1697, 1713, 1719, 1784, 1803, 1813, 1825, 1847, 1854, 1878, 1910, 1919, 1924, 1966, 1992, 2039, 2050, 2088, 2091, 2135, 2140, 2156, 2158, 2171, 2246, 2270, 2318, 2324, 2326, 2334, 2337, 2370, 2372, 2373, 2383, 2397, 2406, 2416, 2458, 2459, 2473, 2487, 2492, 2546, 2576, 2695, 2708, 2729, 2752, 2769, 2808, 2816, 2838, 2849, 2857, 2902, 2917, 2941, 2990, 3002, 3005, 3021, 3022, 3139, 3147, 3184, 3237, 3244, 3410, 3424, 3454, 3471, 3493, 3499, 3509, 3511, 3602, 3603, 3605, 3619, 3637, 3639, 3650, 3653, 3657, 3671, 3678, 3699, 3701, 3719, 3720, 3732, 3741, 3825, 3829, 3860, 9999]"]}, {"cell_type": "code", "execution_count": 18, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>name</th>\n", "      <th>description</th>\n", "      <th>path</th>\n", "      <th>item_source</th>\n", "      <th>item_source_id</th>\n", "      <th>segment_id</th>\n", "      <th>or_keywords</th>\n", "      <th>and_keywords</th>\n", "      <th>user_source_id</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>人口發展研究者</td>\n", "      <td>關注人口變遷及其對社會影響的分析者。這群人對生育率和人口結構的變化有深入的見解。</td>\n", "      <td>根目錄 &gt; 綜合聯播網 &gt; 3542 &gt; 閱讀傾向 &gt; 人口與生育</td>\n", "      <td>VMFive</td>\n", "      <td>3542</td>\n", "      <td>tm:d_3542_k_001</td>\n", "      <td>[老齡化, 移民, 出生率, 家庭結構, 城鄉差異, 人口老化, 人口流動, 社會福利, 住...</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>人口結構分析家</td>\n", "      <td>專注於分析人口結構變化及其影響的專業人士。這群人常常提供重要的政策建議。</td>\n", "      <td>根目錄 &gt; 綜合聯播網 &gt; 3542 &gt; 閱讀傾向 &gt; 人口與生育 &gt; 人口結構變化</td>\n", "      <td>VMFive</td>\n", "      <td>3542</td>\n", "      <td>tm:d_3542_k_002</td>\n", "      <td>[老齡化, 移民, 出生率, 家庭結構, 城鄉差異, 人口老化, 人口流動, 社會福利, 住...</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>生育趨勢觀察者</td>\n", "      <td>關注生育率變化及其社會影響的重要分析者。這些人旨在提高對生育問題的意識與討論。</td>\n", "      <td>根目錄 &gt; 綜合聯播網 &gt; 3542 &gt; 閱讀傾向 &gt; 人口與生育 &gt; 生育率</td>\n", "      <td>VMFive</td>\n", "      <td>3542</td>\n", "      <td>tm:d_3542_k_003</td>\n", "      <td>[生育政策, 育兒支持, 家庭福利, 育嬰假, 生育補貼, 社會老化, 少子化, 性別平等,...</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>人際關係建設者</td>\n", "      <td>專注於改善人際關係及社交互動的人士。這群人希望促進良好的溝通與互信。</td>\n", "      <td>根目錄 &gt; 綜合聯播網 &gt; 3542 &gt; 閱讀傾向 &gt; 人際關係</td>\n", "      <td>VMFive</td>\n", "      <td>3542</td>\n", "      <td>tm:d_3542_k_004</td>\n", "      <td>[離婚, 伴侶溝通, 親子教育, 家庭衝突, 婚姻咨詢, 情感經營, 家庭關係, 婚姻問題,...</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>婚姻關係倡導者</td>\n", "      <td>專注於婚姻與家庭關係的促進及優化的實踐者。這群人致力於改善親密關係並解決衝突。</td>\n", "      <td>根目錄 &gt; 綜合聯播網 &gt; 3542 &gt; 閱讀傾向 &gt; 人際關係 &gt; 婚姻與家庭</td>\n", "      <td>VMFive</td>\n", "      <td>3542</td>\n", "      <td>tm:d_3542_k_005</td>\n", "      <td>[離婚, 伴侶溝通, 親子教育, 家庭衝突, 婚姻咨詢, 情感經營, 家庭關係, 婚姻問題,...</td>\n", "      <td>[]</td>\n", "      <td>0</td>\n", "    </tr>\n", "    <tr>\n", "      <th>...</th>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "      <td>...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>200</th>\n", "      <td>潮流風格與品牌聯名支持者</td>\n", "      <td>近期曾造訪 Wave Shine 網站，且在站外關注潮流風格或聯名商品的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 1465 &gt;  潮流風格與品牌聯名支持者（站外）</td>\n", "      <td></td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_1465_r_014</td>\n", "      <td>[動漫, 卡通, 聯名, 漫畫, 動畫, 街頭風格, 街頭文化, 街頭時尚, 拼接, 拼色,...</td>\n", "      <td>[]</td>\n", "      <td>1465</td>\n", "    </tr>\n", "    <tr>\n", "      <th>201</th>\n", "      <td>Orbis 保養入門者</td>\n", "      <td>近期曾造訪Orbis網站，在站外關注入門保養商品或議題的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 107 &gt;  Orbis 保養入門者</td>\n", "      <td>Orbis</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_107_c_032</td>\n", "      <td>[護膚, 溫和配方, 無添加, 基礎保養, 簡單保養, 保養入門, 入門保養, 隔離霜, 卸...</td>\n", "      <td>[]</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>202</th>\n", "      <td>Orbis 理性消費者</td>\n", "      <td>近期曾造訪Orbis網站，在站外關注促銷資訊，也會留意投資或財務議題</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 107 &gt;  Orbis 理性消費者</td>\n", "      <td>Orbis</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_107_c_033</td>\n", "      <td>[股票, 促銷, 優惠, 特價, 財經, 台股, 美股, 平價, 中低價, 低價, 配息, ...</td>\n", "      <td>[]</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>203</th>\n", "      <td>Orbis 敏感肌消費者</td>\n", "      <td>近期曾造訪Orbis網站，在站外關注敏感肌保養商品及議題的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 107 &gt;  Orbis 敏感肌消費者</td>\n", "      <td>Orbis</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_107_c_034</td>\n", "      <td>[天然成分, 敏感肌, 低敏, 無刺激配方, 天然成分, 無添加, 刺激成分, 敏肌, 舒緩...</td>\n", "      <td>[]</td>\n", "      <td>107</td>\n", "    </tr>\n", "    <tr>\n", "      <th>204</th>\n", "      <td>Orbis 成熟保養需求者</td>\n", "      <td>近期曾造訪Orbis網站，在站外關注成熟保養商品及議題的使用者</td>\n", "      <td>根目錄 &gt; 塔圖科技 &gt; 消費意圖 &gt; 107 &gt;  Orbis 成熟保養需求者</td>\n", "      <td>Orbis</td>\n", "      <td>9999</td>\n", "      <td>tm:c_9999_107_c_035</td>\n", "      <td>[老化, 緊緻, 奢華護膚, 延緩衰老, 抗衰老, 抗皺, 皺紋, 暗沈, 細紋, 法令紋,...</td>\n", "      <td>[]</td>\n", "      <td>107</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "<p>205 rows × 9 columns</p>\n", "</div>"], "text/plain": ["              name                               description  \\\n", "0          人口發展研究者  關注人口變遷及其對社會影響的分析者。這群人對生育率和人口結構的變化有深入的見解。   \n", "1          人口結構分析家      專注於分析人口結構變化及其影響的專業人士。這群人常常提供重要的政策建議。   \n", "2          生育趨勢觀察者   關注生育率變化及其社會影響的重要分析者。這些人旨在提高對生育問題的意識與討論。   \n", "3          人際關係建設者        專注於改善人際關係及社交互動的人士。這群人希望促進良好的溝通與互信。   \n", "4          婚姻關係倡導者   專注於婚姻與家庭關係的促進及優化的實踐者。這群人致力於改善親密關係並解決衝突。   \n", "..             ...                                       ...   \n", "200   潮流風格與品牌聯名支持者   近期曾造訪 Wave Shine 網站，且在站外關注潮流風格或聯名商品的使用者   \n", "201    Orbis 保養入門者           近期曾造訪Orbis網站，在站外關注入門保養商品或議題的使用者   \n", "202    Orbis 理性消費者        近期曾造訪Orbis網站，在站外關注促銷資訊，也會留意投資或財務議題   \n", "203   Orbis 敏感肌消費者          近期曾造訪Orbis網站，在站外關注敏感肌保養商品及議題的使用者   \n", "204  Orbis 成熟保養需求者           近期曾造訪Orbis網站，在站外關注成熟保養商品及議題的使用者   \n", "\n", "                                             path item_source  item_source_id  \\\n", "0               根目錄 > 綜合聯播網 > 3542 > 閱讀傾向 > 人口與生育      VMFive            3542   \n", "1      根目錄 > 綜合聯播網 > 3542 > 閱讀傾向 > 人口與生育 > 人口結構變化      VMFive            3542   \n", "2         根目錄 > 綜合聯播網 > 3542 > 閱讀傾向 > 人口與生育 > 生育率      VMFive            3542   \n", "3                根目錄 > 綜合聯播網 > 3542 > 閱讀傾向 > 人際關係      VMFive            3542   \n", "4        根目錄 > 綜合聯播網 > 3542 > 閱讀傾向 > 人際關係 > 婚姻與家庭      VMFive            3542   \n", "..                                            ...         ...             ...   \n", "200  根目錄 > 塔圖科技 > 消費意圖 > 1465 >  潮流風格與品牌聯名支持者（站外）                        9999   \n", "201        根目錄 > 塔圖科技 > 消費意圖 > 107 >  Orbis 保養入門者       Orbis            9999   \n", "202        根目錄 > 塔圖科技 > 消費意圖 > 107 >  Orbis 理性消費者       Orbis            9999   \n", "203       根目錄 > 塔圖科技 > 消費意圖 > 107 >  Orbis 敏感肌消費者       Orbis            9999   \n", "204      根目錄 > 塔圖科技 > 消費意圖 > 107 >  Orbis 成熟保養需求者       Orbis            9999   \n", "\n", "               segment_id                                        or_keywords  \\\n", "0         tm:d_3542_k_001  [老齡化, 移民, 出生率, 家庭結構, 城鄉差異, 人口老化, 人口流動, 社會福利, 住...   \n", "1         tm:d_3542_k_002  [老齡化, 移民, 出生率, 家庭結構, 城鄉差異, 人口老化, 人口流動, 社會福利, 住...   \n", "2         tm:d_3542_k_003  [生育政策, 育兒支持, 家庭福利, 育嬰假, 生育補貼, 社會老化, 少子化, 性別平等,...   \n", "3         tm:d_3542_k_004  [離婚, 伴侶溝通, 親子教育, 家庭衝突, 婚姻咨詢, 情感經營, 家庭關係, 婚姻問題,...   \n", "4         tm:d_3542_k_005  [離婚, 伴侶溝通, 親子教育, 家庭衝突, 婚姻咨詢, 情感經營, 家庭關係, 婚姻問題,...   \n", "..                    ...                                                ...   \n", "200  tm:c_9999_1465_r_014  [動漫, 卡通, 聯名, 漫畫, 動畫, 街頭風格, 街頭文化, 街頭時尚, 拼接, 拼色,...   \n", "201   tm:c_9999_107_c_032  [護膚, 溫和配方, 無添加, 基礎保養, 簡單保養, 保養入門, 入門保養, 隔離霜, 卸...   \n", "202   tm:c_9999_107_c_033  [股票, 促銷, 優惠, 特價, 財經, 台股, 美股, 平價, 中低價, 低價, 配息, ...   \n", "203   tm:c_9999_107_c_034  [天然成分, 敏感肌, 低敏, 無刺激配方, 天然成分, 無添加, 刺激成分, 敏肌, 舒緩...   \n", "204   tm:c_9999_107_c_035  [老化, 緊緻, 奢華護膚, 延緩衰老, 抗衰老, 抗皺, 皺紋, 暗沈, 細紋, 法令紋,...   \n", "\n", "    and_keywords  user_source_id  \n", "0             []               0  \n", "1             []               0  \n", "2             []               0  \n", "3             []               0  \n", "4             []               0  \n", "..           ...             ...  \n", "200           []            1465  \n", "201           []             107  \n", "202           []             107  \n", "203           []             107  \n", "204           []             107  \n", "\n", "[205 rows x 9 columns]"]}, "execution_count": 18, "metadata": {}, "output_type": "execute_result"}], "source": ["audience_packages_df[\n", "    audience_packages_df['item_source_id'].isin(valid_keys)\n", "    # audience_packages_df['user_source_id'].isin(valid_keys)\n", "]"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-09 02:50:24,443 | INFO : {'message': '參數已匯入', 'current_date': '20250409'}\n", "2025-04-09 02:50:24,493 | INFO : {'message': 'GSIO 已建立', 'project_id': 'tagtoo-ml-workflow'}\n", "2025-04-09 02:50:24,495 | INFO : {'message': 'BigQuery 查詢已跳過，直接使用 Cloud Storage 檔案', 'temp_table_id': 'tagtoo-ml-workflow.temp_tables.user_query_results', 'label_gcs_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/label_query_results/20250409/*.parquet', 'user_item_gcs_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/20250409/*.parquet'}\n", "2025-04-09 02:50:42,247 | INFO : {'message': '資料載入完成', 'unique_users': 2319992, 'labeled_items': 360244}\n"]}], "source": ["\n", "\n", "# @functions_framework.http\n", "# def main(request):\n", "\n", "    # # 根據環境設定logging\n", "    # try:\n", "    #     # Cloud Functions環境\n", "    #     logging_client = google.cloud.logging.Client()\n", "    #     logging_client.setup_logging()\n", "    # except Exception:\n", "    #     # 本地測試環境\n", "    #     setup_local_logging()\n", "    #     logging.info(\"使用本地端logging設定\")\n", "\n", "    # 解析請求參數\n", "    # 測試 cloud function 部署，請忽略這行註解\n", "    # ---\n", "    # # 取得並驗證必要的請求參數\n", "    # request_json = request.get_json(silent=True)\n", "    # if not request_json:\n", "    #     return '請求必須包含 JSON 資料', 400\n", "        \n", "    # required_params = {\n", "    #     # 'segment_to_keywords_map_gcs_path': '受眾包關鍵字檔案的 GCS 路徑',\n", "    #     # 'segment_gcs_path': '受眾包檔案的 GCS路徑',\n", "    #     'page_name': '頁面名稱',\n", "    #     'save_gate': '保存開關（0 或 1）',\n", "    #     'query_gate': '查詢開關（0 或 1）', # 是否需要再查詢一次\n", "    #     'upload_gate': '上傳開關（0 或 1）'\n", "    # }\n", "\n", "    # params = {}\n", "    # for param_key, description in required_params.items():\n", "    #     if param_key not in request_json:\n", "    #         return f'缺少必要參數 {param_key}：{description}', 400\n", "    #     params[param_key] = request_json[param_key]\n", "    \n", "    # def validate_binary_param(params, param_name):\n", "    #     try:\n", "    #         params[param_name] = int(params[param_name])\n", "    #         if params[param_name] not in [0, 1]:\n", "    #             return f'{param_name} 必須為 0 或 1', 400\n", "    #         return None\n", "    #     except ValueError:\n", "    #         return f'{param_name} 必須為數字', 400\n", "\n", "    # # 檢查 upload_gate\n", "    # error = validate_binary_param(params, 'upload_gate')\n", "    # if error:\n", "    #     return error\n", "\n", "    # # 檢查 save_gate\n", "    # error = validate_binary_param(params, 'save_gate')\n", "    # if error:\n", "    #     return error\n", "    \n", "    # # 檢查 save_gate\n", "    # error = validate_binary_param(params, 'query_gate')\n", "    # if error:\n", "    #     return error\n", "        \n", "params = {\n", "\t\"page_name\": \"prod\",\n", "\t\"upload_gate\": 0,\n", "\t\"save_gate\": 1,\n", "\t\"query_gate\": 0,\n", "}\n", "\n", "# 解構參數以供後續使用\n", "page_name = params.get('page_name')\n", "upload_gate = params.get('upload_gate')\n", "save_gate = params.get('save_gate')\n", "query_gate = params.get('query_gate')\n", "current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime(\"%Y%m%d\")\n", "user_item_gcs_path = f\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/user_item_query_results/{current_date}/*.parquet\"\n", "\n", "label_gcs_path = f\"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/label_query_results/{current_date}/*.parquet\"\n", "temp_table_id = \"tagtoo-ml-workflow.temp_tables.user_query_results\"\n", "\n", "logging.info({\"message\": \"參數已匯入\", \"current_date\": current_date})\n", "google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)\n", "GSIO = GoogleStorageIO(credentials=google_credentials)\n", "logging.info({\"message\": \"GSIO 已建立\", \"project_id\": project_id})\n", "\n", "# bq_client = bigquery.Client(credentials=google_credentials)\n", "\n", "if query_gate:\n", "\t# 因為標籤資料有 JSON 欄位，所以需要先下載到 local 才能讀取\n", "\tsave_query_results(\n", "\t\tQUERY=LABEL_QUERY,\n", "\t\tcredentials=google_credentials,\n", "\t\ttemp_table_id=temp_table_id,\n", "\t\tdestination_uri=label_gcs_path\n", "\t)\n", "\n", "\tsave_query_results(\n", "\t\tQUERY=USER_QUERY,\n", "\t\tcredentials=google_credentials,\n", "\t\ttemp_table_id=temp_table_id,\n", "\t\tdestination_uri=user_item_gcs_path\n", "\t)\n", "\tlogging.info({\"message\": \"BigQuery 查詢完成\", \"temp_table_id\": temp_table_id})\n", "else:\n", "\tlogging.info({\"message\": \"BigQuery 查詢已跳過，直接使用 Cloud Storage 檔案\", \"temp_table_id\": temp_table_id, \"label_gcs_path\": label_gcs_path, \"user_item_gcs_path\": user_item_gcs_path})\n", "\n", "# # 下載 segment_id 對應到 keywords 的字典\n", "# segment_to_keywords_map_path = \"/tmp/segment_to_keywords_map.json\"\n", "# GSIO.download_to_path(gsuri=segment_to_keywords_map_gcs_path, localpath=segment_to_keywords_map_path)\n", "\n", "\"\"\"主要處理流程\"\"\"\n", "# 1. 載入資料\n", "label_df, user_item_df = load_data_with_dask(\n", "\tlabel_gcs_path,\n", "\tuser_item_gcs_path,\n", "\t# segment_gcs_path\n", ")\n", "# segment_keywords_map = read_json(segment_to_keywords_map_path)\n", "logging.info({\n", "\t\"message\": \"資料載入完成\",\n", "\t\"unique_users\": len(user_item_df['permanent'].drop_duplicates()),\n", "\t\"labeled_items\": len(label_df),\n", "\t# \"new_segments\": len(new_segment_df)\n", "})"]}, {"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-09 02:57:10,069 | WARNING : Field 'PotentialPainPoints' contains unexpected type: <class 'list'>, value: ['易於搭配的衣物']\n", "2025-04-09 02:57:15,376 | INFO : {'message': '標籤處理完成'}\n"]}], "source": ["# 2. 處理標籤\n", "# * 會根據不同資料來源媒合受眾包\n", "label_df['structured_labels'] = label_df['structured_labels_str'].apply(lambda x: json.loads(x))\n", "columns_to_extract = [\n", "\t'Interests', 'OccupationalTraits', 'PersonalOpinions', \n", "\t'BrowsingMotivations', 'PotentialPainPoints',\n", "\t'Category', 'Features', 'Industry', 'MarketSegmentation',\n", "\t'PotentialIssues', 'Purpose', 'Tone',\n", "\t'CoreDescription', 'Keywords'\n", "]\n", "label_df = extract_label_fields(label_df, columns_to_extract)\n", "label_df = label_df.assign(label_str=lambda x: x.apply(concat_label_fields, axis=1)).query('label_str != \"\"')\n", "logging.info({\"message\": \"標籤處理完成\",})"]}, {"cell_type": "code", "execution_count": 8, "metadata": {}, "outputs": [], "source": ["# 3. 合併與映射處理\n", "merged_df = process_merged_data(user_item_df, label_df)"]}, {"cell_type": "code", "execution_count": 9, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-09 02:57:32,249 | INFO : {'message': '資料合併與映射完成'}\n"]}], "source": ["# 4. 篩選出個資料來源\n", "item_label_map = create_source_mappings(merged_df)\n", "logging.info({\"message\": \"資料合併與映射完成\",})"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/plain": ["7291982"]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["len(merged_df)"]}, {"cell_type": "code", "execution_count": 13, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-09 02:58:15,050 | WARNING : file_cache is unavailable when using oauth2client >= 4.0.0 or google-auth\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/__init__.py\", line 36, in autodetect\n", "    from google.appengine.api import memcache\n", "ModuleNotFoundError: No module named 'google.appengine'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/file_cache.py\", line 33, in <module>\n", "    from oauth2client.contrib.locked_file import LockedFile\n", "ModuleNotFoundError: No module named 'oauth2client.contrib.locked_file'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/file_cache.py\", line 37, in <module>\n", "    from oauth2client.locked_file import LockedFile\n", "ModuleNotFoundError: No module named 'oauth2client.locked_file'\n", "\n", "During handling of the above exception, another exception occurred:\n", "\n", "Traceback (most recent call last):\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/__init__.py\", line 42, in autodetect\n", "    from . import file_cache\n", "  File \"/opt/conda/envs/tensorflow/lib/python3.10/site-packages/googleapiclient/discovery_cache/file_cache.py\", line 40, in <module>\n", "    raise ImportError(\n", "ImportError: file_cache is unavailable when using oauth2client >= 4.0.0 or google-auth\n", "2025-04-09 02:58:15,052 | INFO : URL being requested: GET https://www.googleapis.com/discovery/v1/apis/sheets/v4/rest\n", "2025-04-09 02:58:16,351 | INFO : URL being requested: GET https://sheets.googleapis.com/v4/spreadsheets/1VZxkSc6XsPSKfFPP8M9V8IyXUh4GxGpc6lq63h1Veco/values/prod%21A2%3AI?alt=json\n"]}], "source": ["# 5. 根據關鍵字大表中的所有 segments 與其關鍵字規則，篩選出符合條件的商品，並查找瀏覽過商品的 permanent\n", "page_name = page_name or \"prod\"\n", "audience_packages_df = read_audience_packages(google_credentials, page_name)"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-09 02:58:17,463 | INFO : 記憶體使用情況 (初始): 10192.21 MB (31.74%)\n", "2025-04-09 02:58:17,468 | INFO : 記憶體使用情況 (資料前處理後): 10192.21 MB (31.74%)\n", "2025-04-09 02:58:17,469 | INFO : 開始建立 segment_id 到 permanent 的映射，共 213 個 segments\n", "2025-04-09 02:58:17,471 | INFO : 已處理 0/213 個 segments\n", "2025-04-09 02:58:17,473 | INFO : 記憶體使用情況 (處理第 0 個 segment): 10192.21 MB (31.74%)\n"]}, {"name": "stderr", "output_type": "stream", "text": ["/home/<USER>/ml-workflow-cloud-functions/item-label-retrieve-permanent/pipeline.py:88: FutureWarning: A value is trying to be set on a copy of a DataFrame or Series through chained assignment using an inplace method.\n", "The behavior will change in pandas 3.0. This inplace method will never work because the intermediate object on which we are setting values always behaves as a copy.\n", "\n", "For example, when doing 'df[col].method(value, inplace=True)', try using 'df.method({col: value}, inplace=True)' or df[col] = df[col].method(value) instead, to perform the operation inplace on the original object.\n", "\n", "\n", "  # 監控記憶體使用情況\n"]}, {"name": "stdout", "output_type": "stream", "text": ["2025-04-09 02:58:46,008 | INFO : 已處理 5/213 個 segments\n", "2025-04-09 02:58:46,009 | INFO : 記憶體使用情況 (處理第 5 個 segment): 10362.09 MB (32.27%)\n", "2025-04-09 02:59:13,400 | INFO : 已處理 10/213 個 segments\n", "2025-04-09 02:59:13,402 | INFO : 記憶體使用情況 (處理第 10 個 segment): 10619.68 MB (33.07%)\n", "2025-04-09 02:59:38,319 | INFO : 已處理 15/213 個 segments\n", "2025-04-09 02:59:38,321 | INFO : 記憶體使用情況 (處理第 15 個 segment): 10847.05 MB (33.78%)\n", "2025-04-09 03:00:39,840 | INFO : 已處理 20/213 個 segments\n", "2025-04-09 03:00:39,842 | INFO : 記憶體使用情況 (處理第 20 個 segment): 11137.85 MB (34.69%)\n", "2025-04-09 03:01:03,484 | INFO : 已處理 25/213 個 segments\n", "2025-04-09 03:01:03,486 | INFO : 記憶體使用情況 (處理第 25 個 segment): 10633.14 MB (33.11%)\n", "2025-04-09 03:01:32,450 | INFO : 已處理 30/213 個 segments\n", "2025-04-09 03:01:32,451 | INFO : 記憶體使用情況 (處理第 30 個 segment): 11094.63 MB (34.55%)\n", "2025-04-09 03:02:01,560 | INFO : 已處理 35/213 個 segments\n", "2025-04-09 03:02:01,561 | INFO : 記憶體使用情況 (處理第 35 個 segment): 11456.70 MB (35.68%)\n", "2025-04-09 03:02:29,200 | INFO : 已處理 40/213 個 segments\n", "2025-04-09 03:02:29,202 | INFO : 記憶體使用情況 (處理第 40 個 segment): 11719.43 MB (36.50%)\n", "2025-04-09 03:03:13,154 | INFO : 已處理 45/213 個 segments\n", "2025-04-09 03:03:13,155 | INFO : 記憶體使用情況 (處理第 45 個 segment): 11346.21 MB (35.33%)\n", "2025-04-09 03:03:40,793 | INFO : 已處理 50/213 個 segments\n", "2025-04-09 03:03:40,795 | INFO : 記憶體使用情況 (處理第 50 個 segment): 11657.50 MB (36.30%)\n", "2025-04-09 03:04:23,546 | INFO : 已處理 55/213 個 segments\n", "2025-04-09 03:04:23,548 | INFO : 記憶體使用情況 (處理第 55 個 segment): 11693.94 MB (36.42%)\n", "2025-04-09 03:04:50,006 | INFO : 已處理 60/213 個 segments\n", "2025-04-09 03:04:50,008 | INFO : 記憶體使用情況 (處理第 60 個 segment): 11920.26 MB (37.12%)\n", "2025-04-09 03:05:16,294 | INFO : 已處理 65/213 個 segments\n", "2025-04-09 03:05:16,295 | INFO : 記憶體使用情況 (處理第 65 個 segment): 12298.02 MB (38.30%)\n", "2025-04-09 03:05:43,499 | INFO : 已處理 70/213 個 segments\n", "2025-04-09 03:05:43,501 | INFO : 記憶體使用情況 (處理第 70 個 segment): 12610.40 MB (39.27%)\n", "2025-04-09 03:06:08,506 | INFO : 已處理 75/213 個 segments\n", "2025-04-09 03:06:08,507 | INFO : 記憶體使用情況 (處理第 75 個 segment): 12903.29 MB (40.18%)\n", "2025-04-09 03:06:47,218 | INFO : 已處理 80/213 個 segments\n", "2025-04-09 03:06:47,220 | INFO : 記憶體使用情況 (處理第 80 個 segment): 12331.80 MB (38.40%)\n", "2025-04-09 03:07:12,000 | INFO : 已處理 85/213 個 segments\n", "2025-04-09 03:07:12,002 | INFO : 記憶體使用情況 (處理第 85 個 segment): 12564.15 MB (39.13%)\n", "2025-04-09 03:07:38,656 | INFO : 已處理 90/213 個 segments\n", "2025-04-09 03:07:38,657 | INFO : 記憶體使用情況 (處理第 90 個 segment): 12875.66 MB (40.10%)\n", "2025-04-09 03:08:14,685 | INFO : 已處理 95/213 個 segments\n", "2025-04-09 03:08:14,687 | INFO : 記憶體使用情況 (處理第 95 個 segment): 13167.79 MB (41.01%)\n", "2025-04-09 03:09:05,687 | INFO : 已處理 100/213 個 segments\n", "2025-04-09 03:09:05,689 | INFO : 記憶體使用情況 (處理第 100 個 segment): 12818.67 MB (39.92%)\n", "2025-04-09 03:09:30,773 | INFO : 已處理 105/213 個 segments\n", "2025-04-09 03:09:30,774 | INFO : 記憶體使用情況 (處理第 105 個 segment): 13015.62 MB (40.53%)\n", "2025-04-09 03:09:57,391 | INFO : 已處理 110/213 個 segments\n", "2025-04-09 03:09:57,393 | INFO : 記憶體使用情況 (處理第 110 個 segment): 13272.03 MB (41.33%)\n", "2025-04-09 03:10:22,954 | INFO : 已處理 115/213 個 segments\n", "2025-04-09 03:10:22,955 | INFO : 記憶體使用情況 (處理第 115 個 segment): 13577.11 MB (42.28%)\n", "2025-04-09 03:10:54,428 | INFO : 已處理 120/213 個 segments\n", "2025-04-09 03:10:54,430 | INFO : 記憶體使用情況 (處理第 120 個 segment): 13810.79 MB (43.01%)\n", "2025-04-09 03:11:18,341 | INFO : 已處理 125/213 個 segments\n", "2025-04-09 03:11:18,344 | INFO : 記憶體使用情況 (處理第 125 個 segment): 13235.97 MB (41.22%)\n", "2025-04-09 03:11:49,197 | INFO : 已處理 130/213 個 segments\n", "2025-04-09 03:11:49,199 | INFO : 記憶體使用情況 (處理第 130 個 segment): 13540.19 MB (42.17%)\n", "2025-04-09 03:12:17,715 | INFO : 已處理 135/213 個 segments\n", "2025-04-09 03:12:17,717 | INFO : 記憶體使用情況 (處理第 135 個 segment): 13786.57 MB (42.93%)\n", "2025-04-09 03:12:42,456 | INFO : 已處理 140/213 個 segments\n", "2025-04-09 03:12:42,458 | INFO : 記憶體使用情況 (處理第 140 個 segment): 14081.18 MB (43.85%)\n", "2025-04-09 03:13:06,010 | INFO : 已處理 145/213 個 segments\n", "2025-04-09 03:13:06,011 | INFO : 記憶體使用情況 (處理第 145 個 segment): 14305.27 MB (44.55%)\n", "2025-04-09 03:13:17,295 | INFO : 已處理 150/213 個 segments\n", "2025-04-09 03:13:17,296 | INFO : 記憶體使用情況 (處理第 150 個 segment): 13645.21 MB (42.49%)\n", "2025-04-09 03:13:20,554 | INFO : 已處理 155/213 個 segments\n", "2025-04-09 03:13:20,555 | INFO : 記憶體使用情況 (處理第 155 個 segment): 13658.17 MB (42.53%)\n", "2025-04-09 03:13:22,848 | INFO : 已處理 160/213 個 segments\n", "2025-04-09 03:13:22,850 | INFO : 記憶體使用情況 (處理第 160 個 segment): 13652.55 MB (42.52%)\n", "2025-04-09 03:13:29,050 | INFO : 已處理 165/213 個 segments\n", "2025-04-09 03:13:29,051 | INFO : 記憶體使用情況 (處理第 165 個 segment): 13679.21 MB (42.60%)\n", "2025-04-09 03:13:29,524 | INFO : 已處理 170/213 個 segments\n", "2025-04-09 03:13:29,525 | INFO : 記憶體使用情況 (處理第 170 個 segment): 13703.63 MB (42.68%)\n", "2025-04-09 03:13:30,297 | INFO : 已處理 175/213 個 segments\n", "2025-04-09 03:13:30,299 | INFO : 記憶體使用情況 (處理第 175 個 segment): 13701.67 MB (42.67%)\n", "2025-04-09 03:13:30,313 | INFO : 已處理 180/213 個 segments\n", "2025-04-09 03:13:30,315 | INFO : 記憶體使用情況 (處理第 180 個 segment): 13701.67 MB (42.67%)\n", "2025-04-09 03:13:30,329 | INFO : 已處理 185/213 個 segments\n", "2025-04-09 03:13:30,330 | INFO : 記憶體使用情況 (處理第 185 個 segment): 13701.67 MB (42.67%)\n", "2025-04-09 03:13:30,342 | INFO : 已處理 190/213 個 segments\n", "2025-04-09 03:13:30,343 | INFO : 記憶體使用情況 (處理第 190 個 segment): 13701.67 MB (42.67%)\n", "2025-04-09 03:13:42,814 | INFO : 已處理 195/213 個 segments\n", "2025-04-09 03:13:42,816 | INFO : 記憶體使用情況 (處理第 195 個 segment): 13688.95 MB (42.63%)\n", "2025-04-09 03:13:46,674 | INFO : 已處理 200/213 個 segments\n", "2025-04-09 03:13:46,676 | INFO : 記憶體使用情況 (處理第 200 個 segment): 13744.55 MB (42.80%)\n", "2025-04-09 03:13:49,069 | INFO : 已處理 205/213 個 segments\n", "2025-04-09 03:13:49,071 | INFO : 記憶體使用情況 (處理第 205 個 segment): 13743.86 MB (42.80%)\n", "2025-04-09 03:13:50,971 | INFO : 已處理 210/213 個 segments\n", "2025-04-09 03:13:50,973 | INFO : 記憶體使用情況 (處理第 210 個 segment): 13743.53 MB (42.80%)\n", "2025-04-09 03:13:51,962 | INFO : 記憶體使用情況 (映射建立完成): 13752.03 MB (42.83%)\n", "2025-04-09 03:13:51,964 | INFO : 完成建立 segment_id 到 permanent 的映射，共 208 個有效 segments\n", "2025-04-09 03:14:42,228 | INFO : 記憶體使用情況 (最終結果): 14056.69 MB (43.78%)\n", "2025-04-09 03:14:42,752 | INFO : {'message': '使用者查找完畢', 'result_rows': 1592152}\n"]}], "source": ["result_df = process_segments(audience_packages_df, item_label_map)\n", "logging.info({\"message\": \"使用者查找完畢\", \"result_rows\": len(result_df)})"]}, {"cell_type": "code", "execution_count": 17, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["2025-04-09 03:32:31,830 | INFO : {'message': '結果保存完成', 'result_gcs_path': 'gs://tagtoo-ml-workflow-cloud-functions/item_label/retrieve_permanent/20250409/result_20250409113214.csv'}\n"]}], "source": ["# 6. 保存結果\n", "if save_gate:\n", "\tfilename = f\"result_{datetime.now(timezone('Asia/Taipei')).strftime('%Y%m%d%H%M%S')}\"\n", "\tresult_path = f\"/tmp/{filename}.csv\"\n", "\tresult_df.to_csv(result_path, index=False)\n", "\tresult_gcs_path = f\"gs://tagtoo-ml-workflow-cloud-functions/item_label/retrieve_permanent/{current_date}/{filename}.csv\"\n", "\tGSIO.upload_file(localpath=result_path, gsuri=result_gcs_path)\n", "\tlogging.info({\"message\": \"結果保存完成\", \"result_gcs_path\": result_gcs_path})\n", "\n", "# 7. 上傳結果\n", "if upload_gate:\n", "\tproject_id = \"tagtoo-ml-workflow\"\n", "\ttable_id = f'tagtoo_export_results.special_lta_temp_for_update_{current_date}'\n", "\tschema_data = [\n", "\t\t{\"name\": \"permanent\", \"type\": \"STRING\"},\n", "\t\t{\"name\": \"segment_id\", \"type\": \"STRING\"}\n", "\t]\n", "\tresult_df.to_gbq(table_id, project_id=project_id, credentials=google_credentials, table_schema=schema_data, if_exists='append')\n", "\tlogging.info({\n", "\t\t\"message\": \"資料上傳完成\",\n", "\t\t\"project_id\": project_id,\n", "\t\t\"table_id\": table_id,\n", "\t\t\"uploaded_rows\": len(result_df)\n", "\t})\n", "\n", "# return '執行完畢', 200"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}], "metadata": {"kernelspec": {"display_name": "tensorflow", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.10.14"}}, "nbformat": 4, "nbformat_minor": 2}