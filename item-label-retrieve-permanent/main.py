import functions_framework
import sys
import logging
import google.cloud.logging
from google_storage import GoogleStorageIO
from config import GOOGLE_APPLICATION_CREDENTIALS_PATH, PIDS_PATH_PREFIX
from datetime import datetime
from pytz import timezone
import json
import pandas as pd
from dask import dataframe as dd
from make_query import LABEL_QUERY, USER_QUERY
from utils import *
from pipeline import *
import pandas_gbq
from tenacity import retry, stop_after_attempt, wait_fixed, wait_exponential
import os

# 新增本地端logging設定函數
def setup_local_logging():
    # 移除所有現有的處理器
    root_logger = logging.getLogger()
    for handler in root_logger.handlers[:]:
        root_logger.removeHandler(handler)
    
    # 設置非常低的級別以確保捕獲所有訊息
    root_logger.setLevel(logging.DEBUG)
    
    # 添加明確的終端處理器
    console_handler = logging.StreamHandler(sys.stdout)  # 明確輸出到標準輸出
    console_handler.setLevel(logging.DEBUG)  # 設置為DEBUG級別
    formatter = logging.Formatter('%(asctime)s - %(levelname)s - %(message)s')
    console_handler.setFormatter(formatter)
    root_logger.addHandler(console_handler)
    
    # 測試日誌
    root_logger.debug("這是一個DEBUG測試日誌")
    root_logger.info("這是一個INFO測試日誌")
    root_logger.warning("這是一個WARNING測試日誌")

@functions_framework.http
def main(request):

    # 根據環境設定logging
    try:
        # Cloud Functions環境
        logging_client = google.cloud.logging.Client()
        logging_client.setup_logging()
    except Exception:
        # 本地測試環境
        setup_local_logging()
        logging.info("使用本地端logging設定")

    # 解析請求參數
    # 測試 cloud function 部署，請忽略這行註解
    # ---
    # 取得並驗證必要的請求參數
    request_json = request.get_json(silent=True)
    if not request_json:
        return '請求必須包含 JSON 資料', 400
        
    required_params = {
        'user_item_gcs_path': '受眾包檔案的 GCS路徑',
        'page_name': '頁面名稱',
        'save_gate': '保存開關（0 或 1）',
        'upload_gate': '上傳開關（0 或 1）',
        'log_idx': '用來查找日誌的索引值',
        'parallel_gate': '平行處理開關（0 或 1）'
    }

    params = {}
    for param_key, description in required_params.items():
        if param_key not in request_json:
            return f'缺少必要參數 {param_key}：{description}', 400
        params[param_key] = request_json[param_key]
    
    # 處理可選參數
    params['test_mode'] = request_json.get('test_mode', 0)  # 預設為 0 (正式模式)
    
    def validate_binary_param(params, param_name):
        try:
            params[param_name] = int(params[param_name])
            if params[param_name] not in [0, 1]:
                return f'{param_name} 必須為 0 或 1', 400
            return None
        except ValueError:
            return f'{param_name} 必須為數字', 400

    # 檢查 upload_gate
    error = validate_binary_param(params, 'upload_gate')
    if error:
        return error

    # 檢查 save_gate
    error = validate_binary_param(params, 'save_gate')
    if error:
        return error
    
    # 檢查 parallel
    error = validate_binary_param(params, 'parallel_gate')
    if error:
        return error
    
    # 檢查 test_mode
    error = validate_binary_param(params, 'test_mode')
    if error:
        return error
        
    # 解構參數以供後續使用
    page_name = params.get('page_name')
    upload_gate = params.get('upload_gate')
    save_gate = params.get('save_gate')
    user_item_gcs_path = params.get('user_item_gcs_path')
    log_idx = params.get('log_idx')
    parallel_gate = params.get('parallel_gate')
    test_mode = params.get('test_mode')
    current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y%m%d")

    # 商品標籤的資料是固定全部匯入
    # 使用者才是批次處理
    label_gcs_path = f"gs://tagtoo-ml-workflow-cloud-functions/item_label/query_data/label_query_results/{current_date}/*.parquet"

    logging.info({"message": f"[{log_idx}] 參數已匯入", "current_date": current_date})
    google_credentials, project_id = get_google_credentials(GOOGLE_APPLICATION_CREDENTIALS_PATH)
    GSIO = GoogleStorageIO(credentials=google_credentials)
    logging.info({"message": f"[{log_idx}] GSIO 已建立", "project_id": project_id})

    """主要處理流程"""
    # 1. 載入資料
    label_df, user_item_df = load_data_with_dask(
        label_gcs_path,
        user_item_gcs_path,
    )
    logging.info({
        "message": f"[{log_idx}] 資料載入完成",
        "unique_users": len(user_item_df['permanent'].drop_duplicates()),
        "labeled_articles": len(label_df),
    })

    # 2. 處理標籤
    # * 會根據不同資料來源媒合受眾包
    label_df['structured_labels'] = label_df['structured_labels_str'].apply(lambda x: json.loads(x))
    columns_to_extract = [
        'Interests', 'OccupationalTraits', 'PersonalOpinions', 
        'BrowsingMotivations', 'PotentialPainPoints',
        'Category', 'Features', 'Industry', 'MarketSegmentation',
        'PotentialIssues', 'Purpose', 'Tone',
        'CoreDescription', 'Keywords'
    ]
    label_df = extract_label_fields(label_df, columns_to_extract)
    label_df = label_df.assign(label_str=lambda x: x.apply(concat_label_fields, axis=1)).query('label_str != ""')
    logging.info({"message": f"[{log_idx}] 標籤處理完成",})
    
    # 3. 合併與映射處理
    merged_df = process_merged_data(user_item_df, label_df)

    # 4. 篩選出個資料來源
    item_label_map = create_source_mappings(merged_df)
    logging.info({"message": f"[{log_idx}] 資料合併與映射完成",})
    
    # 5. 根據關鍵字大表中的所有 segments 與其關鍵字規則，篩選出符合條件的商品，並查找瀏覽過商品的 permanent
    page_name = page_name or "prod"
    audience_packages_df = read_audience_packages(google_credentials, page_name)
    # 預處理受眾包資料
    audience_packages_df = preprocess_audience_packages(audience_packages_df, item_label_map)

    result_df, process_time = process_segments(audience_packages_df, item_label_map, pids_path_prefix=PIDS_PATH_PREFIX, parallel=parallel_gate)
    logging.info({"message": f"[{log_idx}] 使用者查找完畢", "result_rows": len(result_df), "process_time": process_time})
    
    # 6. 保存結果
    if save_gate:
        filename = f"result_{datetime.now(timezone('Asia/Taipei')).strftime('%Y%m%d%H%M%S')}"
        result_path = f"/tmp/{filename}.csv"
        result_df.to_csv(result_path, index=False)
        result_gcs_path = f"gs://tagtoo-ml-workflow-cloud-functions/item_label/retrieve_permanent/{current_date}/{filename}.csv"
        GSIO.upload_file(localpath=result_path, gsuri=result_gcs_path)
        logging.info({"message": f"[{log_idx}] 結果保存完成", "result_gcs_path": result_gcs_path})

    # 7. 上傳結果
    if upload_gate:
        project_id = "tagtoo-ml-workflow"
        if test_mode:
            table_id = f'temp_tables.special_lta_temp_for_update_{current_date}'
        else:
            table_id = f'tagtoo_export_results.special_lta_temp_for_update_{current_date}'
        
        # 新增必要欄位到 result_df
        current_timestamp = datetime.now(timezone('UTC'))
        execution_id = os.environ.get('X_CLOUD_TRACE_CONTEXT', f'local-run-{datetime.now().strftime("%Y%m%d%H%M%S")}')
        
        result_df = result_df.copy()
        result_df['created_at'] = current_timestamp
        result_df['source_type'] = 'keyword'
        result_df['source_entity'] = 'item-label-retrieve-permanent'
        result_df['execution_id'] = execution_id
        
        schema_data = [
            {"name": "permanent", "type": "STRING"},
            {"name": "segment_id", "type": "STRING"},
            {"name": "created_at", "type": "TIMESTAMP"},
            {"name": "source_type", "type": "STRING"}, 
            {"name": "source_entity", "type": "STRING"},
            {"name": "execution_id", "type": "STRING"}
        ]
        
        @retry(stop=stop_after_attempt(4), wait=wait_exponential(min=10))
        def upload_to_gbq():
            pandas_gbq.to_gbq(
                result_df, 
                destination_table=table_id,
                project_id=project_id,
                credentials=google_credentials,
                table_schema=schema_data,
                if_exists='append'
            )
            logging.info({
                "message": f"[{log_idx}] 資料上傳成功 (attempt successful)",
                "project_id": project_id,
                "table_id": table_id,
                "uploaded_rows": len(result_df)
            })

        try:
            upload_to_gbq()
        except Exception as e:
             logging.error({
                "message": f"[{log_idx}] 資料上傳失敗，重試多次後仍然失敗",
                "project_id": project_id,
                "table_id": table_id,
                "error": str(e)
            })
             return f'資料上傳失敗: {e}', 500


    return '執行完畢', 200

# ? 原本想用 functions_framework 來模擬 cloud function 接收函數的方式，但日誌顯示還有問題，所以先放棄
# if __name__ == "__main__":
    # 啟動 Functions Framework
    # functions_framework.cloud_event(main) # 入口

    # logging.basicConfig(level=logging.DEBUG)
    # logging.info("在主模組中初始化日誌")
    
    # functions_framework.http(main)  # 改用http模式，與裝飾器一致

# ** 如果是直接執行此檔案(本地測試) **
if __name__ == "__main__":
    import argparse
    import flask
    
    # 解決 SSL 憑證問題
    import os
    import certifi
    import ssl

    os.environ['SSL_CERT_FILE'] = certifi.where()
    os.environ['REQUESTS_CA_BUNDLE'] = certifi.where()

    # 設定命令列參數
    parser = argparse.ArgumentParser(description='測試 Cloud Function')
    parser.add_argument('--page_name', type=str, default='prod', help='頁面名稱')
    parser.add_argument('--user_item_gcs_path', type=str, help='使用者資料大表的 GCS 路徑')
    parser.add_argument('--log_idx', type=str, help='日誌索引值')
    parser.add_argument('--save_gate', type=int, default=0, choices=[0, 1], help='保存開關（0 或 1）')
    parser.add_argument('--upload_gate', type=int, default=0, choices=[0, 1], help='上傳開關（0 或 1）')
    parser.add_argument('--parallel_gate', type=int, default=0, help='平行處理開關（0 或 1）')
    parser.add_argument('--test_mode', type=int, default=0, choices=[0, 1], help='測試模式開關（0 或 1）')
    
    args = parser.parse_args()
    
    # 設定日誌記錄
    setup_local_logging()
    logging.info("在主模組中初始化日誌")
    
    # 創建一個模擬的請求對象
    mock_request = flask.Request.from_values(
        json={
            'page_name': args.page_name,
            'save_gate': args.save_gate,
            'upload_gate': args.upload_gate,
            'user_item_gcs_path': args.user_item_gcs_path,
            'log_idx': args.log_idx,
            'parallel_gate': args.parallel_gate,
            'test_mode': args.test_mode
        },
        content_type='application/json'
    )
    
    # 直接調用主函數
    response = main(mock_request)
    
    # 輸出響應
    print(f"\n結果: {response}")