aiohttp
aiohappyeyeballs
aiosignal
async-timeout
attrs
blinker
cachetools
certifi
charset-normalizer
click
cloudevents
cloudpickle
dask
db-dtypes
decorator
deprecated
deprecation
flask
frozenlist
fsspec
functions-framework
gcsfs
google-api-core[grpc]
google-api-python-client
google-auth
google-auth-httplib2
google-auth-oauthlib
google-cloud-appengine-logging
google-cloud-audit-log
google-cloud-bigquery
google-cloud-core
google-cloud-logging
google-cloud-storage
google-crc32c
google-resumable-media
googleapis-common-protos[grpc]
grpc-google-iam-v1
grpcio
grpcio-status
gunicorn
httplib2
idna
importlib-metadata
itsdangerous
jinja2
locket
markupsafe
multidict
numpy
oauthlib
opentelemetry-api
packaging
pandas
pandas-gbq
partd
propcache
proto-plus
protobuf
pyarrow
pyasn1
pyasn1-modules
pydata-google-auth
pyparsing
python-dateutil
pytz
pyyaml
requests
requests-oauthlib
rsa
six
toolz
typing-extensions
tzdata
uritemplate
urllib3
watchdog
werkzeug
wrapt
yarl
zipp
pytest
psutil
snakeviz
tenacity
# The following packages are considered to be unsafe in a requirements file:
# setuptools