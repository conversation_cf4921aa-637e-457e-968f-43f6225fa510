import functions_framework
import logging
import numpy as np
import pandas as pd
import random
import block_timer
import dask_utils
from dask_utils import load_data, parquet_load_data
import joblib
import google_storage
import dask.dataframe as dd
from google.cloud import bigquery
import datetime
from datetime import datetime, timedelta
from pytz import timezone
from typing import List, Tu<PERSON>, Dict, Set
import json
from google.oauth2 import service_account

GSIO = google_storage.GoogleStorageIO()

def trans(alist: pd.Series) -> List[int]:
    """
    Extract values from a dictionary based on indices of a list if the list value > 0.

    Args:
    - alist (List[int]): Input list with string indices and integer values.
    - dict1 (Dict[str, int]): Dictionary with string keys and integer values -> {label_name: semgent_id}.

    Returns:
    - List: A list containing unique **segment_id** extracted from the dictionary.
    """
    temp = list(alist.index[alist > 0])
    # temp = [col for col in alist.index]
    # temp = [*set(temp)] # Remove duplicates from the temporary list
    # temp = [item for item in temp if item is not None]
    # temp = [x for x in temp if type(x) is int]
    # temp = [str(x) for x in temp]
        
    return temp

def str_isin(x,input_str):
  return (input_str == x)

def make_date_arguments(current_datetime: datetime) -> Dict[str, str]:

  DATE_FORMAT = "%Y-%m-%d"
  tmp_dict = {}
  
  tmp_dict['start_date'] = (current_datetime - timedelta(days=31)).strftime(DATE_FORMAT)
  tmp_dict['end_date'] = (current_datetime - timedelta(days=1)).strftime(DATE_FORMAT)

  return tmp_dict

dictionary = {'760': ['761', '762', '763', '764', '765'], 
'776': ['766', '767', '768', '769', '770', '771', '772', '773', '774', '775', '777', '778', '779', '780', '781'], '788': ['776', '782', '783', '784', '785', '786', '787'],
'794': ['789', '792', '793'],
'792': ['790', '791'], '804': ['795', '796', '797', '798', '799', '800', '801', '802', '803'],
'825': ['806', '807', '808', '809', '810', '811', '812', '813', '814', '815', '816', '817', '819', '821', '824'],
'819': ['818', '820'], '821': ['822', '823'], '857': ['826', '838', '856'],
'826': ['827', '828', '829', '830', '831', '832', '833', '834', '835', '836', '837'],
'838': ['839', '840', '841', '842', '843', '844', '845', '846', '847', '848', '849', '850', '851', '852', '853', '854', '855'],
'883': ['858', '859', '860', '861', '862', '863', '864', '865', '866', '867', '868', '869', '870', '871', '872', '873', '874', '875', '876', '877', '878', '879', '880', '881', '882'],
'896': ['884', '885', '886', '887', '888', '889', '890', '891', '892', '893', '894', '895'],
'917': ['897', '898', '910'],
'910': ['899', '900', '901', '902', '903', '904', '905', '906', '907', '908', '909', '911', '912', '913', '914', '915', '916'],
'937': ['918', '919', '920', '921', '922', '923', '924', '925', '926', '927', '928', '929', '930', '931', '932', '933', '934', '935', '936'],
'947': ['939', '946'], '939': ['940', '941', '942', '943', '944', '945'], '955': ['948', '949', '950', '953', '954'], '950': ['951', '952'],
'966': ['956', '957', '958', '959', '960', '961', '962', '963', '964', '965'], '975': ['967', '969', '973'],
'967': ['968'], '969': ['970', '971', '972'], '973': ['974'], '979': ['976', '977', '978'],
'987': ['980', '981', '982', '983', '984', '985', '986', '988', '989', '990'], '1011': ['987', '991', '992', '997'], 
'992': ['993', '994', '995', '996'], '997': ['998', '999', '1000', '1001', '1002', '1003', '1004', '1005', '1006', '1007', '1008', '1009', '1010'],
'1020': ['1012', '1015', '1016', '1017'], '1012': ['1013', '1014'], '1017': ['1018', '1019'], '1023': ['1021', '1022', '1024', '1025', '1026'], '1029': ['1023', '1027', '1028']}

def update_list_with_dictionary_values(input_list, dictionary):
    input_set = set(input_list)  # Convert input_list to a set for faster lookup
    result = []
    for key, values in dictionary.items():
        if any(value in input_set for value in values):  # Check if any value is in input_list
            result.append(key)
    return input_list + result

@functions_framework.http
def main(request):
    
    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)

    GSIO = google_storage.GoogleStorageIO(credentials=credentials)
    today = datetime.now(timezone('Asia/Taipei'))
    date_dict = make_date_arguments(today)

    # ttd mapping
    print("start ttd mapping query")
    client = bigquery.Client(credentials=credentials)

    ttd_query = '''
        SELECT
      tagtoo_cookie AS permanent,
      ttd_cookie
    FROM (
      SELECT
        ROW_NUMBER() OVER(PARTITION BY tagtoo_cookie ORDER BY tagtoo_cookie, created DESC ) AS row_number,
        tagtoo_cookie,
        ttd_cookie,
        created
      FROM
        `gothic-province-823.tagtooad.ttd_cookie_mapping` ) ordered
      inner join `tagtoo-ml-workflow.tagtoo_export_results.lta_model_fb_permanent` p
      on ordered.tagtoo_cookie = p.group_id
    WHERE
      ordered.row_number = 1
      AND ordered.tagtoo_cookie != ''
    '''
    ttd_query_job = client.query(ttd_query)
    df_ttd_mapping = ttd_query_job.to_dataframe()
    # df_ttd_mapping = pd.read_gbq(ttd_query, dialect="standard")
    print("ttd mapping query finish")

    # load data
    # export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature/{date_dict['start_date']}_{date_dict['end_date']}/transformed/*.parquet"
    export_path = f"gs://tagtoo-ml-workflow/topic10/LTA_feature/{date_dict['start_date']}_{date_dict['end_date']}/transformed/*.ftr"
    print(export_path)
    # df = parquet_load_data(export_path)
    df = pd.read_feather(export_path)
    # df = df.iloc[:500]
    df = df.reset_index(drop=True)
 
    # start predicting
    user = df.iloc[:, :1]
    df_feature = df.iloc[:,1:]
    feature = np.array(df_feature)

    # load model 1
    print('loading model')
    GSIO.download_to_path(gsuri='gs://tagtoo-ml-workflow/topic10/firstparty/model/lta_new.pkl', localpath='/tmp/lta_new.pkl')
    clf = joblib.load('/tmp/lta_new.pkl')

    # start predicting
    user = df.iloc[:, :1]
    df_feature = df.iloc[:,1:]
    feature = np.array(df_feature)

    print('start predicting')
    predictions = clf.predict(feature)
    print('finish predicting')

    df = pd.concat([user, pd.DataFrame(predictions)], axis=1)

    # labeling
    # labeling
    cols = [
      'permanent',
      'item_value_1', 'item_value_2', 'item_value_3', 'item_value_4', 'item_value_5', 'item_value_6', 'item_value_7',
      'order_value_1', 'order_value_2', 'order_value_3', 'order_value_4', 'order_value_5', 'order_value_6',
      'purchase_Arcade Equipment', 'purchase_Arts & Crafts', 'purchase_Audio', 'purchase_Baby & Toddler Clothing', 'purchase_Baby & Toddler Furniture', 'purchase_Baby Bathing',
      'purchase_Baby Health', 'purchase_Baby Safety', 'purchase_Baby Toys & Activity Equipment', 'purchase_Baby Transport', 'purchase_Baby Transport Accessories',
      'purchase_Bakery', 'purchase_Baseball & Softball', 'purchase_Basketball', 'purchase_Bathroom Accessories', 'purchase_Beds & Accessories', 'purchase_Benches',
      'purchase_Bird Supplies', 'purchase_Board Games', 'purchase_Book Accessories', 'purchase_Books', 'purchase_Boxing & Martial Arts', 'purchase_Building Consumables',
      'purchase_Building Materials', 'purchase_Business & Home Security', 'purchase_Cabinets & Storage', 'purchase_Camping & Hiking', 'purchase_Candy & Chocolate', 'purchase_Card Games',
      'purchase_Carts & Islands', 'purchase_Cat Supplies', 'purchase_Chairs', 'purchase_Circuit Boards & Components', 'purchase_Clothing Accessories', 'purchase_Coffee',
      'purchase_Communications', 'purchase_Components', 'purchase_Computer Software', 'purchase_Computers', 'purchase_Condiments & Sauces', 'purchase_Cosmetics',
      'purchase_Costumes & Accessories', 'purchase_Cotton Balls', 'purchase_Cotton Swabs', 'purchase_Cycling', 'purchase_DVDs & Videos', 'purchase_Dairy Products',
      'purchase_Decor', 'purchase_Deodorant & Anti-Perspirant', 'purchase_Desktop Computers', 'purchase_Diapering', 'purchase_Digital Goods & Currency',
      'purchase_Dog Supplies', 'purchase_Dresses', 'purchase_Ear Care', 'purchase_Educational Toys', 'purchase_Electronics Accessories', 'purchase_Emergency Preparedness',
      'purchase_Entertainment Centers & TV Stands', 'purchase_Event Tickets', 'purchase_Executive Toys', 'purchase_Exercise & Fitness', 'purchase_Feminine Sanitary Supplies',
      'purchase_Fireplaces', 'purchase_Fish Supplies', 'purchase_Fishing', 'purchase_Flood, Fire & Gas Safety', 'purchase_Foot Care', 'purchase_Furniture Sets', 'purchase_Futon Pads',
      'purchase_Futons', 'purchase_GPS Navigation Systems', 'purchase_General Office Supplies', 'purchase_Grains, Rice & Cereal', 'purchase_Hair Care', 'purchase_Handbag & Wallet Accessories',
      'purchase_Health Care', 'purchase_Heating, Ventilation & Air Conditioning', 'purchase_Hot Chocolate', 'purchase_Household Appliance Accessories', 'purchase_Household Appliances',
      'purchase_Household Supplies', 'purchase_Jewelry', 'purchase_Jewelry Cleaning & Care', 'purchase_Kitchen & Dining', 'purchase_Laptops', 'purchase_Lawn & Garden', 'purchase_Lighting',
      'purchase_Lighting Accessories', 'purchase_Linens & Bedding', 'purchase_Locks & Keys', 'purchase_Magazines', 'purchase_Massage & Relaxation', 'purchase_Meat, Seafood & Eggs',
      'purchase_Milk', 'purchase_Motor Vehicle Parts', 'purchase_Motor Vehicles', 'purchase_Music & Sound Recordings', 'purchase_Musical Instruments', 'purchase_Networking',
      'purchase_Newspapers', 'purchase_Nursing & Feeding', 'purchase_Nuts & Seeds', 'purchase_Office & Chair Mats', 'purchase_Office Carts', 'purchase_Office Equipment',
      'purchase_Office Furniture Accessories', 'purchase_Office Instruments', 'purchase_Oral Care', 'purchase_Ottomans', 'purchase_Outdoor Furniture',
      'purchase_Outdoor Play Equipment', 'purchase_Outerwear', 'purchase_Pants', 'purchase_Parasols & Rain Umbrellas', 'purchase_Party & Celebration', 'purchase_Pasta & Noodles',
      'purchase_Ping Pong', 'purchase_Plants', 'purchase_Plumbing', 'purchase_Pool & Spa', 'purchase_Power & Electrical Supplies', 'purchase_Presentation Supplies', 'purchase_Print, Copy, Scan & Fax',
      'purchase_Puzzles', 'purchase_Religious Veils', 'purchase_Reptile & Amphibian Supplies', 'purchase_Room Divider Accessories', 'purchase_Room Dividers', 'purchase_Seasonings & Spices',
      'purchase_Sex Toys', 'purchase_Shaving & Grooming', 'purchase_Sheet Music', 'purchase_Shelving', 'purchase_Shelving Accessories', 'purchase_Shipping Supplies', 'purchase_Shirts & Tops',
      'purchase_Shoe Accessories', 'purchase_Shoes', 'purchase_Shorts', 'purchase_Skirts', 'purchase_Skorts', 'purchase_Sleeping Aids', 'purchase_Sleepwear & Loungewear', 'purchase_Small Engines',
      'purchase_Smoking Accessories', 'purchase_Soccer', 'purchase_Soda', 'purchase_Soups & Broths', 'purchase_Sports & Energy Drinks', 'purchase_Spray Tanning Tents', 'purchase_Storage Tanks',
      'purchase_Suits', 'purchase_Swaddling & Receiving Blankets', 'purchase_Swimwear', 'purchase_Table Accessories', 'purchase_Tables', 'purchase_Tarot Cards', 'purchase_Tea & Infusions',
      'purchase_Team Handball', 'purchase_Televisions', 'purchase_Tennis', 'purchase_Throwing Darts', 'purchase_Tobacco Products', 'purchase_Tofu, Soy & Vegetarian Products', 'purchase_Tool Accessories',
      'purchase_Tools', 'purchase_Umbrella Sleeves & Cases', 'purchase_Underwear & Socks', 'purchase_Uniforms', 'purchase_Vehicle Maintenance, Care & Decor',
      'purchase_Video Game Consoles', 'purchase_Video Game Software', 'purchase_Vision Care', 'purchase_Wallyball Equipment', 'purchase_Water', 'purchase_Weapons', 'purchase_Wedding & Bridal Party Dresses',
      'view_0', 'view_1', 'view_2', 'view_3', 'view_4', 'view_5', 'view_8', 'view_9', 'view_10', 'view_14', 'view_16', 'view_18', 'view_19', 'view_20', 'view_21', 'view_22', 'view_23', 'view_26', 'view_28',
      'purchase_0', 'purchase_1', 'purchase_2', 'purchase_3', 'purchase_4', 'purchase_5', 'purchase_8', 'purchase_9', 'purchase_14', 'purchase_16',
      'purchase_19', 'purchase_20', 'purchase_21', 'purchase_23', 'purchase_26', 'purchase_28']

    GSIO.download_to_path(
      gsuri="gs://tagtoo-ml-workflow/topic10/firstparty/segment_name_to_id/label_to_segment_id.json",
      localpath="/tmp/label_to_segment_id.json"
    )
    with open("/tmp/label_to_segment_id.json", "r") as file:
        dict1 = json.load(file)
    print('dictionary loaded')

    for i in range(len(cols[1:])):
      try:
        cols[i+1] = str(dict1[cols[i+1]])
      except:
        pass
    
    # for col in df.columns[1:]:  # 跳过 'permanent'
    #   if col in dict1:
    #       df.rename(columns={col: str(dict1[col])}, inplace=True)
    
    df.columns = cols
    # cols = [col for col in df.columns if type(col) is int]
    df = df[['permanent']+[col for col in df.columns if col.isnumeric()]]

    # df.columns = cols
    print('prediction dataframe done')

  
    # df['segment_id'] = df.apply(lambda row: trans(row[2:]), axis = 1)
    df['segment_id'] = df.drop('permanent', axis=1).apply(lambda row: trans(row), axis=1)

    date = datetime.now(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
    name = f'lta_remarketing_{date}'
    
    # df_second_layer = parquet_load_data(f'gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_second_layer.parquet')
    # df = pd.merge(df, df_second_layer, on = ['permanent'])
    # print(df.columns)

    # df['segment_id'] = df['segment_id_x'] + df['segment_id_y'].apply(lambda x: x.tolist())
    # df['segment_id'] = df['segment_id'].apply(lambda x: [*set(x)])
    # df = df[df['segment_id']!=[]]

    df['segment_id'] = df.drop('permanent', axis=1).apply(lambda row: trans(row), axis=1)
    print('finish trans')
    df['segment_id'] = df['segment_id'].apply(lambda x: update_list_with_dictionary_values(x, dictionary))
    print('finish layers')
    df = df[['permanent', 'segment_id']]

    df = df[['permanent', 'segment_id']]
    print("first layer done")

    # ttd mapping
    print("start making tables")
    df_ttd = df.copy()
    # df = df.groupby('permanent', as_index=False).sum()
    # df['segment_id'] = df['segment_id'].apply(lambda x: list(set(x)))
    df['segment_id'] = df['segment_id'].apply(lambda x: ','.join(['tm:d'+i for i in x]))
    # df['segment_id'] = df['segment_id'].str.join(',').apply(lambda x: 'tm:d' + x)
    # df['segment_id'] = df['segment_id'].apply(lambda x:['tm:d'+str(i) for i in x])
    # df['segment_id'] = df['segment_id'].apply(lambda x: ','.join(x) if x!=[] else '')
    df = df[df['segment_id']!='']
    
    # df_ttd['segment_id'] = df_ttd['segment_id'].apply(lambda x: [str(idx) for idx in x])
    # df_ttd['segment_id_length'] = df_ttd['segment_id'].apply(lambda x: len(x))
    # df_ttd = df_ttd[df_ttd['segment_id_length']!=0]
    # df_ttd.drop('segment_id_length', axis=1, inplace=True)
    df_ttd = df_ttd.merge(df_ttd_mapping, on='permanent', how='inner')
    df_ttd = df_ttd[['permanent', 'segment_id', 'ttd_cookie']]
    df_ttd.columns = ['track_user', 'segment_id', 'ttd_cookie']
    df_ttd = df_ttd[~df_ttd['ttd_cookie'].isna()]

    print("tables done")
    
    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    schema = [
    {"name": "permanent", "type": "STRING"},
    {"name": 'segment_id', "type": "STRING"}
    ]
    
    
    # date = datetime.now(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
    # name = f'lta_remarketing_{date}'

    project_id = "tagtoo-ml-workflow"
    table_id = f'tagtoo_export_results.lta_model_fb'
    df.to_gbq(table_id, project_id=project_id, table_schema = schema, if_exists = 'replace') 

    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    # df_second_layer = parquet_load_data(f'gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date}/lta_remarketing_{date}_ttd_model_second_layer.parquet')
    # df_ttd = pd.merge(df_ttd, df_second_layer, on = ['track_user', 'ttd_cookie'])
    # df_ttd['segment_id'] = df_ttd['segment_id_x'] + df_ttd['segment_id_y'].apply(lambda x: x.tolist())
    # df_ttd['segment_id'] = df_ttd['segment_id'].apply(lambda x: [*set(x)])
    # df_ttd.drop(columns=['segment_id_x', 'segment_id_y'], inplace = True)

    import os
    os.environ['GOOGLE_APPLICATION_CREDENTIALS'] = 'tagtoo-ml-workflow-61e119f3d094_new.json'

    rname = f'{name}_ttd_model.csv'
    # rname = f'{name}_ttd_model_first_layer.csv'
    df_ttd.to_csv(f'/tmp/{rname}', index=False)
    GSIO.upload_file(f'/tmp/{rname}', f'gs://tagtoo-ml-workflow/topic10/firstparty/first_party_{date}/{rname}')
            
    print('Job complete.')
    print('fb: ', len(df))
    print('ttd: ', len(df_ttd))

    return 'Success', 200