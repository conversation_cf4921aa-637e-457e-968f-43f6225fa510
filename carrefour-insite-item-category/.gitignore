# .gitignore for Cloud Functions
# 此檔案定義了不應該提交到 Git 的檔案和目錄

# Python
__pycache__/
*.py[cod]
*$py.class
*.so
.Python
build/
develop-eggs/
dist/
downloads/
eggs/
.eggs/
lib/
lib64/
parts/
sdist/
var/
wheels/
*.egg-info/
.installed.cfg
*.egg
MANIFEST

# Virtual environments
venv/
.venv/
env/
.env/
ENV/
env.bak/
venv.bak/

# IDEs
.vscode/
.idea/
*.swp
*.swo
*~

# Jupyter Notebook
.ipynb_checkpoints

# pyenv
.python-version

# pipenv
Pipfile.lock

# pytest
.pytest_cache/
.coverage
htmlcov/
.tox/

# mypy
.mypy_cache/
.dmypy.json
dmypy.json

# Google Cloud
*.json
!cloudbuild.yaml
!package*.json

# Terraform
*.tfstate
*.tfstate.*
.terraform/
.terraform.lock.hcl
terraform.tfvars
!terraform.tfvars.example

# Local configuration
.env
.env.local
.env.*.local
config.local.yaml

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp
.DS_Store
Thumbs.db

# Build artifacts
source.zip
deployment-package.zip

# Local development
local_test.py
dev_test.py
scratch.py

# Documentation builds
docs/_build/

# Coverage reports
.coverage
coverage.xml
*.cover
.hypothesis/

# Backup files
*.bak
*.backup

# OS specific
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes
ehthumbs.db
Thumbs.db
