# Standard Makefile for Cloud Functions
# 此檔案提供標準化的構建和部署命令

# 變數定義
FUNCTION_NAME ?= carrefour-insite-item-category
PROJECT_ID ?= tagtoo-ml-workflow
REGION ?= asia-east1
RUNTIME ?= python311
MEMORY ?= 256MB
TIMEOUT ?= 60s
ENV ?= production

# 顏色定義 (用於美化輸出)
RED := \033[31m
GREEN := \033[32m
YELLOW := \033[33m
BLUE := \033[34m
RESET := \033[0m

# 預設目標
.DEFAULT_GOAL := help

# 幫助信息
.PHONY: help
help: ## 顯示可用的命令
	@echo "$(BLUE)Available commands:$(RESET)"
	@grep -E '^[a-zA-Z_-]+:.*?## .*$$' $(MAKEFILE_LIST) | sort | awk 'BEGIN {FS = ":.*?## "}; {printf "  $(GREEN)%-20s$(RESET) %s\n", $$1, $$2}'

# 開發環境設定
.PHONY: setup
setup: ## 設定開發環境
	@echo "$(YELLOW)Setting up development environment...$(RESET)"
	python -m venv venv
	source venv/bin/activate && pip install --upgrade pip
	source venv/bin/activate && pip install -r requirements.txt
	@echo "$(GREEN)Development environment setup complete!$(RESET)"

# 安裝依賴
.PHONY: install
install: ## 安裝 Python 依賴
	@echo "$(YELLOW)Installing dependencies...$(RESET)"
	pip install -r requirements.txt
	@echo "$(GREEN)Dependencies installed!$(RESET)"

# 代碼格式化
.PHONY: format
format: ## 格式化 Python 代碼
	@echo "$(YELLOW)Formatting code...$(RESET)"
	black .
	isort .
	@echo "$(GREEN)Code formatted!$(RESET)"

# 代碼檢查
.PHONY: lint
lint: ## 執行代碼品質檢查
	@echo "$(YELLOW)Running code quality checks...$(RESET)"
	flake8 --max-line-length=88 --exclude=venv,lib .
	mypy . --ignore-missing-imports
	@echo "$(GREEN)Code quality checks passed!$(RESET)"

# 語法檢查
.PHONY: syntax-check
syntax-check: ## 檢查 Python 語法
	@echo "$(YELLOW)Checking Python syntax...$(RESET)"
	python -m py_compile main.py
	@if [ -f "test_main.py" ]; then python -m py_compile test_main.py; fi
	@echo "$(GREEN)Syntax check passed!$(RESET)"

# 執行測試
.PHONY: test
test: ## 執行單元測試
	@echo "$(YELLOW)Running tests...$(RESET)"
	@if [ -f "test_main.py" ]; then \
		python -m pytest test_main.py -v; \
	else \
		echo "$(YELLOW)No tests found$(RESET)"; \
	fi
	@echo "$(GREEN)Tests completed!$(RESET)"

# 本地執行
.PHONY: run
run: ## 在本地執行 Cloud Function
	@echo "$(YELLOW)Starting local server...$(RESET)"
	@echo "$(BLUE)Function will be available at: http://localhost:8080$(RESET)"
	functions-framework --target=main --port=8080

# 構建檢查
.PHONY: build-check
build-check: syntax-check lint test ## 執行完整的構建前檢查
	@echo "$(GREEN)All build checks passed!$(RESET)"

# 部署到開發環境
.PHONY: deploy-dev
deploy-dev: build-check ## 部署到開發環境
	@echo "$(YELLOW)Deploying to development environment...$(RESET)"
	gcloud functions deploy $(FUNCTION_NAME)-dev \
		--source=. \
		--entry-point=main \
		--runtime=$(RUNTIME) \
		--trigger-http \
		--allow-unauthenticated \
		--memory=$(MEMORY) \
		--timeout=$(TIMEOUT) \
		--region=$(REGION) \
		--project=$(PROJECT_ID) \
		--set-env-vars="ENV=development"
	@echo "$(GREEN)Deployed to development!$(RESET)"

# 部署到生產環境
.PHONY: deploy-prod
deploy-prod: build-check ## 部署到生產環境
	@echo "$(YELLOW)Deploying to production environment...$(RESET)"
	gcloud functions deploy $(FUNCTION_NAME) \
		--source=. \
		--entry-point=main \
		--runtime=$(RUNTIME) \
		--trigger-http \
		--allow-unauthenticated \
		--memory=$(MEMORY) \
		--timeout=$(TIMEOUT) \
		--region=$(REGION) \
		--project=$(PROJECT_ID) \
		--set-env-vars="ENV=production"
	@echo "$(GREEN)Deployed to production!$(RESET)"

# 使用 Cloud Build 部署
.PHONY: deploy-cloudbuild
deploy-cloudbuild: ## 使用 Cloud Build 部署
	@echo "$(YELLOW)Deploying using Cloud Build...$(RESET)"
	gcloud builds submit --config=cloudbuild.yaml --project=$(PROJECT_ID)
	@echo "$(GREEN)Cloud Build deployment submitted!$(RESET)"

# 檢查函數狀態
.PHONY: status
status: ## 檢查 Cloud Function 狀態
	@echo "$(YELLOW)Checking function status...$(RESET)"
	gcloud functions describe $(FUNCTION_NAME) --region=$(REGION) --project=$(PROJECT_ID)

# 查看函數日誌
.PHONY: logs
logs: ## 查看 Cloud Function 日誌
	@echo "$(YELLOW)Fetching function logs...$(RESET)"
	gcloud functions logs read $(FUNCTION_NAME) --region=$(REGION) --project=$(PROJECT_ID) --limit=50

# 刪除函數
.PHONY: delete
delete: ## 刪除 Cloud Function
	@echo "$(RED)Deleting function $(FUNCTION_NAME)...$(RESET)"
	@read -p "Are you sure you want to delete $(FUNCTION_NAME)? [y/N]: " confirm; \
	if [ "$$confirm" = "y" ] || [ "$$confirm" = "Y" ]; then \
		gcloud functions delete $(FUNCTION_NAME) --region=$(REGION) --project=$(PROJECT_ID); \
		echo "$(GREEN)Function deleted!$(RESET)"; \
	else \
		echo "$(YELLOW)Deletion cancelled$(RESET)"; \
	fi

# 清理
.PHONY: clean
clean: ## 清理構建產物和快取
	@echo "$(YELLOW)Cleaning up...$(RESET)"
	find . -type d -name "__pycache__" -exec rm -rf {} + 2>/dev/null || true
	find . -type d -name "*.egg-info" -exec rm -rf {} + 2>/dev/null || true
	find . -type f -name "*.pyc" -delete 2>/dev/null || true
	rm -rf lib/ 2>/dev/null || true
	@echo "$(GREEN)Cleanup complete!$(RESET)"

# 顯示配置
.PHONY: config
config: ## 顯示目前配置
	@echo "$(BLUE)Current Configuration:$(RESET)"
	@echo "  Function Name: $(FUNCTION_NAME)"
	@echo "  Project ID:    $(PROJECT_ID)"
	@echo "  Region:        $(REGION)"
	@echo "  Runtime:       $(RUNTIME)"
	@echo "  Memory:        $(MEMORY)"
	@echo "  Timeout:       $(TIMEOUT)"
	@echo "  Environment:   $(ENV)"

# 初始化新專案
.PHONY: init
init: ## 初始化新的 Cloud Function 專案
	@echo "$(YELLOW)Initializing new Cloud Function project...$(RESET)"
	@if [ ! -f "main.py" ]; then \
		echo "$(RED)Error: main.py not found. Please create main.py first.$(RESET)"; \
		exit 1; \
	fi
	@if [ ! -f "requirements.txt" ]; then \
		echo "$(RED)Error: requirements.txt not found. Please create requirements.txt first.$(RESET)"; \
		exit 1; \
	fi
	@if [ ! -f "cloudbuild.yaml" ]; then \
		echo "$(RED)Error: cloudbuild.yaml not found. Please create cloudbuild.yaml first.$(RESET)"; \
		exit 1; \
	fi
	@echo "$(GREEN)Project structure validated!$(RESET)"
	$(MAKE) setup
	@echo "$(GREEN)Project initialization complete!$(RESET)"

# 顯示專案信息
.PHONY: info
info: ## 顯示專案信息
	@echo "$(BLUE)Project Information:$(RESET)"
	@echo "  Directory:     $$(pwd)"
	@echo "  Function:      $(FUNCTION_NAME)"
	@echo "  Python files:  $$(find . -name '*.py' | wc -l | tr -d ' ')"
	@echo "  Last modified: $$(stat -f '%Sm' main.py)"
	@if [ -f "test_main.py" ]; then echo "  Tests:         Available"; else echo "  Tests:         Not available"; fi
