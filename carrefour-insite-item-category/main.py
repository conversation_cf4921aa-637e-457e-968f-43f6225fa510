"""
{{FUNCTION_NAME}} Cloud Function

此檔案是 {{FUNCTION_NAME}} Cloud Function 的主要進入點。
請根據具體需求修改此檔案。
"""

import functions_framework
from flask import Request
import json
import logging
import os
from typing import Any, Dict

import numpy as np
import pandas as pd
import joblib
import google_storage
from google.cloud import bigquery
from google.cloud.bigquery import LoadJobConfig, WriteDisposition
from google.oauth2 import service_account
from googleapiclient.discovery import build
from googleapiclient.errors import HttpError
from datetime import datetime, timedelta
from pytz import timezone

# 設定日誌
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)


@functions_framework.http
def main(request: Request) -> tuple[str, int]:
    """
    HTTP Cloud Function 主要進入點

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (response_body, status_code)
    """
    try:
        # 解析請求
        if request.method == 'OPTIONS':
            # 處理 CORS 預檢請求
            return handle_cors()

        elif request.method == 'POST':
            # 處理 POST 請求
            return handle_post_request(request)

        elif request.method == 'GET':
            # 處理 GET 請求
            return handle_get_request(request)

        else:
            return json.dumps({
                'error': f'不支援的請求方法: {request.method}',
                'status': 'error'
            }), 405

    except Exception as e:
        logger.error(f"處理請求時發生錯誤: {str(e)}", exc_info=True)
        return json.dumps({
            'error': '內部伺服器錯誤',
            'status': 'error'
        }), 500


def handle_cors() -> tuple[str, int]:
    """處理 CORS 預檢請求"""
    headers = {
        'Access-Control-Allow-Origin': '*',
        'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
        'Access-Control-Allow-Headers': 'Content-Type, Authorization',
        'Access-Control-Max-Age': '3600'
    }
    return '', 204


def handle_post_request(request: Request) -> tuple[str, int]:
    """
    處理 POST 請求

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (response_body, status_code)
    """
    try:
        # 解析 JSON 數據
        request_json = request.get_json(silent=True)
        if not request_json:
            return json.dumps({
                'error': '無效的 JSON 格式',
                'status': 'error'
            }), 400

        logger.info(f"收到 POST 請求: {request_json}")

        # TODO: 在此處添加您的業務邏輯
        result = process_data(request_json)

        response = {
            'status': 'success',
            'data': result,
            'message': '請求處理成功'
        }

        return json.dumps(response, ensure_ascii=False), 200

    except Exception as e:
        logger.error(f"處理 POST 請求失敗: {str(e)}")
        return json.dumps({
            'error': '處理請求失敗',
            'status': 'error'
        }), 500


def handle_get_request(request: Request) -> tuple[str, int]:
    """
    處理 GET 請求

    Args:
        request: Flask Request 物件

    Returns:
        tuple: (response_body, status_code)
    """
    try:
        # 獲取查詢參數
        params = request.args.to_dict()
        logger.info(f"收到 GET 請求，參數: {params}")

        # 呼叫 segment_id mapping 主流程
        result = get_segment_id_mapping()

        response = {
            'status': 'success',
            'data': result,
            'message': '查詢成功'
        }
        return json.dumps(response, ensure_ascii=False), 200

    except Exception as e:
        logger.error(f"處理 GET 請求失敗: {str(e)}")
        return json.dumps({
            'error': '查詢失敗',
            'status': 'error'
        }), 500


def process_data(data: Dict[str, Any]) -> Dict[str, Any]:
    """
    處理業務數據（POST 請求）

    Args:
        data: 輸入數據

    Returns:
        Dict: 處理結果
    """
    logger.info("處理數據中...")

    try:
        # 檢查是否為 scheduler 觸發的同步請求
        if data.get('source') == 'scheduler':
            logger.info("檢測到 scheduler 觸發的同步請求")
            # 執行 segment_id mapping 業務邏輯
            result_df = get_segment_id_mapping()
            
            processed_data = {
                'status': 'success',
                'message': '同步完成',
                'processed_at': datetime.now(timezone('Asia/Taipei')).isoformat(),
                'records_processed': len(result_df),
                'source': 'scheduler'
            }
        else:
            # 其他類型的 POST 請求處理
            processed_data = {
                'original_data': data,
                'processed_at': datetime.now(timezone('Asia/Taipei')).isoformat(),
                'processing_function': 'carrefour-insite-item-category',
                'result': 'success'
            }
        
        logger.info(f"數據處理完成: {processed_data}")
        return processed_data
        
    except Exception as e:
        logger.error(f"處理數據時發生錯誤: {str(e)}", exc_info=True)
        raise


def get_data(params: Dict[str, str]) -> Dict[str, Any]:
    """
    獲取數據（GET 請求）

    Args:
        params: 查詢參數

    Returns:
        Dict: 查詢結果
    """
    # TODO: 實現您的查詢邏輯
    logger.info("查詢數據中...")

    # 範例查詢邏輯
    result_data = {
        'query_params': params,
        'queried_at': '2024-01-01T00:00:00Z',
        'function_name': '{{FUNCTION_NAME}}',
        'data': []  # 在此處添加實際的查詢結果
    }

    return result_data


def get_segment_id_mapping():
    try:
        logger.info("開始執行 get_segment_id_mapping")
        
        # 1. 認證與初始化
        logger.info("初始化認證和服務...")
        credentials = None  # 讓 GCF 自動抓取預設服務帳號
        GSIO = google_storage.GoogleStorageIO(credentials=credentials)
        SCOPES = ["https://www.googleapis.com/auth/spreadsheets.readonly"]
        credentials_sheet = credentials
        client = bigquery.Client(credentials=credentials)
        logger.info("認證初始化完成")

        # 2. 查詢 BigQuery
        logger.info("開始查詢 BigQuery...")
        query = '''
            SELECT
                distinct tr.permanent,
                tr.event.name AS event_name,
                item.name AS item_name,
                item.id AS item_id
            FROM
                `tagtoo-tracking.event_prod.tagtoo_event` tr
            JOIN
                UNNEST(tr.event.items) AS item
            WHERE
                DATE(tr.event_time) BETWEEN DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 1 DAY)
              AND CURRENT_DATE("Asia/Taipei")
              AND tr.ec_id = 715
              AND tr.event.name IN ('view_item', 'add_to_cart', 'purchase')
        '''
        query_job = client.query(query)
        df = query_job.to_dataframe()
        logger.info(f"BigQuery 查詢完成，取得 {len(df)} 筆資料")

        # 3. 讀取 Google Sheet
        logger.info("開始讀取 Google Sheet...")
        SPREADSHEET_ID = "18DhKpzkDpjCTNauZVhRaBJdqjZtA3Pl79PmL-wBe3lQ"
        SHEET_NAME = "所有商品分類"
        RANGE = f"{SHEET_NAME}!A:M"
        try:
            service = build("sheets", "v4", credentials=credentials_sheet)
            values = service.spreadsheets().values().get(
                spreadsheetId=SPREADSHEET_ID,
                range=RANGE,
                majorDimension="ROWS"
            ).execute()["values"]
            logger.info(f"Google Sheet 讀取完成，取得 {len(values)} 行資料")
        except HttpError as e:
            logger.error(f"讀取 Sheet 失敗：{e}")
            raise RuntimeError(f"讀取 Sheet 失敗：{e}")

        columns = values[0]
        rows = values[1:]
        gs_df = pd.DataFrame(rows, columns=columns)
        gs_df["Tagtoo Event 商品編號"] = gs_df["Tagtoo Event 商品編號"].astype(str).str.zfill(5)
        logger.info(f"Google Sheet 資料處理完成，共 {len(gs_df)} 筆")

        # 4. Merge 與 segment_id 生成
        logger.info("開始資料合併和 segment_id 生成...")
        df["prefix"] = df["item_id"].astype(str).str[:5]
        merged = df.merge(
            gs_df,
            how="left",
            left_on="prefix",
            right_on="Tagtoo Event 商品編號"
        )
        merged = merged[merged["Tagtoo Event 商品編號"].notna()].copy()
        logger.info(f"資料合併完成，有效資料 {len(merged)} 筆")

        def build_segment(row):
            if row["event_name"] in ("view_item", "add_to_cart"):
                return ",".join([row.get("瀏覽Segment_id", ""), row.get("瀏覽中分類Segment_id", ""), row.get("瀏覽大分類Segment_id", "")]).rstrip(",")
            elif row["event_name"] == "purchase":
                return ",".join([row.get("購買Segment_id", ""), row.get("購買中分類Segment_id", ""), row.get("購買大分類Segment_id", "")]).rstrip(",")
            return ""
        
        merged["segment_id"] = merged.apply(build_segment, axis=1)
        merged["segment_list"] = merged["segment_id"].str.split(",")

        grouped = (
            merged.groupby("permanent")["segment_list"]
            .sum()
            .apply(lambda x: list(set(filter(None, x))))
            .reset_index()
        )
        grouped["segment_id"] = grouped["segment_list"].apply(lambda lst: ",".join(sorted(lst)))
        logger.info(f"Segment ID 生成完成，共 {len(grouped)} 個用戶")

        final_df = grouped[['permanent','segment_id']]
        
        # 檢查 final_df 是否為空
        if final_df.empty:
            logger.warning("final_df 為空，沒有資料可以上傳")
            return final_df
            
        logger.info(f"準備上傳 {len(final_df)} 筆資料到 BigQuery")

        # 5. 上傳到 GCS
        logger.info("開始上傳到 GCS...")
        current_date = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y-%m-%d")
        output_path = f'gs://tagtoo-ml-workflow/topic10/carrefour_insite_item_category/{current_date}' # output gcs path
        path = "/tmp/result.csv"
        gcs_path = output_path + '/result/result.csv'
        final_df.to_csv(path, index=False)
        GSIO.upload_file(gsuri=gcs_path, localpath=path)
        logger.info(f"GCS 上傳完成: {gcs_path}")

        # 6. 上傳到 BigQuery
        logger.info("開始上傳到 BigQuery...")
        DATE = datetime.now(timezone('Asia/Taipei')).astimezone(timezone('Asia/Taipei')).strftime("%Y%m%d")
        project_id = "tagtoo-ml-workflow"
        table_id = f'tagtoo_export_results.special_lta_temp_for_update_{DATE}'
        
        # 檢查表格是否存在，如果不存在則建立
        try:
            client.get_table(table_id)
            logger.info(f"BigQuery 表格已存在：{table_id}")
        except Exception:
            logger.info(f"BigQuery 表格不存在，正在建立：{table_id}")
            # 建立 schema
            schema = [
                bigquery.SchemaField("permanent", "STRING", mode="REQUIRED"),
                bigquery.SchemaField("segment_id", "STRING", mode="NULLABLE")
            ]
            table = bigquery.Table(table_id, schema=schema)
            client.create_table(table)
            logger.info(f"已建立新表格 {table_id}")
        
        # 使用 LoadJobConfig 進行更穩定的上傳
        job_config = LoadJobConfig(
            write_disposition=WriteDisposition.WRITE_APPEND
        )
        
        try:
            logger.info(f"開始上傳 {len(final_df)} 筆資料到 {table_id}")
            load_job = client.load_table_from_dataframe(final_df, table_id, job_config=job_config)
            load_job.result()  # 等待上傳完成
            logger.info(f"BigQuery 上傳完成: {table_id}，成功上傳 {load_job.output_rows} 筆資料")
            
            # 驗證上傳結果
            table = client.get_table(table_id)
            logger.info(f"表格 {table_id} 目前共有 {table.num_rows} 筆資料")
            
        except Exception as e:
            logger.error(f"BigQuery 上傳失敗: {str(e)}")
            logger.error(f"final_df 資訊: 形狀={final_df.shape}, 欄位={list(final_df.columns)}")
            logger.error(f"final_df 前5筆資料: {final_df.head().to_dict()}")
            raise
        
        logger.info("get_segment_id_mapping 執行完成")
        return final_df
        
    except Exception as e:
        logger.error(f"get_segment_id_mapping 執行失敗: {str(e)}", exc_info=True)
        raise
# 用於本地測試
if __name__ == '__main__':
    from flask import Flask
    app = Flask(__name__)
    app.add_url_rule('/', 'main', main, methods=['GET', 'POST', 'OPTIONS'])
    app.run(debug=True, host='0.0.0.0', port=8080)
