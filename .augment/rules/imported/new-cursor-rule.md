---
type: "agent_requested"
description: "Example description"
---
# 描述
description: 適用於 GCP、Python、網路廣告、資料結構與資料科學專案的 Cursor AI 規則

# 規則清單
rules:
  - pattern: "*.py"  # 適用於 Python 文件
    guidelines:
      - "遵循 PEP 8 代碼風格規範"
      - "優先使用 Python 內建函數與標準庫"
      - "資料處理時，使用 pandas 和 numpy 等高效工具"
      - "機器學習模型應使用 scikit-learn 或 TensorFlow"
      - "確保代碼模組化，避免過於冗長的函數"
      - "為每個函數和類別添加清晰的 docstring 註解"

  - pattern: "gcp_*.py"  # 適用於 GCP 相關的 Python 文件
    guidelines:
      - "使用 Google Cloud 官方提供的 Python SDK（如 google-cloud-storage、google-cloud-bigquery）"
      - "確保所有 API 調用都包含錯誤處理邏輯"
      - "遵循 GCP 的最佳實踐，例如使用 IAM 角色進行權限管理"
      - "避免硬編碼憑證，使用環境變數或 Secret Manager"
      - "對於大規模資料處理，優先使用 BigQuery 或 Dataflow"

  - pattern: "ads_*.py"  # 適用於網路廣告相關的 Python 文件
    guidelines:
      - "使用 Google Ads API 或 Facebook Ads API 進行廣告管理"
      - "確保代碼能處理多種廣告格式（如文字廣告、圖片廣告、影片廣告）"
      - "實現廣告效能追蹤功能，並將數據存儲於 BigQuery 或 Cloud SQL"
      - "遵循 GDPR 和 CCPA 等隱私法規，確保數據處理合規"
      - "為每個廣告活動生成詳細的報告，包含點擊率 (CTR)、轉換率 (CVR) 等指標"

  - pattern: "data_structure_*.py"  # 適用於資料結構相關的 Python 文件
    guidelines:
      - "實現經典資料結構（如鏈結串列、堆疊、佇列、樹、圖）的高效算法"
      - "優化時間與空間複雜度，並在 docstring 中標註"
      - "使用 unittest 或 pytest 為每個資料結構實現單元測試"
      - "對於大規模資料處理，考慮使用分散式資料結構（如 Dask 或 Spark）"

  - pattern: "data_science_*.py"  # 適用於資料科學相關的 Python 文件
    guidelines:
      - "資料清理與預處理應使用 pandas 和 numpy"
      - "視覺化工具優先使用 matplotlib 和 seaborn"
      - "機器學習模型應使用 scikit-learn、TensorFlow 或 PyTorch"
      - "確保模型訓練過程中有適當的交叉驗證"
      - "為每個模型生成性能報告，包含準確率、召回率、F1 分數等指標"
      - "將模型部署至 GCP 的 AI Platform 或 Vertex AI"

  - pattern: "*.yaml"  # 適用於 GCP 配置文件
    guidelines:
      - "遵循 YAML 格式規範，確保縮排正確"
      - "配置文件應包含詳細的註解，說明每個參數的用途"
      - "避免硬編碼敏感信息，使用環境變數或 Secret Manager"
      - "確保配置文件與 Terraform 或 Deployment Manager 集成"

  - pattern: "*.ipynb"  # 適用於 Jupyter Notebook 文件
    guidelines:
      - "每個 Notebook 應包含清晰的章節劃分（如資料清理、特徵工程、模型訓練）"
      - "確保代碼塊簡潔，避免過多的輸出"
      - "為每個步驟添加 Markdown 註解，解釋其目的"
      - "Notebook 結果應可重現，確保所有隨機種子固定"
      - "將 Notebook 轉換為 Python 腳本以便於部署"

# 全域規則
global_guidelines:
  - "所有代碼應具備清晰的結構與命名規範"
  - "確保代碼安全性，避免 SQL 注入與其他常見漏洞"
  - "為每個模組撰寫單元測試，測試覆蓋率應達到 80% 以上"
  - "代碼提交前應通過靜態代碼分析工具（如 pylint 或 mypy）"
  - "每次更新程式碼之前，都要確認目前的內容已經是最新的。"
