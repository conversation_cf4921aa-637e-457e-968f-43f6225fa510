import functions_framework
import pandas as pd
import dask.dataframe as dd
from datetime import datetime
from datetime import timedelta
from typing import List, Dict, Tuple, Set
from ctypes import ArgumentError
from google.oauth2 import service_account
import google_storage
from google.cloud import bigquery
from google.cloud import storage
from config import TIMEZON<PERSON>, DATE_FORMAT


def load_data(path: str, file_format: str = 'PARQUET', **kwargs) -> pd.core.frame.DataFrame:
  if file_format == 'PARQUET':
    df = dd.read_parquet(path, assume_missing=True, **kwargs).compute()
  elif file_format == 'CSV':
    df = dd.read_csv(path, assume_missing=True, **kwargs).compute()
  else:
    raise ArgumentError("Invalid file format")
  return df

def object_exists(bucket_name: str, blob_name: str, credentials: service_account.Credentials) -> bool:
  """Check if an object exists in the GCS bucket.

  Args:
  - bucket_name: Name of the GCS bucket.
  - blob_name: Name (path) of the object within the bucket.

  Returns:
  - True if the object exists, otherwise False.
  """
  storage_client = storage.Client(credentials=credentials)
  bucket = storage_client.bucket(bucket_name)
  blob = bucket.blob(blob_name)
  return blob.exists()


@functions_framework.http
def main(request):
    """HTTP Cloud Function.
    Args:
        request (flask.Request): The request object.
        <https://flask.palletsprojects.com/en/1.1.x/api/#incoming-request-data>
    Returns:
        The response text, or any set of values that can be turned into a
        Response object using `make_response`
        <https://flask.palletsprojects.com/en/1.1.x/api/#flask.make_response>.
    """
    # * external argument
    current_date = datetime.now(TIMEZONE).astimezone(TIMEZONE).strftime(DATE_FORMAT)
    output_path = f'gs://tagtoo-ml-workflow/topic10/industry_price_segment/{current_date}' # output gcs path
    query_path = output_path + '/query/*.parquet'

    # Credentials
    key_path = 'tagtoo-ml-workflow-61e119f3d094_new.json'
    credentials = service_account.Credentials.from_service_account_file(key_path)
    project_id = credentials.project_id
    GSIO = google_storage.GoogleStorageIO(credentials=credentials)

    # * Query if not exist
    bucket_name = 'tagtoo-ml-workflow'
    blob_name = '/'.join(query_path.split('/')[3:-1]) + '/************.parquet'
    if object_exists(bucket_name, blob_name, credentials):
        print(f"{query_path} exist.")
    else:
        QUERY = f"""
        EXPORT DATA
        OPTIONS (
            uri = '{query_path}',
            format = 'PARQUET',
            overwrite = true)
        AS (
    WITH
      UnnestedItems AS (
      SELECT
        DISTINCT permanent,
        items.id AS item_id,
        items.name AS item_name
      FROM
        `tagtoo-tracking.event_prod.tagtoo_event`,
        UNNEST(event.items) items
      WHERE
        DATE(event_time, "Asia/Taipei") = DATE_SUB(CURRENT_DATE("Asia/Taipei"), INTERVAL 1 DAY)
        AND event.name IN ('view_item',
          'purchase') ),
      JoinedById AS (
      SELECT
        UI.permanent,
        PROD.industry_id,
        PROD.price_std_range
      FROM
        UnnestedItems AS UI
      JOIN
        `tagtoo-ml-workflow.data_prod.Product_info` AS PROD
      ON
        UI.item_id = PROD.id ),
      JoinedByName AS (
      SELECT
        UI.permanent,
        PROD.industry_id,
        PROD.price_std_range
      FROM
        UnnestedItems AS UI
      JOIN
        `tagtoo-ml-workflow.data_prod.Product_info` AS PROD
      ON
        UI.item_name = PROD.item_name )
    SELECT
      permanent,
      industry_id,
      price_std_range
    FROM
      JoinedById
    UNION ALL
    SELECT
      permanent,
      industry_id,
      price_std_range
    FROM
      JoinedByName)
        """
        client = bigquery.Client(credentials=credentials)
        query_job = client.query(QUERY)  # API request
        rows = query_job.result()  # Waits for query to finish
        print("Query job complete.")

    # * Load data
    df = load_data(query_path, file_format='PARQUET')
    df.reset_index(drop=True, inplace=True)

    segment_mapping = {
        4: {
            '-4~-3': 'tm:d1815', # 服飾配件產業節約達人
            '-3~-2': 'tm:d1815', # 服飾配件產業節約達人
            '-2~-1': 'tm:d1819', # 服飾配件產業性價比愛好者
            '-1~+1': 'tm:d1823', # 服飾配件產業主流購物族
            '+1~+2': 'tm:d1827', # 服飾配件產業品質追求者
            '+2~+3': 'tm:d1831', # 服飾配件產業奢侈品愛好者
            '+3~+4': 'tm:d1831', # 服飾配件產業奢侈品愛好者
            '>+4': 'tm:d1831', # 服飾配件產業奢侈品愛好者
        },
        8: {
            '-4~-3': 'tm:d1816', # 養生美容產業節約達人
            '-3~-2': 'tm:d1816', # 養生美容產業節約達人
            '-2~-1': 'tm:d1820', # 養生美容產業性價比愛好者
            '-1~+1': 'tm:d1824', # 養生美容產業主流購物族
            '+1~+2': 'tm:d1828', # 養生美容產業品質追求者
            '+2~+3': 'tm:d1832', # 養生美容產業奢侈品愛好者
            '+3~+4': 'tm:d1832', # 養生美容產業奢侈品愛好者
            '>+4': 'tm:d1832', # 養生美容產業奢侈品愛好者
        },
        23: {
            '-4~-3': 'tm:d1817', # 綜合電商產業節約達人
            '-3~-2': 'tm:d1817', # 綜合電商產業節約達人
            '-2~-1': 'tm:d1821', # 綜合電商產業性價比愛好者
            '-1~+1': 'tm:d1825', # 綜合電商產業主流購物族
            '+1~+2': 'tm:d1829', # 綜合電商產業品質追求者
            '+2~+3': 'tm:d1833', # 綜合電商產業奢侈品愛好者
            '+3~+4': 'tm:d1833', # 綜合電商產業奢侈品愛好者
            '>+4': 'tm:d1833', # 綜合電商產業奢侈品愛好者
        },
        26: {
            '-4~-3': 'tm:d1818', # 書報雜誌產業節約達人
            '-3~-2': 'tm:d1818', # 書報雜誌產業節約達人
            '-2~-1': 'tm:d1822', # 書報雜誌產業性價比愛好者
            '-1~+1': 'tm:d1826', # 書報雜誌產業主流購物族
            '+1~+2': 'tm:d1830', # 書報雜誌產業品質追求者
            '+2~+3': 'tm:d1834', # 書報雜誌產業奢侈品愛好者
            '+3~+4': 'tm:d1834', # 書報雜誌產業奢侈品愛好者
            '>+4': 'tm:d1834', # 書報雜誌產業奢侈品愛好者
        }
    }

    results = []
    industry_ids = [
        4, # 服飾產業
        8, # 養生美容
        23, # 綜合電商
        26, # 書報雜誌
    ]

    for ind_id in industry_ids:
        df_ind = df[df['industry_id']==ind_id]
        df_ind['segment_id'] = df_ind['price_std_range'].map(segment_mapping[ind_id])
        results.append(df_ind[['permanent', 'segment_id']])

    result = pd.concat(results)
    result = result.drop_duplicates()

    print("Upload dataframe to BigQuery.")
    project_id = "tagtoo-ml-workflow"
    table_id = f'tagtoo_export_results.special_lta_temp_for_update_{current_date.replace("-", "")}'
    print(f"{project_id}.{table_id}")
    schema_data = [
        {"name": "permanent", "type": "STRING"},
        {"name": "segment_id", "type": "STRING"}
    ]
    result.to_gbq(table_id, project_id=project_id, credentials=credentials, table_schema=schema_data, if_exists='append')

    print("Job complete.")

    return 'Suceess', 200