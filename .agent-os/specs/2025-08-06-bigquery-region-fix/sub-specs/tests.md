# Tests Specification

This is the tests coverage details for the spec detailed in @.agent-os/specs/2025-08-06-bigquery-region-fix/spec.md

> Created: 2025-08-06
> Version: 1.0.0

## Test Coverage

### Unit Tests

**save_query_results Function**
- Test default parameter value is "asia-east1"
- Test function accepts custom location parameter
- Test BigQuery client initialization with correct location
- Test temporary dataset creation in specified region

**main Function**
- Test save_query_results calls use correct region parameter
- Test error handling when BigQuery operations fail

### Integration Tests

**BigQuery Region Functionality**
- Test successful query execution against asia-east1 datasets
- Test temporary dataset creation and cleanup in asia-east1
- Test data extraction from asia-east1 to GCS
- Test error scenarios when dataset truly doesn't exist

### End-to-End Tests

**Complete Workflow Validation**
- Test full HTTP request processing with real BigQuery dataset access
- Test successful data export to GCS from asia-east1 query results
- Test log messages show correct region information
- Verify no 404 "Dataset not found in location US" errors occur

### Mocking Requirements

- **BigQuery Client:** Mock for unit tests, use real client for integration tests
- **GCS Operations:** Mock for unit tests to avoid actual file uploads
- **HTTP Requests:** Mock incoming requests for isolated function testing