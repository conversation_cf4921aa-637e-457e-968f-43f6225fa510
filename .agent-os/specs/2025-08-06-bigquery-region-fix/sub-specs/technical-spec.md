# Technical Specification

This is the technical specification for the spec detailed in @.agent-os/specs/2025-08-06-bigquery-region-fix/spec.md

> Created: 2025-08-06
> Version: 1.0.0

## Technical Requirements

- Update function parameter default value from "US" to "asia-east1" in save_query_results function (line 130)
- Modify hardcoded location parameter in save_query_results call at line 334 in main function
- Modify hardcoded location parameter in save_query_results call at line 344 in main function
- Ensure all BigQuery operations (query execution, dataset creation, table extraction) use asia-east1 region
- Maintain backward compatibility - function signature remains the same
- Preserve all existing error handling and logging functionality

## Approach Options

**Option A:** Update only function calls (keep default parameter as "US")
- Pros: Explicit control over location in each call
- Cons: Requires remembering to specify location in future calls, leaves US as misleading default

**Option B:** Update default parameter and function calls (Selected)
- Pros: Consistent default matches new infrastructure, prevents future mistakes
- Cons: Slightly more changes required

**Rationale:** Option B is selected because it provides better long-term maintainability. Since the dataset has permanently moved to asia-east1, updating the default parameter prevents future developers from accidentally using the wrong region.

## Implementation Details

### File: main.py

**Line 130 Change:**
```python
# Before:
def save_query_results(QUERY: str, credentials, temp_table_id_base: str, destination_uri: str, query_job_location: str = "US"):

# After:  
def save_query_results(QUERY: str, credentials, temp_table_id_base: str, destination_uri: str, query_job_location: str = "asia-east1"):
```

**Line 334 Change:**
```python
# Before:
query_job_location="US"  # Specify job location

# After:
query_job_location="asia-east1"  # Specify job location
```

**Line 344 Change:**
```python
# Before:
query_job_location="US"  # Specify job location

# After:
query_job_location="asia-east1"  # Specify job location
```

## External Dependencies

No new dependencies required. The change only affects configuration parameters within existing Google Cloud BigQuery client usage.