# Spec Requirements Document

> Spec: BigQuery Region Configuration Fix
> Created: 2025-08-06
> Status: Planning

## Overview

Fix BigQuery region configuration in item-label-retrieve-permanent-controller to resolve 404 errors caused by the dataset migration from US to asia-east1 region. This will restore functionality for the save_query_results function and prevent job failures.

## User Stories

### BigQuery Query Execution Failure

As a system administrator, I want the item-label-retrieve-permanent-controller cloud function to query BigQuery datasets in the correct region, so that data processing jobs complete successfully without 404 location errors.

When the function attempts to query the `tagtoo-tracking:event_prod` dataset, it should automatically use the asia-east1 region where the dataset is now located, rather than defaulting to the US region where it previously resided.

## Spec Scope

1. **save_query_results Function Parameter** - Update default query_job_location parameter from "US" to "asia-east1"
2. **Main Function Query Calls** - Update hardcoded location parameters in the two save_query_results function calls
3. **Error Resolution** - Eliminate 404 "Dataset not found in location US" errors

## Out of Scope

- Other cloud functions in the ml-workflow project
- Database schema modifications
- BigQuery dataset migration (already completed by infrastructure team)
- Performance optimization beyond fixing the location mismatch

## Expected Deliverable

1. Cloud function successfully queries BigQuery datasets in asia-east1 region without 404 errors
2. All existing functionality preserved with only region configuration changed
3. Error logs show successful query execution and data export to GCS

## Spec Documentation

- Tasks: @.agent-os/specs/2025-08-06-bigquery-region-fix/tasks.md
- Technical Specification: @.agent-os/specs/2025-08-06-bigquery-region-fix/sub-specs/technical-spec.md
- Tests Specification: @.agent-os/specs/2025-08-06-bigquery-region-fix/sub-specs/tests.md