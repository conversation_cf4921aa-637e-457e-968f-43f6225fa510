# Spec Tasks

These are the tasks to be completed for the spec detailed in @.agent-os/specs/2025-07-29-bigquery-streaming-buffer-monitor/spec.md

> Created: 2025-07-29
> Status: Ready for Implementation

## Tasks

- [ ] 1. Set up project structure and testing framework
  - [ ] 1.1 Write unit tests for main HTTP handler function with mocked BigQuery client
  - [ ] 1.2 Create bigquery-streaming-buffer-monitor directory with standard Cloud Function structure
  - [ ] 1.3 Set up requirements.txt with BigQuery SDK, Functions Framework, and testing dependencies
  - [ ] 1.4 Create Makefile following carrefour-insite-item-category template with test commands
  - [ ] 1.5 Verify initial test framework runs and reports results correctly

- [ ] 2. Implement core BigQuery streaming buffer functionality
  - [ ] 2.1 Write tests for BigQuery client initialization using Application Default Credentials
  - [ ] 2.2 Implement main.py with HTTP request handling (GET, POST, OPTIONS methods)
  - [ ] 2.3 Add BigQuery table reference creation and streaming buffer property access using runtime service account
  - [ ] 2.4 Implement response formatting with JSON structure matching API specification
  - [ ] 2.5 Verify core functionality tests pass with mocked BigQuery responses

- [ ] 3. Add comprehensive error handling and edge cases
  - [ ] 3.1 Write tests for error scenarios (table not found, no buffer, API failures)
  - [ ] 3.2 Implement exception handling for BigQuery API errors with appropriate HTTP status codes
  - [ ] 3.3 Add parameter validation for project_id, dataset_id, table_id inputs
  - [ ] 3.4 Implement CORS handling and unsupported HTTP method responses
  - [ ] 3.5 Verify all error handling tests pass and return correct error responses

- [x] 4. Create Terraform infrastructure deployment
  - [x] 4.1 Write tests to validate Terraform configuration syntax and structure
  - [x] 4.2 Create terraform directory with main.tf based on carrefour-insite-item-category template
  - [x] 4.3 Configure Cloud Function resource with runtime service account email and appropriate memory, timeout, and runtime settings
  - [x] 4.4 Set up IAM <NAME_EMAIL> BigQuery access permissions
  - [x] 4.5 Add 3 Cloud Scheduler resources for 3:00 AM, 5:00 AM, and 7:00 AM UTC+8 with OIDC authentication
  - [x] 4.6 Verify Terraform plan executes successfully and infrastructure tests pass

- [x] 5. Integration testing and production validation
  - [x] 5.1 Write integration tests that connect to actual BigQuery staging environment using runtime service account
  - [x] 5.2 Test function deployment using Terraform in staging environment with runtime service account configuration
  - [x] 5.3 Validate function responds correctly to HTTP requests and Cloud Scheduler triggers with real BigQuery data
  - [x] 5.4 Test Cloud Scheduler jobs execute successfully at configured times with proper OIDC authentication
  - [x] 5.5 Test monitoring integration with existing Cloud_function_monitor system
  - [x] 5.6 Verify all tests pass including unit, integration, scheduler, and deployment validation tests