# Technical Specification

This is the technical specification for the spec detailed in @.agent-os/specs/2025-07-29-bigquery-streaming-buffer-monitor/spec.md

> Created: 2025-07-29
> Version: 1.0.0

## Technical Requirements

- **BigQuery Python SDK Integration:** Use `google-cloud-bigquery` library with Application Default Credentials to access streaming buffer information via `Table.streaming_buffer` property
- **HTTP Cloud Function:** Implement HTTP-triggered function using Functions Framework with JSON request/response handling
- **Cloud Scheduler Integration:** Support scheduled execution via 3 Cloud Scheduler jobs at 3:00 AM, 5:00 AM, and 7:00 AM UTC+8 Taiwan time
- **Runtime Service Account:** Use Cloud Function's runtime service account (<EMAIL>) with Application Default Credentials
- **Target Table:** Primary monitoring target is `gothic-province-823.tagtooad.partner_uid_mapping` with configurable project/dataset/table parameters
- **Response Format:** Return standardized JSON structure with buffer status, estimated metrics, and timestamps
- **Error Resilience:** Handle API timeouts, authentication failures, table not found, and no streaming buffer scenarios
- **Performance:** Sub-second response time for buffer status queries with minimal memory footprint
- **Environment Configuration:** Support project, dataset, and table specification via environment variables and request parameters

## Approach Options

**Option A:** Direct BigQuery Table Object Access
- Pros: Simple implementation, direct SDK usage, minimal API calls
- Cons: Requires table-level permissions, single table per request

**Option B:** BigQuery Jobs API with Custom Query (Selected)
- Pros: More flexible, can handle multiple tables, better error handling
- Cons: More complex implementation, requires job management

**Option C:** BigQuery Information Schema Queries  
- Pros: SQL-based approach, familiar to data engineers
- Cons: Information schema doesn't contain streaming buffer details

**Rationale:** Option A is selected for initial implementation due to simplicity and direct access to streaming buffer properties. The SDK's `Table.streaming_buffer` property provides exactly the required information with minimal overhead. Option B can be considered for future enhancements if multi-table support is needed.

## External Dependencies

- **google-cloud-bigquery>=3.11.0** - Primary BigQuery SDK for streaming buffer access
- **Justification:** Required for accessing BigQuery streaming buffer API and table metadata
- **functions-framework>=3.5.0** - Cloud Functions runtime framework  
- **Justification:** Standard framework for HTTP-triggered Cloud Functions
- **google-cloud-logging>=3.5.0** - Cloud Logging integration
- **Justification:** Structured logging for monitoring and debugging

## Implementation Details

### Function Entry Point
```python
@functions_framework.http
def main(request):
    """Main HTTP handler for BigQuery streaming buffer monitoring"""
```

### Core Logic Flow
1. **Request Validation:** Parse and validate project_id, dataset_id, table_id parameters
2. **BigQuery Client:** Initialize BigQuery client using Application Default Credentials (`client = bigquery.Client()`)
3. **Table Reference:** Create table reference and retrieve table object
4. **Buffer Check:** Access `table.streaming_buffer` property to get current statistics
5. **Response Formation:** Format buffer information into standardized JSON response
6. **Error Handling:** Catch and handle BigQuery API exceptions with appropriate HTTP status codes

### Authentication
- Use Cloud Function's runtime service account: `<EMAIL>`
- Initialize BigQuery client with Application Default Credentials (no explicit credential files)
- Python code: `client = bigquery.Client()` (no credentials parameter)
- Terraform specifies service account email in function configuration
- IAM bindings provide BigQuery access permissions

### Cloud Scheduler Configuration
- **Schedule 1:** Daily at 3:00 AM UTC+8 (19:00 UTC previous day)
- **Schedule 2:** Daily at 5:00 AM UTC+8 (21:00 UTC previous day)  
- **Schedule 3:** Daily at 7:00 AM UTC+8 (23:00 UTC previous day)
- **Authentication:** OIDC authentication to Cloud Function endpoint
- **Payload:** Default monitoring request for partner_uid_mapping table
- **Timezone:** Asia/Taipei for UTC+8 scheduling

### Monitoring Integration
- Integrate with existing `Cloud_function_monitor` system for health checks
- Use structured logging compatible with current monitoring infrastructure
- Follow established error handling and alerting patterns from other functions