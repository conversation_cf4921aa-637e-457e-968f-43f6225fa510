# API Specification

This is the API specification for the spec detailed in @.agent-os/specs/2025-07-29-bigquery-streaming-buffer-monitor/spec.md

> Created: 2025-07-29
> Version: 1.0.0

## Endpoints

### GET /

**Purpose:** Retrieve BigQuery streaming buffer status for specified table
**Parameters:** 
- `project_id` (optional): GCP project ID, defaults to "gothic-province-823"
- `dataset_id` (optional): BigQuery dataset ID, defaults to "tagtooad" 
- `table_id` (optional): BigQuery table ID, defaults to "partner_uid_mapping"

**Response:** JSON object with streaming buffer statistics
**Errors:** 
- 400: Invalid parameters or malformed request
- 403: Insufficient permissions to access BigQuery table
- 404: Table not found
- 500: Internal server error or BigQuery API failure

**Example Request:**
```
GET /?project_id=gothic-province-823&dataset_id=tagtooad&table_id=partner_uid_mapping
```

**Example Response (Buffer Present):**
```json
{
  "has_streaming_buffer": true,
  "estimated_rows": "68849",
  "estimated_bytes": "9338470",
  "oldest_entry_time": "2025-07-29T03:01:59+00:00",
  "table_reference": {
    "project_id": "gothic-province-823",
    "dataset_id": "tagtooad", 
    "table_id": "partner_uid_mapping"
  },
  "queried_at": "2025-07-29T11:50:15+00:00",
  "status": "success"
}
```

**Example Response (No Buffer):**
```json
{
  "has_streaming_buffer": false,
  "estimated_rows": "0",
  "estimated_bytes": "0", 
  "oldest_entry_time": null,
  "table_reference": {
    "project_id": "gothic-province-823",
    "dataset_id": "tagtooad",
    "table_id": "partner_uid_mapping"
  },
  "queried_at": "2025-07-29T11:50:15+00:00",
  "status": "success"
}
```

### POST /

**Purpose:** Retrieve streaming buffer status with request body parameters (alternative to GET)
**Parameters:** JSON body with same parameter structure as GET
**Response:** Identical to GET endpoint
**Errors:** Same as GET endpoint plus JSON parsing errors

### OPTIONS /

**Purpose:** CORS preflight handling
**Parameters:** None
**Response:** Empty response with CORS headers
**Errors:** None

## Controllers

### main(request)
**Action Name:** HTTP request handler and router
**Business Logic:** 
- Route requests based on HTTP method (GET, POST, OPTIONS)
- Validate and extract table specification parameters
- Initialize BigQuery client using Application Default Credentials (`client = bigquery.Client()`)
- Execute buffer status query using runtime service account authentication
- Format response with standardized JSON structure
**Error Handling:**
- Catch BigQuery API exceptions and return appropriate HTTP status codes
- Handle authentication and permission errors with detailed error messages  
- Validate input parameters and return 400 for invalid requests

### get_buffer_status(project_id, dataset_id, table_id)
**Action Name:** Core buffer status retrieval logic
**Business Logic:**
- Create BigQuery table reference from parameters
- Access table.streaming_buffer property to get current statistics
- Extract buffer metrics including row count, byte size, and oldest entry time
- Handle cases where streaming buffer is None (no active streaming)
**Error Handling:**
- Handle table not found exceptions
- Manage BigQuery API rate limiting and timeouts
- Provide detailed logging for debugging and monitoring

### format_response(buffer_stats, table_ref)
**Action Name:** Response formatting and standardization
**Business Logic:**
- Convert BigQuery streaming buffer object to JSON-serializable format
- Add metadata including query timestamp and table reference
- Ensure consistent response structure across all scenarios
**Error Handling:**
- Handle datetime serialization for timestamp fields
- Manage null/None values for missing buffer data
- Validate response structure before returning