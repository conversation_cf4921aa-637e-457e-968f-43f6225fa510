# Tests Specification

This is the tests coverage details for the spec detailed in @.agent-os/specs/2025-07-29-bigquery-streaming-buffer-monitor/spec.md

> Created: 2025-07-29
> Version: 1.0.0

## Test Coverage

### Unit Tests

**TestMainFunction**
- test_get_request_with_defaults - Test GET request using default table parameters
- test_get_request_with_custom_params - Test GET request with custom project/dataset/table
- test_post_request_with_json_body - Test POST request with JSON parameters
- test_options_request_cors - Test CORS preflight handling returns proper headers
- test_unsupported_http_method - Test DELETE/PUT methods return 405 Method Not Allowed
- test_invalid_parameters - Test malformed or missing required parameters return 400

**TestBufferStatusRetrieval**
- test_buffer_status_with_active_buffer - Test table with active streaming buffer returns proper statistics
- test_buffer_status_no_buffer - Test table with no streaming buffer returns has_streaming_buffer: false
- test_table_not_found - Test non-existent table returns 404 error
- test_bigquery_client_authentication_error - Test authentication failure scenarios
- test_bigquery_api_timeout - Test API timeout handling and retry logic
- test_permission_denied - Test insufficient BigQuery permissions return 403

**TestResponseFormatting**
- test_format_response_with_buffer - Test response formatting for active buffer scenario
- test_format_response_no_buffer - Test response formatting when no buffer exists
- test_datetime_serialization - Test proper ISO 8601 datetime formatting in responses
- test_response_structure_validation - Test all required fields present in response JSON
- test_large_numbers_handling - Test proper handling of large estimated_bytes values

### Integration Tests

**TestBigQueryIntegration**
- test_real_table_buffer_query - Test actual BigQuery API call to partner_uid_mapping table
- test_service_account_authentication - Test service account authentication works properly
- test_cross_project_table_access - Test accessing table in different project
- test_api_rate_limiting_behavior - Test function behavior under BigQuery API rate limits

**TestEndToEndWorkflow**
- test_complete_get_workflow - Test full GET request workflow from HTTP input to JSON response
- test_complete_post_workflow - Test full POST request workflow with JSON body
- test_error_scenarios_end_to_end - Test complete error handling workflows
- test_concurrent_requests - Test function behavior under concurrent request load

### Mocking Requirements

- **BigQuery Client:** Mock `google.cloud.bigquery.Client` for unit tests to avoid actual API calls
- **Table Object:** Mock `google.cloud.bigquery.Table` with streaming_buffer property scenarios
- **Authentication:** Mock service account authentication for testing different auth scenarios
- **HTTP Request:** Mock Flask Request objects for testing different HTTP method scenarios
- **Datetime:** Mock datetime.utcnow() for consistent timestamp testing in responses
- **BigQuery Exceptions:** Mock various BigQuery exception types (NotFound, Forbidden, etc.)

## Test Data Scenarios

### Active Buffer Scenario
```python
mock_streaming_buffer = Mock()
mock_streaming_buffer.estimated_rows = 68849
mock_streaming_buffer.estimated_bytes = 9338470
mock_streaming_buffer.oldest_entry_time = datetime(2025, 7, 29, 3, 1, 59, tzinfo=timezone.utc)
```

### No Buffer Scenario  
```python
mock_table.streaming_buffer = None
```

### Error Scenarios
```python
# Table not found
mock_client.get_table.side_effect = NotFound("Table not found")

# Permission denied  
mock_client.get_table.side_effect = Forbidden("Insufficient permissions")

# API timeout
mock_client.get_table.side_effect = TimeoutError("Request timeout")
```

## CI/CD Integration

### Makefile Test Commands
- `make test` - Run complete test suite including unit and integration tests
- `make test-unit` - Run only unit tests with mocked dependencies
- `make test-integration` - Run integration tests against actual BigQuery (for staging)
- `make test-coverage` - Generate test coverage reports with minimum 90% threshold

### GitHub Actions Requirements
- All tests must pass before deployment is allowed
- Integration tests run against staging environment before production deployment  
- Test coverage reports uploaded to monitoring dashboard
- Failed tests block PR merges and deployment pipeline

### Test Environment Setup
- Mock BigQuery service account credentials for unit tests
- Staging BigQuery environment with test tables for integration tests
- Automated test data cleanup after integration test runs
- Test isolation ensuring tests don't interfere with each other