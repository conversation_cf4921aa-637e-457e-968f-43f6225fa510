```mermaid
graph TD
    %% BigQuery Region Configuration Change - Dependency Map
    
    %% Core Infrastructure Nodes
    A[BigQuery Client] --> B[save_query_results Function]
    
    %% Business Logic Nodes  
    B --> C[main Function - Line 334]
    B --> D[main Function - Line 344]
    
    %% Configuration Node
    E[config.py LOCATION_ID] --> F[create_cloud_task Function]
    
    %% Leaf Node - The Change Target
    G[query_job_location Parameter] --> B
    
    %% External Dependencies (Outside this function)
    H[tagtoo-tracking:event_prod Dataset] --> A
    I[GCS Bucket] --> B
    
    %% Styling and classification
    classDef coreNode fill:#ff6b6b,stroke:#000,stroke-width:3px
    classDef businessNode fill:#ffd93d,stroke:#000,stroke-width:2px  
    classDef leafNode fill:#6bcf7f,stroke:#000,stroke-width:1px
    classDef configNode fill:#87ceeb,stroke:#000,stroke-width:2px
    
    class A,H,I coreNode
    class B,C,D,F businessNode
    class G leafNode
    class E configNode
```